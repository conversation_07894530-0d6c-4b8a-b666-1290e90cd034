#!/usr/local/bin/php -q
<?php
/**
 * 더존 자동전표 API 연동 - 매입전표 (PH23)
 * 기존 sp_duzon_ph23.psh를 더존 API 호출 방식으로 변환
 * PDO 드라이버 문제 해결을 위해 기존 DBController 사용
 */

// 동적 경로 설정 - Windows와 Linux 모두 지원
$ROOT_PATH = dirname(__DIR__);
$_SERVER['DOCUMENT_ROOT'] = $ROOT_PATH;
define('ROOT_PATH', $ROOT_PATH);

include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/douzone/api_config.php");

// PH23용 wehago-sign 인증 API 헬퍼 클래스
class DouzoneApiHelper {
    private $apiUrl;
    private $authToken;
    private $companyCode;
    private $logFile;
    
    public function __construct($apiUrl, $authToken, $companyCode) {
        $this->apiUrl = $apiUrl;
        $this->authToken = $authToken;
        $this->companyCode = $companyCode;
        $this->logFile = '/home/<USER>/log/douzone_api_' . date('Ymd') . '.log';
    }
    
    private function getHashKey() {
        $api_config = getDouzoneApiConfig();
        return $api_config['hash_key'];
    }
    
    private function getCallerName() {
        $api_config = getDouzoneApiConfig();
        return $api_config['caller_name'];
    }
    
    private function getGroupSeq() {
        $api_config = getDouzoneApiConfig();
        return $api_config['group_seq'];
    }
    
    /**
     * 자동전표 등록 API 호출
     */
    public function registerAutoVoucher($data) {
        $endpoint = '/apiproxy/api11A37'; // 매입매출자동전표 API
        $url = $this->apiUrl . $endpoint;
        
        // wehago-sign 인증에 필요한 값들
        $timestamp = time();
        $transactionId = uniqid('', true);
        
        // Simple_debug.php 방식: authToken + transactionId + timestamp + endpoint
        $message = $this->authToken . $transactionId . $timestamp . $endpoint;
        $wehagoSign = base64_encode(hash_hmac('sha256', $message, $this->getHashKey(), true));
        
        $requestData = [
            'items' => []
        ];
        
        foreach ($data as $item) {
            $requestData['items'][] = [
                'coCd' => $this->companyCode,
                'inDivCd' => $item['inDivCd'],
                'acctTy' => $item['acctTy'] ?? '2', // 1: 매출, 2: 매입
                'acctFg' => $item['acctFg'] ?? '1000',
                'refDt' => $item['refDt'],
                'trCd' => $item['trCd'],
                'trNm' => $item['trNm'],
                'regNb' => $item['regNb'] ?? '',
                'taxFg' => $item['taxFg'],
                'clsgAm' => $item['clsgAm'],
                'clsvAm' => $item['clsvAm'],
                'clshAm' => $item['clshAm'],
                'bankAm' => $item['bankAm'] ?? 0,
                'misuAm' => $item['misuAm'],
                'baNb' => $item['baNb'] ?? '',
                'isuDoc' => $item['isuDoc'] ?? '',
                'rmkDc' => $item['rmkDc'],
                'attrCd' => $item['attrCd'] ?? '',
                'ctDept' => $item['ctDept'] ?? '',
                'pjtCd' => $item['pjtCd'] ?? '',
                'approKey' => $item['approKey'],
                'jeonjaYn' => $item['jeonjaYn'] ?? '',
                'cardCd' => $item['cardCd'] ?? '',
                'dummy1' => $item['dummy1'] ?? '',
                'ctNb' => $item['ctNb'] ?? '',
                'dummy2' => $item['dummy2'] ?? '',
                'ctQt' => $item['ctQt'] ?? 0,
                'issNo' => $item['issNo'] ?? ''
            ];
        }
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->authToken,
            'Accept: application/json',
            'wehago-sign: ' . $wehagoSign,
            'transaction-id: ' . $transactionId,
            'timestamp: ' . $timestamp,
            'callerName: ' . $this->getCallerName(),
            'groupSeq: ' . $this->getGroupSeq()
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $this->log("CURL Error: " . $error);
            return ['success' => false, 'error' => $error];
        }
        
        if ($httpCode !== 200) {
            $this->log("HTTP Error: {$httpCode} - {$response}");
            return ['success' => false, 'error' => "HTTP {$httpCode}", 'response' => $response];
        }
        
        $result = json_decode($response, true);
        $this->log("API Response: " . $response);
        
        return ['success' => true, 'result' => $result];
    }
    
    /**
     * API 결과 로깅
     */
    public function logApiResult($result, $context = '') {
        $message = "[" . date('Y-m-d H:i:s') . "] {$context}: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($this->logFile, $message, FILE_APPEND);
        echo $message;
    }
    
    private function log($message) {
        $logMessage = "[" . date('Y-m-d H:i:s') . "] " . $message . "\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
    }
}

// SPERP 데이터베이스 연결
$dbconn_e = new DBController($db['sperp_posbank']);
if(empty($dbconn_e->success)) {
    echo "[" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패입니다.";
    exit;
}

// API 설정 (api_config.php에서 로드)
$api_config = getDouzoneApiConfig();

$douzone_api = new DouzoneApiHelper(
    $api_config['api_url'],
    $api_config['auth_token'],
    $api_config['company_code']
);

echo date("Y-m-d H:i:s")." - API 전송 시작 (매입전표)\n";

// 매입 데이터 조회 SQL (기존 ph23.psh와 동일)
$SQL = "SELECT KEY,
        ROW_NUMBER() OVER(PARTITION BY KEY ORDER BY KEY, GU2,FN_CD) AS SNO,
        RCT_CODE,HDATE,HDATE2,CT_CODE,CT_NAME,AMT,VAT,AMT+VAT CUAMT,AMT2,CT_LINK,CT_LINK2, 
        PRNAME,HNO,HNO2,HGU,DZ_LINK,TMSTYLE,ACSTYLE,FN_CD,FN_LINK,PH23AC_GUBUN,PH99,
        SPERP_CPYID,GU1,GU1_NAME,CHA,CHA_NAME,GU2
    FROM(        
     -- 매입 금액
     SELECT CASE WHEN PH99='1' THEN HDATE2||HNO2 ELSE (A.HDATE || A.HNO) END AS KEY, 
            A.RCT_CODE, A.HDATE, HDATE2, A.CT_CODE, C.CT_NAME, D.AMT, 0 VAT, 0 AMT2,C.CT_LINK, C.CT_LINK2, 
            B.PRNAME, A.HNO,HNO2, A.HGU, A.DZ_LINK, A.TMSTYLE, A.ACSTYLE,D.FN_CD,D.FN_LINK,PH23AC_GUBUN,PH99,
            CASE WHEN PH99='1' THEN '5'|| HNO2 ELSE '1'|| A.HNO END AS SPERP_CPYID,
            CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '21'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '27'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '28'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '25'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '21'
                 WHEN A.TMSTYLE='O' THEN '22'
                 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='4' THEN '25'
                 WHEN A.TMSTYLE='Z' AND A.ACSTYLE<>'4' THEN '23'
            END  GU1,
            CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '과세매입'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '카드매입'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '현금영수증매입'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '수입'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '과세매입'
                 WHEN A.TMSTYLE='O' THEN '영세매입'
                 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='4' THEN '수입'
                 WHEN A.TMSTYLE='Z' AND A.ACSTYLE<>'4' THEN '면세매입'
            END  GU1_NAME,
            CASE WHEN D.FN_CD='11305001' THEN 'D'
                 WHEN (D.FN_CD='21101001' OR D.FN_CD='11301003' OR D.FN_CD='21105003') OR (PH99='1' AND D.FN_CD='11207000')  THEN 'C'
                 ELSE 'D'
            END CHA,
            CASE WHEN D.FN_CD='11305001' THEN '3'
                 WHEN (D.FN_CD='21101001' OR D.FN_CD='11301003' OR D.FN_CD='21105003') OR (PH99='1' AND D.FN_CD='11207000')  THEN '4'
                 ELSE '3'
            END CHA_NAME,
            '1' GU2
     FROM TMH A, TMS B, CT C, 
          ( 
           SELECT A.TMHID, CASE WHEN A.HCT_CODE='100042' THEN '11207000' ELSE (C.BAS_OP5) END FN_CD,nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
                  AC_GUBUN PH23AC_GUBUN, '0' PH99,SUBSTR(A.TMHID, 5, 8) HDATE2,SUBSTR(A.TMHID, 15,4) HNO2,
                  SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT
           FROM PD23 A
                LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE 
                LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%' 
                LEFT JOIN PH23 H ON A.RCT_CODE=H.RCT_CODE AND A.HDATE=H.HDATE AND A.HNO=H.HNO 
                LEFT JOIN FN D ON C.BAS_OP5 = D.FN_CD1 || D.FN_CD2
           WHERE A.TMHID IS NOT NULL 
             AND A.STATE = '8' 
             AND SUBSTR(A.TMHID, 5,8) >= TO_CHAR(sysdate - 45, 'YYYYMMDD')
           GROUP BY A.TMHID, C.BAS_OP5,A.HCT_CODE,AC_GUBUN,nvl(D.FN_LINK2,D.FN_LINK)
           Union All
             SELECT A.TMHID,C.BAS_OP5 FN_CD,nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
                    AC_GUBUN PH23AC_GUBUN, '1' PH99,SUBSTR(H.PH99_LINK, 5,8) HDATE2,SUBSTR(H.PH99_LINK, 13,4) HNO2,
                    SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT
             FROM PD23 A
                  LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE
                  LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%'
                  LEFT JOIN PH23 H ON A.RCT_CODE=H.RCT_CODE AND A.HDATE=H.HDATE AND A.HNO=H.HNO
                  LEFT JOIN FN D ON C.BAS_OP5 = D.FN_CD1 || D.FN_CD2
                  WHERE A.TMHID Is Not Null
               AND A.STATE = '8'
               AND SUBSTR(H.PH99_LINK, 5,8) >= TO_CHAR(sysdate - 45, 'YYYYMMDD')
               AND H.HCT_CODE='100042' AND NVL(H.ARRIVAL_STATE,' ')='1' AND H.PH99_LINK IS NOT NULL
                  GROUP BY A.TMHID, C.BAS_OP5,A.HCT_CODE,AC_GUBUN,H.PH99_LINK,nvl(D.FN_LINK2,D.FN_LINK)
          ) D 
     WHERE A.RCT_CODE = '1000' 
       AND A.TCHK <> 'Y'  
       AND A.RCTYPE = 'C'  
       AND A.HGU = '23'  
       AND A.RCT_CODE = B.RCT_CODE 
       AND A.HDATE = B.HDATE 
       AND A.HNO = B.HNO 
       AND A.HGU = B.HGU 
       AND B.SNO = 1 
       AND A.CT_CODE = C.CT_CODE (+)
       AND SUBSTR(D.TMHID, 0, 4) = A.RCT_CODE 
       AND SUBSTR(D.TMHID, 5, 8) = A.HDATE 
       AND SUBSTR(D.TMHID, 13, 2) = A.HGU 
       AND SUBSTR(D.TMHID, 15, 4) = A.HNO 
       AND (D.AMT + D.VAT) <> 0 
    
    UNION ALL
    -- 부가세대급금
    SELECT CASE WHEN PH99='1' THEN HDATE2||HNO2 ELSE (A.HDATE || A.HNO) END AS KEY, 
            A.RCT_CODE, A.HDATE, HDATE2, A.CT_CODE, C.CT_NAME, 0 AMT, VAT,D.AMT2,C.CT_LINK, C.CT_LINK2, 
            B.PRNAME, A.HNO,HNO2, A.HGU, A.DZ_LINK, A.TMSTYLE, A.ACSTYLE,'11305001' FN_CD, '13500' FN_LINK ,PH23AC_GUBUN,PH99,
            CASE WHEN PH99='1' THEN '5'|| HNO2 ELSE '1'|| A.HNO END AS SPERP_CPYID,
            CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '21'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '27'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '28'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '25'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '21'
                 WHEN A.TMSTYLE='O' THEN '22'
                 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='4' THEN '25'
                 WHEN A.TMSTYLE='Z' AND A.ACSTYLE<>'4' THEN '23'
            END  GU1,
            CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '과세매입'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '카드매입'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '현금영수증매입'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '수입'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '과세매입'
                 WHEN A.TMSTYLE='O' THEN '영세매입'
                 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='4' THEN '수입'
                 WHEN A.TMSTYLE='Z' AND A.ACSTYLE<>'4' THEN '면세매입'
            END  GU1_NAME,
            'D' CHA,
            '3' CHA_NAME,
            '2' GU2
     FROM TMH A, TMS B, CT C, 
          ( 
           SELECT A.TMHID, CASE WHEN A.HCT_CODE='100042' THEN '11207000' ELSE (C.BAS_OP5) END FN_CD,nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
                  AC_GUBUN PH23AC_GUBUN, '0' PH99,SUBSTR(A.TMHID, 5, 8) HDATE2,SUBSTR(A.TMHID, 15,4) HNO2,
                  SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT,SUM(A.AMT) AS AMT2
           FROM PD23 A
                LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE 
                LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%' 
                LEFT JOIN PH23 H ON A.RCT_CODE=H.RCT_CODE AND A.HDATE=H.HDATE AND A.HNO=H.HNO 
                LEFT JOIN FN D ON C.BAS_OP5 = D.FN_CD1 || D.FN_CD2
           WHERE A.TMHID IS NOT NULL 
             AND A.STATE = '8' 
             AND SUBSTR(A.TMHID, 5,8) >= TO_CHAR(sysdate - 45, 'YYYYMMDD')
           GROUP BY A.TMHID, C.BAS_OP5,A.HCT_CODE,AC_GUBUN,nvl(D.FN_LINK2,D.FN_LINK)
           Union All
             SELECT A.TMHID,C.BAS_OP5 FN_CD,nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
                    AC_GUBUN PH23AC_GUBUN, '1' PH99,SUBSTR(H.PH99_LINK, 5,8) HDATE2,SUBSTR(H.PH99_LINK, 13,4) HNO2,
                    SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT,SUM(A.AMT) AS AMT2
             FROM PD23 A
                  LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE
                  LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%'
                  LEFT JOIN PH23 H ON A.RCT_CODE=H.RCT_CODE AND A.HDATE=H.HDATE AND A.HNO=H.HNO
                  LEFT JOIN FN D ON C.BAS_OP5 = D.FN_CD1 || D.FN_CD2
                  WHERE A.TMHID Is Not Null
               AND A.STATE = '8'
               AND SUBSTR(H.PH99_LINK, 5,8) >= TO_CHAR(sysdate - 45, 'YYYYMMDD')
               AND H.HCT_CODE='100042' AND NVL(H.ARRIVAL_STATE,' ')='1' AND H.PH99_LINK IS NOT NULL
                  GROUP BY A.TMHID, C.BAS_OP5,A.HCT_CODE,AC_GUBUN,H.PH99_LINK,nvl(D.FN_LINK2,D.FN_LINK)
          ) D 
     WHERE A.RCT_CODE = '1000' 
       AND A.TCHK <> 'Y'  
       AND A.RCTYPE = 'C'  
       AND A.HGU = '23'  
       AND A.RCT_CODE = B.RCT_CODE 
       AND A.HDATE = B.HDATE 
       AND A.HNO = B.HNO 
       AND A.HGU = B.HGU 
       AND B.SNO = 1 
       AND A.CT_CODE = C.CT_CODE (+)
       AND SUBSTR(D.TMHID, 0, 4) = A.RCT_CODE 
       AND SUBSTR(D.TMHID, 5, 8) = A.HDATE 
       AND SUBSTR(D.TMHID, 13, 2) = A.HGU 
       AND SUBSTR(D.TMHID, 15, 4) = A.HNO 
       AND (D.AMT + D.VAT) <> 0 
    
    UNION ALL
    -- 외상매입금/선급금/미지급금
    SELECT CASE WHEN PH99='1' THEN HDATE2||HNO2 ELSE (A.HDATE || A.HNO) END AS KEY, 
            A.RCT_CODE, A.HDATE, HDATE2, A.CT_CODE, C.CT_NAME, D.AMT AMT, D.VAT,0 AMT2, C.CT_LINK, C.CT_LINK2, 
            B.PRNAME, A.HNO,HNO2, A.HGU, A.DZ_LINK, A.TMSTYLE, A.ACSTYLE,
            DECODE(PH99,'0',DECODE(PH23AC_GUBUN,'0','21101001','1','11301003','2','21105003'),'1','11207000') FN_CD, 
            DECODE(PH99,'0',DECODE(PH23AC_GUBUN,'0','25100','1','13100','2','25300'),'1','15600') FN_LINK,
            PH23AC_GUBUN,PH99,
            CASE WHEN PH99='1' THEN '5'|| HNO2 ELSE '1'|| A.HNO END AS SPERP_CPYID,
            CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '21'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '27'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '28'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '25'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '21'
                 WHEN A.TMSTYLE='O' THEN '22'
                 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='4' THEN '25'
                 WHEN A.TMSTYLE='Z' AND A.ACSTYLE<>'4' THEN '23'
            END  GU1,
            CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '과세매입'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '카드매입'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '현금영수증매입'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '수입'
                 WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '과세매입'
                 WHEN A.TMSTYLE='O' THEN '영세매입'
                 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='4' THEN '수입'
                 WHEN A.TMSTYLE='Z' AND A.ACSTYLE<>'4' THEN '면세매입'
            END  GU1_NAME,
            'C' CHA,
            '4' CHA_NAME,
            '3' GU2
     FROM TMH A, TMS B, CT C, 
          ( 
           SELECT A.TMHID, CASE WHEN A.HCT_CODE='100042' THEN '11207000' ELSE (C.BAS_OP5) END FN_CD,nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
                  AC_GUBUN PH23AC_GUBUN, '0' PH99,SUBSTR(A.TMHID, 5, 8) HDATE2,SUBSTR(A.TMHID, 15,4) HNO2,
                  SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT
           FROM PD23 A
                LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE 
                LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%' 
                LEFT JOIN PH23 H ON A.RCT_CODE=H.RCT_CODE AND A.HDATE=H.HDATE AND A.HNO=H.HNO 
                LEFT JOIN FN D ON C.BAS_OP5 = D.FN_CD1 || D.FN_CD2
           WHERE A.TMHID IS NOT NULL 
             AND A.STATE = '8' 
             AND SUBSTR(A.TMHID, 5,8) >= TO_CHAR(sysdate - 45, 'YYYYMMDD')
           GROUP BY A.TMHID, C.BAS_OP5,A.HCT_CODE,AC_GUBUN,nvl(D.FN_LINK2,D.FN_LINK)
           Union All
             SELECT A.TMHID,C.BAS_OP5 FN_CD,nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
                    AC_GUBUN PH23AC_GUBUN, '1' PH99,SUBSTR(H.PH99_LINK, 5,8) HDATE2,SUBSTR(H.PH99_LINK, 13,4) HNO2,
                    SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT
             FROM PD23 A
                  LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE
                  LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%'
                  LEFT JOIN PH23 H ON A.RCT_CODE=H.RCT_CODE AND A.HDATE=H.HDATE AND A.HNO=H.HNO
                  LEFT JOIN FN D ON C.BAS_OP5 = D.FN_CD1 || D.FN_CD2
                  WHERE A.TMHID Is Not Null
               AND A.STATE = '8'
               AND SUBSTR(H.PH99_LINK, 5,8) >= TO_CHAR(sysdate - 45, 'YYYYMMDD')
               AND H.HCT_CODE='100042' AND NVL(H.ARRIVAL_STATE,' ')='1' AND H.PH99_LINK IS NOT NULL
                  GROUP BY A.TMHID, C.BAS_OP5,A.HCT_CODE,AC_GUBUN,H.PH99_LINK,nvl(D.FN_LINK2,D.FN_LINK)
          ) D 
     WHERE A.RCT_CODE = '1000' 
       AND A.TCHK <> 'Y'  
       AND A.RCTYPE = 'C'  
       AND A.HGU = '23'  
       AND A.RCT_CODE = B.RCT_CODE 
       AND A.HDATE = B.HDATE 
       AND A.HNO = B.HNO 
       AND A.HGU = B.HGU 
       AND B.SNO = 1 
       AND A.CT_CODE = C.CT_CODE (+)
       AND SUBSTR(D.TMHID, 0, 4) = A.RCT_CODE 
       AND SUBSTR(D.TMHID, 5, 8) = A.HDATE 
       AND SUBSTR(D.TMHID, 13, 2) = A.HGU 
       AND SUBSTR(D.TMHID, 15, 4) = A.HNO 
       AND (D.AMT + D.VAT) <> 0 
    ) ORDER BY HDATE,HNO,HGU,PH99,GU2,FN_CD";

$rows = $dbconn_e->query_rows($SQL);
$total_count = 0;
$success_count = 0;
$error_count = 0;

if($rows) {
    // 키별로 그룹화하여 API 호출
    $grouped_data = array();
    
    foreach($rows as $row) {
        $key = $row['KEY'];
        if (!isset($grouped_data[$key])) {
            $grouped_data[$key] = array();
        }
        $grouped_data[$key][] = $row;
    }
    
    foreach($grouped_data as $key => $voucher_lines) {
        $total_count++;
        
        // 이미 처리된 데이터인지 확인 (API 테스트를 위해 주석 처리)
        $first_line = $voucher_lines[0];
        /*
        if (!empty($first_line['DZ_LINK'])) {
            echo "이미 처리됨: {$key}\n";
            continue;
        }
        */
        
        // API 데이터 구성
        $api_data = array();
        
        foreach($voucher_lines as $line) {
            // 부가세 계산
            $supplyAmount = floatval($line['AMT']);
            $vatAmount = floatval($line['VAT']);
            $totalAmount = floatval($line['CUAMT']);
            
            $api_data[] = array(
                'refDt' => $line['HDATE2'], // PH23는 HDATE2 사용
                'trCd' => $line['CT_LINK'],
                'trNm' => $line['CT_NAME'],
                'taxFg' => $line['GU1'],
                'clsgAm' => $supplyAmount,
                'clsvAm' => $vatAmount,
                'clshAm' => $totalAmount,
                'misuAm' => $totalAmount,
                'rmkDc' => $line['PRNAME'],
                'approKey' => $key,
                'inDivCd' => '1000',
                'acctTy' => '2', // 매입
                'acctFg' => '1000'
            );
        }
        
        if (!empty($api_data)) {
            // API 호출
            $result = $douzone_api->registerAutoVoucher($api_data);
            
            // 결과 처리
            if ($result['success']) {
                $response_data = $result['result'];
                
                if (isset($response_data['resultCode']) && $response_data['resultCode'] == '0') {
                    $success_count++;
                    
                    // 성공시 DZ_LINK 업데이트
                    $update_sql = "UPDATE TMH 
                                  SET DZ_LINK = '".$first_line['SPERP_CPYID']."'
                                  WHERE RCT_CODE = '1000' 
                                    AND HDATE = '".$first_line['HDATE2']."'
                                    AND HNO = '".$first_line['HNO2']."'
                                    AND HGU = '23'";
                    
                    $update_result = $dbconn_e->iud_query($update_sql);
                    echo "성공: {$key} - {$first_line['PRNAME']}\n";
                    
                } else {
                    $error_count++;
                    echo "API 응답 오류: {$key} - " . json_encode($response_data, JSON_UNESCAPED_UNICODE) . "\n";
                    $douzone_api->logApiResult($result, "매입전표 {$key}");
                }
                
            } else {
                $error_count++;
                echo "API 호출 실패: {$key} - {$result['error']}\n";
                $douzone_api->logApiResult($result, "매입전표 {$key}");
            }
        }
        
        // API 호출 간격 조절
        usleep(100000); // 0.1초 대기
    }
}

echo date("Y-m-d H:i:s")." - API 전송 완료\n";
echo "전체: {$total_count}건, 성공: {$success_count}건, 실패: {$error_count}건\n";
echo "---------------------------\n";

// 스케줄 처리 상황 monitor DB에 저장
crontab_execution(86400, "더존 API 전송 완료 (매입)");

?>