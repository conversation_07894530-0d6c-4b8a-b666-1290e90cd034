<?php
#비밀번호 복호화 
function encodeval($sText, $sCode = null) {
	if(!$sCode){
		$sCode = '#z3k&x4';
	}

	$cntData = strlen( $sText ) - 1;
	$cntCode = strlen( $sCode ) - 1;
	$arrData = array(  );
	$arrCode = array(  );
	$i = 0;

	while ($i <= $cntData) {
		$arrData[$i] = $sText[$i];
		++$i;
	}

	$i = 0;

	while($i <= $cntCode){
		$arrCode[$i] = $sCode[$i];
		++$i;
	}

	$flag = 0;
	$strResult = '';
	$i = 0;

	while ($i <= $cntData) {
		$strResult = $strResult . ( ord( $arrData[$i] ) ^ ord( $arrCode[$flag] ) ) . chr( 8 );

		if ($flag == $cntCode) {
			$flag = 0;
		}else{
			++$flag;
		}
		++$i;
	}

	return base64_encode( $strResult );
}

function decodeval($sText, $sCode = null) {
	if (!$sCode) {
		$sCode = '#z3k&x4';
	}

	$sText = base64_decode( $sText );
	//$arrData = split(chr( 8 ), $sText );
	$arrData = explode(chr( 8 ), $sText );
	$arrCode = array(  );
	$cntData = count( $arrData ) - 2;
	$cntCode = strlen( $sCode ) - 1;
	$i = 0;

	while ($i <= $cntCode) {
		$arrCode[$i] = $sCode[$i];
		++$i;
	}

	$flag = 0;
	$strResult = '';
	$i = 0;

	while ($i <= $cntData) {
		$strResult = $strResult . chr( (int)$arrData[$i] ^ ord( $arrCode[$flag] ) );

		if ($flag == $cntCode) {
			$flag = 0;
		}else{
			++$flag;
		}
		++$i;
	}
	return $strResult;
}
?>
