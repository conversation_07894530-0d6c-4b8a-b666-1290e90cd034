ll
cd open_api/
ll
php exchange_rate.psh 
exchange_rate.sh 
php exchange_rate.psh 
php business_number.psh 
business_number.sh 
ll
cd open_api/
ll
php exchange_rate.psh 
ll
php exchange_rate.psh 
exit
ll
cd open_api/
LL
ll
php exchange_rate.psh
ll
cd open_api/
ll
php business_number.psh 
ll
php exchange_rate.psh 
exit
ll
cd open_api/
ll
cd..
..
ll
.
..
../
cd ..
ll
cd..
cd ..
ll
cd schedule/
ll
cd diuaone
cd douzone/
ll
cd douzone/
ll
psh sp_duzon.psh 
php sp_duzon.psh 

php sp_duzon.psh 
ll
cd open_api/
cd ..
cd douzone/
php sp_duzon.psh 
CD D
ㅊㅇ ㅇ
cd douzone/
php sp_duzon.psh 
ㅊㅇ ㅇ
cd..
ll
cd ..
ll
cd open_api/
ll
php exchange_rate.psh
cd ..
ll
cd douzone/
php sp_duzon.psh 
LL
ll
cd douzone/
cd sp_d
php sp_duzon.psh 
LL CHA in /ho CHA in /ho
ll
cd douzone/
php sp_duzon.psh 
ll
php sp_duzon.psh 
ll
cd do
cd douzone/
php sp_duzon_ph13.psh 
php sp_duzon_ph23.psh 
ll
cd douzone/
php sp_duzon_ph23.psh 
cd douzone/
ll
php sp_duzon.psh 
exit
ll
cd sperp/
ll
php tsms_send.psh 
php tsms_send_pbs.psh 
exit
ll
cd douzone/
ll
php sp_duzon_ph23.psh 
ll
cd douzone/
ll
php sp_duzon_ph23.psh 
php sp_duzon_ph13.psh 
ll
cd open_api/
ll
php /home/<USER>/open_api/exchange_rate.psh
php /home/<USER>/intra/trans_check.psh
php /home/<USER>/open_api/exchange_rate.psh
exit
ll
cd sperp/
ll
php tsms_send.sh 
/usr/bin/php /home/<USER>/sperp/tsms_send_pb.psh >> /home/<USER>/logs/tsms_send_pb.psh.20220920.log
/usr/bin/php /home/<USER>/sperp/tsms_send_pbs.psh >> /home/<USER>/logs/tsms_send_pbs.psh.20220920.log
/usr/bin/php /home/<USER>/sperp/tsms_send_pbs.psh
/usr/bin/php /home/<USER>/sperp/tsms_send_pb.psh
/usr/bin/php /home/<USER>/sperp/tsms_send_pbs.psh
/usr/bin/php /home/<USER>/sperp/tsms_send_pb.psh
/usr/bin/php /home/<USER>/sperp/tsms_send_pbs.psh
/usr/bin/php /home/<USER>/sperp/tsms_send_pb.psh
/usr/bin/php /home/<USER>/sperp/tsms_send.sh
/usr/bin/php /home/<USER>/sperp/tsms_send_pb.psh
/usr/bin/php /home/<USER>/sperp/tsms_send.sh
/usr/bin/php /home/<USER>/sperp/tsms_send_pb.psh
/usr/bin/php /home/<USER>/sperp/tsms_send_pbs.psh
/usr/bin/php /home/<USER>/sperp/tsms_send_pb.psh
/usr/bin/php /home/<USER>/sperp/tsms_send_pbs.psh
/usr/bin/php /home/<USER>/sperp/tsms_send_pb.psh
/usr/bin/php /home/<USER>/sperp/tsms_send_pbs.psh
php /home/<USER>/sperp/tsms_send_pb.psh
php /home/<USER>/sperp/tsms_send_pbs.psh
php /home/<USER>/sperp/tsms_send_pb.psh
php /home/<USER>/sperp/tsms_send_pbs.psh
php /home/<USER>/sperp/tsms_send_pb.psh
php /home/<USER>/sperp/tsms_send_pbs.psh
php /home/<USER>/sperp/tsms_send_pb.psh
ll
cd open_api/
ll
php business_number.psh
ll
cd sperp/
ll
php hanmaeum_webbasket_log_del.psh 
ll
php cms_result.psh 
cd ..
cd etc/
ll
php oracle_account_stats_chk.psh 
ll
php log_del.psh 
cd ..
cd log/
ll
find /home/<USER>/log -name '*.log' -mtime +90 -exec rm -rf {} \;
ll
cd ..
ll
cd sperp/
ll
php faulty_issue.psh 
cd ..
cd intra/
ll
php ssl_expir.psh 
php trans_check.psh 
pwd
php trans_check.psh 
ll
cd douzone/
ll
cd ..
cd sperp/
ll
php cms_result.psh 
php notice_not_reply.psh
ll
php cms_result.psh 
ping 219.255.134.104
php cms_result.psh 
cd ..
cd etc/
ll
php log_del.psh 
ll
exit
ll
cd ..
ll
cd posbankintra/
ll
cd doc
ll
cd /etc
vi passwd
ls
cd nginx
ls
vi conf.d
cd conf.d
ls
cd ..
vi nginx.conf
ls
cd sites-enabled/
ls
exit
ll
cd open_api/
ll
php goolge_webhooks.psh 
cd ..
ll
cd sperp/
ll
php test.psh 
ll
cd open_api/
ll
php goolge_webhooks.psh 
exit
ll
cd sperp/
ll
php doch.psh 
ll
cd sperp/
ll
php mail_send.psh 
ll
cd open_api/
ll
php goolge_webhooks_test.psh 
cd ..
ll
cd intra/
ll
php trans_check.psh 
php devel_board_check.psh 
cd ..
cd sperp/
ll
php faulty_issue2.psh 
ll
cd intra/
ll
php devel_board_check.psh 
cd ..
cd sperp/
ll
php faulty_issue.psh 
ll
cd open_api/
ll
php exchange_rate.psh 
ll
cd sperp/
ll
php mail_send.psh 
ll
php mail_send.psh 
ll
cd sperp/
ll
php test.psh 
exit
crontab -l
cd /etc
ls
ls cron*
cd crontab
ls
cd /var/cron
cd /var/
ls
cd spool
ls
cd conr
ls
cd cron
ls
ls -al
cd crontabs
exit
pwd
cd sperp
ls
php ic_reader_shape.psh 
php /home/<USER>/sperp/ic_reader_shape.psh 
php -q /home/<USER>/sperp/ic_reader_shape.psh 
php -v
php -q /home/<USER>/sperp/ic_reader_shape.psh 
select
php -q /home/<USER>/sperp/ic_reader_shape.psh
php ic_reader_shape.psh
ls
ls -al
cd ..
ls
ls -al
vi .bash_history 
ls
cd sperp/
ls
php ic_reader_shape.sh
cd ..
ls
ls -al
vi .bash_history 
cd sperp
ls
./ic_reader_shape.sh
su -
su
crontab -l
ls
cd sperp
ls
ic_reader_shape.sh
./ic_reader_shape.sh
ls -al
chmod 755 ic_reader_shape.sh
ls -al
./ic_reader_shape.sh
ic_reader_shape.sh
/home/<USER>/sperp/ic_reader_shape.sh
/home/<USER>/sperp/ic_reader_shape.sh > /dev/null 2>&1
/usr/bin/php /home/<USER>/sperp/ic_reader_shape.psh
[905] ORA-00905: missing keyword
/usr/bin/php /home/<USER>/sperp/ic_reader_shape.psh
php -q /home/<USER>/sperp/ic_reader_shape.sh
/home/<USER>/sperp/ic_reader_shape.sh
exit
ll
cd open_api
ll
php exchange_rate.psh 
exit
/usr/bin/php /home/<USER>/sperp/snm_line_use_seting.psh >> /home/<USER>/log/snm_line_use_seting.psh.20231121.log
vi /home/<USER>/log/snm_line_use_seting.psh.20231121.log
/usr/bin/php /home/<USER>/sperp/snm_line_use_seting.psh >> /home/<USER>/log/snm_line_use_seting.psh.20231121.log
exit
php -q /home/<USER>/sperp/snm_line_use_seting.psh
exit
/home/<USER>/sperp/snm_line_use_seting.sh
exit
cd sperp
ls
exit
ll
cd sperp/
ll
php mail_send.psh 
PWD
pwd
cd sperp/
ls
./pi_approver_h.sh
exit
ll
cd open_api/
ll
php exchange_rate.psh 
php -m | grep curl
php exchange_rate.psh 
cd ..
ll
cd sperp/
ll
php pi_approver_h.psh 
ll
cd sperp/
ll
php verify_mant.psh 
/home/<USER>/sperp/change_mant.psh
ls -al
cd sperp
ls
ls -al
exit
LS
ls
cd sperp/
LS
ls
ls -al
./home/<USER>/sperp/change_mant.psh
/home/<USER>/sperp/change_mant.psh
ls -al
/home/<USER>/sperp/change_mant.psh
.change_mant.psh
./change_mant.psh
php -q /home/<USER>/sperp/change_mant.psh
/home/<USER>/sperp/change_mant.sh
exit
LS
ls
cd sperp/
ls
ls -al
exit
LS
l;s
ls
cd sperp
ls
./iv_mon_avg.sh
.iv_mon_avg.sh
pwd
/home/<USER>/sperp/iv_mon_avg.sh
/usr/bin/php /home/<USER>/sperp/iv_mon_avg.psh
ps ax
exit
/home/<USER>/sperp/iv_mon_avg.psh
cd sperp/
ls
pwd
/home/<USER>/sperp/iv_mon_avg.sh
ls -al
/usr/bin/php /home/<USER>/sperp/iv_mon_avg.psh
exit
php -q /home/<USER>/sperp/iv_mon_avg.psh
exit
php -q /home/<USER>/sperp/iv_mon_avg.psh
exit
ll
cd sperp/
ll
php test.psh 
exit
li
ls
cd sperp
ls
php project_confirm.psh
sudo su
ls
cd sperp
ls
project_confirm.psh
bash
ll
cd sperp/
ll
php change_mant2.psh 
exit
ll
cd sperp/
ll
php change_mant2.psh 
exit
CD SPERP
cd sperp
ls
ls -al
CD sperp
CD SPERP
cd sperp
ls -a
ls -al
cmd 775 online_as_sms.psh
chmod 775 online_as_sms.psh
chmod 775 online_as_sms.sh
ls -al
./online_as_sms.psh
online_as_sms.psh
./schedule/schedule/online_as_sms.psh
./ ls
ls -a
online_as_sms.psh
/online_as_sms.psh
ls -l online_as_sms.psh
./online_as_sms
./online_as_sms.psh
which php
./online_as_sms.psh
cat online_as_sms.psh
php online_as_sms.psh
cat cd/
cd..
cd inc
ls -a
cd ..
ls -a
cd schedule
las -a
ls -a
cd inc
cat db_config
ls -a
cat db_config.php
cd ..
cd sperp
ls -a
cat online_as_sms.psh
php online_as_sms.psh
clr
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
php online_as_sms2.psh
php online_as_sms.psh
clear
php online_as_sms.psh
ls -a
cd sperp
ls -a
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
clear
php online_as_sms.psh
cd sperp
ls -a
php online_as_sms.shDDDDDDDDDDD
clear
cd //
cd..
cd ..
cd ../
clear
ls -a
cd sperp
ls -al
chmod +x online_as_sms2.psh
ls -al
chmod +x online_as_sms2.sh
ls -al
./ online_as_sms.sh
./online_as_sms.sh
cd log
cd ..
ls -a
cd log
ls -a
cd ..
ls -al
clear
./online_as_sms.sh
cd sperp
./online_as_sms.sh
./online_as_sms.psh
php online_as_sms.psh
clear
php online_as_sms.psh
ls -a
cd log
ls -a
cat online_as_sms.psh.202502.log
clear
uname -a
cat online_as_sms.psh.202502.log
cd sperp
cat online_as_sms.psh.202502.log
cd ..
cd log
cd ..
cd log
cat online_as_sms.psh.202502.log
clear
cd sperp
ls -a
cd ..
ls -a
cd log
ls -a
cat online_as_sms.psh.202502.log
cd ..
cd sperp
clear
ls -a
ls -al
chmod g+w online_as_sms.psh
chmod g+w online_as_sms.sh
chmod g+w online_as_sms2.sh
chmod g+w online_as_sms2.psh
ls -al
cd ..
ls -a
cd open_api
ls -a
ls -al
cd ..
ls
cd log
ls -al
cd log
ls -al;
which php
groups root
cd ..
cd sperp
ls -al
php -q online_as_sms.psh
ls -al
cd .
cd..
ls -al
cd ..
ls -al
cd log
ls -al
cd log
ls 
cat online_as_sms.psh.202503.log
ls
cd sperp
ls
project_confirm.psh
./project_confirm.psh
sh project_confirm.psh
bash project_confirm.psh
sudo su
php project_confirm.psh
ll
cd sperp/
ll
php pi_approver_h.psh 
ll
php test.psh 
php online_as_sms3.psh 
cat online_as_sms.psh.202503.log
cat online_as_sms3.php
cat online_as_sms3.psh
ls
cd sperp
ls

clear
cd ..
clear
cd sperp
php online_as_sms3.psh 
php online_as_sms2.psh 
clear
php online_as_sms3.psh 
php online_as_sms2.psh 
php online_as_sms3.psh 
clear
php online_as_sms3.psh 
clear
php online_as_sms3.psh 
clear
php online_as_sms3.psh 
php online_as_sms4.psh 
clear
php online_as_sms4.psh 
cd sperp
php online_as_sms4.psh
clear
php online_as_sms4.psh
clear
php online_as_sms3.psh
clear
php online_as_sms3.psh
clear
php online_as_sms3.psh
clear
php online_as_sms3.psh
php online_as_sms4.psh
cd sperp
php online_as_sms4.psh
php online_as_sms3.psh
cd sperp 
php online_as_sms3.psh
cd sperp
php as_delay_mail_send.psh
clear
php as_delay_mail_send.psh
clear
php as_delay_mail_send.psh
clear
php as_delay_mail_send.psh
clear
php as_delay_mail_send.psh
clear
php as_delay_mail_send.psh
clear
php as_delay_mail_send.psh
clear
php as_delay_mail_send.psh
clear
php as_delay_mail_send.psh
php as_delay_mail_send.sh
clear
bash as_delay_mail_send.sh
clear
sh as_delay_mail_send.sh
clear
bash as_delay_mail_send.sh
clear
bash as_delay_mail_send.sh
clear
vim as_delay_mail_send.sh
set -i 's/\r//' as_delay_mail_send.sh
./as_delay_mail_send.sh
cd sperp
./as_delay_mail_send.sh
cd sperp
./as_delay_mail_send.sh
ls
cd sperp
ls
php test.psh
[A
php test.psh
CLEAR
clear
php test.psh
clear
php test.psh
clear
./as_delay_mail_send.sh
./as_delay_mail_send.sh 
cd sperp
./as_delay_mail_send.sh
cd sperp
php as_delay_mail_send.psh
clear
php as_delay_mail_send.psh
clear
php as_delay_mail_send.psh
clear
php as_delay_mail_send.psh
clear
php as_delay_mail_send.psh
./as_delay_mail_send.sh
cd sperp
./as_delay_mail_send.sh
cp sperp
cd sperp
./as_delay_mail_send.sh
cd sperp
./as_delay_mail_send.sh
./minus_iv_mail_send.sh
ls la
-ls la
ls -la
chmod +x minus_iv_mail_send.sh
chmod +x minus_iv_mail_send.psh
ls -la
./minus_iv_mail_send.sh
cd ..
ls -la
cd sperp
cd ..
ls -la
cronteabe -e
cd sperp
ls -la
cd ..
ls -la
cd sperp
php ./online_as_sms3.psh
clear
php ./minus_iv_mail_send.psh
clear
php ./minus_iv_mail_send.psh
clear
php ./minus_iv_mail_send.psh
clear
php ./minus_iv_mail_send.psh
clear
php ./minus_iv_mail_send.psh
clear
./minus_iv_mail_send.sh
clear
