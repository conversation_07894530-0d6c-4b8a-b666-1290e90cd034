<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_DocumentProperties</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___clone" title="__clone :: Implement PHP __clone to create a deep clone, not just a shallow copy."><span class="description">Implement PHP __clone to create a deep clone, not just a shallow copy.</span><pre>__clone()</pre></a></li>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_DocumentProperties"><span class="description">Create a new PHPExcel_DocumentProperties</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_convertProperty" title="convertProperty :: "><span class="description">convertProperty()
        </span><pre>convertProperty()</pre></a></li>
<li class="method public "><a href="#method_convertPropertyType" title="convertPropertyType :: "><span class="description">convertPropertyType()
        </span><pre>convertPropertyType()</pre></a></li>
<li class="method public "><a href="#method_getCategory" title="getCategory :: Get Category"><span class="description">Get Category</span><pre>getCategory()</pre></a></li>
<li class="method public "><a href="#method_getCompany" title="getCompany :: Get Company"><span class="description">Get Company</span><pre>getCompany()</pre></a></li>
<li class="method public "><a href="#method_getCreated" title="getCreated :: Get Created"><span class="description">Get Created</span><pre>getCreated()</pre></a></li>
<li class="method public "><a href="#method_getCreator" title="getCreator :: Get Creator"><span class="description">Get Creator</span><pre>getCreator()</pre></a></li>
<li class="method public "><a href="#method_getCustomProperties" title="getCustomProperties :: Get a List of Custom Property Names"><span class="description">Get a List of Custom Property Names</span><pre>getCustomProperties()</pre></a></li>
<li class="method public "><a href="#method_getCustomPropertyType" title="getCustomPropertyType :: Get a Custom Property Type"><span class="description">Get a Custom Property Type</span><pre>getCustomPropertyType()</pre></a></li>
<li class="method public "><a href="#method_getCustomPropertyValue" title="getCustomPropertyValue :: Get a Custom Property Value"><span class="description">Get a Custom Property Value</span><pre>getCustomPropertyValue()</pre></a></li>
<li class="method public "><a href="#method_getDescription" title="getDescription :: Get Description"><span class="description">Get Description</span><pre>getDescription()</pre></a></li>
<li class="method public "><a href="#method_getKeywords" title="getKeywords :: Get Keywords"><span class="description">Get Keywords</span><pre>getKeywords()</pre></a></li>
<li class="method public "><a href="#method_getLastModifiedBy" title="getLastModifiedBy :: Get Last Modified By"><span class="description">Get Last Modified By</span><pre>getLastModifiedBy()</pre></a></li>
<li class="method public "><a href="#method_getManager" title="getManager :: Get Manager"><span class="description">Get Manager</span><pre>getManager()</pre></a></li>
<li class="method public "><a href="#method_getModified" title="getModified :: Get Modified"><span class="description">Get Modified</span><pre>getModified()</pre></a></li>
<li class="method public "><a href="#method_getSubject" title="getSubject :: Get Subject"><span class="description">Get Subject</span><pre>getSubject()</pre></a></li>
<li class="method public "><a href="#method_getTitle" title="getTitle :: Get Title"><span class="description">Get Title</span><pre>getTitle()</pre></a></li>
<li class="method public "><a href="#method_isCustomPropertySet" title="isCustomPropertySet :: Check if a Custom Property is defined"><span class="description">Check if a Custom Property is defined</span><pre>isCustomPropertySet()</pre></a></li>
<li class="method public "><a href="#method_setCategory" title="setCategory :: Set Category"><span class="description">Set Category</span><pre>setCategory()</pre></a></li>
<li class="method public "><a href="#method_setCompany" title="setCompany :: Set Company"><span class="description">Set Company</span><pre>setCompany()</pre></a></li>
<li class="method public "><a href="#method_setCreated" title="setCreated :: Set Created"><span class="description">Set Created</span><pre>setCreated()</pre></a></li>
<li class="method public "><a href="#method_setCreator" title="setCreator :: Set Creator"><span class="description">Set Creator</span><pre>setCreator()</pre></a></li>
<li class="method public "><a href="#method_setCustomProperty" title="setCustomProperty :: Set a Custom Property"><span class="description">Set a Custom Property</span><pre>setCustomProperty()</pre></a></li>
<li class="method public "><a href="#method_setDescription" title="setDescription :: Set Description"><span class="description">Set Description</span><pre>setDescription()</pre></a></li>
<li class="method public "><a href="#method_setKeywords" title="setKeywords :: Set Keywords"><span class="description">Set Keywords</span><pre>setKeywords()</pre></a></li>
<li class="method public "><a href="#method_setLastModifiedBy" title="setLastModifiedBy :: Set Last Modified By"><span class="description">Set Last Modified By</span><pre>setLastModifiedBy()</pre></a></li>
<li class="method public "><a href="#method_setManager" title="setManager :: Set Manager"><span class="description">Set Manager</span><pre>setManager()</pre></a></li>
<li class="method public "><a href="#method_setModified" title="setModified :: Set Modified"><span class="description">Set Modified</span><pre>setModified()</pre></a></li>
<li class="method public "><a href="#method_setSubject" title="setSubject :: Set Subject"><span class="description">Set Subject</span><pre>setSubject()</pre></a></li>
<li class="method public "><a href="#method_setTitle" title="setTitle :: Set Title"><span class="description">Set Title</span><pre>setTitle()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__category" title="$_category :: Category"><span class="description"></span><pre>$_category</pre></a></li>
<li class="property private "><a href="#property__company" title="$_company :: Company"><span class="description"></span><pre>$_company</pre></a></li>
<li class="property private "><a href="#property__created" title="$_created :: Created"><span class="description"></span><pre>$_created</pre></a></li>
<li class="property private "><a href="#property__creator" title="$_creator :: Creator"><span class="description"></span><pre>$_creator</pre></a></li>
<li class="property private "><a href="#property__customProperties" title="$_customProperties :: Custom Properties"><span class="description"></span><pre>$_customProperties</pre></a></li>
<li class="property private "><a href="#property__description" title="$_description :: Description"><span class="description"></span><pre>$_description</pre></a></li>
<li class="property private "><a href="#property__keywords" title="$_keywords :: Keywords"><span class="description"></span><pre>$_keywords</pre></a></li>
<li class="property private "><a href="#property__lastModifiedBy" title="$_lastModifiedBy :: LastModifiedBy"><span class="description"></span><pre>$_lastModifiedBy</pre></a></li>
<li class="property private "><a href="#property__manager" title="$_manager :: Manager"><span class="description"></span><pre>$_manager</pre></a></li>
<li class="property private "><a href="#property__modified" title="$_modified :: Modified"><span class="description"></span><pre>$_modified</pre></a></li>
<li class="property private "><a href="#property__subject" title="$_subject :: Subject"><span class="description"></span><pre>$_subject</pre></a></li>
<li class="property private "><a href="#property__title" title="$_title :: Title"><span class="description"></span><pre>$_title</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_PROPERTY_TYPE_BOOLEAN" title="PROPERTY_TYPE_BOOLEAN :: constants"><span class="description">constants</span><pre>PROPERTY_TYPE_BOOLEAN</pre></a></li>
<li class="constant  "><a href="#constant_PROPERTY_TYPE_DATE" title="PROPERTY_TYPE_DATE :: "><span class="description">PROPERTY_TYPE_DATE</span><pre>PROPERTY_TYPE_DATE</pre></a></li>
<li class="constant  "><a href="#constant_PROPERTY_TYPE_FLOAT" title="PROPERTY_TYPE_FLOAT :: "><span class="description">PROPERTY_TYPE_FLOAT</span><pre>PROPERTY_TYPE_FLOAT</pre></a></li>
<li class="constant  "><a href="#constant_PROPERTY_TYPE_INTEGER" title="PROPERTY_TYPE_INTEGER :: "><span class="description">PROPERTY_TYPE_INTEGER</span><pre>PROPERTY_TYPE_INTEGER</pre></a></li>
<li class="constant  "><a href="#constant_PROPERTY_TYPE_STRING" title="PROPERTY_TYPE_STRING :: "><span class="description">PROPERTY_TYPE_STRING</span><pre>PROPERTY_TYPE_STRING</pre></a></li>
<li class="constant  "><a href="#constant_PROPERTY_TYPE_UNKNOWN" title="PROPERTY_TYPE_UNKNOWN :: "><span class="description">PROPERTY_TYPE_UNKNOWN</span><pre>PROPERTY_TYPE_UNKNOWN</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_DocumentProperties"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_DocumentProperties.html">PHPExcel_DocumentProperties</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_DocumentProperties</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.html">PHPExcel</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___clone"></a><div class="element clickable method public method___clone" data-toggle="collapse" data-target=".method___clone .collapse">
<h2>Implement PHP __clone to create a deep clone, not just a shallow copy.</h2>
<pre>__clone() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_DocumentProperties</h2>
<pre>__construct() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_convertProperty"></a><div class="element clickable method public method_convertProperty" data-toggle="collapse" data-target=".method_convertProperty .collapse">
<h2>convertProperty()
        </h2>
<pre>convertProperty($propertyValue, $propertyType) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$propertyValue</h4></div>
<div class="subelement argument"><h4>$propertyType</h4></div>
</div></div>
</div>
<a id="method_convertPropertyType"></a><div class="element clickable method public method_convertPropertyType" data-toggle="collapse" data-target=".method_convertPropertyType .collapse">
<h2>convertPropertyType()
        </h2>
<pre>convertPropertyType($propertyType) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$propertyType</h4></div>
</div></div>
</div>
<a id="method_getCategory"></a><div class="element clickable method public method_getCategory" data-toggle="collapse" data-target=".method_getCategory .collapse">
<h2>Get Category</h2>
<pre>getCategory() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getCompany"></a><div class="element clickable method public method_getCompany" data-toggle="collapse" data-target=".method_getCompany .collapse">
<h2>Get Company</h2>
<pre>getCompany() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getCreated"></a><div class="element clickable method public method_getCreated" data-toggle="collapse" data-target=".method_getCreated .collapse">
<h2>Get Created</h2>
<pre>getCreated() : \datetime</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>\datetime</code></div>
</div></div>
</div>
<a id="method_getCreator"></a><div class="element clickable method public method_getCreator" data-toggle="collapse" data-target=".method_getCreator .collapse">
<h2>Get Creator</h2>
<pre>getCreator() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getCustomProperties"></a><div class="element clickable method public method_getCustomProperties" data-toggle="collapse" data-target=".method_getCustomProperties .collapse">
<h2>Get a List of Custom Property Names</h2>
<pre>getCustomProperties() : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>array</code>of string</div>
</div></div>
</div>
<a id="method_getCustomPropertyType"></a><div class="element clickable method public method_getCustomPropertyType" data-toggle="collapse" data-target=".method_getCustomPropertyType .collapse">
<h2>Get a Custom Property Type</h2>
<pre>getCustomPropertyType(string $propertyName) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$propertyName</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getCustomPropertyValue"></a><div class="element clickable method public method_getCustomPropertyValue" data-toggle="collapse" data-target=".method_getCustomPropertyValue .collapse">
<h2>Get a Custom Property Value</h2>
<pre>getCustomPropertyValue(string $propertyName) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$propertyName</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getDescription"></a><div class="element clickable method public method_getDescription" data-toggle="collapse" data-target=".method_getDescription .collapse">
<h2>Get Description</h2>
<pre>getDescription() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getKeywords"></a><div class="element clickable method public method_getKeywords" data-toggle="collapse" data-target=".method_getKeywords .collapse">
<h2>Get Keywords</h2>
<pre>getKeywords() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getLastModifiedBy"></a><div class="element clickable method public method_getLastModifiedBy" data-toggle="collapse" data-target=".method_getLastModifiedBy .collapse">
<h2>Get Last Modified By</h2>
<pre>getLastModifiedBy() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getManager"></a><div class="element clickable method public method_getManager" data-toggle="collapse" data-target=".method_getManager .collapse">
<h2>Get Manager</h2>
<pre>getManager() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getModified"></a><div class="element clickable method public method_getModified" data-toggle="collapse" data-target=".method_getModified .collapse">
<h2>Get Modified</h2>
<pre>getModified() : \datetime</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>\datetime</code></div>
</div></div>
</div>
<a id="method_getSubject"></a><div class="element clickable method public method_getSubject" data-toggle="collapse" data-target=".method_getSubject .collapse">
<h2>Get Subject</h2>
<pre>getSubject() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getTitle"></a><div class="element clickable method public method_getTitle" data-toggle="collapse" data-target=".method_getTitle .collapse">
<h2>Get Title</h2>
<pre>getTitle() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_isCustomPropertySet"></a><div class="element clickable method public method_isCustomPropertySet" data-toggle="collapse" data-target=".method_isCustomPropertySet .collapse">
<h2>Check if a Custom Property is defined</h2>
<pre>isCustomPropertySet(string $propertyName) : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$propertyName</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_setCategory"></a><div class="element clickable method public method_setCategory" data-toggle="collapse" data-target=".method_setCategory .collapse">
<h2>Set Category</h2>
<pre>setCategory(string $pValue) : <a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></code></div>
</div></div>
</div>
<a id="method_setCompany"></a><div class="element clickable method public method_setCompany" data-toggle="collapse" data-target=".method_setCompany .collapse">
<h2>Set Company</h2>
<pre>setCompany(string $pValue) : <a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></code></div>
</div></div>
</div>
<a id="method_setCreated"></a><div class="element clickable method public method_setCreated" data-toggle="collapse" data-target=".method_setCreated .collapse">
<h2>Set Created</h2>
<pre>setCreated(\datetime $pValue) : <a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>\datetime</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></code></div>
</div></div>
</div>
<a id="method_setCreator"></a><div class="element clickable method public method_setCreator" data-toggle="collapse" data-target=".method_setCreator .collapse">
<h2>Set Creator</h2>
<pre>setCreator(string $pValue) : <a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></code></div>
</div></div>
</div>
<a id="method_setCustomProperty"></a><div class="element clickable method public method_setCustomProperty" data-toggle="collapse" data-target=".method_setCustomProperty .collapse">
<h2>Set a Custom Property</h2>
<pre>setCustomProperty(string $propertyName, mixed $propertyValue, string $propertyType) : <a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$propertyName</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$propertyValue</h4>
<code>mixed</code>
</div>
<div class="subelement argument">
<h4>$propertyType</h4>
<code>string</code><p>'i'    : Integer
  'f' : Floating Point
  's' : String
  'd' : Date/Time
  'b' : Boolean</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></code></div>
</div></div>
</div>
<a id="method_setDescription"></a><div class="element clickable method public method_setDescription" data-toggle="collapse" data-target=".method_setDescription .collapse">
<h2>Set Description</h2>
<pre>setDescription(string $pValue) : <a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></code></div>
</div></div>
</div>
<a id="method_setKeywords"></a><div class="element clickable method public method_setKeywords" data-toggle="collapse" data-target=".method_setKeywords .collapse">
<h2>Set Keywords</h2>
<pre>setKeywords(string $pValue) : <a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></code></div>
</div></div>
</div>
<a id="method_setLastModifiedBy"></a><div class="element clickable method public method_setLastModifiedBy" data-toggle="collapse" data-target=".method_setLastModifiedBy .collapse">
<h2>Set Last Modified By</h2>
<pre>setLastModifiedBy(string $pValue) : <a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></code></div>
</div></div>
</div>
<a id="method_setManager"></a><div class="element clickable method public method_setManager" data-toggle="collapse" data-target=".method_setManager .collapse">
<h2>Set Manager</h2>
<pre>setManager(string $pValue) : <a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></code></div>
</div></div>
</div>
<a id="method_setModified"></a><div class="element clickable method public method_setModified" data-toggle="collapse" data-target=".method_setModified .collapse">
<h2>Set Modified</h2>
<pre>setModified(\datetime $pValue) : <a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>\datetime</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></code></div>
</div></div>
</div>
<a id="method_setSubject"></a><div class="element clickable method public method_setSubject" data-toggle="collapse" data-target=".method_setSubject .collapse">
<h2>Set Subject</h2>
<pre>setSubject(string $pValue) : <a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></code></div>
</div></div>
</div>
<a id="method_setTitle"></a><div class="element clickable method public method_setTitle" data-toggle="collapse" data-target=".method_setTitle .collapse">
<h2>Set Title</h2>
<pre>setTitle(string $pValue) : <a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentProperties.html">\PHPExcel_DocumentProperties</a></code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__category"> </a><div class="element clickable property private property__category" data-toggle="collapse" data-target=".property__category .collapse">
<h2></h2>
<pre>$_category : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__company"> </a><div class="element clickable property private property__company" data-toggle="collapse" data-target=".property__company .collapse">
<h2></h2>
<pre>$_company : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__created"> </a><div class="element clickable property private property__created" data-toggle="collapse" data-target=".property__created .collapse">
<h2></h2>
<pre>$_created : \datetime</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__creator"> </a><div class="element clickable property private property__creator" data-toggle="collapse" data-target=".property__creator .collapse">
<h2></h2>
<pre>$_creator : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__customProperties"> </a><div class="element clickable property private property__customProperties" data-toggle="collapse" data-target=".property__customProperties .collapse">
<h2></h2>
<pre>$_customProperties : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__description"> </a><div class="element clickable property private property__description" data-toggle="collapse" data-target=".property__description .collapse">
<h2></h2>
<pre>$_description : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__keywords"> </a><div class="element clickable property private property__keywords" data-toggle="collapse" data-target=".property__keywords .collapse">
<h2></h2>
<pre>$_keywords : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__lastModifiedBy"> </a><div class="element clickable property private property__lastModifiedBy" data-toggle="collapse" data-target=".property__lastModifiedBy .collapse">
<h2></h2>
<pre>$_lastModifiedBy : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__manager"> </a><div class="element clickable property private property__manager" data-toggle="collapse" data-target=".property__manager .collapse">
<h2></h2>
<pre>$_manager : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__modified"> </a><div class="element clickable property private property__modified" data-toggle="collapse" data-target=".property__modified .collapse">
<h2></h2>
<pre>$_modified : \datetime</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__subject"> </a><div class="element clickable property private property__subject" data-toggle="collapse" data-target=".property__subject .collapse">
<h2></h2>
<pre>$_subject : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__title"> </a><div class="element clickable property private property__title" data-toggle="collapse" data-target=".property__title .collapse">
<h2></h2>
<pre>$_title : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_PROPERTY_TYPE_BOOLEAN"> </a><div class="element clickable constant  constant_PROPERTY_TYPE_BOOLEAN" data-toggle="collapse" data-target=".constant_PROPERTY_TYPE_BOOLEAN .collapse">
<h2>constants</h2>
<pre>PROPERTY_TYPE_BOOLEAN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_PROPERTY_TYPE_DATE"> </a><div class="element clickable constant  constant_PROPERTY_TYPE_DATE" data-toggle="collapse" data-target=".constant_PROPERTY_TYPE_DATE .collapse">
<h2>PROPERTY_TYPE_DATE</h2>
<pre>PROPERTY_TYPE_DATE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_PROPERTY_TYPE_FLOAT"> </a><div class="element clickable constant  constant_PROPERTY_TYPE_FLOAT" data-toggle="collapse" data-target=".constant_PROPERTY_TYPE_FLOAT .collapse">
<h2>PROPERTY_TYPE_FLOAT</h2>
<pre>PROPERTY_TYPE_FLOAT </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_PROPERTY_TYPE_INTEGER"> </a><div class="element clickable constant  constant_PROPERTY_TYPE_INTEGER" data-toggle="collapse" data-target=".constant_PROPERTY_TYPE_INTEGER .collapse">
<h2>PROPERTY_TYPE_INTEGER</h2>
<pre>PROPERTY_TYPE_INTEGER </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_PROPERTY_TYPE_STRING"> </a><div class="element clickable constant  constant_PROPERTY_TYPE_STRING" data-toggle="collapse" data-target=".constant_PROPERTY_TYPE_STRING .collapse">
<h2>PROPERTY_TYPE_STRING</h2>
<pre>PROPERTY_TYPE_STRING </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_PROPERTY_TYPE_UNKNOWN"> </a><div class="element clickable constant  constant_PROPERTY_TYPE_UNKNOWN" data-toggle="collapse" data-target=".constant_PROPERTY_TYPE_UNKNOWN .collapse">
<h2>PROPERTY_TYPE_UNKNOWN</h2>
<pre>PROPERTY_TYPE_UNKNOWN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:35Z.<br></footer></div>
</div>
</body>
</html>
