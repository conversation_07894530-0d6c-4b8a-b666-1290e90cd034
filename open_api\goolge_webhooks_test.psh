#!/usr/bin/php -q
<?php
	// 0 12 * * * php -q /home/<USER>/open_api/goolge_webhooks.psh
	# 구글 웹훅 발송
	$ROOT_PATH = "/home/<USER>";
	$_SERVER['DOCUMENT_ROOT'] = $ROOT_PATH;
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/func.php");

	// DB접속
	$dbconn_chrome_push = new DBController($db['chrome_push']);
	if(empty($dbconn_chrome_push->success)) {
		echo "dbconn error [" . $db['chrome_push']['host'] . "] 데이터베이스 연결 실패 \n";
	}

	// DB접속
	$dbconn_posbank_intra = new DBController($db['posbank_intra']);
	if(empty($dbconn_posbank_intra->success)) {
		echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패 \n";
	}

	$chk_time = date("YmdHi");

	echo "[".$chk_time."] ".date("Y-m-d H:i:s")." - 구글 웹훅 시작 \n";

	// 10일 지난 데이타 삭제
	$Checkday = date("Ymd", strtotime ("-10 day"));
	$query = "delete from goolge_webhooks where STATE='2' and IDATE<'".$Checkday."' ";
	$rs = $dbconn_chrome_push->iud_query($query);

	$arr_head = [];
	$arr_head[0] = "content-type:application/json; charset=UTF-8";


	for($cnt=0; $cnt<1; $cnt++){
		if($chk_time == date("YmdHi")) { // 시간이 다르면 빠져나감

			// 전송 대상 가져오기
			$arr_data = [];
			$arr_HID = [];
			$SQL = "select 
							HID, PROGRAM, GU, ID, TITLE, PREVIEW, MSG_TYPE, MESSAGE, LINK, LINK_NM
							,BUTTONS
						from 
							goolge_webhooks
						where 
							HID='33937' ";
			$rows = $dbconn_chrome_push->query_rows($SQL);
			if($rows){
				foreach($rows as $key => $row) {
					$arr_HID[] = $row['HID'];
					$arr_data[$row['HID']] = $row;
					$arr_data[$row['HID']]['ID'] = json_decode($row['ID']);
					if($row['MSG_TYPE']=='1') $arr_data[$row['HID']]['MESSAGE'] = json_decode($row['MESSAGE']);
				}

				if($arr_HID){
					// 상태 (전송중) 변경
					$query = "update goolge_webhooks set STATE='1' where STATE='0' and HID in ('".implode("','",$arr_HID)."') ";
					$rs = $dbconn_chrome_push->iud_query($query);
				}

				// 웹훅 전송
				if($arr_data){
					foreach($arr_data as $key => $row) {
						$arr_body = [];
						$arr_body['cards_v2']['card_id'] = "createCardMessage";

						if($row['TITLE']) $arr_body['cards_v2']['card']['header']['title'] = $row['TITLE'];
						$arr_body['cards_v2']['card']['header']['imageUrl'] = "https://www.posbank.com/favicon/apple-touch-icon.png";
						$arr_body['cards_v2']['card']['header']['imageType'] = "CIRCLE";

						$n = 0;
						// 메세지
						if($row['MESSAGE']){
							if($row['MSG_TYPE']=='2'){
								$MESSAGE = $row['MESSAGE'];
								// 인트라넷용으로 사용된 html메시지가 여기서는 적용이 안되어 </div>을 줄바꿈처리 하였음
								$MESSAGE = str_replace("</div>", "</div><br>", $MESSAGE);

								$arr_body['cards_v2']['card']['sections']['widgets'][$n]['textParagraph']['text'] = $MESSAGE;
								$n++;
							}else{
								foreach($row['MESSAGE'] as $key2 => $row2) {
									$arr_body['cards_v2']['card']['sections']['widgets'][$n]['textParagraph']['text'] = $row2[0];
									$n++;
								}
							}
						}
						if($row['BUTTONS']){
							$arr_BUTTONS = json_decode($row['BUTTONS'],true);
							if($arr_BUTTONS){
								foreach($arr_BUTTONS as $btn_key => $btn_row) {
									$arr_body['cards_v2']['card']['sections']['widgets'][$n]['buttonList']['buttons'][$btn_key]['text'] = $btn_row['name'];
									$arr_body['cards_v2']['card']['sections']['widgets'][$n]['buttonList']['buttons'][$btn_key]['onClick']['openLink']['url'] = $btn_row['link'];
								}
							}
						}

						// 미리보기
						if ($row['PREVIEW']) { $arr_body['text'] = strip_tags($row['PREVIEW']); }

						// 직원 확인및 웹훅 url가져오기
						$url_user = goolge_webhooks_member_chk([
							'PROGRAM'=>$row['PROGRAM']
							,'GU'=>$row['GU']
							,'ID'=>$row['ID']
						]); 
						//echo json_encode($url_user, JSON_PRETTY_PRINT|JSON_UNESCAPED_UNICODE)."\n";

						if($url_user){ 
							foreach($url_user as $key2 => $value) {
								$mail = $value['EMAIL'];
								$url = $value['GOOLGEWEBHOOKS_URL'];
								$return = make_curl($url, "POST", $arr_head, $arr_body);
								//echo json_encode($return['result']['name'], JSON_PRETTY_PRINT|JSON_UNESCAPED_UNICODE)."\n";
							}
						}

						
					}
				}

				// 상태 (완료) 변경
				$query = "update goolge_webhooks set STATE='2',UDATE=now() where STATE='1' and HID in ('".implode("','",$arr_HID)."') ";
				$rs = $dbconn_chrome_push->iud_query($query);

				echo "[".$chk_time."] ".date("Y-m-d H:i:s")." - ".($cnt+1)." - ";
				echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
			}else{
				echo "[".$chk_time."] ".date("Y-m-d H:i:s")." - ".($cnt+1)." \n";
			}

			sleep(5); // 5초 지연시킴
		}else{
			$cnt = 100;
		}
	}

	echo "[".$chk_time."] ".date("Y-m-d H:i:s")." - 구글 웹훅 끝\n";

	## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(60, "구글 웹훅 전송");
