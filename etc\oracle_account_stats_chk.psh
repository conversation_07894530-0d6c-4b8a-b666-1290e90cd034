#!/usr/bin/php -q
<?php
// 17 3 * * * php -q /home/<USER>/etc/oracle_account_stats_chk.sh
# 한마음공동체 DB의 WEBBASKET_LOG 테이블의 10일 지난 데이터 삭제 처리
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

echo date("Y-m-d H:i:s")." - 실행 \n";

$dbconn_monitor = new DBController($db['monitor']);
if(empty($dbconn_monitor->success)) {
	echo "dbconn error [" . $db['monitor']['host'] . "] 데이터베이스 연결 실패";
}
/**********************************************************/

$setParams = [];
$setParams['msgType'] = '1008'; // 1008:알림톡,1009:친구톡
$setParams['userId'] = 'posbank2'; // 아이디
$setParams['sendType'] = "0"; // 전송구분(0:즉시전송)
$setParams['dispatchPhone'] = '1588-6335'; // 발신번호
$setParams['returnUrl'] = ""; // 리턴될 페이지 (절대경로로 입력)
//$setParams['MsgView'] = "N";
$setParams['temCode'] = 'monitor_000008'; // 알림톡 전용
$setParams['imgUrl'] = ''; // 친구톡 전용
$setParams['imgLink'] = ''; // 친구톡 전용
//$setParams['etc1'] = $arr['etc1']; // 친구톡확장 SMS대체 문구
//$setParams['etc2'] = $arr['etc2']; // 친구톡확장
//$setParams['etc3'] = $arr['etc3']; // 친구톡확장
$setParams['subject'] = '[포스뱅크 시스템 점검 안내]'; // 제목
$setParams['receptionPhone'] = "010-8967-6809/강영보;010-4300-5557/장오중;010-6584-9990/홍영근;"; // 수신자
//$setParams['receptionPhone'] = "010-4503-2846/장진식;"; // 수신자


$SQL = "
	select 
		tnsnames, serverName, serverIP
	from 
		baseServerList 
	where 
		tnsnames is not null
		and PortMonitor like '%1521%'
	order by serverIP ";
$arrRow = $dbconn_monitor->query_rows($SQL);
if($arrRow){
	foreach($arrRow as $key => $row) {

		$db['sperp']['host'] = $row['serverIP']."/ORCL";
		$db['sperp']['userid'] = "SYSTEM";
		$db['sperp']['password'] = "ALSUDHKTJRFB";
		//$db['sperp']['database'] = "PBKADMIN";
		$db['sperp']['charset'] = "utf8";
		$db['sperp']['dbdriver'] = "oracle";

		$dbconn_sperp = new DBController($db['sperp']);
		if(empty($dbconn_sperp->success)) {
			echo "[".date('H:i:s', time())."] ". $row['serverIP']." / ".$row['serverName']." / Connect fail!!\n";

			unset($setParams['toMsg']);
			$setParams['toMsg'] = "[포스뱅크 시스템 점검 안내]\n";
			$setParams['toMsg'] .= "[". $row['serverIP']." / ".$row['serverName']."]\n";
			$setParams['toMsg'] .= "[Oracle Connect Fail]\n";
			$setParams['toMsg'] .= "Process Down!!";
			$setParams['etc1'] = $setParams['toMsg']; // 친구톡확장 SMS대체 문구

			// 카카오톡 보내기
			//$rs = KakaoTalk_send($setParams);
		}else{
			echo "[".date('H:i:s', time())."] ". $row['serverIP']." / ".$row['serverName']." / Connect success!!\n";

			$where = "";
			if($row['serverIP']=='**************') $where = "USERNAME in ('SYS','SYSTEM','GWANGJIN','NEEDSNET','ICOM','POSBANK')";
			if($row['serverIP']=='**************') $where = "USERNAME in ('SYS','SYSTEM','POSBANK','ERP')";
			if($row['serverIP']=='**************') $where = "USERNAME in ('SYS','SYSTEM','PBSVC','POSBANK','PBKADMIN','ERP','SVCLINE','PBUSADMIN')";

			$SQL2 = "select USERNAME, ACCOUNT_STATUS 
						from DBA_USERS 
						where ".$where;
			$arrRow2 = $dbconn_sperp->query_rows($SQL2);
			if($arrRow2){
				foreach($arrRow2 as $key2 => $row2) {
					if($row2['ACCOUNT_STATUS']=='OPEN'){
						echo "[".date('H:i:s', time())."] ". $row['serverIP']." / ".$row2['USERNAME']." / Account is normality\n";
					}else{
						echo "[".date('H:i:s', time())."] ". $row['serverIP']." / ".$row2['USERNAME']." / Account is lock!!\n";

						unset($setParams['toMsg']);
						$setParams['toMsg'] = "[포스뱅크 시스템 점검 안내]\n";
						$setParams['toMsg'] .= "[". $row['serverIP']." / ".$row['serverName']."]\n";
						$setParams['toMsg'] .= "[". $row2['USERNAME']." USER NOT OPEN]\n";
						$setParams['toMsg'] .= "Process Down!!";
						$setParams['etc1'] = $setParams['toMsg']; // 친구톡확장 SMS대체 문구

						// 카카오톡 보내기
						//$rs = KakaoTalk_send($setParams);
					}
				}
			}
			//echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n\n";
		}
	}
}

echo date("Y-m-d H:i:s")." - 종료\n";

## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(180, "시스템 점검 안내");
