#!/usr/local/bin/php -q
<?php

/*
	## QMS > QMS관리 > 변경점 3개월 지연 알림 : 접수일자로 부터 3개월
	작업일 : 2024.09.26. 현주가
	경로 : /home/<USER>/sperp/change_mant.psh


*/


	// 0 9 * * * php -q /home/<USER>/sperp/change_mant.psh
	# 변경점 3개월 지연 알림 : 접수일자로 부터 3개월
	$ROOT_PATH = "/home/<USER>";
	include($ROOT_PATH . "/inc/func.php");
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/Encode.php");
	include($ROOT_PATH . "/inc/func_state.php");

	$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
	if(empty($dbconn_sperp_posbank->success)) {
		echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
	}

	$dbconn_posbank_intra = new DBController($db['posbank_intra']);
	if(empty($dbconn_posbank_intra->success)) {
		echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패";
	}
	/**********************************************************/

	if(in_array(date('w'),array("0","6"))){
		echo date("Y-m-d") . " - 휴무일\n";
		## 스케즐 처리 상황 intra DB에 저장
		crontab_execution(86400, "변경점 3개월 지연 알림 : 접수일자로 부터 3개월");
		exit;
	}

	$SQL = "SELECT NAME FROM HOLIDAY_DATA WHERE to_char(HDATE,'YYYYMMDD')='".date('Ymd')."'";
	$HOLIDAY_NM = $dbconn_sperp_posbank->query_one($SQL);
	if($HOLIDAY_NM){
		echo date("Y-m-d") . " - 휴무일(".$HOLIDAY_NM.")\n";
		## 스케즐 처리 상황 intra DB에 저장
		crontab_execution(86400, "변경점 3개월 지연 알림 : 접수일자로 부터 3개월");
		exit;
	}

	$Checkday = date("Ymd", strtotime ("-3 month"));

	$SQL = "
		SELECT
		    H.RCT_CODE||H.HDATE||H.HNO HID
		    ,CASE H.STATE WHEN '8' THEN '요청' WHEN '0' THEN '접수' WHEN '1' THEN '결재중' WHEN 'A' THEN 'ECN' WHEN 'B' THEN 'PCN' WHEN 'C' THEN 'IOS' WHEN 'D' THEN 'PCCB' WHEN '9' THEN '보류' ELSE '' END  STATE_NM
		    ,TO_CHAR(H.REG_IDATE,'YYYY-MM-DD') REG_IDATE2
		    , F_STNAME (H.STCODE) AS STNAME
		    ,F_BAS(H.SCOPE_BAS,'NAME') SCOPE_NM  -- 변경범위
		    ,F_BAS(H.TARGET_BAS,'NAME') TARGET_NM -- 변경사항
		    ,H.NOTICE_GU
		    ,CASE H.NOTICE_GU WHEN 'A' THEN '사내공지' WHEN 'B' THEN '대외공지' ELSE '' END  NOTICE_GU_NM -- 공지구분
		FROM
		    CHANGE_MANT H
		WHERE H.STATE IN ('A','B','C') AND H.HDATE >= '********' AND  H.HDATE < '".$Checkday."' 
		ORDER BY H.REG_IDATE DESC, HID DESC
	";
	$arrRow = $dbconn_sperp_posbank->query_rows($SQL);




	$SQLST = "
		SELECT DISTINCT(STCODE) AS STCODE FROM (
			SELECT H.STCODE AS STCODE FROM CHANGE_MANT H WHERE H.STATE IN ('A','B','C') AND H.HDATE >= '********' AND  H.HDATE < '".$Checkday."'
			UNION ALL
			SELECT H.STCODE_B AS STCODE FROM CHANGE_MANT H WHERE H.STATE IN ('A','B','C') AND H.HDATE >= '********' AND  H.HDATE < '".$Checkday."' AND H.STCODE_B IS NOT NULL
			UNION ALL
			SELECT H.STCODE_C AS STCODE FROM CHANGE_MANT H WHERE H.STATE IN ('A','B','C') AND H.HDATE >= '********' AND  H.HDATE < '".$Checkday."' AND H.STCODE_C IS NOT NULL
			UNION ALL 
			SELECT H.STCODE_D AS STCODE FROM CHANGE_MANT H WHERE H.STATE IN ('A','B','C') AND H.HDATE >= '********' AND  H.HDATE < '".$Checkday."'AND H.STCODE_D IS NOT NULL
			UNION ALL 
			SELECT 
			    DISTINCT (A.APPV_STCODE) AS STCODE
			FROM
			    CHANGE_MANT H
			    LEFT OUTER JOIN CHANGE_MANT_APPV A ON A.RCT_CODE=H.RCT_CODE AND A.HDATE=H.HDATE AND A.HNO = H.HNO
			WHERE H.STATE IN ('A','B','C') AND H.HDATE >= '********' AND  H.HDATE < '".$Checkday."' AND A.STATE = '0'
		) S1	
	";
	$arrSTRow = $dbconn_sperp_posbank->query_rows($SQLST);
	$arr_ST_TEMP02 = array();
	if ($arrSTRow) {

		foreach ($arrSTRow AS $k => $v) {
		    // print_r($v);
			$arr_ST_TEMP02[] = $v["STCODE"];
		}	
	}

	// 발송 대상자
	$SQL2 = "SELECT BAS_OP4 as STCODE FROM BAS WHERE BAS_CODE='E079'";
	$stcode_str = $dbconn_sperp_posbank->query_one($SQL2);

// $stcode_str = "100994"; // 신현주
	$arr_ST_TEMP01 = explode(",", $stcode_str);

	$arr_ST = array_merge($arr_ST_TEMP01, $arr_ST_TEMP02);


//print_r($arr_ST);
// exit;

	$style = "font-size:12px;line-height:25px;border:1px solid #333333;padding:3px;line-height:120%;";
	$style .= "text-overflow:ellipsis; table-layout:fixed;  overflow-x:hidden; overflow-y:hidden; white-space:nowrap;";
	$title = "[변경점 검증 3개월 지연] " . sizeof($arrRow) . "건";
	$content = "<div>\n";
	$content .= "<div style=\"font-size:12px;line-height:50px;\"><b>변경점 처리가 3개월 이상 지연된 내역</b></div>\n";
	$content .= "<div><a href=\"https://www.spqms.co.kr/?pageCode=MTE1MzM=\" target=\"_blank\">[변경점 관리 보러가기]</a></div>";
	$content .= "<table>";
	$content .= "<tr>";
	$content .= "<th style=\"".$style."width:20px;\">No</th>";
	$content .= "<th style=\"".$style."width:110px;\">고유번호</th>";
	$content .= "<th style=\"".$style."width:25px;\">공지<br>구분</th>";
	$content .= "<th style=\"".$style."width:35px;\">변경<br>범위</th>";
	$content .= "<th style=\"".$style."\">변경사항</th>";
	$content .= "<th style=\"".$style."width:70px;\">상태</th>";
	$content .= "<th style=\"".$style."width:70px;\">접수일자</th>";
	$content .= "<th style=\"".$style."\">접수자</th>";
	$content .= "</tr>\n";
	if($arrRow){
		foreach($arrRow as $key => $row) {
			$link = "https://www.spqms.co.kr/?pageCode=MTE1MzM=&searHID=".$row['HID']."";

			$content .= "<tr>";
			$content .= "<td style=\"".$style." text-align:center;\">".($key+1)."</td>";
			$content .= "<td style=\"".$style." text-align:center;\"><a href=\"".$link."\" target=\"_blank\">".$row['HID']."</a></td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['NOTICE_GU_NM']."</td>";
			$content .= "<td style=\"".$style." letter-spacing:-1px; text-align:center;\">".$row['SCOPE_NM']."</td>";
			$content .= "<td style=\"".$style."\">".$row['TARGET_NM']."</td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['STATE_NM']."</td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['REG_IDATE2']."</td>";
			$content .= "<td style=\"".$style."\">".$row['STNAME']."</td>";
			$content .= "</tr>\n";
		}
	}
	$content .= "</table>\n";
	$content .= "</div>";


	//인트라넷 업무연락 보내는 함수(ERP 사원코드)
	$rs = intra_send_erp('',$arr_ST,$title,$content);
	echo date("Y-m-d H:i:s")." 업무연락 발송 \n";
	echo $title . "\n";
	echo "(".$stcode_str.") \n";
	echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";



	##### End. 2024-09-26. 신규 스케줄링
	###########################################



	## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(86400, "변경점 3개월 지연 알림");

	echo date("Y-m-d H:i:s")." - 끝\n";

?>