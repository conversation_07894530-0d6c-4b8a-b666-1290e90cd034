#!/usr/local/bin/php -q
<?php
// 0 9 * * * php -q /home/<USER>/sperp/faulty_issue.psh
# 불량이슈 처리기한 알림
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/inc/Encode.php");

$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
if(empty($dbconn_sperp_posbank->success)) {
	echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
}

$dbconn_posbank_intra = new DBController($db['posbank_intra']);
if(empty($dbconn_posbank_intra->success)) {
	echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패";
}
/**********************************************************/

if(in_array(date('w'),array("0","6"))){
	echo date("Y-m-d") . " - 휴무일\n";
	## 스케즐 처리 상황 intra DB에 저장 
	crontab_execution(86400, "ERP 불량이슈 처리기한 알람");
	exit;
}


$SQL = "SELECT NAME FROM HOLIDAY_DATA WHERE to_char(HDATE,'YYYYMMDD')='".date('Ymd')."'";
$HOLIDAY_NM = $dbconn_sperp_posbank->query_one($SQL);
if($HOLIDAY_NM){
	echo date("Y-m-d") . " - 휴무일(".$HOLIDAY_NM.")\n";
	## 스케즐 처리 상황 intra DB에 저장 
	crontab_execution(86400, "ERP 불량이슈 처리기한 알람");
	exit;
}


$SQL = "SELECT 
				H.RCT_CODE||H.HDATE||H.HNO HID
				,H.HDATE,H.HCODE,H.STATE,H.U_COMPANY,H.SYMPTOM,H.STCODE
				,TO_CHAR(TO_DATE(H.HDATE,'YYYYMMDD'),'YYYY-MM-DD') HDATE2
				,(SELECT DBMS_LOB.SUBSTR(WM_CONCAT(STCODE)) FROM FAULTY_ISSUE_ASSING A WHERE H.RCT_CODE=A.RCT_CODE AND H.HDATE=A.HDATE AND H.HNO=A.HNO AND A.STATE='M') ASSING_ST
				-- ,(SELECT WM_CONCAT(B.CONTENTS) FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='D') D_CONTENTS
				,(SELECT CONTENTS FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='D' AND B.DNO=
					(SELECT max(B.DNO) FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='D')
				) D_CONTENTS
			FROM 
				FAULTY_ISSUE H 
				LEFT JOIN FAULTY_ISSUE_PROGRESS D ON (H.RCT_CODE=D.RCT_CODE AND H.HDATE=D.HDATE AND H.HNO=D.HNO AND D.STATE='M')
			WHERE H.STATE='D' AND TO_CHAR(SYSDATE,'YYYYMMDD')>TO_CHAR(F_CALCULATE_BUSINESS_DAYS(TO_DATE(H.HDATE,'YYYYMMDD'),DDAY_M),'YYYY-MM-DD') AND D.RCT_CODE IS NULL
			UNION ALL
			SELECT 
				H.RCT_CODE||H.HDATE||H.HNO HID
				,H.HDATE,H.HCODE,H.STATE,H.U_COMPANY,H.SYMPTOM,H.STCODE
				,TO_CHAR(TO_DATE(H.HDATE,'YYYYMMDD'),'YYYY-MM-DD') HDATE2
				,(SELECT DBMS_LOB.SUBSTR(WM_CONCAT(STCODE)) FROM FAULTY_ISSUE_ASSING A WHERE H.RCT_CODE=A.RCT_CODE AND H.HDATE=A.HDATE AND H.HNO=A.HNO AND A.STATE='A') ASSING_ST
				-- ,(SELECT WM_CONCAT(B.CONTENTS) FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='M') D_CONTENTS
				,(SELECT CONTENTS FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='M' AND B.DNO=
					(SELECT max(B.DNO) FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='M')
				) D_CONTENTS
			FROM 
				FAULTY_ISSUE H 
				LEFT JOIN FAULTY_ISSUE_PROGRESS D ON (H.RCT_CODE=D.RCT_CODE AND H.HDATE=D.HDATE AND H.HNO=D.HNO AND D.STATE='A')
			WHERE H.STATE='M' AND TO_CHAR(SYSDATE,'YYYYMMDD')>TO_CHAR(F_CALCULATE_BUSINESS_DAYS(TO_DATE(H.HDATE,'YYYYMMDD'),DDAY_A),'YYYY-MM-DD') AND D.RCT_CODE IS NULL
			UNION ALL
			SELECT 
				H.RCT_CODE||H.HDATE||H.HNO HID
				,H.HDATE,H.HCODE,H.STATE,H.U_COMPANY,H.SYMPTOM,H.STCODE
				,TO_CHAR(TO_DATE(H.HDATE,'YYYYMMDD'),'YYYY-MM-DD') HDATE2
				,(SELECT DBMS_LOB.SUBSTR(WM_CONCAT(STCODE)) FROM FAULTY_ISSUE_ASSING A WHERE H.RCT_CODE=A.RCT_CODE AND H.HDATE=A.HDATE AND H.HNO=A.HNO AND A.STATE='I') ASSING_ST
				-- ,(SELECT WM_CONCAT(B.CONTENTS) FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='A') D_CONTENTS
				,(SELECT CONTENTS FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='A' AND B.DNO=
					(SELECT max(B.DNO) FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='A')
				) D_CONTENTS
			FROM 
				FAULTY_ISSUE H 
				LEFT JOIN FAULTY_ISSUE_PROGRESS D ON (H.RCT_CODE=D.RCT_CODE AND H.HDATE=D.HDATE AND H.HNO=D.HNO AND D.STATE='I')
			WHERE H.STATE='A' AND TO_CHAR(SYSDATE,'YYYYMMDD')>TO_CHAR(F_CALCULATE_BUSINESS_DAYS(TO_DATE(H.HDATE,'YYYYMMDD'),DDAY_I),'YYYY-MM-DD') AND D.RCT_CODE IS NULL
			UNION ALL
			SELECT 
				H.RCT_CODE||H.HDATE||H.HNO HID
				,H.HDATE,H.HCODE,H.STATE,H.U_COMPANY,H.SYMPTOM,H.STCODE
				,TO_CHAR(TO_DATE(H.HDATE,'YYYYMMDD'),'YYYY-MM-DD') HDATE2
				,(SELECT DBMS_LOB.SUBSTR(WM_CONCAT(STCODE)) FROM FAULTY_ISSUE_ASSING A WHERE H.RCT_CODE=A.RCT_CODE AND H.HDATE=A.HDATE AND H.HNO=A.HNO AND A.STATE='C') ASSING_ST
				-- ,(SELECT WM_CONCAT(B.CONTENTS) FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='I') D_CONTENTS
				,(SELECT CONTENTS FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='I' AND B.DNO=
					(SELECT max(B.DNO) FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='I')
				) D_CONTENTS
			FROM 
				FAULTY_ISSUE H 
				LEFT JOIN FAULTY_ISSUE_PROGRESS D ON (H.RCT_CODE=D.RCT_CODE AND H.HDATE=D.HDATE AND H.HNO=D.HNO AND D.STATE='C')
			WHERE H.STATE='I' AND TO_CHAR(SYSDATE,'YYYYMMDD')>TO_CHAR(F_CALCULATE_BUSINESS_DAYS(TO_DATE(H.HDATE,'YYYYMMDD'),DDAY_C),'YYYY-MM-DD') AND D.RCT_CODE IS NULL
			ORDER BY HID DESC";
$arrRow = $dbconn_sperp_posbank->query_rows($SQL);

$SQL2 = "SELECT BAS_OP4 FROM BAS WHERE BAS_CODE='E009'";
$BAS_E009 = $dbconn_sperp_posbank->query_one($SQL2);




$arr_FAULTY_ISSUE_STATE = [];
$arr_FAULTY_ISSUE_STATE['D'] = "등록";//정의
$arr_FAULTY_ISSUE_STATE['M'] = "접수";//측정
$arr_FAULTY_ISSUE_STATE['A'] = "진단";//분석
$arr_FAULTY_ISSUE_STATE['I'] = "처리";//개선
$arr_FAULTY_ISSUE_STATE['C'] = "승인";//관리

$arr_hp = [];
if($arrRow){
	foreach($arrRow as $key => $row) { 
		unset($arr_hp);
		$arr_ST = [];
		$Kakao_TemCode = "";
		$kakaowork_msg = [];
		$goolgework_Params = [];


		if($row['ASSING_ST']){

//$row[ASSING_ST]="100695";
			$title = "[불량이슈] 처리기한이 지났습니다. (" . $row['HID'] . ")";
			$sms_msg = "[불량이슈]\n처리기한이 지났습니다.\n(" . $row['HID'] . ")";
			$regdate = strftime("%Y-%m-%d-%H-%M");
	
			$content = "<div style=\"font-size:12px;\">";
			$content .= "<div><b>[불량이슈]</b></div>";
			$content .= "<div>---------------------------------</div>";
			$content .= "<div style=\"clear:both;padding:5px;\"><div style=\"float:left;width:100px;\">이슈번호</div><div style=\"float:left;\">".$row['HID']."</div></div>";
			$content .= "<div style=\"clear:both;padding:5px;\"><div style=\"float:left;width:100px;\">업체명</div><div style=\"float:left;\">".$row['U_COMPANY']."</div></div>";
			$content .= "<div style=\"clear:both;padding:5px;\"><div style=\"float:left;width:100px;\">증상</div><div style=\"float:left;\">".nl2br($row['SYMPTOM'])."</div></div>";
			$content .= "<div style=\"clear:both;\">---------------------------------</div>";
			$content .= "<div><a href=\"https://i.posbank.com/sperp_login.html?https://www.spqms.co.kr/?pageCode=MTE0MzM=\" target=\"_blank\">[불량이슈 보러가기]</a></div>";
			$content .= "</div>";

//			$Kakao_msg2 = "[포스뱅크 QMS 알림]\n\n";
//			$Kakao_msg2 .= "불량이슈 처리기한이 지났습니다.\n\n";
//			$Kakao_msg2 .= "[구분]\n불량이슈\n\n";
//			$Kakao_msg2 .= "[이슈번호]\n" . $row['HID'] . "\n\n";
//			$Kakao_msg2 .= "[업체명]\n" . $row['U_COMPANY'] . "\n\n";
//			$Kakao_msg2 .= "[증상]\n" . mb_substr($row['SYMPTOM'], 0, 400, 'euc-kr') . "\n\n";
//			$Kakao_msg2 .= "[불량이슈 보러가기 아래 클릭]\n";
//			$Kakao_msg2 .= "https://www.spqms.co.kr/etc/kakao_link.html?mode=faulty_issue&HID=".$row['HID'];
//			$Kakao_TemCode = "posbank_000015";

			$kakaowork_msg[] = ["구분","불량이슈"];
			$kakaowork_msg[] = ["이슈번호",$row['HID']];
			$kakaowork_msg[] = ["업체명",$row['U_COMPANY']];
			$kakaowork_msg[] = ["증상",mb_substr($row['SYMPTOM'], 0, 500, 'euc-kr')];

			$goolgework_Params['MESSAGE'][] = ["<b>구분 : </b><font color='#555555'>불량이슈</font>"];
			$goolgework_Params['MESSAGE'][] = ["<b>이슈번호 : </b><font color='#555555'>".$row['HID']."</font>"];
			$goolgework_Params['MESSAGE'][] = ["<b>업체명 : </b><font color='#555555'>".$row['U_COMPANY']."</font>"];
			$goolgework_Params['MESSAGE'][] = ["<b>증상 : </b><font color='#555555'>".mb_substr($row['SYMPTOM'], 0, 400, 'euc-kr')."</font>"];

			$arr_ST = explode(",", $row['ASSING_ST']);

		}else{

			$arr_ST = explode(",", $BAS_E009);

			switch($row['STATE']){
				case "D": $state2 = "접수"; break; // 등록
				case "M": $state2 = "진단"; break; // 접수
				case "A": $state2 = "처리"; break; // 진단
				case "I": $state2 = "승인"; break; // 처리
			}
			$state3 = $arr_FAULTY_ISSUE_STATE[$row['STATE']];
			$title = "[불량이슈] ${state2}자를 지정해 주세요. (" . $row['HID'] . ")";
			$sms_msg = "[불량이슈]\n${state2}자를 지정해 주세요.\n(" . $row['HID'] . ")";
			$regdate = strftime("%Y-%m-%d-%H-%M");
			$hdate = date("Ymd");

			$content = "<div style=\"font-size:12px;\">";
			$content .= "<div style=\"font-size:14px;\"><b>[불량이슈]</b></div>";
			$content .= "<div>[상태:".$state3."] 처리 되었습니다.</div>";
			$content .= "<div>[다음상태:".$state2."] 처리자를 지정해 주세요.</div>";
			$content .= "<div>------------------------------------------------------------------</div>";
			$content .= "<div style=\"clear:both;padding:5px;\"><div style=\"float:left;width:100px;\">이슈번호</div><div style=\"float:left;\">".$row['HID']."</div></div>";
			$content .= "<div style=\"clear:both;padding:5px;\"><div style=\"float:left;width:100px;\">업체명</div><div style=\"float:left;\">".$row['U_COMPANY']."</div></div>";
			$content .= "<div style=\"clear:both;padding:5px;\"><div style=\"float:left;width:100px;\">증상</div><div style=\"float:left;\">".nl2br($row['SYMPTOM'])."</div></div>";
			$content .= "<div style=\"clear:both;\">------------------------------------------------------------------</div>";
			$content .= "<div style=\"clear:both;padding:5px;\"><div style=\"float:left;width:100px;\">$state3 내용</div><div style=\"float:left;\">".nl2br($row['D_CONTENTS'])."</div></div>";
			$content .= "<div style=\"clear:both;\">------------------------------------------------------------------</div>";
			$content .= "<div><a href=\"https://i.posbank.com/sperp_login.html?https://www.spqms.co.kr/?pageCode=MTE0MzM=\" target=\"_blank\">[불량이슈 보러가기]</a></div>";
			$content .= "</div>";


//			$Kakao_msg2 = "[포스뱅크 QMS 알림]\n\n";
//			$Kakao_msg2 .= $title."\n";
//			$Kakao_msg2 .= "[상태:".$state3."] 처리 되었습니다.\n\n";
//			$Kakao_msg2 .= "[다음상태:".$state2."] 처리자를 지정해 주세요.\n";
//			$Kakao_msg2 .= "-----------------------------\n\n";
//			$Kakao_msg2 .= "[구분]\n불량이슈\n\n";
//			$Kakao_msg2 .= "[이슈번호]\n" . $row['HID'] . "\n\n";
//			$Kakao_msg2 .= "[업체명]\n" . $row['U_COMPANY'] . "\n\n";
//			$Kakao_msg2 .= "[증상]\n" . mb_substr($row['SYMPTOM'], 0, 500, 'euc-kr') . "\n\n";
//			$Kakao_msg2 .= "[".$state3." 내용]\n" . mb_substr($row['D_CONTENTS'], 0, 500, 'euc-kr') . "\n\n";
//			$Kakao_msg2 .= "[불량이슈 보러가기 아래 클릭]\n";
//			$Kakao_msg2 .= "https://www.spqms.co.kr/etc/kakao_link.html?mode=faulty_issue&HID=".$row['HID'];
//			$Kakao_TemCode = "posbank_000015";


			$kakaowork_msg[] = ["구분","불량이슈"];
			$kakaowork_msg[] = ["이슈번호",$row['HID']];
			$kakaowork_msg[] = ["업체명",$row['U_COMPANY']];
			$kakaowork_msg[] = ["증상",mb_substr($row['SYMPTOM'], 0, 400, 'euc-kr')];
			$kakaowork_msg[] = [$state3." 내용",mb_substr($row['D_CONTENTS'], 0, 400, 'euc-kr')];

			$goolgework_Params['MESSAGE'][] = ["<b>구분 : </b><font color='#555555'>불량이슈</font>"];
			$goolgework_Params['MESSAGE'][] = ["<b>이슈번호 : </b><font color='#555555'>".$row['HID']."</font>"];
			$goolgework_Params['MESSAGE'][] = ["<b>업체명 : </b><font color='#555555'>".$row['U_COMPANY']."</font>"];
			$goolgework_Params['MESSAGE'][] = ["<b>증상 : </b><font color='#555555'>".mb_substr($row['SYMPTOM'], 0, 400, 'euc-kr')."</font>"];
			$goolgework_Params['MESSAGE'][] = ["<b>".$state3." 내용 : </b><font color='#555555'>".mb_substr($row['D_CONTENTS'], 0, 400, 'euc-kr')."</font>"];
		}
//$row['STCODE']="100695";
//$arr_ST = array('100695'); // 장진식
		//인트라넷 업무연락 보내는 함수(ERP 사원코드)
		$rs = intra_send_erp($row['STCODE'],$arr_ST,$title,$content);



//		$SQL3 = "SELECT PRS_NUM FROM PRS_MASTER WHERE FLAG='1' AND ERP_CODE IN ('".implode("','",$arr_ST)."')";
//		$arr_prsNum = $dbconn_posbank_intra->query_array($SQL3,'PRS_NUM','PRS_NUM');


//		$kakaowork_Params = [];
//		$kakaowork_Params['bot_gu'] = "qms"; // 봇구분(trans:업무연락,appv:결재문서,qms:QMS,system:시스템) 값 없을시 기본 봇
//		$kakaowork_Params['to'] = $arr_prsNum;
//		$kakaowork_Params['text'] = $title;
//		$kakaowork_Params['header'] = "불량이슈";
//		$kakaowork_Params['header_color'] = "blue";
//		$kakaowork_msg[] = ["일시",date('Y년 m월 d일 H시 i분')];
//		$kakaowork_Params['msg'] = $kakaowork_msg;
//		$kakaowork_Params['button'] = ["확인하러가기","https://www.spqms.co.kr/?pageCode=MTE0MzM=&&searKey=H.RCT_CODE||H.HDATE||H.HNO&searKeyword=".$row['HID']];
//
//		// 카카오워크 보내기
//		$rs = kakaowork_send($kakaowork_Params);


		// 구글워크스테이션 웹훅 보내기 2023-01-17 jjs
		$goolgework_Params['PROGRAM'] = "sperp";
		$goolgework_Params['GU'] = "qms";
		$goolgework_Params['ID'] = $arr_ST;
		$goolgework_Params['PREVIEW'] = $title;
		$goolgework_Params['TITLE'] = "불량이슈";
		$goolgework_Params['LINK_NM'] = "확인하러가기";
		$goolgework_Params['LINK'] = "https://www.spqms.co.kr/?pageCode=MTE0MzM=&&searKey=H.RCT_CODE||H.HDATE||H.HNO&searKeyword=".$row['HID'];
		$rs = goolgework_send($goolgework_Params);



		echo date("Y-m-d H:i:s")." - 이슈번호 : ".$row['HID']." - 업무연락 발송\n";
		echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";

/*
		$SQL3 = "SELECT PRS_NUM,PRS_NM,PART_CODE,PART_NM,HP FROM PRS_MASTER WHERE FLAG='1' AND IFNULL(ERP_CODE,'')<>'' AND ERP_CODE IN ('".implode("','",$arr_ST)."')";
		$SQL3_rows = $dbconn_posbank_intra->query_rows($SQL3);
		if($SQL3_rows){
			foreach($SQL3_rows as $key3 => $row3) {
				$arr_hp[$row3['HP']] = $row3['HP']."/".$row3['PRS_NM'];
			}
		}
*/



/*
		if($arr_hp){
			unset($KakaoTalk_Params);
			// 알림톡
			$KakaoTalk_Params['SMS_ID'] = "posbank2";
			$KakaoTalk_Params['dispatchPhone'] = "1588-6335";
			$KakaoTalk_Params['msgType'] = "1008";
			$KakaoTalk_Params['temCode'] = $Kakao_TemCode;
			$KakaoTalk_Params['receptionPhone'] = implode(";",$arr_hp);
			$KakaoTalk_Params['imgUrl'] = "";
			$KakaoTalk_Params['imgLink'] = "";
			$KakaoTalk_Params['toMsg'] = $Kakao_msg2;
			$KakaoTalk_Params['etc1'] = $sms_msg; // 친구톡 실패시 문자 발송 내용

//			$rs = KakaoTalk_send($KakaoTalk_Params);
			echo date("Y-m-d H:i:s")." - 이슈번호 : $row[HID] - $row3[PRS_NM] 카카오톡 정상발송\n";
		}
*/
	}
}


## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(86400, "ERP 불량이슈 처리기한 알람");
?>
