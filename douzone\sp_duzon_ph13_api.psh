#!/usr/local/bin/php -q
<?
# 더존 자동전표 API 연동 - 매출전표 (PH13)
# API 방식으로 변경된 버전

$ROOT_PATH = "/home/<USER>";
$_SERVER['DOCUMENT_ROOT'] = $ROOT_PATH;
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/douzone/douzone_api_helper.php");

// 데이터베이스 연결
$dbconn_e = new DBController($db['sperp_posbank']);
if(empty($dbconn_e->success)) {
    echo "[" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패입니다.";
    exit;
}

// API 설정 (실제 환경에 맞게 수정 필요)
$api_config = array(
    'api_url' => 'https://your-douzone-api-server.com',  // 실제 API 서버 URL
    'auth_token' => 'your-auth-token-here',              // 실제 인증 토큰
    'company_code' => '2005',                            // 회사코드
    'group_seq' => ''                                    // 그룹시퀀스
);

$douzone_api = new DouzoneApiHelper(
    $api_config['api_url'],
    $api_config['auth_token'],
    $api_config['company_code'],
    $api_config['group_seq']
);

echo date("Y-m-d H:i:s")." - API 전송 시작 (매출전표)\n";

// 기존 SELECT 쿼리 (동일)
$SQL = "SELECT KEY,
        ROW_NUMBER() OVER(PARTITION BY KEY ORDER BY KEY, GU2,FN_CD) AS SNO,RCT_CODE,HDATE,CT_CODE,CT_NAME,AMT,VAT,AMT+VAT CUAMT,AMT2,CT_LINK,CT_LINK2,  
        PRNAME,HNO,HGU,DZ_LINK,TMSTYLE,ACSTYLE,FN_CD,FN_LINK,SPERP_CPYID,GU1,
    CASE  WHEN GU1='11' THEN '과세매출'
            WHEN GU1='17' THEN '카드매출'
            WHEN GU1='31' THEN '현금과세'
            WHEN GU1='14' THEN '건별매출'
            WHEN GU1='16' THEN '수출'
            WHEN GU1='12' THEN '영세매출'
            WHEN GU1='13' THEN '면세매출'
            WHEN GU1='18' THEN '면세카드매출'
            WHEN GU1='32' THEN '현금면세'
    END  GU1_NAME, CHA,DECODE(CHA,'D','3','4') CHA_NAME
     FROM(
         SELECT (A.HDATE || A.HNO) AS KEY, A.RCT_CODE, A.HDATE, A.CT_CODE, C.CT_NAME, D.AMT, 0 VAT, 
                0 AMT2,CT_LINK,C.CT_LINK2,  
                B.PRNAME, A.HNO, A.HGU,A.DZ_LINK, A.TMSTYLE, A.ACSTYLE, D.FN_CD, D.FN_LINK,'2'|| A.HNO SPERP_CPYID,
                CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '11'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '17'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '31'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '14'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '11'
                     WHEN A.TMSTYLE='O' AND A.ACSTYLE='4' THEN '16'
                     WHEN A.TMSTYLE='O' AND A.ACSTYLE<>'4' THEN '12'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='1' THEN '13'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='2' THEN '18'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='3' THEN '32'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE>'3' THEN '13'
                END  GU1,
                CASE WHEN D.FN_CD='21107001' THEN 'C'
                     WHEN D.FN_CD='11105001' THEN 'D'
                     ELSE 'C'
                END CHA,
                '1' GU2
         FROM TMH A, TMS B, CT C,  
              ( 
               SELECT  A.TMHID, (C.BAS_OP4) AS FN_CD, nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
                       SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT
               FROM PD13 A 
                    LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE 
                    LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%' 
                    LEFT JOIN FN D ON C.BAS_OP4 = D.FN_CD1 || D.FN_CD2
               WHERE A.TMHID IS NOT NULL 
                 AND A.STATE = '8' 
                 AND SUBSTR(A.TMHID, 5,8) >= TO_CHAR(sysdate - 45,'YYYYMMDD')
               GROUP BY A.TMHID, C.BAS_OP4,nvl(D.FN_LINK2,D.FN_LINK)
              ) D 
         WHERE A.RCT_CODE = '1000' 
           AND A.HDATE >= TO_CHAR(sysdate - 45,'YYYYMMDD')
           AND A.TCHK <> 'Y'  
           AND A.RCTYPE = 'C'  
           AND A.HGU = '13'  
           AND A.RCT_CODE = B.RCT_CODE 
           AND A.HDATE = B.HDATE 
           AND A.HNO = B.HNO 
           AND A.HGU = B.HGU 
           AND B.SNO = 1 
           AND A.CT_CODE = C.CT_CODE (+)
           AND SUBSTR(D.TMHID, 0, 4) = A.RCT_CODE 
           AND SUBSTR(D.TMHID, 5, 8) = A.HDATE 
           AND SUBSTR(D.TMHID, 13, 2) = A.HGU 
           AND SUBSTR(D.TMHID, 15, 4) = A.HNO 
           AND (D.AMT + D.VAT) <> 0 
         UNION ALL
         -- 부가세예수금
         SELECT (A.HDATE || A.HNO) AS KEY, A.RCT_CODE, A.HDATE, A.CT_CODE, C.CT_NAME, 0 AMT, SUM(D.VAT) VAT, 
                 SUM(D.AMT2) AMT2,C.CT_LINK,C.CT_LINK2,  
                B.PRNAME, A.HNO, A.HGU,A.DZ_LINK, A.TMSTYLE, A.ACSTYLE, '21107001' FN_CD, '25500' FN_LINK,'2'|| A.HNO SPERP_CPYID,
                CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '11'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '17'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '31'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '14'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '11'
                     WHEN A.TMSTYLE='O' AND A.ACSTYLE='4' THEN '16'
                     WHEN A.TMSTYLE='O' AND A.ACSTYLE<>'4' THEN '12'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='1' THEN '13'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='2' THEN '18'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='3' THEN '32'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE>'3' THEN '13'
                END  GU1,
                'C' CHA,
                '2' GU2
         FROM TMH A, TMS B, CT C,  
              ( 
               SELECT  A.TMHID, (C.BAS_OP4) AS FN_CD, nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
                       SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT,SUM(A.AMT) AS AMT2
               FROM PD13 A 
                    LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE 
                    LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%' 
                    LEFT JOIN FN D ON C.BAS_OP4 = D.FN_CD1 || D.FN_CD2
               WHERE A.TMHID IS NOT NULL 
                 AND A.STATE = '8' 
                 AND SUBSTR(A.TMHID, 5,8) >= TO_CHAR(sysdate - 45,'YYYYMMDD')
               GROUP BY A.TMHID, C.BAS_OP4,nvl(D.FN_LINK2,D.FN_LINK)
              ) D 
         WHERE A.RCT_CODE = '1000' 
           AND A.HDATE >= TO_CHAR(sysdate - 45,'YYYYMMDD')
           AND A.TCHK <> 'Y'  
           AND A.RCTYPE = 'C'  
           AND A.HGU = '13'  
           AND A.RCT_CODE = B.RCT_CODE 
           AND A.HDATE = B.HDATE 
           AND A.HNO = B.HNO 
           AND A.HGU = B.HGU 
           AND B.SNO = 1 
           AND A.CT_CODE = C.CT_CODE (+)
           AND SUBSTR(D.TMHID, 0, 4) = A.RCT_CODE 
           AND SUBSTR(D.TMHID, 5, 8) = A.HDATE 
           AND SUBSTR(D.TMHID, 13, 2) = A.HGU 
           AND SUBSTR(D.TMHID, 15, 4) = A.HNO 
           AND (D.AMT + D.VAT) <> 0 
           GROUP BY (A.HDATE || A.HNO),A.RCT_CODE,A.HDATE, A.CT_CODE, C.CT_NAME,C.CT_LINK,C.CT_LINK2,  
                B.PRNAME, A.HNO, A.HGU, C.CT_LINK, A.DZ_LINK, A.TMSTYLE, A.ACSTYLE,'2'|| A.HNO,
                A.TMSTYLE,A.ACSTYLE
        UNION ALL
        -- 외상매출금
        SELECT (A.HDATE || A.HNO) AS KEY, A.RCT_CODE, A.HDATE, A.CT_CODE, C.CT_NAME,  SUM(D.AMT+D.VAT) AMT, 0 VAT, 
                0 AMT2,C.CT_LINK,C.CT_LINK2,  
                B.PRNAME, A.HNO, A.HGU,A.DZ_LINK, A.TMSTYLE, A.ACSTYLE, '11105001' FN_CD, '10800' FN_LINK,'2'|| A.HNO SPERP_CPYID,
                CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '11'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '17'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '31'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '14'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '11'
                     WHEN A.TMSTYLE='O' AND A.ACSTYLE='4' THEN '16'
                     WHEN A.TMSTYLE='O' AND A.ACSTYLE<>'4' THEN '12'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='1' THEN '13'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='2' THEN '18'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='3' THEN '32'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE>'3' THEN '13'
                END  GU1,
                'D' CHA,
                '3' GU2
         FROM TMH A, TMS B, CT C,  
              ( 
               SELECT  A.TMHID, (C.BAS_OP4) AS FN_CD, nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
                       SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT
               FROM PD13 A 
                    LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE 
                    LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%' 
                    LEFT JOIN FN D ON C.BAS_OP4 = D.FN_CD1 || D.FN_CD2
               WHERE A.TMHID IS NOT NULL 
                 AND A.STATE = '8' 
                 AND SUBSTR(A.TMHID, 5,8) >= TO_CHAR(sysdate - 45,'YYYYMMDD')
               GROUP BY A.TMHID, C.BAS_OP4,nvl(D.FN_LINK2,D.FN_LINK)
              ) D 
         WHERE A.RCT_CODE = '1000' 
           AND A.HDATE >= TO_CHAR(sysdate - 45,'YYYYMMDD')
           AND A.TCHK <> 'Y'  
           AND A.RCTYPE = 'C'  
           AND A.HGU = '13'  
           AND A.RCT_CODE = B.RCT_CODE 
           AND A.HDATE = B.HDATE 
           AND A.HNO = B.HNO 
           AND A.HGU = B.HGU 
           AND B.SNO = 1 
           AND A.CT_CODE = C.CT_CODE (+)
           AND SUBSTR(D.TMHID, 0, 4) = A.RCT_CODE 
           AND SUBSTR(D.TMHID, 5, 8) = A.HDATE 
           AND SUBSTR(D.TMHID, 13, 2) = A.HGU 
           AND SUBSTR(D.TMHID, 15, 4) = A.HNO 
           AND (D.AMT + D.VAT) <> 0 
           GROUP BY (A.HDATE || A.HNO),A.RCT_CODE,A.HDATE, A.CT_CODE, C.CT_NAME,C.CT_LINK,C.CT_LINK2,  
                B.PRNAME, A.HNO, A.HGU, C.CT_LINK, A.DZ_LINK, A.TMSTYLE, A.ACSTYLE,'2'|| A.HNO,
                A.TMSTYLE,A.ACSTYLE
    )ORDER BY HDATE,HNO,HGU,GU2,FN_CD";

$rows = $dbconn_e->query_rows($SQL);
$total_count = 0;
$success_count = 0;
$error_count = 0;

if($rows) {
    // 키별로 그룹화하여 API 호출
    $grouped_data = array();
    
    foreach($rows as $row) {
        $key = $row['KEY'];
        if (!isset($grouped_data[$key])) {
            $grouped_data[$key] = array();
        }
        $grouped_data[$key][] = $row;
    }
    
    foreach($grouped_data as $key => $voucher_lines) {
        $total_count++;
        
        // API 데이터 구성
        $api_data = array();
        
        foreach($voucher_lines as $line) {
            // 기존 전송 여부 확인 (API 방식에서는 중복 체크 로직 변경 필요)
            // 여기서는 DZ_LINK가 없는 경우만 전송하는 것으로 가정
            if (empty($line['DZ_LINK'])) {
                $api_data[] = array(
                    'inDivCd' => '1000',
                    'menuDt' => $line['HDATE'],
                    'menuSq' => intval($line['SPERP_CPYID']),
                    'menuLnSq' => intval($line['SNO']),
                    'isuDoc' => $line['PRNAME'],
                    'docuTy' => '3', // 매출
                    'drcrFg' => $line['CHA_NAME'],
                    'acctCd' => $line['FN_LINK'],
                    'trCd' => $line['CT_LINK'],
                    'trNm' => $line['CT_NAME'],
                    'acctAm' => floatval($line['CUAMT']),
                    'rmkDc' => $line['PRNAME'],
                    
                    // 부가세 관련 (필요시 추가)
                    'taxFg' => $line['GU1'],
                    'supAm' => floatval($line['AMT2'])
                );
            }
        }
        
        if (!empty($api_data)) {
            // API 호출
            $result = $douzone_api->registerAutoVoucher($api_data);
            
            // 결과 처리
            if ($result['success'] && isset($result['result']['resultCode']) && $result['result']['resultCode'] == 0) {
                $success_count++;
                
                // 성공시 DZ_LINK 업데이트
                $first_line = $voucher_lines[0];
                $update_sql = "UPDATE TMH 
                              SET DZ_LINK = '".$first_line['SPERP_CPYID']."'
                              WHERE RCT_CODE = '1000' 
                                AND HDATE = '".$first_line['HDATE']."'
                                AND HNO = '".$first_line['HNO']."'
                                AND HGU = '13'";
                $dbconn_e->iud_query($update_sql);
                
                echo "성공: {$key} - {$first_line['PRNAME']}\n";
            } else {
                $error_count++;
                $douzone_api->logApiResult($result, "매출전표 {$key}");
            }
        }
    }
}

echo date("Y-m-d H:i:s")." - API 전송 완료\n";
echo "전체: {$total_count}건, 성공: {$success_count}건, 실패: {$error_count}건\n";
echo "---------------------------\n";

// 스케줄 처리 상황 monitor DB에 저장
crontab_execution(86400, "더존 API 전송 완료 (매출)");

?>