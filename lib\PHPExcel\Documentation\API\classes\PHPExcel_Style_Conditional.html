<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Style_Conditional</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___clone" title="__clone :: Implement PHP __clone to create a deep clone, not just a shallow copy."><span class="description">Implement PHP __clone to create a deep clone, not just a shallow copy.</span><pre>__clone()</pre></a></li>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_Style_Conditional"><span class="description">Create a new PHPExcel_Style_Conditional</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_addCondition" title="addCondition :: Add Condition"><span class="description">Add Condition</span><pre>addCondition()</pre></a></li>
<li class="method public "><a href="#method_getCondition" title="getCondition :: Get Condition"><span class="description">Get Condition</span><pre>getCondition()</pre></a></li>
<li class="method public "><a href="#method_getConditionType" title="getConditionType :: Get Condition type"><span class="description">Get Condition type</span><pre>getConditionType()</pre></a></li>
<li class="method public "><a href="#method_getConditions" title="getConditions :: Get Conditions"><span class="description">Get Conditions</span><pre>getConditions()</pre></a></li>
<li class="method public "><a href="#method_getHashCode" title="getHashCode :: Get hash code"><span class="description">Get hash code</span><pre>getHashCode()</pre></a></li>
<li class="method public "><a href="#method_getOperatorType" title="getOperatorType :: Get Operator type"><span class="description">Get Operator type</span><pre>getOperatorType()</pre></a></li>
<li class="method public "><a href="#method_getStyle" title="getStyle :: Get Style"><span class="description">Get Style</span><pre>getStyle()</pre></a></li>
<li class="method public "><a href="#method_getText" title="getText :: Get text"><span class="description">Get text</span><pre>getText()</pre></a></li>
<li class="method public "><a href="#method_setCondition" title="setCondition :: Set Condition"><span class="description">Set Condition</span><pre>setCondition()</pre></a></li>
<li class="method public "><a href="#method_setConditionType" title="setConditionType :: Set Condition type"><span class="description">Set Condition type</span><pre>setConditionType()</pre></a></li>
<li class="method public "><a href="#method_setConditions" title="setConditions :: Set Conditions"><span class="description">Set Conditions</span><pre>setConditions()</pre></a></li>
<li class="method public "><a href="#method_setOperatorType" title="setOperatorType :: Set Operator type"><span class="description">Set Operator type</span><pre>setOperatorType()</pre></a></li>
<li class="method public "><a href="#method_setStyle" title="setStyle :: Set Style"><span class="description">Set Style</span><pre>setStyle()</pre></a></li>
<li class="method public "><a href="#method_setText" title="setText :: Set text"><span class="description">Set text</span><pre>setText()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__condition" title="$_condition :: Condition"><span class="description"></span><pre>$_condition</pre></a></li>
<li class="property private "><a href="#property__conditionType" title="$_conditionType :: Condition type"><span class="description"></span><pre>$_conditionType</pre></a></li>
<li class="property private "><a href="#property__operatorType" title="$_operatorType :: Operator type"><span class="description"></span><pre>$_operatorType</pre></a></li>
<li class="property private "><a href="#property__style" title="$_style :: Style"><span class="description"></span><pre>$_style</pre></a></li>
<li class="property private "><a href="#property__text" title="$_text :: Text"><span class="description"></span><pre>$_text</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_CONDITION_CELLIS" title="CONDITION_CELLIS :: "><span class="description">CONDITION_CELLIS</span><pre>CONDITION_CELLIS</pre></a></li>
<li class="constant  "><a href="#constant_CONDITION_CONTAINSTEXT" title="CONDITION_CONTAINSTEXT :: "><span class="description">CONDITION_CONTAINSTEXT</span><pre>CONDITION_CONTAINSTEXT</pre></a></li>
<li class="constant  "><a href="#constant_CONDITION_EXPRESSION" title="CONDITION_EXPRESSION :: "><span class="description">CONDITION_EXPRESSION</span><pre>CONDITION_EXPRESSION</pre></a></li>
<li class="constant  "><a href="#constant_CONDITION_NONE" title="CONDITION_NONE :: "><span class="description">CONDITION_NONE</span><pre>CONDITION_NONE</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_BEGINSWITH" title="OPERATOR_BEGINSWITH :: "><span class="description">OPERATOR_BEGINSWITH</span><pre>OPERATOR_BEGINSWITH</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_BETWEEN" title="OPERATOR_BETWEEN :: "><span class="description">OPERATOR_BETWEEN</span><pre>OPERATOR_BETWEEN</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_CONTAINSTEXT" title="OPERATOR_CONTAINSTEXT :: "><span class="description">OPERATOR_CONTAINSTEXT</span><pre>OPERATOR_CONTAINSTEXT</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_ENDSWITH" title="OPERATOR_ENDSWITH :: "><span class="description">OPERATOR_ENDSWITH</span><pre>OPERATOR_ENDSWITH</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_EQUAL" title="OPERATOR_EQUAL :: "><span class="description">OPERATOR_EQUAL</span><pre>OPERATOR_EQUAL</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_GREATERTHAN" title="OPERATOR_GREATERTHAN :: "><span class="description">OPERATOR_GREATERTHAN</span><pre>OPERATOR_GREATERTHAN</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_GREATERTHANOREQUAL" title="OPERATOR_GREATERTHANOREQUAL :: "><span class="description">OPERATOR_GREATERTHANOREQUAL</span><pre>OPERATOR_GREATERTHANOREQUAL</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_LESSTHAN" title="OPERATOR_LESSTHAN :: "><span class="description">OPERATOR_LESSTHAN</span><pre>OPERATOR_LESSTHAN</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_LESSTHANOREQUAL" title="OPERATOR_LESSTHANOREQUAL :: "><span class="description">OPERATOR_LESSTHANOREQUAL</span><pre>OPERATOR_LESSTHANOREQUAL</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_NONE" title="OPERATOR_NONE :: "><span class="description">OPERATOR_NONE</span><pre>OPERATOR_NONE</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_NOTCONTAINS" title="OPERATOR_NOTCONTAINS :: "><span class="description">OPERATOR_NOTCONTAINS</span><pre>OPERATOR_NOTCONTAINS</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_NOTEQUAL" title="OPERATOR_NOTEQUAL :: "><span class="description">OPERATOR_NOTEQUAL</span><pre>OPERATOR_NOTEQUAL</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Style_Conditional"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Style_Conditional.html">PHPExcel_Style_Conditional</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Style_Conditional</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Style.html">PHPExcel_Style</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___clone"></a><div class="element clickable method public method___clone" data-toggle="collapse" data-target=".method___clone .collapse">
<h2>Implement PHP __clone to create a deep clone, not just a shallow copy.</h2>
<pre>__clone() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_Style_Conditional</h2>
<pre>__construct() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_addCondition"></a><div class="element clickable method public method_addCondition" data-toggle="collapse" data-target=".method_addCondition .collapse">
<h2>Add Condition</h2>
<pre>addCondition(string $pValue) : <a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>Condition</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></code></div>
</div></div>
</div>
<a id="method_getCondition"></a><div class="element clickable method public method_getCondition" data-toggle="collapse" data-target=".method_getCondition .collapse">
<h2>Get Condition</h2>
<pre>getCondition() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>deprecated</th>
<td>Deprecated, use getConditions instead</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getConditionType"></a><div class="element clickable method public method_getConditionType" data-toggle="collapse" data-target=".method_getConditionType .collapse">
<h2>Get Condition type</h2>
<pre>getConditionType() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getConditions"></a><div class="element clickable method public method_getConditions" data-toggle="collapse" data-target=".method_getConditions .collapse">
<h2>Get Conditions</h2>
<pre>getConditions() : string[]</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string[]</code></div>
</div></div>
</div>
<a id="method_getHashCode"></a><div class="element clickable method public method_getHashCode" data-toggle="collapse" data-target=".method_getHashCode .collapse">
<h2>Get hash code</h2>
<pre>getHashCode() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Hash code</div>
</div></div>
</div>
<a id="method_getOperatorType"></a><div class="element clickable method public method_getOperatorType" data-toggle="collapse" data-target=".method_getOperatorType .collapse">
<h2>Get Operator type</h2>
<pre>getOperatorType() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getStyle"></a><div class="element clickable method public method_getStyle" data-toggle="collapse" data-target=".method_getStyle .collapse">
<h2>Get Style</h2>
<pre>getStyle() : <a href="../classes/PHPExcel_Style.html">\PHPExcel_Style</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style.html">\PHPExcel_Style</a></code></div>
</div></div>
</div>
<a id="method_getText"></a><div class="element clickable method public method_getText" data-toggle="collapse" data-target=".method_getText .collapse">
<h2>Get text</h2>
<pre>getText() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_setCondition"></a><div class="element clickable method public method_setCondition" data-toggle="collapse" data-target=".method_setCondition .collapse">
<h2>Set Condition</h2>
<pre>setCondition(string $pValue) : <a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>deprecated</th>
<td>Deprecated, use setConditions instead</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>Condition</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></code></div>
</div></div>
</div>
<a id="method_setConditionType"></a><div class="element clickable method public method_setConditionType" data-toggle="collapse" data-target=".method_setConditionType .collapse">
<h2>Set Condition type</h2>
<pre>setConditionType(string $pValue) : <a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>PHPExcel_Style_Conditional condition type</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></code></div>
</div></div>
</div>
<a id="method_setConditions"></a><div class="element clickable method public method_setConditions" data-toggle="collapse" data-target=".method_setConditions .collapse">
<h2>Set Conditions</h2>
<pre>setConditions(string[] $pValue) : <a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string[]</code><p>Condition</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></code></div>
</div></div>
</div>
<a id="method_setOperatorType"></a><div class="element clickable method public method_setOperatorType" data-toggle="collapse" data-target=".method_setOperatorType .collapse">
<h2>Set Operator type</h2>
<pre>setOperatorType(string $pValue) : <a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>PHPExcel_Style_Conditional operator type</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></code></div>
</div></div>
</div>
<a id="method_setStyle"></a><div class="element clickable method public method_setStyle" data-toggle="collapse" data-target=".method_setStyle .collapse">
<h2>Set Style</h2>
<pre>setStyle(\PHPExcel_Style $pValue) : <a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code><a href="../classes/PHPExcel_Style.html">\PHPExcel_Style</a></code>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></code></div>
</div></div>
</div>
<a id="method_setText"></a><div class="element clickable method public method_setText" data-toggle="collapse" data-target=".method_setText .collapse">
<h2>Set text</h2>
<pre>setText(string $value) : <a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Conditional.html">\PHPExcel_Style_Conditional</a></code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__condition"> </a><div class="element clickable property private property__condition" data-toggle="collapse" data-target=".property__condition .collapse">
<h2></h2>
<pre>$_condition : string[]</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__conditionType"> </a><div class="element clickable property private property__conditionType" data-toggle="collapse" data-target=".property__conditionType .collapse">
<h2></h2>
<pre>$_conditionType : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__operatorType"> </a><div class="element clickable property private property__operatorType" data-toggle="collapse" data-target=".property__operatorType .collapse">
<h2></h2>
<pre>$_operatorType : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__style"> </a><div class="element clickable property private property__style" data-toggle="collapse" data-target=".property__style .collapse">
<h2></h2>
<pre>$_style : <a href="../classes/PHPExcel_Style.html">\PHPExcel_Style</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__text"> </a><div class="element clickable property private property__text" data-toggle="collapse" data-target=".property__text .collapse">
<h2></h2>
<pre>$_text : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_CONDITION_CELLIS"> </a><div class="element clickable constant  constant_CONDITION_CELLIS" data-toggle="collapse" data-target=".constant_CONDITION_CELLIS .collapse">
<h2>CONDITION_CELLIS</h2>
<pre>CONDITION_CELLIS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CONDITION_CONTAINSTEXT"> </a><div class="element clickable constant  constant_CONDITION_CONTAINSTEXT" data-toggle="collapse" data-target=".constant_CONDITION_CONTAINSTEXT .collapse">
<h2>CONDITION_CONTAINSTEXT</h2>
<pre>CONDITION_CONTAINSTEXT </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CONDITION_EXPRESSION"> </a><div class="element clickable constant  constant_CONDITION_EXPRESSION" data-toggle="collapse" data-target=".constant_CONDITION_EXPRESSION .collapse">
<h2>CONDITION_EXPRESSION</h2>
<pre>CONDITION_EXPRESSION </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CONDITION_NONE"> </a><div class="element clickable constant  constant_CONDITION_NONE" data-toggle="collapse" data-target=".constant_CONDITION_NONE .collapse">
<h2>CONDITION_NONE</h2>
<pre>CONDITION_NONE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_BEGINSWITH"> </a><div class="element clickable constant  constant_OPERATOR_BEGINSWITH" data-toggle="collapse" data-target=".constant_OPERATOR_BEGINSWITH .collapse">
<h2>OPERATOR_BEGINSWITH</h2>
<pre>OPERATOR_BEGINSWITH </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_BETWEEN"> </a><div class="element clickable constant  constant_OPERATOR_BETWEEN" data-toggle="collapse" data-target=".constant_OPERATOR_BETWEEN .collapse">
<h2>OPERATOR_BETWEEN</h2>
<pre>OPERATOR_BETWEEN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_CONTAINSTEXT"> </a><div class="element clickable constant  constant_OPERATOR_CONTAINSTEXT" data-toggle="collapse" data-target=".constant_OPERATOR_CONTAINSTEXT .collapse">
<h2>OPERATOR_CONTAINSTEXT</h2>
<pre>OPERATOR_CONTAINSTEXT </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_ENDSWITH"> </a><div class="element clickable constant  constant_OPERATOR_ENDSWITH" data-toggle="collapse" data-target=".constant_OPERATOR_ENDSWITH .collapse">
<h2>OPERATOR_ENDSWITH</h2>
<pre>OPERATOR_ENDSWITH </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_EQUAL"> </a><div class="element clickable constant  constant_OPERATOR_EQUAL" data-toggle="collapse" data-target=".constant_OPERATOR_EQUAL .collapse">
<h2>OPERATOR_EQUAL</h2>
<pre>OPERATOR_EQUAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_GREATERTHAN"> </a><div class="element clickable constant  constant_OPERATOR_GREATERTHAN" data-toggle="collapse" data-target=".constant_OPERATOR_GREATERTHAN .collapse">
<h2>OPERATOR_GREATERTHAN</h2>
<pre>OPERATOR_GREATERTHAN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_GREATERTHANOREQUAL"> </a><div class="element clickable constant  constant_OPERATOR_GREATERTHANOREQUAL" data-toggle="collapse" data-target=".constant_OPERATOR_GREATERTHANOREQUAL .collapse">
<h2>OPERATOR_GREATERTHANOREQUAL</h2>
<pre>OPERATOR_GREATERTHANOREQUAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_LESSTHAN"> </a><div class="element clickable constant  constant_OPERATOR_LESSTHAN" data-toggle="collapse" data-target=".constant_OPERATOR_LESSTHAN .collapse">
<h2>OPERATOR_LESSTHAN</h2>
<pre>OPERATOR_LESSTHAN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_LESSTHANOREQUAL"> </a><div class="element clickable constant  constant_OPERATOR_LESSTHANOREQUAL" data-toggle="collapse" data-target=".constant_OPERATOR_LESSTHANOREQUAL .collapse">
<h2>OPERATOR_LESSTHANOREQUAL</h2>
<pre>OPERATOR_LESSTHANOREQUAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_NONE"> </a><div class="element clickable constant  constant_OPERATOR_NONE" data-toggle="collapse" data-target=".constant_OPERATOR_NONE .collapse">
<h2>OPERATOR_NONE</h2>
<pre>OPERATOR_NONE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_NOTCONTAINS"> </a><div class="element clickable constant  constant_OPERATOR_NOTCONTAINS" data-toggle="collapse" data-target=".constant_OPERATOR_NOTCONTAINS .collapse">
<h2>OPERATOR_NOTCONTAINS</h2>
<pre>OPERATOR_NOTCONTAINS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_NOTEQUAL"> </a><div class="element clickable constant  constant_OPERATOR_NOTEQUAL" data-toggle="collapse" data-target=".constant_OPERATOR_NOTEQUAL .collapse">
<h2>OPERATOR_NOTEQUAL</h2>
<pre>OPERATOR_NOTEQUAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:37Z.<br></footer></div>
</div>
</body>
</html>
