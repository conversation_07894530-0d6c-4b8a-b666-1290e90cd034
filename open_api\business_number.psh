#!/usr/bin/php -q
<?php
	// 0 12 * * * php -q /home/<USER>/open_api/business_number.psh
	# 공공데이터포털 Open Api 적용 (https://www.data.go.kr/)
	# 사업자등록정보 상태조회
	$ROOT_PATH = "/home/<USER>";
	$_SERVER['DOCUMENT_ROOT'] = $ROOT_PATH;
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/func.php");

	echo date("Y-m-d H:i:s")." - 사업자번호 상태 저장 시작\n";

	$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
	if(empty($dbconn_sperp_posbank->success)) {
		echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
	}

	$Checkday = date("Ymd", strtotime ("-1 day"));
	$SQL = "select 
					count(*)
				from 
					CUST A
					left join CT B on A.CT_CODE=B.CT_CODE
					left join CT_NO_STATUS C on A.CT_CODE=C.CT_CODE
				where 
					A.CT_END<>'2' 
					and length(replace(trim(B.CT_NO),'-',''))=10
					and (C.CT_CODE is null or (C.B_STT_CD<>'03' and to_char(C.REG_DATE,'yyyymmdd')<'".$Checkday."'))";
	$chk = $dbconn_sperp_posbank->query_one($SQL);
	$len = ceil($chk / 100);
//$len = 10;
	for($n=0; $n<$len; $n++){
		$arr_ct = [];
		$arr_body = [];
		$arr_head = [];
		$arr_query = [];
		$cnt1 = 0;
		$cnt2 = 0;

		$SQL = "select 
						A.CT_CODE,trim(B.CT_NO) CT_NO,A.CT_END
					from 
						CUST A
						left join CT B on A.CT_CODE=B.CT_CODE
						left join CT_NO_STATUS C on A.CT_CODE=C.CT_CODE
					where 
						A.CT_END<>'2' 
						and length(replace(trim(B.CT_NO),'-',''))=10
						and (C.CT_CODE is null or (C.B_STT_CD<>'03' and to_char(C.REG_DATE,'yyyymmdd')<'".$Checkday."'))
						and ROWNUM<=100";
		$rows = $dbconn_sperp_posbank->query_rows($SQL);

		if($rows){
			foreach($rows as $key => $row) {
				$ct_no = num_replace($row['CT_NO']);
				$arr_body['b_no'][] = $ct_no;
				$arr_ct[$ct_no] = $row['CT_CODE'];
			}
		}

		if($arr_body && $arr_body['b_no']){
			$arr_body['b_no'] = array_unique2($arr_body['b_no']);

			$serviceKey = "nBWE4Mp9voUIcCFxeiYEYDk7q5GXLLA9KfUQPoBKr3N8m2SBYjR%2BRamLGlsQotzzV7RQxnE%2F4Jtf%2FIdQ3Rf5mg%3D%3D";
			$url = "https://api.odcloud.kr/api/nts-businessman/v1/status?serviceKey=".$serviceKey;
			$arr_head[0] = "accept: application/json";
			$arr_head[1] = "Content-Type: application/json";
//			unset($arr_body);
//			$arr_body['b_no'][0] = "**********";
//			$arr_body['b_no'][1] = "**********";
			$data = make_curl($url, "POST", $arr_head, $arr_body);
//print_r($data);
			if($data['result']){
				if($data['result']['data']){
					foreach($data['result']['data'] as $key => $row) {
						$ct_code = $arr_ct[$row['b_no']];
						if($row && $ct_code){
							$arr_query[] = "
										MERGE INTO CT_NO_STATUS
											USING DUAL
												ON (CT_CODE='".$ct_code."')
											WHEN MATCHED THEN
												UPDATE SET
													B_NO = '".$row['b_no']."'
													,B_STT = '".$row['b_stt']."'
													,B_STT_CD = '".$row['b_stt_cd']."'
													,TAX_TYPE = '".$row['tax_type']."'
													,TAX_TYPE_CD = '".$row['tax_type_cd']."'
													,END_DT = '".$row['end_dt']."'
													,TAX_TYPE_CHANGE_DT = '".$row['tax_type_change_dt']."'
													,INVOICE_APPLY_DT = '".$row['invoice_apply_dt']."'
													,REG_DATE = SYSDATE
											WHEN NOT MATCHED THEN
												INSERT (CT_CODE,B_NO,B_STT,B_STT_CD,TAX_TYPE,TAX_TYPE_CD,END_DT,TAX_TYPE_CHANGE_DT,INVOICE_APPLY_DT,REG_IDATE,REG_DATE)
												VALUES ('".$ct_code."','".$row['b_no']."','".$row['b_stt']."','".$row['b_stt_cd']."','".$row['tax_type']."','".$row['tax_type_cd']."','".$row['end_dt']."','".$row['tax_type_change_dt']."','".$row['invoice_apply_dt']."',SYSDATE,SYSDATE)
										";
						}
					}
					$cnt1 = $data['result']['request_cnt'];
				}
			}
			if($arr_query){
				$rs = $dbconn_sperp_posbank->iud_query($arr_query);

				if(!$rs['state']) {
					echo " iud_query error : " . $rs['error'] . "\n";
				}
				$cnt2 = sizeof($arr_query);
			}
		}

		echo date("Y-m-d H:i:s")." - 사업자번호 상태 처리 ".($n+1)."(".$chk."/".$cnt1."/".$cnt2.")\n";

		//sleep(1); // 1초 지연시킴
		usleep(300000); // 0.3초 지연시킴
	}

	echo date("Y-m-d H:i:s")." - 사업자번호 상태 저장 끝\n";

	## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(21600, "ERP 사업자번호 상태 저장");
