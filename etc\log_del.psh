#!/usr/bin/php -q
<?php
# 60일 지난 로그파일 삭제
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

echo date("Y-m-d H:i:s")." - 시작 \n";

$log_dir = $ROOT_PATH . "/log";
$day = "60";

system ("find ".$log_dir." -name '*.log' -mtime +".$day." -exec rm -rf {} \;");

echo date("Y-m-d H:i:s") . " - 종료\n";

## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(604800, "로그파일 삭제"); // 매주 일요일 실행
?>