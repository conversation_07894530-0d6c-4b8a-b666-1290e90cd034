<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_ReferenceHelper</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___clone" title="__clone :: __clone implementation."><span class="description">__clone implementation.</span><pre>__clone()</pre></a></li>
<li class="method public "><a href="#method_cellReverseSort" title="cellReverseSort :: Compare two cell addresses
Intended for use as a Callback function for sorting cell addresses by column and row"><span class="description">Compare two cell addresses
Intended for use as a Callback function for sorting cell addresses by column and row</span><pre>cellReverseSort()</pre></a></li>
<li class="method public "><a href="#method_cellSort" title="cellSort :: Compare two cell addresses
Intended for use as a Callback function for sorting cell addresses by column and row"><span class="description">Compare two cell addresses
Intended for use as a Callback function for sorting cell addresses by column and row</span><pre>cellSort()</pre></a></li>
<li class="method public "><a href="#method_columnReverseSort" title="columnReverseSort :: Compare two column addresses
Intended for use as a Callback function for reverse sorting column addresses by column"><span class="description">Compare two column addresses
Intended for use as a Callback function for reverse sorting column addresses by column</span><pre>columnReverseSort()</pre></a></li>
<li class="method public "><a href="#method_columnSort" title="columnSort :: Compare two column addresses
Intended for use as a Callback function for sorting column addresses by column"><span class="description">Compare two column addresses
Intended for use as a Callback function for sorting column addresses by column</span><pre>columnSort()</pre></a></li>
<li class="method public "><a href="#method_getInstance" title="getInstance :: Get an instance of this class"><span class="description">Get an instance of this class</span><pre>getInstance()</pre></a></li>
<li class="method public "><a href="#method_insertNewBefore" title="insertNewBefore :: Insert a new column or row, updating all possible related data"><span class="description">Insert a new column or row, updating all possible related data</span><pre>insertNewBefore()</pre></a></li>
<li class="method public "><a href="#method_updateCellReference" title="updateCellReference :: Update cell reference"><span class="description">Update cell reference</span><pre>updateCellReference()</pre></a></li>
<li class="method public "><a href="#method_updateFormulaReferences" title="updateFormulaReferences :: Update references within formulas"><span class="description">Update references within formulas</span><pre>updateFormulaReferences()</pre></a></li>
<li class="method public "><a href="#method_updateNamedFormulas" title="updateNamedFormulas :: Update named formulas (i.e."><span class="description">Update named formulas (i.e.</span><pre>updateNamedFormulas()</pre></a></li>
</ul>
</li>
<li class="nav-header protected">» Protected
                    <ul>
<li class="method protected "><a href="#method___construct" title="__construct :: Create a new PHPExcel_ReferenceHelper"><span class="description">Create a new PHPExcel_ReferenceHelper</span><pre>__construct()</pre></a></li>
<li class="method protected "><a href="#method__adjustColumnDimensions" title="_adjustColumnDimensions :: Update column dimensions when inserting/deleting rows/columns"><span class="description">Update column dimensions when inserting/deleting rows/columns</span><pre>_adjustColumnDimensions()</pre></a></li>
<li class="method protected "><a href="#method__adjustComments" title="_adjustComments :: Update cell comments when inserting/deleting rows/columns"><span class="description">Update cell comments when inserting/deleting rows/columns</span><pre>_adjustComments()</pre></a></li>
<li class="method protected "><a href="#method__adjustDataValidations" title="_adjustDataValidations :: Update data validations when inserting/deleting rows/columns"><span class="description">Update data validations when inserting/deleting rows/columns</span><pre>_adjustDataValidations()</pre></a></li>
<li class="method protected "><a href="#method__adjustHyperlinks" title="_adjustHyperlinks :: Update hyperlinks when inserting/deleting rows/columns"><span class="description">Update hyperlinks when inserting/deleting rows/columns</span><pre>_adjustHyperlinks()</pre></a></li>
<li class="method protected "><a href="#method__adjustMergeCells" title="_adjustMergeCells :: Update merged cells when inserting/deleting rows/columns"><span class="description">Update merged cells when inserting/deleting rows/columns</span><pre>_adjustMergeCells()</pre></a></li>
<li class="method protected "><a href="#method__adjustPageBreaks" title="_adjustPageBreaks :: Update page breaks when inserting/deleting rows/columns"><span class="description">Update page breaks when inserting/deleting rows/columns</span><pre>_adjustPageBreaks()</pre></a></li>
<li class="method protected "><a href="#method__adjustProtectedCells" title="_adjustProtectedCells :: Update protected cells when inserting/deleting rows/columns"><span class="description">Update protected cells when inserting/deleting rows/columns</span><pre>_adjustProtectedCells()</pre></a></li>
<li class="method protected "><a href="#method__adjustRowDimensions" title="_adjustRowDimensions :: Update row dimensions when inserting/deleting rows/columns"><span class="description">Update row dimensions when inserting/deleting rows/columns</span><pre>_adjustRowDimensions()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="method private "><a href="#method__updateCellRange" title="_updateCellRange :: Update cell range"><span class="description">Update cell range</span><pre>_updateCellRange()</pre></a></li>
<li class="method private "><a href="#method__updateSingleCellReference" title="_updateSingleCellReference :: Update single cell reference"><span class="description">Update single cell reference</span><pre>_updateSingleCellReference()</pre></a></li>
<li class="method private "><a href="#method_cellAddressInDeleteRange" title="cellAddressInDeleteRange :: Test whether a cell address falls within a defined range of cells"><span class="description">Test whether a cell address falls within a defined range of cells</span><pre>cellAddressInDeleteRange()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul><li class="property private "><a href="#property__instance" title="$_instance :: Instance of this class"><span class="description"></span><pre>$_instance</pre></a></li></ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_REFHELPER_REGEXP_CELLRANGE" title="REFHELPER_REGEXP_CELLRANGE :: "><span class="description">REFHELPER_REGEXP_CELLRANGE</span><pre>REFHELPER_REGEXP_CELLRANGE</pre></a></li>
<li class="constant  "><a href="#constant_REFHELPER_REGEXP_CELLREF" title="REFHELPER_REGEXP_CELLREF :: Regular Expressions"><span class="description">Regular Expressions</span><pre>REFHELPER_REGEXP_CELLREF</pre></a></li>
<li class="constant  "><a href="#constant_REFHELPER_REGEXP_COLRANGE" title="REFHELPER_REGEXP_COLRANGE :: "><span class="description">REFHELPER_REGEXP_COLRANGE</span><pre>REFHELPER_REGEXP_COLRANGE</pre></a></li>
<li class="constant  "><a href="#constant_REFHELPER_REGEXP_ROWRANGE" title="REFHELPER_REGEXP_ROWRANGE :: "><span class="description">REFHELPER_REGEXP_ROWRANGE</span><pre>REFHELPER_REGEXP_ROWRANGE</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_ReferenceHelper"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_ReferenceHelper.html">PHPExcel_ReferenceHelper</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_ReferenceHelper (Singleton)</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.html">PHPExcel</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___clone"></a><div class="element clickable method public method___clone" data-toggle="collapse" data-target=".method___clone .collapse">
<h2>__clone implementation.</h2>
<pre>__clone() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Cloning should not be allowed in a Singleton!</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
</div></div>
</div>
<a id="method_cellReverseSort"></a><div class="element clickable method public method_cellReverseSort" data-toggle="collapse" data-target=".method_cellReverseSort .collapse">
<h2>Compare two cell addresses
Intended for use as a Callback function for sorting cell addresses by column and row</h2>
<pre>cellReverseSort(string $a, string $b) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$a</h4>
<code>string</code><p>First cell to test (e.g. 'AA1')</p>
</div>
<div class="subelement argument">
<h4>$b</h4>
<code>string</code><p>Second cell to test (e.g. 'Z1')</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>integer</code></div>
</div></div>
</div>
<a id="method_cellSort"></a><div class="element clickable method public method_cellSort" data-toggle="collapse" data-target=".method_cellSort .collapse">
<h2>Compare two cell addresses
Intended for use as a Callback function for sorting cell addresses by column and row</h2>
<pre>cellSort(string $a, string $b) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$a</h4>
<code>string</code><p>First cell to test (e.g. 'AA1')</p>
</div>
<div class="subelement argument">
<h4>$b</h4>
<code>string</code><p>Second cell to test (e.g. 'Z1')</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>integer</code></div>
</div></div>
</div>
<a id="method_columnReverseSort"></a><div class="element clickable method public method_columnReverseSort" data-toggle="collapse" data-target=".method_columnReverseSort .collapse">
<h2>Compare two column addresses
Intended for use as a Callback function for reverse sorting column addresses by column</h2>
<pre>columnReverseSort(string $a, string $b) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$a</h4>
<code>string</code><p>First column to test (e.g. 'AA')</p>
</div>
<div class="subelement argument">
<h4>$b</h4>
<code>string</code><p>Second column to test (e.g. 'Z')</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>integer</code></div>
</div></div>
</div>
<a id="method_columnSort"></a><div class="element clickable method public method_columnSort" data-toggle="collapse" data-target=".method_columnSort .collapse">
<h2>Compare two column addresses
Intended for use as a Callback function for sorting column addresses by column</h2>
<pre>columnSort(string $a, string $b) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$a</h4>
<code>string</code><p>First column to test (e.g. 'AA')</p>
</div>
<div class="subelement argument">
<h4>$b</h4>
<code>string</code><p>Second column to test (e.g. 'Z')</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>integer</code></div>
</div></div>
</div>
<a id="method_getInstance"></a><div class="element clickable method public method_getInstance" data-toggle="collapse" data-target=".method_getInstance .collapse">
<h2>Get an instance of this class</h2>
<pre>getInstance() : <a href="../classes/PHPExcel_ReferenceHelper.html">\PHPExcel_ReferenceHelper</a></pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_ReferenceHelper.html">\PHPExcel_ReferenceHelper</a></code></div>
</div></div>
</div>
<a id="method_insertNewBefore"></a><div class="element clickable method public method_insertNewBefore" data-toggle="collapse" data-target=".method_insertNewBefore .collapse">
<h2>Insert a new column or row, updating all possible related data</h2>
<pre>insertNewBefore(string $pBefore, integer $pNumCols, integer $pNumRows, \PHPExcel_Worksheet $pSheet) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>string</code><p>Insert before this cell address (e.g. 'A1')</p>
</div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>integer</code><p>Number of columns to insert/delete (negative values indicate deletion)</p>
</div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>integer</code><p>Number of rows to insert/delete (negative values indicate deletion)</p>
</div>
<div class="subelement argument">
<h4>$pSheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><p>The worksheet that we're editing</p>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
</div></div>
</div>
<a id="method_updateCellReference"></a><div class="element clickable method public method_updateCellReference" data-toggle="collapse" data-target=".method_updateCellReference .collapse">
<h2>Update cell reference</h2>
<pre>updateCellReference(string $pCellRange, int $pBefore, int $pNumCols, int $pNumRows) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pCellRange</h4>
<code>string</code><p>Cell range</p></div>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>int</code><p>Insert before this one</p></div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>int</code><p>Number of columns to increment</p></div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>int</code><p>Number of rows to increment</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Updated cell range</div>
</div></div>
</div>
<a id="method_updateFormulaReferences"></a><div class="element clickable method public method_updateFormulaReferences" data-toggle="collapse" data-target=".method_updateFormulaReferences .collapse">
<h2>Update references within formulas</h2>
<pre>updateFormulaReferences(string $pFormula, int $pBefore, int $pNumCols, int $pNumRows, string $sheetName) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pFormula</h4>
<code>string</code><p>Formula to update</p></div>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>int</code><p>Insert before this one</p></div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>int</code><p>Number of columns to insert</p></div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>int</code><p>Number of rows to insert</p></div>
<div class="subelement argument">
<h4>$sheetName</h4>
<code>string</code><p>Worksheet name/title</p>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Updated formula</div>
</div></div>
</div>
<a id="method_updateNamedFormulas"></a><div class="element clickable method public method_updateNamedFormulas" data-toggle="collapse" data-target=".method_updateNamedFormulas .collapse">
<h2>Update named formulas (i.e.</h2>
<pre>updateNamedFormulas(\PHPExcel $pPhpExcel, string $oldName, string $newName) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>containing worksheet references / named ranges)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pPhpExcel</h4>
<code><a href="../classes/PHPExcel.html">\PHPExcel</a></code><p>Object to update</p></div>
<div class="subelement argument">
<h4>$oldName</h4>
<code>string</code><p>Old name (name to replace)</p>
</div>
<div class="subelement argument">
<h4>$newName</h4>
<code>string</code><p>New name</p></div>
</div></div>
</div>
<a id="method___construct"></a><div class="element clickable method protected method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_ReferenceHelper</h2>
<pre>__construct() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__adjustColumnDimensions"></a><div class="element clickable method protected method__adjustColumnDimensions" data-toggle="collapse" data-target=".method__adjustColumnDimensions .collapse">
<h2>Update column dimensions when inserting/deleting rows/columns</h2>
<pre>_adjustColumnDimensions(<a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a> $pSheet, string $pBefore, integer $beforeColumnIndex, integer $pNumCols, integer $beforeRow, integer $pNumRows) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pSheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><p>The worksheet that we're editing</p>
</div>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>string</code><p>Insert/Delete before this cell address (e.g. 'A1')</p>
</div>
<div class="subelement argument">
<h4>$beforeColumnIndex</h4>
<code>integer</code><p>Index number of the column we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>integer</code><p>Number of columns to insert/delete (negative values indicate deletion)</p>
</div>
<div class="subelement argument">
<h4>$beforeRow</h4>
<code>integer</code><p>Number of the row we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>integer</code><p>Number of rows to insert/delete (negative values indicate deletion)</p>
</div>
</div></div>
</div>
<a id="method__adjustComments"></a><div class="element clickable method protected method__adjustComments" data-toggle="collapse" data-target=".method__adjustComments .collapse">
<h2>Update cell comments when inserting/deleting rows/columns</h2>
<pre>_adjustComments(<a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a> $pSheet, string $pBefore, integer $beforeColumnIndex, integer $pNumCols, integer $beforeRow, integer $pNumRows) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pSheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><p>The worksheet that we're editing</p>
</div>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>string</code><p>Insert/Delete before this cell address (e.g. 'A1')</p>
</div>
<div class="subelement argument">
<h4>$beforeColumnIndex</h4>
<code>integer</code><p>Index number of the column we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>integer</code><p>Number of columns to insert/delete (negative values indicate deletion)</p>
</div>
<div class="subelement argument">
<h4>$beforeRow</h4>
<code>integer</code><p>Number of the row we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>integer</code><p>Number of rows to insert/delete (negative values indicate deletion)</p>
</div>
</div></div>
</div>
<a id="method__adjustDataValidations"></a><div class="element clickable method protected method__adjustDataValidations" data-toggle="collapse" data-target=".method__adjustDataValidations .collapse">
<h2>Update data validations when inserting/deleting rows/columns</h2>
<pre>_adjustDataValidations(<a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a> $pSheet, string $pBefore, integer $beforeColumnIndex, integer $pNumCols, integer $beforeRow, integer $pNumRows) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pSheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><p>The worksheet that we're editing</p>
</div>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>string</code><p>Insert/Delete before this cell address (e.g. 'A1')</p>
</div>
<div class="subelement argument">
<h4>$beforeColumnIndex</h4>
<code>integer</code><p>Index number of the column we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>integer</code><p>Number of columns to insert/delete (negative values indicate deletion)</p>
</div>
<div class="subelement argument">
<h4>$beforeRow</h4>
<code>integer</code><p>Number of the row we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>integer</code><p>Number of rows to insert/delete (negative values indicate deletion)</p>
</div>
</div></div>
</div>
<a id="method__adjustHyperlinks"></a><div class="element clickable method protected method__adjustHyperlinks" data-toggle="collapse" data-target=".method__adjustHyperlinks .collapse">
<h2>Update hyperlinks when inserting/deleting rows/columns</h2>
<pre>_adjustHyperlinks(<a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a> $pSheet, string $pBefore, integer $beforeColumnIndex, integer $pNumCols, integer $beforeRow, integer $pNumRows) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pSheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><p>The worksheet that we're editing</p>
</div>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>string</code><p>Insert/Delete before this cell address (e.g. 'A1')</p>
</div>
<div class="subelement argument">
<h4>$beforeColumnIndex</h4>
<code>integer</code><p>Index number of the column we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>integer</code><p>Number of columns to insert/delete (negative values indicate deletion)</p>
</div>
<div class="subelement argument">
<h4>$beforeRow</h4>
<code>integer</code><p>Number of the row we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>integer</code><p>Number of rows to insert/delete (negative values indicate deletion)</p>
</div>
</div></div>
</div>
<a id="method__adjustMergeCells"></a><div class="element clickable method protected method__adjustMergeCells" data-toggle="collapse" data-target=".method__adjustMergeCells .collapse">
<h2>Update merged cells when inserting/deleting rows/columns</h2>
<pre>_adjustMergeCells(<a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a> $pSheet, string $pBefore, integer $beforeColumnIndex, integer $pNumCols, integer $beforeRow, integer $pNumRows) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pSheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><p>The worksheet that we're editing</p>
</div>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>string</code><p>Insert/Delete before this cell address (e.g. 'A1')</p>
</div>
<div class="subelement argument">
<h4>$beforeColumnIndex</h4>
<code>integer</code><p>Index number of the column we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>integer</code><p>Number of columns to insert/delete (negative values indicate deletion)</p>
</div>
<div class="subelement argument">
<h4>$beforeRow</h4>
<code>integer</code><p>Number of the row we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>integer</code><p>Number of rows to insert/delete (negative values indicate deletion)</p>
</div>
</div></div>
</div>
<a id="method__adjustPageBreaks"></a><div class="element clickable method protected method__adjustPageBreaks" data-toggle="collapse" data-target=".method__adjustPageBreaks .collapse">
<h2>Update page breaks when inserting/deleting rows/columns</h2>
<pre>_adjustPageBreaks(\PHPExcel_Worksheet $pSheet, string $pBefore, integer $beforeColumnIndex, integer $pNumCols, integer $beforeRow, integer $pNumRows) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pSheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><p>The worksheet that we're editing</p>
</div>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>string</code><p>Insert/Delete before this cell address (e.g. 'A1')</p>
</div>
<div class="subelement argument">
<h4>$beforeColumnIndex</h4>
<code>integer</code><p>Index number of the column we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>integer</code><p>Number of columns to insert/delete (negative values indicate deletion)</p>
</div>
<div class="subelement argument">
<h4>$beforeRow</h4>
<code>integer</code><p>Number of the row we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>integer</code><p>Number of rows to insert/delete (negative values indicate deletion)</p>
</div>
</div></div>
</div>
<a id="method__adjustProtectedCells"></a><div class="element clickable method protected method__adjustProtectedCells" data-toggle="collapse" data-target=".method__adjustProtectedCells .collapse">
<h2>Update protected cells when inserting/deleting rows/columns</h2>
<pre>_adjustProtectedCells(<a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a> $pSheet, string $pBefore, integer $beforeColumnIndex, integer $pNumCols, integer $beforeRow, integer $pNumRows) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pSheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><p>The worksheet that we're editing</p>
</div>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>string</code><p>Insert/Delete before this cell address (e.g. 'A1')</p>
</div>
<div class="subelement argument">
<h4>$beforeColumnIndex</h4>
<code>integer</code><p>Index number of the column we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>integer</code><p>Number of columns to insert/delete (negative values indicate deletion)</p>
</div>
<div class="subelement argument">
<h4>$beforeRow</h4>
<code>integer</code><p>Number of the row we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>integer</code><p>Number of rows to insert/delete (negative values indicate deletion)</p>
</div>
</div></div>
</div>
<a id="method__adjustRowDimensions"></a><div class="element clickable method protected method__adjustRowDimensions" data-toggle="collapse" data-target=".method__adjustRowDimensions .collapse">
<h2>Update row dimensions when inserting/deleting rows/columns</h2>
<pre>_adjustRowDimensions(<a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a> $pSheet, string $pBefore, integer $beforeColumnIndex, integer $pNumCols, integer $beforeRow, integer $pNumRows) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pSheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><p>The worksheet that we're editing</p>
</div>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>string</code><p>Insert/Delete before this cell address (e.g. 'A1')</p>
</div>
<div class="subelement argument">
<h4>$beforeColumnIndex</h4>
<code>integer</code><p>Index number of the column we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>integer</code><p>Number of columns to insert/delete (negative values indicate deletion)</p>
</div>
<div class="subelement argument">
<h4>$beforeRow</h4>
<code>integer</code><p>Number of the row we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>integer</code><p>Number of rows to insert/delete (negative values indicate deletion)</p>
</div>
</div></div>
</div>
<a id="method__updateCellRange"></a><div class="element clickable method private method__updateCellRange" data-toggle="collapse" data-target=".method__updateCellRange .collapse">
<h2>Update cell range</h2>
<pre>_updateCellRange(string $pCellRange, int $pBefore, int $pNumCols, int $pNumRows) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pCellRange</h4>
<code>string</code><p>Cell range  (e.g. 'B2:D4', 'B:C' or '2:3')</p>
</div>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>int</code><p>Insert before this one</p></div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>int</code><p>Number of columns to increment</p></div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>int</code><p>Number of rows to increment</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Updated cell range</div>
</div></div>
</div>
<a id="method__updateSingleCellReference"></a><div class="element clickable method private method__updateSingleCellReference" data-toggle="collapse" data-target=".method__updateSingleCellReference .collapse">
<h2>Update single cell reference</h2>
<pre>_updateSingleCellReference(string $pCellReference, int $pBefore, int $pNumCols, int $pNumRows) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pCellReference</h4>
<code>string</code><p>Single cell reference</p></div>
<div class="subelement argument">
<h4>$pBefore</h4>
<code>int</code><p>Insert before this one</p></div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>int</code><p>Number of columns to increment</p></div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>int</code><p>Number of rows to increment</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Updated cell reference</div>
</div></div>
</div>
<a id="method_cellAddressInDeleteRange"></a><div class="element clickable method private method_cellAddressInDeleteRange" data-toggle="collapse" data-target=".method_cellAddressInDeleteRange .collapse">
<h2>Test whether a cell address falls within a defined range of cells</h2>
<pre>cellAddressInDeleteRange(string $cellAddress, integer $beforeRow, integer $pNumRows, integer $beforeColumnIndex, integer $pNumCols) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$cellAddress</h4>
<code>string</code><p>Address of the cell we're testing</p>
</div>
<div class="subelement argument">
<h4>$beforeRow</h4>
<code>integer</code><p>Number of the row we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumRows</h4>
<code>integer</code><p>Number of rows to insert/delete (negative values indicate deletion)</p>
</div>
<div class="subelement argument">
<h4>$beforeColumnIndex</h4>
<code>integer</code><p>Index number of the column we're inserting/deleting before</p>
</div>
<div class="subelement argument">
<h4>$pNumCols</h4>
<code>integer</code><p>Number of columns to insert/delete (negative values indicate deletion)</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__instance"> </a><div class="element clickable property private property__instance" data-toggle="collapse" data-target=".property__instance .collapse">
<h2></h2>
<pre>$_instance : <a href="../classes/PHPExcel_ReferenceHelper.html">\PHPExcel_ReferenceHelper</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_REFHELPER_REGEXP_CELLRANGE"> </a><div class="element clickable constant  constant_REFHELPER_REGEXP_CELLRANGE" data-toggle="collapse" data-target=".constant_REFHELPER_REGEXP_CELLRANGE .collapse">
<h2>REFHELPER_REGEXP_CELLRANGE</h2>
<pre>REFHELPER_REGEXP_CELLRANGE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_REFHELPER_REGEXP_CELLREF"> </a><div class="element clickable constant  constant_REFHELPER_REGEXP_CELLREF" data-toggle="collapse" data-target=".constant_REFHELPER_REGEXP_CELLREF .collapse">
<h2>Regular Expressions</h2>
<pre>REFHELPER_REGEXP_CELLREF </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_REFHELPER_REGEXP_COLRANGE"> </a><div class="element clickable constant  constant_REFHELPER_REGEXP_COLRANGE" data-toggle="collapse" data-target=".constant_REFHELPER_REGEXP_COLRANGE .collapse">
<h2>REFHELPER_REGEXP_COLRANGE</h2>
<pre>REFHELPER_REGEXP_COLRANGE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_REFHELPER_REGEXP_ROWRANGE"> </a><div class="element clickable constant  constant_REFHELPER_REGEXP_ROWRANGE" data-toggle="collapse" data-target=".constant_REFHELPER_REGEXP_ROWRANGE .collapse">
<h2>REFHELPER_REGEXP_ROWRANGE</h2>
<pre>REFHELPER_REGEXP_ROWRANGE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:36Z.<br></footer></div>
</div>
</body>
</html>
