<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \EigenvalueDecomposition</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___construct" title="__construct :: Constructor: Check for symmetry, then construct the eigenvalue decomposition"><span class="description">Constructor: Check for symmetry, then construct the eigenvalue decomposition</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_getD" title="getD :: Return the block diagonal eigenvalue matrix"><span class="description">Return the block diagonal eigenvalue matrix</span><pre>getD()</pre></a></li>
<li class="method public "><a href="#method_getImagEigenvalues" title="getImagEigenvalues :: Return the imaginary parts of the eigenvalues"><span class="description">Return the imaginary parts of the eigenvalues</span><pre>getImagEigenvalues()</pre></a></li>
<li class="method public "><a href="#method_getRealEigenvalues" title="getRealEigenvalues :: Return the real parts of the eigenvalues"><span class="description">Return the real parts of the eigenvalues</span><pre>getRealEigenvalues()</pre></a></li>
<li class="method public "><a href="#method_getV" title="getV :: Return the eigenvector matrix"><span class="description">Return the eigenvector matrix</span><pre>getV()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="method private "><a href="#method_cdiv" title="cdiv :: Performs complex division."><span class="description">Performs complex division.</span><pre>cdiv()</pre></a></li>
<li class="method private "><a href="#method_hqr2" title="hqr2 :: Nonsymmetric reduction from Hessenberg to real Schur form."><span class="description">Nonsymmetric reduction from Hessenberg to real Schur form.</span><pre>hqr2()</pre></a></li>
<li class="method private "><a href="#method_orthes" title="orthes :: Nonsymmetric reduction to Hessenberg form."><span class="description">Nonsymmetric reduction to Hessenberg form.</span><pre>orthes()</pre></a></li>
<li class="method private "><a href="#method_tql2" title="tql2 :: Symmetric tridiagonal QL algorithm."><span class="description">Symmetric tridiagonal QL algorithm.</span><pre>tql2()</pre></a></li>
<li class="method private "><a href="#method_tred2" title="tred2 :: Symmetric Householder reduction to tridiagonal form."><span class="description">Symmetric Householder reduction to tridiagonal form.</span><pre>tred2()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property_H" title="$H :: Array for internal storage of nonsymmetric Hessenberg form."><span class="description"></span><pre>$H</pre></a></li>
<li class="property private "><a href="#property_V" title="$V :: Array for internal storage of eigenvectors."><span class="description"></span><pre>$V</pre></a></li>
<li class="property private "><a href="#property_cdivi" title="$cdivi :: "><span class="description"></span><pre>$cdivi</pre></a></li>
<li class="property private "><a href="#property_cdivr" title="$cdivr :: Used for complex scalar division."><span class="description"></span><pre>$cdivr</pre></a></li>
<li class="property private "><a href="#property_d" title="$d :: Arrays for internal storage of eigenvalues."><span class="description"></span><pre>$d</pre></a></li>
<li class="property private "><a href="#property_e" title="$e :: "><span class="description"></span><pre>$e</pre></a></li>
<li class="property private "><a href="#property_issymmetric" title="$issymmetric :: Internal symmetry flag."><span class="description"></span><pre>$issymmetric</pre></a></li>
<li class="property private "><a href="#property_n" title="$n :: Row and column dimension (square matrix)."><span class="description"></span><pre>$n</pre></a></li>
<li class="property private "><a href="#property_ort" title="$ort :: Working storage for nonsymmetric algorithm."><span class="description"></span><pre>$ort</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\EigenvalueDecomposition"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/EigenvalueDecomposition.html">EigenvalueDecomposition</a>
</li>
</ul>
<div class="element class">
<p class="short_description"></p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>package</th>
<td><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.%0D%0AIf%20A%20is%20symmetric,%20then%20A%20=%20V*D*V'%20where%20the%20eigenvalue%20matrix%20D%0D%0Ais%20diagonal%20and%20the%20eigenvector%20matrix%20V%20is%20orthogonal%20(i.e.%0D%0AA%20=%20V.times(D.times(V.transpose()))%20and%20V.times(V.transpose())%0D%0Aequals%20the%20identity%20matrix).%0D%0AIf%20A%20is%20not%20symmetric,%20then%20the%20eigenvalue%20matrix%20D%20is%20block%20diagonal%0D%0Awith%20the%20real%20eigenvalues%20in%201-by-1%20blocks%20and%20any%20complex%20eigenvalues,%0D%0Alambda%20+%20i*mu,%20in%202-by-2%20blocks,%20%5Blambda,%20mu;%20-mu,%20lambda%5D.%20%20The%0D%0Acolumns%20of%20V%20represent%20the%20eigenvectors%20in%20the%20sense%20that%20A*V%20=%20V*D,%0D%0Ai.e.%20A.times(V)%20equals%20V.times(D).%20%20The%20matrix%20V%20may%20be%20badly%0D%0Aconditioned,%20or%20even%20singular,%20so%20the%20validity%20of%20the%20equation%0D%0AA%20=%20V*D*inverse(V)%20depends%20upon%20V.cond()..html">JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix.
If A is symmetric, then A = V*D*V' where the eigenvalue matrix D
is diagonal and the eigenvector matrix V is orthogonal (i.e.
A = V.times(D.times(V.transpose())) and V.times(V.transpose())
equals the identity matrix).
If A is not symmetric, then the eigenvalue matrix D is block diagonal
with the real eigenvalues in 1-by-1 blocks and any complex eigenvalues,
lambda + i*mu, in 2-by-2 blocks, [lambda, mu; -mu, lambda].  The
columns of V represent the eigenvectors in the sense that A*V = V*D,
i.e. A.times(V) equals V.times(D).  The matrix V may be badly
conditioned, or even singular, so the validity of the equation
A = V*D*inverse(V) depends upon V.cond().</a></td>
</tr>
<tr>
<th>author</th>
<td><a href="">Paul Meagher</a></td>
</tr>
<tr>
<th>license</th>
<td><a href="">PHP v3.0</a></td>
</tr>
<tr>
<th>version</th>
<td>1.1</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Constructor: Check for symmetry, then construct the eigenvalue decomposition</h2>
<pre>__construct(\A $Arg) : \Structure</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$Arg</h4>
<code>\A</code><p>Square matrix</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>\Structure</code>to access D and V.</div>
</div></div>
</div>
<a id="method_getD"></a><div class="element clickable method public method_getD" data-toggle="collapse" data-target=".method_getD .collapse">
<h2>Return the block diagonal eigenvalue matrix</h2>
<pre>getD() : \D</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>\D</code></div>
</div></div>
</div>
<a id="method_getImagEigenvalues"></a><div class="element clickable method public method_getImagEigenvalues" data-toggle="collapse" data-target=".method_getImagEigenvalues .collapse">
<h2>Return the imaginary parts of the eigenvalues</h2>
<pre>getImagEigenvalues() : \imag(diag(D))</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>\imag(diag(D))</code></div>
</div></div>
</div>
<a id="method_getRealEigenvalues"></a><div class="element clickable method public method_getRealEigenvalues" data-toggle="collapse" data-target=".method_getRealEigenvalues .collapse">
<h2>Return the real parts of the eigenvalues</h2>
<pre>getRealEigenvalues() : \real(diag(D))</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>\real(diag(D))</code></div>
</div></div>
</div>
<a id="method_getV"></a><div class="element clickable method public method_getV" data-toggle="collapse" data-target=".method_getV .collapse">
<h2>Return the eigenvector matrix</h2>
<pre>getV() : \V</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>\V</code></div>
</div></div>
</div>
<a id="method_cdiv"></a><div class="element clickable method private method_cdiv" data-toggle="collapse" data-target=".method_cdiv .collapse">
<h2>Performs complex division.</h2>
<pre>cdiv($xr, $xi, $yr, $yi) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>private</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$xr</h4></div>
<div class="subelement argument"><h4>$xi</h4></div>
<div class="subelement argument"><h4>$yr</h4></div>
<div class="subelement argument"><h4>$yi</h4></div>
</div></div>
</div>
<a id="method_hqr2"></a><div class="element clickable method private method_hqr2" data-toggle="collapse" data-target=".method_hqr2 .collapse">
<h2>Nonsymmetric reduction from Hessenberg to real Schur form.</h2>
<pre>hqr2() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Code is derived from the Algol procedure hqr2,
by Martin and Wilkinson, Handbook for Auto. Comp.,
Vol.ii-Linear Algebra, and the corresponding
Fortran subroutine in EISPACK.</p></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>private</td>
</tr></table>
</div></div>
</div>
<a id="method_orthes"></a><div class="element clickable method private method_orthes" data-toggle="collapse" data-target=".method_orthes .collapse">
<h2>Nonsymmetric reduction to Hessenberg form.</h2>
<pre>orthes() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>This is derived from the Algol procedures orthes and ortran,
by Martin and Wilkinson, Handbook for Auto. Comp.,
Vol.ii-Linear Algebra, and the corresponding
Fortran subroutines in EISPACK.</p></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>private</td>
</tr></table>
</div></div>
</div>
<a id="method_tql2"></a><div class="element clickable method private method_tql2" data-toggle="collapse" data-target=".method_tql2 .collapse">
<h2>Symmetric tridiagonal QL algorithm.</h2>
<pre>tql2() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>This is derived from the Algol procedures tql2, by
Bowdler, Martin, Reinsch, and Wilkinson, Handbook for
Auto. Comp., Vol.ii-Linear Algebra, and the corresponding
Fortran subroutine in EISPACK.</p></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>private</td>
</tr></table>
</div></div>
</div>
<a id="method_tred2"></a><div class="element clickable method private method_tred2" data-toggle="collapse" data-target=".method_tred2 .collapse">
<h2>Symmetric Householder reduction to tridiagonal form.</h2>
<pre>tred2() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>private</td>
</tr></table>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property_H"> </a><div class="element clickable property private property_H" data-toggle="collapse" data-target=".property_H .collapse">
<h2></h2>
<pre>$H : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_V"> </a><div class="element clickable property private property_V" data-toggle="collapse" data-target=".property_V .collapse">
<h2></h2>
<pre>$V : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_cdivi"> </a><div class="element clickable property private property_cdivi" data-toggle="collapse" data-target=".property_cdivi .collapse">
<h2></h2>
<pre>$cdivi </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_cdivr"> </a><div class="element clickable property private property_cdivr" data-toggle="collapse" data-target=".property_cdivr .collapse">
<h2></h2>
<pre>$cdivr : float</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_d"> </a><div class="element clickable property private property_d" data-toggle="collapse" data-target=".property_d .collapse">
<h2></h2>
<pre>$d : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_e"> </a><div class="element clickable property private property_e" data-toggle="collapse" data-target=".property_e .collapse">
<h2></h2>
<pre>$e </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_issymmetric"> </a><div class="element clickable property private property_issymmetric" data-toggle="collapse" data-target=".property_issymmetric .collapse">
<h2></h2>
<pre>$issymmetric : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_n"> </a><div class="element clickable property private property_n" data-toggle="collapse" data-target=".property_n .collapse">
<h2></h2>
<pre>$n : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_ort"> </a><div class="element clickable property private property_ort" data-toggle="collapse" data-target=".property_ort .collapse">
<h2></h2>
<pre>$ort : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:36Z.<br></footer></div>
</div>
</body>
</html>
