2025-03-01 - 휴무일
2025-03-02 - 휴무일
2025-03-03 - 휴무일(삼일절(대체휴일))
<br>
 date_NOW : 20250304<br>
 date_30D : 20250602<br>
 date_2M : 20250504<br>
 date_3M : 20250604<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250504' AND '20250604' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250305<br>
 date_30D : 20250603<br>
 date_2M : 20250505<br>
 date_3M : 20250605<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250505' AND '20250605' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250306<br>
 date_30D : 20250604<br>
 date_2M : 20250506<br>
 date_3M : 20250606<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250506' AND '20250606' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250307<br>
 date_30D : 20250605<br>
 date_2M : 20250507<br>
 date_3M : 20250607<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250507' AND '20250607' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-03-08 - 휴무일
2025-03-09 - 휴무일
<br>
 date_NOW : 20250310<br>
 date_30D : 20250608<br>
 date_2M : 20250510<br>
 date_3M : 20250610<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250510' AND '20250610' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250311<br>
 date_30D : 20250609<br>
 date_2M : 20250511<br>
 date_3M : 20250611<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250511' AND '20250611' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250312<br>
 date_30D : 20250610<br>
 date_2M : 20250512<br>
 date_3M : 20250612<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250512' AND '20250612' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250313<br>
 date_30D : 20250611<br>
 date_2M : 20250513<br>
 date_3M : 20250613<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250513' AND '20250613' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250314<br>
 date_30D : 20250612<br>
 date_2M : 20250514<br>
 date_3M : 20250614<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250514' AND '20250614' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-03-15 - 휴무일
2025-03-16 - 휴무일
<br>
 date_NOW : 20250317<br>
 date_30D : 20250615<br>
 date_2M : 20250517<br>
 date_3M : 20250617<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250517' AND '20250617' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250318<br>
 date_30D : 20250616<br>
 date_2M : 20250518<br>
 date_3M : 20250618<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250518' AND '20250618' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250319<br>
 date_30D : 20250617<br>
 date_2M : 20250519<br>
 date_3M : 20250619<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250519' AND '20250619' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250320<br>
 date_30D : 20250618<br>
 date_2M : 20250520<br>
 date_3M : 20250620<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250520' AND '20250620' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250321<br>
 date_30D : 20250619<br>
 date_2M : 20250521<br>
 date_3M : 20250621<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250521' AND '20250621' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-03-22 - 휴무일
2025-03-23 - 휴무일
<br>
 date_NOW : 20250324<br>
 date_30D : 20250622<br>
 date_2M : 20250524<br>
 date_3M : 20250624<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250524' AND '20250624' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250325<br>
 date_30D : 20250623<br>
 date_2M : 20250525<br>
 date_3M : 20250625<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250525' AND '20250625' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250326<br>
 date_30D : 20250624<br>
 date_2M : 20250526<br>
 date_3M : 20250626<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250526' AND '20250626' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250327<br>
 date_30D : 20250625<br>
 date_2M : 20250527<br>
 date_3M : 20250627<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250527' AND '20250627' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250328<br>
 date_30D : 20250626<br>
 date_2M : 20250528<br>
 date_3M : 20250628<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250528' AND '20250628' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-03-29 - 휴무일
2025-03-30 - 휴무일
<br>
 date_NOW : 20250331<br>
 date_30D : 20250629<br>
 date_2M : 20250531<br>
 date_3M : 20250701<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250531' AND '20250701' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
