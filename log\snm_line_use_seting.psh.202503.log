<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250301 | sEdate : 20250412<br>
<br>
 MAX_DAY  : 20250411<br>
 DIFF_DAY  : 42<br>
 20250301 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 이미 등록되어있음......<br>
 20250411 이미 등록되어있음......<br>
 20250411 이미 등록되어있음......<br>
 20250411 이미 등록되어있음......<br>
 20250411 이미 등록되어있음......<br>
 20250412 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250302 | sEdate : 20250413<br>
<br>
 MAX_DAY  : 20250411<br>
 DIFF_DAY  : 42<br>
 20250302 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 이미 등록되어있음......<br>
 20250411 이미 등록되어있음......<br>
 20250411 이미 등록되어있음......<br>
 20250411 이미 등록되어있음......<br>
 20250411 이미 등록되어있음......<br>
 20250412 주말일 경우 패스.....<br>
 20250413 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250303 | sEdate : 20250414<br>
<br>
 MAX_DAY  : 20250411<br>
 DIFF_DAY  : 42<br>
 20250303 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250411 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 이미 등록되어있음......<br>
 20250411 이미 등록되어있음......<br>
 20250411 이미 등록되어있음......<br>
 20250411 이미 등록되어있음......<br>
 20250411 이미 등록되어있음......<br>
 20250412 주말일 경우 패스.....<br>
 20250413 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250303001<br>
 입력 SLUS_ID  : 20250303002<br>
 입력 SLUS_ID  : 20250303003<br>
 입력 SLUS_ID  : 20250303004<br>
 입력 SLUS_ID  : 20250303005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250304 | sEdate : 20250415<br>
<br>
 MAX_DAY  : 20250414<br>
 DIFF_DAY  : 42<br>
 20250304 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250414 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 이미 등록되어있음......<br>
 20250414 이미 등록되어있음......<br>
 20250414 이미 등록되어있음......<br>
 20250414 이미 등록되어있음......<br>
 20250414 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250304001<br>
 입력 SLUS_ID  : 20250304002<br>
 입력 SLUS_ID  : 20250304003<br>
 입력 SLUS_ID  : 20250304004<br>
 입력 SLUS_ID  : 20250304005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250305 | sEdate : 20250416<br>
<br>
 MAX_DAY  : 20250415<br>
 DIFF_DAY  : 42<br>
 20250305 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250415 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 이미 등록되어있음......<br>
 20250415 이미 등록되어있음......<br>
 20250415 이미 등록되어있음......<br>
 20250415 이미 등록되어있음......<br>
 20250415 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250305001<br>
 입력 SLUS_ID  : 20250305002<br>
 입력 SLUS_ID  : 20250305003<br>
 입력 SLUS_ID  : 20250305004<br>
 입력 SLUS_ID  : 20250305005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250306 | sEdate : 20250417<br>
<br>
 MAX_DAY  : 20250416<br>
 DIFF_DAY  : 42<br>
 20250306 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250416 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 이미 등록되어있음......<br>
 20250416 이미 등록되어있음......<br>
 20250416 이미 등록되어있음......<br>
 20250416 이미 등록되어있음......<br>
 20250416 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250306001<br>
 입력 SLUS_ID  : 20250306002<br>
 입력 SLUS_ID  : 20250306003<br>
 입력 SLUS_ID  : 20250306004<br>
 입력 SLUS_ID  : 20250306005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250307 | sEdate : 20250418<br>
<br>
 MAX_DAY  : 20250417<br>
 DIFF_DAY  : 42<br>
 20250307 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250417 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 이미 등록되어있음......<br>
 20250417 이미 등록되어있음......<br>
 20250417 이미 등록되어있음......<br>
 20250417 이미 등록되어있음......<br>
 20250417 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250307001<br>
 입력 SLUS_ID  : 20250307002<br>
 입력 SLUS_ID  : 20250307003<br>
 입력 SLUS_ID  : 20250307004<br>
 입력 SLUS_ID  : 20250307005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250308 | sEdate : 20250419<br>
<br>
 MAX_DAY  : 20250418<br>
 DIFF_DAY  : 42<br>
 20250308 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 이미 등록되어있음......<br>
 20250418 이미 등록되어있음......<br>
 20250418 이미 등록되어있음......<br>
 20250418 이미 등록되어있음......<br>
 20250418 이미 등록되어있음......<br>
 20250419 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250309 | sEdate : 20250420<br>
<br>
 MAX_DAY  : 20250418<br>
 DIFF_DAY  : 42<br>
 20250309 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 이미 등록되어있음......<br>
 20250418 이미 등록되어있음......<br>
 20250418 이미 등록되어있음......<br>
 20250418 이미 등록되어있음......<br>
 20250418 이미 등록되어있음......<br>
 20250419 주말일 경우 패스.....<br>
 20250420 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250310 | sEdate : 20250421<br>
<br>
 MAX_DAY  : 20250418<br>
 DIFF_DAY  : 42<br>
 20250310 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250418 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 이미 등록되어있음......<br>
 20250418 이미 등록되어있음......<br>
 20250418 이미 등록되어있음......<br>
 20250418 이미 등록되어있음......<br>
 20250418 이미 등록되어있음......<br>
 20250419 주말일 경우 패스.....<br>
 20250420 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250310001<br>
 입력 SLUS_ID  : 20250310002<br>
 입력 SLUS_ID  : 20250310003<br>
 입력 SLUS_ID  : 20250310004<br>
 입력 SLUS_ID  : 20250310005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250311 | sEdate : 20250422<br>
<br>
 MAX_DAY  : 20250421<br>
 DIFF_DAY  : 42<br>
 20250311 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250421 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 이미 등록되어있음......<br>
 20250421 이미 등록되어있음......<br>
 20250421 이미 등록되어있음......<br>
 20250421 이미 등록되어있음......<br>
 20250421 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250311001<br>
 입력 SLUS_ID  : 20250311002<br>
 입력 SLUS_ID  : 20250311003<br>
 입력 SLUS_ID  : 20250311004<br>
 입력 SLUS_ID  : 20250311005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250312 | sEdate : 20250423<br>
<br>
 MAX_DAY  : 20250422<br>
 DIFF_DAY  : 42<br>
 20250312 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250422 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 이미 등록되어있음......<br>
 20250422 이미 등록되어있음......<br>
 20250422 이미 등록되어있음......<br>
 20250422 이미 등록되어있음......<br>
 20250422 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250312001<br>
 입력 SLUS_ID  : 20250312002<br>
 입력 SLUS_ID  : 20250312003<br>
 입력 SLUS_ID  : 20250312004<br>
 입력 SLUS_ID  : 20250312005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250313 | sEdate : 20250424<br>
<br>
 MAX_DAY  : 20250423<br>
 DIFF_DAY  : 42<br>
 20250313 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250423 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 이미 등록되어있음......<br>
 20250423 이미 등록되어있음......<br>
 20250423 이미 등록되어있음......<br>
 20250423 이미 등록되어있음......<br>
 20250423 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250313001<br>
 입력 SLUS_ID  : 20250313002<br>
 입력 SLUS_ID  : 20250313003<br>
 입력 SLUS_ID  : 20250313004<br>
 입력 SLUS_ID  : 20250313005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250314 | sEdate : 20250425<br>
<br>
 MAX_DAY  : 20250424<br>
 DIFF_DAY  : 42<br>
 20250314 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250424 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 이미 등록되어있음......<br>
 20250424 이미 등록되어있음......<br>
 20250424 이미 등록되어있음......<br>
 20250424 이미 등록되어있음......<br>
 20250424 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250314001<br>
 입력 SLUS_ID  : 20250314002<br>
 입력 SLUS_ID  : 20250314003<br>
 입력 SLUS_ID  : 20250314004<br>
 입력 SLUS_ID  : 20250314005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250315 | sEdate : 20250426<br>
<br>
 MAX_DAY  : 20250425<br>
 DIFF_DAY  : 42<br>
 20250315 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 이미 등록되어있음......<br>
 20250425 이미 등록되어있음......<br>
 20250425 이미 등록되어있음......<br>
 20250425 이미 등록되어있음......<br>
 20250425 이미 등록되어있음......<br>
 20250426 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250316 | sEdate : 20250427<br>
<br>
 MAX_DAY  : 20250425<br>
 DIFF_DAY  : 42<br>
 20250316 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 이미 등록되어있음......<br>
 20250425 이미 등록되어있음......<br>
 20250425 이미 등록되어있음......<br>
 20250425 이미 등록되어있음......<br>
 20250425 이미 등록되어있음......<br>
 20250426 주말일 경우 패스.....<br>
 20250427 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250317 | sEdate : 20250428<br>
<br>
 MAX_DAY  : 20250425<br>
 DIFF_DAY  : 42<br>
 20250317 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250425 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 이미 등록되어있음......<br>
 20250425 이미 등록되어있음......<br>
 20250425 이미 등록되어있음......<br>
 20250425 이미 등록되어있음......<br>
 20250425 이미 등록되어있음......<br>
 20250426 주말일 경우 패스.....<br>
 20250427 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250317001<br>
 입력 SLUS_ID  : 20250317002<br>
 입력 SLUS_ID  : 20250317003<br>
 입력 SLUS_ID  : 20250317004<br>
 입력 SLUS_ID  : 20250317005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250318 | sEdate : 20250429<br>
<br>
 MAX_DAY  : 20250428<br>
 DIFF_DAY  : 42<br>
 20250318 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250428 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 이미 등록되어있음......<br>
 20250428 이미 등록되어있음......<br>
 20250428 이미 등록되어있음......<br>
 20250428 이미 등록되어있음......<br>
 20250428 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250318001<br>
 입력 SLUS_ID  : 20250318002<br>
 입력 SLUS_ID  : 20250318003<br>
 입력 SLUS_ID  : 20250318004<br>
 입력 SLUS_ID  : 20250318005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250319 | sEdate : 20250430<br>
<br>
 MAX_DAY  : 20250429<br>
 DIFF_DAY  : 42<br>
 20250319 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250429 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 이미 등록되어있음......<br>
 20250429 이미 등록되어있음......<br>
 20250429 이미 등록되어있음......<br>
 20250429 이미 등록되어있음......<br>
 20250429 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250319001<br>
 입력 SLUS_ID  : 20250319002<br>
 입력 SLUS_ID  : 20250319003<br>
 입력 SLUS_ID  : 20250319004<br>
 입력 SLUS_ID  : 20250319005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250320 | sEdate : 20250501<br>
<br>
 MAX_DAY  : 20250430<br>
 DIFF_DAY  : 42<br>
 20250320 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 이미 등록되어있음......<br>
 20250430 이미 등록되어있음......<br>
 20250430 이미 등록되어있음......<br>
 20250430 이미 등록되어있음......<br>
 20250430 이미 등록되어있음......<br>
 근로자의날 휴무일인 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250321 | sEdate : 20250502<br>
<br>
 MAX_DAY  : 20250430<br>
 DIFF_DAY  : 42<br>
 20250321 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250430 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 이미 등록되어있음......<br>
 20250430 이미 등록되어있음......<br>
 20250430 이미 등록되어있음......<br>
 20250430 이미 등록되어있음......<br>
 20250430 이미 등록되어있음......<br>
 근로자의날 휴무일인 경우 패스.....<br>
 입력 SLUS_ID  : 20250321001<br>
 입력 SLUS_ID  : 20250321002<br>
 입력 SLUS_ID  : 20250321003<br>
 입력 SLUS_ID  : 20250321004<br>
 입력 SLUS_ID  : 20250321005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250322 | sEdate : 20250503<br>
<br>
 MAX_DAY  : 20250502<br>
 DIFF_DAY  : 42<br>
 20250322 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250503 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250323 | sEdate : 20250504<br>
<br>
 MAX_DAY  : 20250502<br>
 DIFF_DAY  : 42<br>
 20250323 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250503 주말일 경우 패스.....<br>
 20250504 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250324 | sEdate : 20250505<br>
<br>
 MAX_DAY  : 20250502<br>
 DIFF_DAY  : 42<br>
 20250324 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250503 주말일 경우 패스.....<br>
 20250504 주말일 경우 패스.....<br>
 어린이날/석가탄신일 휴무일인 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250325 | sEdate : 20250506<br>
<br>
 MAX_DAY  : 20250502<br>
 DIFF_DAY  : 42<br>
 20250325 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250503 주말일 경우 패스.....<br>
 20250504 주말일 경우 패스.....<br>
 어린이날/석가탄신일 휴무일인 경우 패스.....<br>
 (대체휴일) 휴무일인 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250326 | sEdate : 20250507<br>
<br>
 MAX_DAY  : 20250502<br>
 DIFF_DAY  : 42<br>
 20250326 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250502 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250502 이미 등록되어있음......<br>
 20250503 주말일 경우 패스.....<br>
 20250504 주말일 경우 패스.....<br>
 어린이날/석가탄신일 휴무일인 경우 패스.....<br>
 (대체휴일) 휴무일인 경우 패스.....<br>
 입력 SLUS_ID  : 20250326001<br>
 입력 SLUS_ID  : 20250326002<br>
 입력 SLUS_ID  : 20250326003<br>
 입력 SLUS_ID  : 20250326004<br>
 입력 SLUS_ID  : 20250326005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250327 | sEdate : 20250508<br>
<br>
 MAX_DAY  : 20250507<br>
 DIFF_DAY  : 42<br>
 20250327 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250507 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 이미 등록되어있음......<br>
 20250507 이미 등록되어있음......<br>
 20250507 이미 등록되어있음......<br>
 20250507 이미 등록되어있음......<br>
 20250507 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250327001<br>
 입력 SLUS_ID  : 20250327002<br>
 입력 SLUS_ID  : 20250327003<br>
 입력 SLUS_ID  : 20250327004<br>
 입력 SLUS_ID  : 20250327005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250328 | sEdate : 20250509<br>
<br>
 MAX_DAY  : 20250508<br>
 DIFF_DAY  : 42<br>
 20250328 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250508 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 이미 등록되어있음......<br>
 20250508 이미 등록되어있음......<br>
 20250508 이미 등록되어있음......<br>
 20250508 이미 등록되어있음......<br>
 20250508 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250328001<br>
 입력 SLUS_ID  : 20250328002<br>
 입력 SLUS_ID  : 20250328003<br>
 입력 SLUS_ID  : 20250328004<br>
 입력 SLUS_ID  : 20250328005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250329 | sEdate : 20250510<br>
<br>
 MAX_DAY  : 20250509<br>
 DIFF_DAY  : 42<br>
 20250329 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 이미 등록되어있음......<br>
 20250509 이미 등록되어있음......<br>
 20250509 이미 등록되어있음......<br>
 20250509 이미 등록되어있음......<br>
 20250509 이미 등록되어있음......<br>
 20250510 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250330 | sEdate : 20250511<br>
<br>
 MAX_DAY  : 20250509<br>
 DIFF_DAY  : 42<br>
 20250330 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 이미 등록되어있음......<br>
 20250509 이미 등록되어있음......<br>
 20250509 이미 등록되어있음......<br>
 20250509 이미 등록되어있음......<br>
 20250509 이미 등록되어있음......<br>
 20250510 주말일 경우 패스.....<br>
 20250511 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250331 | sEdate : 20250512<br>
<br>
 MAX_DAY  : 20250509<br>
 DIFF_DAY  : 42<br>
 20250331 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250509 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 이미 등록되어있음......<br>
 20250509 이미 등록되어있음......<br>
 20250509 이미 등록되어있음......<br>
 20250509 이미 등록되어있음......<br>
 20250509 이미 등록되어있음......<br>
 20250510 주말일 경우 패스.....<br>
 20250511 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250331001<br>
 입력 SLUS_ID  : 20250331002<br>
 입력 SLUS_ID  : 20250331003<br>
 입력 SLUS_ID  : 20250331004<br>
 입력 SLUS_ID  : 20250331005