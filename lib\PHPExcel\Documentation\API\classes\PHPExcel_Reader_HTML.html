<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Reader_HTML</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_Reader_HTML"><span class="description">Create a new PHPExcel_Reader_HTML</span><pre>__construct()</pre></a></li>
<li class="method public inherited"><a href="#method_canRead" title="canRead :: Can the current PHPExcel_Reader_IReader read the file?"><span class="description">Can the current PHPExcel_Reader_IReader read the file?</span><pre>canRead()</pre></a></li>
<li class="method public inherited"><a href="#method_getIncludeCharts" title="getIncludeCharts :: Read charts in workbook?
	If this is true, then the Reader will include any charts that exist in the workbook."><span class="description">Read charts in workbook?
	If this is true, then the Reader will include any charts that exist in the workbook.</span><pre>getIncludeCharts()</pre></a></li>
<li class="method public "><a href="#method_getInputEncoding" title="getInputEncoding :: Get input encoding"><span class="description">Get input encoding</span><pre>getInputEncoding()</pre></a></li>
<li class="method public inherited"><a href="#method_getLoadSheetsOnly" title="getLoadSheetsOnly :: Get which sheets to load
Returns either an array of worksheet names (the list of worksheets that should be loaded), or a null
	indicating that all worksheets in the workbook should be loaded."><span class="description">Get which sheets to load
Returns either an array of worksheet names (the list of worksheets that should be loaded), or a null
	indicating that all worksheets in the workbook should be loaded.</span><pre>getLoadSheetsOnly()</pre></a></li>
<li class="method public inherited"><a href="#method_getReadDataOnly" title="getReadDataOnly :: Read data only?
	If this is true, then the Reader will only read data values for cells, it will not read any formatting information."><span class="description">Read data only?
	If this is true, then the Reader will only read data values for cells, it will not read any formatting information.</span><pre>getReadDataOnly()</pre></a></li>
<li class="method public inherited"><a href="#method_getReadFilter" title="getReadFilter :: Read filter"><span class="description">Read filter</span><pre>getReadFilter()</pre></a></li>
<li class="method public "><a href="#method_getSheetIndex" title="getSheetIndex :: Get sheet index"><span class="description">Get sheet index</span><pre>getSheetIndex()</pre></a></li>
<li class="method public "><a href="#method_load" title="load :: Loads PHPExcel from file"><span class="description">Loads PHPExcel from file</span><pre>load()</pre></a></li>
<li class="method public "><a href="#method_loadIntoExisting" title="loadIntoExisting :: Loads PHPExcel from file into PHPExcel instance"><span class="description">Loads PHPExcel from file into PHPExcel instance</span><pre>loadIntoExisting()</pre></a></li>
<li class="method public inherited"><a href="#method_setIncludeCharts" title="setIncludeCharts :: Set read charts in workbook
	Set to true, to advise the Reader to include any charts that exist in the workbook."><span class="description">Set read charts in workbook
	Set to true, to advise the Reader to include any charts that exist in the workbook.</span><pre>setIncludeCharts()</pre></a></li>
<li class="method public "><a href="#method_setInputEncoding" title="setInputEncoding :: Set input encoding"><span class="description">Set input encoding</span><pre>setInputEncoding()</pre></a></li>
<li class="method public inherited"><a href="#method_setLoadAllSheets" title="setLoadAllSheets :: Set all sheets to load
	Tells the Reader to load all worksheets from the workbook."><span class="description">Set all sheets to load
	Tells the Reader to load all worksheets from the workbook.</span><pre>setLoadAllSheets()</pre></a></li>
<li class="method public inherited"><a href="#method_setLoadSheetsOnly" title="setLoadSheetsOnly :: Set which sheets to load"><span class="description">Set which sheets to load</span><pre>setLoadSheetsOnly()</pre></a></li>
<li class="method public inherited"><a href="#method_setReadDataOnly" title="setReadDataOnly :: Set read data only
	Set to true, to advise the Reader only to read data values for cells, and to ignore any formatting information."><span class="description">Set read data only
	Set to true, to advise the Reader only to read data values for cells, and to ignore any formatting information.</span><pre>setReadDataOnly()</pre></a></li>
<li class="method public inherited"><a href="#method_setReadFilter" title="setReadFilter :: Set read filter"><span class="description">Set read filter</span><pre>setReadFilter()</pre></a></li>
<li class="method public "><a href="#method_setSheetIndex" title="setSheetIndex :: Set sheet index"><span class="description">Set sheet index</span><pre>setSheetIndex()</pre></a></li>
</ul>
</li>
<li class="nav-header protected">» Protected
                    <ul>
<li class="method protected "><a href="#method__isValidFormat" title="_isValidFormat :: Validate that the current file is an HTML file"><span class="description">Validate that the current file is an HTML file</span><pre>_isValidFormat()</pre></a></li>
<li class="method protected inherited"><a href="#method__openFile" title="_openFile :: Open file for reading"><span class="description">Open file for reading</span><pre>_openFile()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="method private "><a href="#method__flushCell" title="_flushCell :: "><span class="description">_flushCell()
        </span><pre>_flushCell()</pre></a></li>
<li class="method private "><a href="#method__getTableStartColumn" title="_getTableStartColumn :: "><span class="description">_getTableStartColumn()
        </span><pre>_getTableStartColumn()</pre></a></li>
<li class="method private "><a href="#method__processDomElement" title="_processDomElement :: "><span class="description">_processDomElement()
        </span><pre>_processDomElement()</pre></a></li>
<li class="method private "><a href="#method__releaseTableStartColumn" title="_releaseTableStartColumn :: "><span class="description">_releaseTableStartColumn()
        </span><pre>_releaseTableStartColumn()</pre></a></li>
<li class="method private "><a href="#method__setTableStartColumn" title="_setTableStartColumn :: "><span class="description">_setTableStartColumn()
        </span><pre>_setTableStartColumn()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header protected">» Protected
                    <ul>
<li class="property protected inherited"><a href="#property__fileHandle" title="$_fileHandle :: "><span class="description"></span><pre>$_fileHandle</pre></a></li>
<li class="property protected inherited"><a href="#property__includeCharts" title="$_includeCharts :: Read charts that are defined in the workbook?
Identifies whether the Reader should read the definitions for any charts that exist in the workbook;"><span class="description"></span><pre>$_includeCharts</pre></a></li>
<li class="property protected inherited"><a href="#property__loadSheetsOnly" title="$_loadSheetsOnly :: Restrict which sheets should be loaded?
This property holds an array of worksheet names to be loaded."><span class="description"></span><pre>$_loadSheetsOnly</pre></a></li>
<li class="property protected inherited"><a href="#property__readDataOnly" title="$_readDataOnly :: Read data only?
Identifies whether the Reader should only read data values for cells, and ignore any formatting information;
	or whether it should read both data and formatting"><span class="description"></span><pre>$_readDataOnly</pre></a></li>
<li class="property protected inherited"><a href="#property__readFilter" title="$_readFilter :: PHPExcel_Reader_IReadFilter instance"><span class="description"></span><pre>$_readFilter</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__dataArray" title="$_dataArray :: "><span class="description"></span><pre>$_dataArray</pre></a></li>
<li class="property private "><a href="#property__formats" title="$_formats :: Formats"><span class="description"></span><pre>$_formats</pre></a></li>
<li class="property private "><a href="#property__inputEncoding" title="$_inputEncoding :: Input encoding"><span class="description"></span><pre>$_inputEncoding</pre></a></li>
<li class="property private "><a href="#property__nestedColumn" title="$_nestedColumn :: "><span class="description"></span><pre>$_nestedColumn</pre></a></li>
<li class="property private "><a href="#property__sheetIndex" title="$_sheetIndex :: Sheet index to read"><span class="description"></span><pre>$_sheetIndex</pre></a></li>
<li class="property private "><a href="#property__tableLevel" title="$_tableLevel :: "><span class="description"></span><pre>$_tableLevel</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Reader_HTML"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Reader_HTML.html">PHPExcel_Reader_HTML</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Reader_HTML</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Reader.html">PHPExcel_Reader</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_Reader_HTML</h2>
<pre>__construct() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_canRead"></a><div class="element clickable method public method_canRead" data-toggle="collapse" data-target=".method_canRead .collapse">
<h2>Can the current PHPExcel_Reader_IReader read the file?</h2>
<pre>canRead(string $pFilename) : boolean</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_IReader::canRead()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pFilename</h4>
<code>string</code>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Reader_Exception.html">\PHPExcel_Reader_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getIncludeCharts"></a><div class="element clickable method public method_getIncludeCharts" data-toggle="collapse" data-target=".method_getIncludeCharts .collapse">
<h2>Read charts in workbook?
	If this is true, then the Reader will include any charts that exist in the workbook.</h2>
<pre>getIncludeCharts() : boolean</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Note that a ReadDataOnly value of false overrides, and charts won't be read regardless of the IncludeCharts value.
    If false (the default) it will ignore any charts defined in the workbook file.</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::getIncludeCharts()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getInputEncoding"></a><div class="element clickable method public method_getInputEncoding" data-toggle="collapse" data-target=".method_getInputEncoding .collapse">
<h2>Get input encoding</h2>
<pre>getInputEncoding() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getLoadSheetsOnly"></a><div class="element clickable method public method_getLoadSheetsOnly" data-toggle="collapse" data-target=".method_getLoadSheetsOnly .collapse">
<h2>Get which sheets to load
Returns either an array of worksheet names (the list of worksheets that should be loaded), or a null
	indicating that all worksheets in the workbook should be loaded.</h2>
<pre>getLoadSheetsOnly() : mixed</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::getLoadSheetsOnly()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>mixed</code></div>
</div></div>
</div>
<a id="method_getReadDataOnly"></a><div class="element clickable method public method_getReadDataOnly" data-toggle="collapse" data-target=".method_getReadDataOnly .collapse">
<h2>Read data only?
	If this is true, then the Reader will only read data values for cells, it will not read any formatting information.</h2>
<pre>getReadDataOnly() : boolean</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>If false (the default) it will read data and formatting.</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::getReadDataOnly()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getReadFilter"></a><div class="element clickable method public method_getReadFilter" data-toggle="collapse" data-target=".method_getReadFilter .collapse">
<h2>Read filter</h2>
<pre>getReadFilter() : <a href="../classes/PHPExcel_Reader_IReadFilter.html">\PHPExcel_Reader_IReadFilter</a></pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::getReadFilter()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Reader_IReadFilter.html">\PHPExcel_Reader_IReadFilter</a></code></div>
</div></div>
</div>
<a id="method_getSheetIndex"></a><div class="element clickable method public method_getSheetIndex" data-toggle="collapse" data-target=".method_getSheetIndex .collapse">
<h2>Get sheet index</h2>
<pre>getSheetIndex() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_load"></a><div class="element clickable method public method_load" data-toggle="collapse" data-target=".method_load .collapse">
<h2>Loads PHPExcel from file</h2>
<pre>load(string $pFilename) : <a href="../classes/PHPExcel.html">\PHPExcel</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pFilename</h4>
<code>string</code>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Reader_Exception.html">\PHPExcel_Reader_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel.html">\PHPExcel</a></code></div>
</div></div>
</div>
<a id="method_loadIntoExisting"></a><div class="element clickable method public method_loadIntoExisting" data-toggle="collapse" data-target=".method_loadIntoExisting .collapse">
<h2>Loads PHPExcel from file into PHPExcel instance</h2>
<pre>loadIntoExisting(string $pFilename, \PHPExcel $objPHPExcel) : <a href="../classes/PHPExcel.html">\PHPExcel</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pFilename</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$objPHPExcel</h4>
<code><a href="../classes/PHPExcel.html">\PHPExcel</a></code>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Reader_Exception.html">\PHPExcel_Reader_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel.html">\PHPExcel</a></code></div>
</div></div>
</div>
<a id="method_setIncludeCharts"></a><div class="element clickable method public method_setIncludeCharts" data-toggle="collapse" data-target=".method_setIncludeCharts .collapse">
<h2>Set read charts in workbook
	Set to true, to advise the Reader to include any charts that exist in the workbook.</h2>
<pre>setIncludeCharts(boolean $pValue) : <a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Note that a ReadDataOnly value of false overrides, and charts won't be read regardless of the IncludeCharts value.
    Set to false (the default) to discard charts.</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::setIncludeCharts()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></code></div>
</div></div>
</div>
<a id="method_setInputEncoding"></a><div class="element clickable method public method_setInputEncoding" data-toggle="collapse" data-target=".method_setInputEncoding .collapse">
<h2>Set input encoding</h2>
<pre>setInputEncoding(string $pValue) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>Input encoding</p></div>
</div></div>
</div>
<a id="method_setLoadAllSheets"></a><div class="element clickable method public method_setLoadAllSheets" data-toggle="collapse" data-target=".method_setLoadAllSheets .collapse">
<h2>Set all sheets to load
	Tells the Reader to load all worksheets from the workbook.</h2>
<pre>setLoadAllSheets() : <a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::setLoadAllSheets()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></code></div>
</div></div>
</div>
<a id="method_setLoadSheetsOnly"></a><div class="element clickable method public method_setLoadSheetsOnly" data-toggle="collapse" data-target=".method_setLoadSheetsOnly .collapse">
<h2>Set which sheets to load</h2>
<pre>setLoadSheetsOnly(mixed $value) : <a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::setLoadSheetsOnly()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>This should be either an array of worksheet names to be loaded, or a string containing a single worksheet name.
	If NULL, then it tells the Reader to read all worksheets in the workbook</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></code></div>
</div></div>
</div>
<a id="method_setReadDataOnly"></a><div class="element clickable method public method_setReadDataOnly" data-toggle="collapse" data-target=".method_setReadDataOnly .collapse">
<h2>Set read data only
	Set to true, to advise the Reader only to read data values for cells, and to ignore any formatting information.</h2>
<pre>setReadDataOnly(boolean $pValue) : <a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Set to false (the default) to advise the Reader to read both data and formatting for cells.</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::setReadDataOnly()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></code></div>
</div></div>
</div>
<a id="method_setReadFilter"></a><div class="element clickable method public method_setReadFilter" data-toggle="collapse" data-target=".method_setReadFilter .collapse">
<h2>Set read filter</h2>
<pre>setReadFilter(\PHPExcel_Reader_IReadFilter $pValue) : <a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::setReadFilter()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code><a href="../classes/PHPExcel_Reader_IReadFilter.html">\PHPExcel_Reader_IReadFilter</a></code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></code></div>
</div></div>
</div>
<a id="method_setSheetIndex"></a><div class="element clickable method public method_setSheetIndex" data-toggle="collapse" data-target=".method_setSheetIndex .collapse">
<h2>Set sheet index</h2>
<pre>setSheetIndex(int $pValue) : <a href="../classes/PHPExcel_Reader_HTML.html">\PHPExcel_Reader_HTML</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>int</code><p>Sheet index</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Reader_HTML.html">\PHPExcel_Reader_HTML</a></code></div>
</div></div>
</div>
<a id="method__isValidFormat"></a><div class="element clickable method protected method__isValidFormat" data-toggle="collapse" data-target=".method__isValidFormat .collapse">
<h2>Validate that the current file is an HTML file</h2>
<pre>_isValidFormat() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method__openFile"></a><div class="element clickable method protected method__openFile" data-toggle="collapse" data-target=".method__openFile .collapse">
<h2>Open file for reading</h2>
<pre>_openFile(string $pFilename) : resource</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::_openFile()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pFilename</h4>
<code>string</code>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Reader_Exception.html">\PHPExcel_Reader_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>resource</code></div>
</div></div>
</div>
<a id="method__flushCell"></a><div class="element clickable method private method__flushCell" data-toggle="collapse" data-target=".method__flushCell .collapse">
<h2>_flushCell()
        </h2>
<pre>_flushCell($sheet, $column, $row, $cellContent) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$sheet</h4></div>
<div class="subelement argument"><h4>$column</h4></div>
<div class="subelement argument"><h4>$row</h4></div>
<div class="subelement argument"><h4>$cellContent</h4></div>
</div></div>
</div>
<a id="method__getTableStartColumn"></a><div class="element clickable method private method__getTableStartColumn" data-toggle="collapse" data-target=".method__getTableStartColumn .collapse">
<h2>_getTableStartColumn()
        </h2>
<pre>_getTableStartColumn() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__processDomElement"></a><div class="element clickable method private method__processDomElement" data-toggle="collapse" data-target=".method__processDomElement .collapse">
<h2>_processDomElement()
        </h2>
<pre>_processDomElement(\DOMNode $element, $sheet, $row, $column, $cellContent) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$element</h4></div>
<div class="subelement argument"><h4>$sheet</h4></div>
<div class="subelement argument"><h4>$row</h4></div>
<div class="subelement argument"><h4>$column</h4></div>
<div class="subelement argument"><h4>$cellContent</h4></div>
</div></div>
</div>
<a id="method__releaseTableStartColumn"></a><div class="element clickable method private method__releaseTableStartColumn" data-toggle="collapse" data-target=".method__releaseTableStartColumn .collapse">
<h2>_releaseTableStartColumn()
        </h2>
<pre>_releaseTableStartColumn() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__setTableStartColumn"></a><div class="element clickable method private method__setTableStartColumn" data-toggle="collapse" data-target=".method__setTableStartColumn .collapse">
<h2>_setTableStartColumn()
        </h2>
<pre>_setTableStartColumn($column) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$column</h4></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__fileHandle"> </a><div class="element clickable property protected property__fileHandle" data-toggle="collapse" data-target=".property__fileHandle .collapse">
<h2></h2>
<pre>$_fileHandle </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::$$_fileHandle</td>
</tr></table>
</div></div>
</div>
<a id="property__includeCharts"> </a><div class="element clickable property protected property__includeCharts" data-toggle="collapse" data-target=".property__includeCharts .collapse">
<h2></h2>
<pre>$_includeCharts : boolean</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::$$_includeCharts</td>
</tr></table>
</div></div>
</div>
<a id="property__loadSheetsOnly"> </a><div class="element clickable property protected property__loadSheetsOnly" data-toggle="collapse" data-target=".property__loadSheetsOnly .collapse">
<h2></h2>
<pre>$_loadSheetsOnly : array</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>If null, then all worksheets will be loaded.</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::$$_loadSheetsOnly</td>
</tr></table>
</div></div>
</div>
<a id="property__readDataOnly"> </a><div class="element clickable property protected property__readDataOnly" data-toggle="collapse" data-target=".property__readDataOnly .collapse">
<h2></h2>
<pre>$_readDataOnly : boolean</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::$$_readDataOnly</td>
</tr></table>
</div></div>
</div>
<a id="property__readFilter"> </a><div class="element clickable property protected property__readFilter" data-toggle="collapse" data-target=".property__readFilter .collapse">
<h2></h2>
<pre>$_readFilter : <a href="../classes/PHPExcel_Reader_IReadFilter.html">\PHPExcel_Reader_IReadFilter</a></pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Reader_Abstract::$$_readFilter</td>
</tr></table>
</div></div>
</div>
<a id="property__dataArray"> </a><div class="element clickable property private property__dataArray" data-toggle="collapse" data-target=".property__dataArray .collapse">
<h2></h2>
<pre>$_dataArray </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__formats"> </a><div class="element clickable property private property__formats" data-toggle="collapse" data-target=".property__formats .collapse">
<h2></h2>
<pre>$_formats : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__inputEncoding"> </a><div class="element clickable property private property__inputEncoding" data-toggle="collapse" data-target=".property__inputEncoding .collapse">
<h2></h2>
<pre>$_inputEncoding : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__nestedColumn"> </a><div class="element clickable property private property__nestedColumn" data-toggle="collapse" data-target=".property__nestedColumn .collapse">
<h2></h2>
<pre>$_nestedColumn </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__sheetIndex"> </a><div class="element clickable property private property__sheetIndex" data-toggle="collapse" data-target=".property__sheetIndex .collapse">
<h2></h2>
<pre>$_sheetIndex : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__tableLevel"> </a><div class="element clickable property private property__tableLevel" data-toggle="collapse" data-target=".property__tableLevel .collapse">
<h2></h2>
<pre>$_tableLevel </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:35Z.<br></footer></div>
</div>
</body>
</html>
