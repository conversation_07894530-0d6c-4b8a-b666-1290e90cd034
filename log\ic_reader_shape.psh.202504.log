<br>
 date_NOW : 20250401<br>
 date_30D : 20250630<br>
 date_2M : 20250601<br>
 date_3M : 20250701<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250601' AND '20250701' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250402<br>
 date_30D : 20250701<br>
 date_2M : 20250602<br>
 date_3M : 20250702<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250602' AND '20250702' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250403<br>
 date_30D : 20250702<br>
 date_2M : 20250603<br>
 date_3M : 20250703<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250603' AND '20250703' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250404<br>
 date_30D : 20250703<br>
 date_2M : 20250604<br>
 date_3M : 20250704<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250604' AND '20250704' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-04-05 - 휴무일
2025-04-06 - 휴무일
<br>
 date_NOW : 20250407<br>
 date_30D : 20250706<br>
 date_2M : 20250607<br>
 date_3M : 20250707<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250607' AND '20250707' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250408<br>
 date_30D : 20250707<br>
 date_2M : 20250608<br>
 date_3M : 20250708<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250608' AND '20250708' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250409<br>
 date_30D : 20250708<br>
 date_2M : 20250609<br>
 date_3M : 20250709<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250609' AND '20250709' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250410<br>
 date_30D : 20250709<br>
 date_2M : 20250610<br>
 date_3M : 20250710<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250610' AND '20250710' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250411<br>
 date_30D : 20250710<br>
 date_2M : 20250611<br>
 date_3M : 20250711<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250611' AND '20250711' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-04-12 - 휴무일
2025-04-13 - 휴무일
<br>
 date_NOW : 20250414<br>
 date_30D : 20250713<br>
 date_2M : 20250614<br>
 date_3M : 20250714<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250614' AND '20250714' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250415<br>
 date_30D : 20250714<br>
 date_2M : 20250615<br>
 date_3M : 20250715<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250615' AND '20250715' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 47
    [VAN_NAME] => KOVAN(코밴)(KOV)
    [POS_NAME] => ECPOS
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => DUALPAY_633DV101
    [EXPIRE_YMD] => 2025-07-15
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '47' <br>
 date_NOW : 20250416<br>
 date_30D : 20250715<br>
 date_2M : 20250616<br>
 date_3M : 20250716<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250616' AND '20250716' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 49
    [VAN_NAME] => KOVAN(코밴)(KOV)
    [POS_NAME] => BIGPOS
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => D633DDPBKR031001
    [EXPIRE_YMD] => 2025-07-15
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '49' <br>
 date_NOW : 20250417<br>
 date_30D : 20250716<br>
 date_2M : 20250617<br>
 date_3M : 20250717<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250617' AND '20250717' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 2
    [VAN_NAME] => KSNET(KS)
    [POS_NAME] => Anyshop e2
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => ######KSR-011100
    [EXPIRE_YMD] => 2025-07-15
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '2' <br>
 date_NOW : 20250418<br>
 date_30D : 20250717<br>
 date_2M : 20250618<br>
 date_3M : 20250718<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250618' AND '20250718' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-04-19 - 휴무일
2025-04-20 - 휴무일
<br>
 date_NOW : 20250421<br>
 date_30D : 20250720<br>
 date_2M : 20250621<br>
 date_3M : 20250721<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250621' AND '20250721' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 24
    [VAN_NAME] => Smartro(SMT)
    [POS_NAME] => Anyshop e2
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => ###SCS-PB0021001
    [EXPIRE_YMD] => 2025-07-20
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '24' <br>
 date_NOW : 20250422<br>
 date_30D : 20250721<br>
 date_2M : 20250622<br>
 date_3M : 20250722<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250622' AND '20250722' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 81
    [VAN_NAME] => NICE(NIC)
    [POS_NAME] => Anyshop e2
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => #####MR-10002201
    [EXPIRE_YMD] => 2025-07-20
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '81' <br>
 date_NOW : 20250423<br>
 date_30D : 20250722<br>
 date_2M : 20250623<br>
 date_3M : 20250723<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250623' AND '20250723' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 127
    [VAN_NAME] => 다우데이타(DAO)
    [POS_NAME] => PD-1000
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => ################
    [EXPIRE_YMD] => 2025-07-20
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '127' <br>
 date_NOW : 20250424<br>
 date_30D : 20250723<br>
 date_2M : 20250624<br>
 date_3M : 20250724<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250624' AND '20250724' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 41
    [VAN_NAME] => 다우데이타(DAO)
    [POS_NAME] => APEXA G
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => 633DPBDOR002D102DAP1POSBK0011001
    [EXPIRE_YMD] => 2025-07-20
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '41' <br>
 date_NOW : 20250425<br>
 date_30D : 20250724<br>
 date_2M : 20250625<br>
 date_3M : 20250725<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250625' AND '20250725' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 121
    [VAN_NAME] => Smartro(SMT)
    [POS_NAME] => Anyshop e2
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => ################
    [EXPIRE_YMD] => 2025-07-20
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '121' 2025-04-26 - 휴무일
2025-04-27 - 휴무일
<br>
 date_NOW : 20250428<br>
 date_30D : 20250727<br>
 date_2M : 20250628<br>
 date_3M : 20250728<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250628' AND '20250728' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 39
    [VAN_NAME] => 다우데이타(DAO)
    [POS_NAME] => PD-1000
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => DUALPAY_633DD101
    [EXPIRE_YMD] => 2025-07-20
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '39' <br>
 date_NOW : 20250429<br>
 date_30D : 20250728<br>
 date_2M : 20250629<br>
 date_3M : 20250729<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250629' AND '20250729' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 40
    [VAN_NAME] => 다우데이타(DAO)
    [POS_NAME] => PD-1000
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => DUALPAY_633DD101DAP1POSBK0011001
    [EXPIRE_YMD] => 2025-07-20
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '40' <br>
 date_NOW : 20250430<br>
 date_30D : 20250729<br>
 date_2M : 20250630<br>
 date_3M : 20250730<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250630' AND '20250730' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 37
    [VAN_NAME] => 다우데이타(DAO)
    [POS_NAME] => Anyshop e2
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => 633D_PBDOR01D102DAP1POSBK0011001
    [EXPIRE_YMD] => 2025-07-20
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '37' 