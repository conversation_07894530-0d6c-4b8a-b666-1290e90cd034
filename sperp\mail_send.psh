#!/usr/bin/php -q
<?php
	// 0 12 * * * php -q /home/<USER>/sperp/mail_send.psh
	# 메일 발송
	$ROOT_PATH = "/home/<USER>";
	$_SERVER['DOCUMENT_ROOT'] = $ROOT_PATH;
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/func.php");

	// DB접속
	$dbconn_chrome_push = new DBController($db['chrome_push']);
	if(empty($dbconn_chrome_push->success)) {
		echo "dbconn error [" . $db['chrome_push']['host'] . "] 데이터베이스 연결 실패 \n";
	}

//	// DB접속
//	$dbconn_posbank_intra = new DBController($db['posbank_intra']);
//	if(empty($dbconn_posbank_intra->success)) {
//		echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패 \n";
//	}

	$chk_time = date("YmdHi");

	echo "[".$chk_time."] ".date("Y-m-d H:i:s")." - 메일 발송 시작 \n";

	// 10일 지난 데이타 삭제
	$Checkday = date("Ymd", strtotime ("-10 day"));
	$query = "delete from mail_send where STATE='2' and IDATE<'".$Checkday."' ";
	$rs = $dbconn_chrome_push->iud_query($query);

	$result = [];

	$url = "https://api.posbank.com/email/email_send";
	$arr_head = [];
	$arr_head[0] = "content-type:application/json";
	$arr_head[1] = "Authorization:posbankTK pFm0P467joqUwPs4PAM6UTVxDuqpT4pFa3wmcTEMqXGlD6Wq6eJTfAfOvBLzHkHFUYMF88k7UxfrOnD1zF34hw==";

//	for($cnt=0; $cnt<12; $cnt++){
//		if($chk_time == date("YmdHi")) { // 시간이 다르면 빠져나감

			// 전송 대상 가져오기
			$arr_data = [];
			$arr_HID = [];
			$SQL = "select 
							HID, PROGRAM, GU, `TO`, `FROM`, TITLE, MESSAGE, FILE_NAME, FILE_ONAME, IDATE, UDATE, EACH_SEND
						from 
							mail_send
						where 
							STATE='0' ";
			$rows = $dbconn_chrome_push->query_rows($SQL);
			if($rows){
				foreach($rows as $key => $row) {
					$arr_HID[] = $row['HID'];
					$arr_data[$row['HID']] = $row;
					$arr_data[$row['HID']]['ID'] = explode(",", $row['TO']);
				}

				if($arr_HID){
					// 상태 (전송중) 변경
					$query = "update mail_send set STATE='1' where STATE='0' and HID in ('".implode("','",$arr_HID)."') ";
					$rs = $dbconn_chrome_push->iud_query($query);
				}

				// 메일 전송
				if($arr_data){
					foreach($arr_data as $hid => $row) {
						$arr_to = [];
						if($row['GU']=='mail'){
							$arr_to = mail_convert($row['TO']);
						}else{
							// 직원 확인및 웹훅 url가져오기
							$url_user = goolge_webhooks_member_chk([
								'PROGRAM'=>$row['PROGRAM']
								,'GU'=>$row['GU']
								,'ID'=>$row['ID']
							]); 
							if($url_user){
								foreach($url_user as $key2 => $row2) {
									$arr_to[] = ['mail'=>$row2['EMAIL'], 'name'=>$row2['PRS_NM']];
								}
							}
						}
						$arr_body = [];
						$arr_body['program'] = $row['PROGRAM'];
						$arr_body['spaces_gu'] = $row['GU'];
						$arr_body['subject'] = $row['TITLE'];
						$arr_body['message'] = $row['MESSAGE'];
						$arr_body['file_name'] = $row['FILE_NAME'];
						$arr_body['file_oname'] = $row['FILE_ONAME'];
						$arr_body['to'] = $arr_to;
						$arr_body['each_send'] = $row['EACH_SEND'];

//						$arr_body['to'] = [];
//						if($url_user){
//							foreach($url_user as $key2 => $row2) {
//								$arr_body['to'][] = ['mail'=>$row2['EMAIL'], 'name'=>$row2['PRS_NM']];
//							}
//						}
						$arr_FROM = explode("|", $row['FROM']);
						$arr_body['from'] = ['mail'=>$arr_FROM[0], 'name'=>$arr_FROM[1]];
//echo json_encode($arr_body, JSON_UNESCAPED_UNICODE)."\n";
						$return = make_curl($url, "POST", $arr_head, $arr_body);

						echo $hid . " - " . json_encode($return, JSON_UNESCAPED_UNICODE)."\n";

						if(isset($return['result']['success']) && $return['result']['success']==true){
							// 상태 (완료) 변경
							$query = "update mail_send set STATE='2',UDATE=now() where STATE='1' and HID='".$hid."' ";
							$rs = $dbconn_chrome_push->iud_query($query);
							$result[] = $hid . " - " . json_encode($rs, JSON_UNESCAPED_UNICODE) . "\n";
						}

						//echo json_encode($return, JSON_PRETTY_PRINT|JSON_UNESCAPED_UNICODE)."\n";
					}
				}



				//echo "[".$chk_time."] ".date("Y-m-d H:i:s")." - ".($cnt+1)." - ";
				//echo "db - " . json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
			}else{
				//echo "[".$chk_time."] ".date("Y-m-d H:i:s")." - ".($cnt+1)." \n";
			}

//			sleep(5); // 5초 지연시킴
//		}else{
//			$cnt = 100;
//		}
//	}

	if($result) echo "db - " . json_encode($result, JSON_UNESCAPED_UNICODE)."\n";

	echo "[".$chk_time."] ".date("Y-m-d H:i:s")." - 메일 발송 끝\n";

	## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(60, "메일 발송");
