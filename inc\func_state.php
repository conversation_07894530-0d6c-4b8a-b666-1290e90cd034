<?php

/************************************************************************/
// 개발/이원화 검증 관리
####### [ 개발/이원화 검증 관리 상태 ] ######
function arr_VERIFY_MANT_STATE(){
	$arr['A'] = "검증의뢰";
	$arr['B'] = "의뢰승인";
	$arr['C'] = "HW검증";
	$arr['C2'] = "HW검증승인";
	$arr['C3'] = "HW시료출고";
	$arr['C4'] = "PCA시료입고";
	$arr['D'] = "PCA검증";
	$arr['D2'] = "PCA검증승인";
	// $arr['E'] = "검증승인";
	$arr['E'] = "품질팀승인";

	//$arr['9'] = "보류";
	return $arr;
}
####### [ 개발/이원화 검증 관리 - 검증등급 ] ######
function arr_VERIFY_MANT_A_LEVEL(){
	$arr['1'] = "긴급";
	$arr['2'] = "일반";
	$arr['9'] = "기타";
	return $arr;
}
####### [ 개발/이원화 검증 관리 - 검증구분 ] ######
function arr_VERIFY_MANT_A_GUBUN(){
	$arr['1'] = "신규";
	$arr['2'] = "변경점";
	$arr['9'] = "기타";
	return $arr;
}
####### [ 변경점 상태 ] ######
function arr_CHANGE_MANT_STATE(){
	$arr['8'] = "요청";
	$arr['0'] = "접수";
	$arr['1'] = "결재중";
	$arr['A'] = "ECN";
	$arr['B'] = "PCN";
	$arr['C'] = "IOS";
	$arr['D'] = "PCCB";
	return $arr;
}
