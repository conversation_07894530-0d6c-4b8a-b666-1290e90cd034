#!/usr/local/bin/php -q
<?php
// 0 9 * * * php -q /home/<USER>/sperp/test.psh
# 테스트용
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/inc/Encode.php");


// 구글워크스테이션 웹훅 보내기 2023-01-17 jjs
$goolgework_Params = [];
$goolgework_Params['PROGRAM'] = "spqms";
$goolgework_Params['GU'] = "";
$goolgework_Params['ID'] = ["100695"];
$goolgework_Params['PREVIEW'] = "테스트";
$goolgework_Params['TITLE'] = "테스트";
$goolgework_Params['MESSAGE'][] = ["<b>구분 : </b><font color='#555555'>AAAA</font>"];
$goolgework_Params['MESSAGE'][] = ["<b>이슈번호 : </b><font color='#555555'>BBBB</font>"];
$goolgework_Params['MESSAGE'][] = ["<b>업체명 : </b><font color='#555555'>CCCC</font>"];
$goolgework_Params['MESSAGE'][] = ["<b>증상 : </b><font color='#555555'>DDDD</font>"];
$goolgework_Params['LINK_NM'] = "확인하러가기";
$goolgework_Params['LINK'] = "https://www.spqms.co.kr";
//$rs = goolgework_send($goolgework_Params);


echo date("Y-m-d H:i:s")."\n";
echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";

?>
