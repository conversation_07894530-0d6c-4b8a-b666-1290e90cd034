#!/usr/local/bin/php -q
<?

$ROOT_PATH = "/home/<USER>";
$_SERVER['DOCUMENT_ROOT'] = $ROOT_PATH;
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

$dbconn_d = new DBController($db['douzone']);
if(empty($dbconn_d->success)) {
	echo "[" . $db['douzone']['host'] . "] 데이터베이스 연결 실패입니다.";
}

$dbconn_e = new DBController($db['sperp_posbank']);
if(empty($dbconn_e->success)) {
	echo "[" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패입니다.";
}


/**********************************************************/

echo date("Y-m-d H:i:s")." - 전송 시작\n";

/*
$SQL = "SELECT TOP 10 BIZ_NO,CORP_NM,CEO_NM FROM dbo.ZN_SMPPDB";
$rows = $dbconn_d->query_rows($SQL);
//$aa = $dbconn->query_one($SQL);
//$row = $dbconn->query_row($SQL);
if($rows){
	foreach($rows as $key => $row) {
		echo $row['BIZ_NO']."/".$row['CORP_NM']."/".$row['CEO_NM']."\n";
	}
}
*/
$arr_query = [];
$arr_query2 = [];

$SQL = "  SELECT KEY,
			ROW_NUMBER() OVER(PARTITION BY KEY ORDER BY KEY, GU2,FN_CD) AS SNO,RCT_CODE,HDATE,CT_CODE,CT_NAME,AMT,VAT,AMT+VAT CUAMT,AMT2,CT_LINK,CT_LINK2,  
        PRNAME,HNO,HGU,DZ_LINK,TMSTYLE,ACSTYLE,FN_CD,FN_LINK,SPERP_CPYID,GU1,
		CASE  WHEN GU1='11' THEN '과세매출'
			    WHEN GU1='17' THEN '카드매출'
				WHEN GU1='31' THEN '현금과세'
				WHEN GU1='14' THEN '건별매출'
				WHEN GU1='16' THEN '수출'
				WHEN GU1='12' THEN '영세매출'
				WHEN GU1='13' THEN '면세매출'
				WHEN GU1='18' THEN '면세카드매출'
				WHEN GU1='32' THEN '현금면세'
		END  GU1_NAME, CHA,DECODE(CHA,'D','3','4') CHA_NAME
		 FROM(
			 SELECT (A.HDATE || A.HNO) AS KEY, A.RCT_CODE, A.HDATE, A.CT_CODE, C.CT_NAME, D.AMT, 0 VAT, 
					0 AMT2,CT_LINK,C.CT_LINK2,  
					B.PRNAME, A.HNO, A.HGU,A.DZ_LINK, A.TMSTYLE, A.ACSTYLE, D.FN_CD, D.FN_LINK,'2'|| A.HNO SPERP_CPYID,
					CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '11'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '17'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '31'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '14'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '11'
						 WHEN A.TMSTYLE='O' AND A.ACSTYLE='4' THEN '16'
						 WHEN A.TMSTYLE='O' AND A.ACSTYLE<>'4' THEN '12'
						 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='1' THEN '13'
						 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='2' THEN '18'
						 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='3' THEN '32'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE>'3' THEN '13'
					END  GU1,
					CASE WHEN D.FN_CD='21107001' THEN 'C'
						 WHEN D.FN_CD='11105001' THEN 'D'
						 ELSE 'C'
					END CHA, --'D'차변 'C'대변,
					'1' GU2
			 FROM TMH A, TMS B, CT C,  
				  ( 
				   SELECT  A.TMHID, (C.BAS_OP4) AS FN_CD, nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
						   SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT
				   FROM PD13 A 
						LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE 
						LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%' 
						LEFT JOIN FN D ON C.BAS_OP4 = D.FN_CD1 || D.FN_CD2
				   WHERE A.TMHID IS NOT NULL 
					 AND A.STATE = '8' 
					 --AND SUBSTR(A.TMHID, 5,8) BETWEEN '20220729' AND '20220729' 
					 AND SUBSTR(A.TMHID, 5,8) >= TO_CHAR(sysdate - 45,'YYYYMMDD')
				   GROUP BY A.TMHID, C.BAS_OP4,nvl(D.FN_LINK2,D.FN_LINK)
				  ) D 
			 WHERE A.RCT_CODE = '1000' 
			   --AND A.HDATE BETWEEN '20220729' AND '20220729' 
			   AND A.HDATE >= TO_CHAR(sysdate - 45,'YYYYMMDD')
			   AND A.TCHK <> 'Y'  
			   AND A.RCTYPE = 'C'  
			   AND A.HGU = '13'  
			   AND A.RCT_CODE = B.RCT_CODE 
			   AND A.HDATE = B.HDATE 
			   AND A.HNO = B.HNO 
			   AND A.HGU = B.HGU 
			   AND B.SNO = 1 
			   AND A.CT_CODE = C.CT_CODE (+)
			   AND SUBSTR(D.TMHID, 0, 4) = A.RCT_CODE 
			   AND SUBSTR(D.TMHID, 5, 8) = A.HDATE 
			   AND SUBSTR(D.TMHID, 13, 2) = A.HGU 
			   AND SUBSTR(D.TMHID, 15, 4) = A.HNO 
			   AND (D.AMT + D.VAT) <> 0 
			 UNION ALL
			 --21107001 부가세예수금
			 SELECT (A.HDATE || A.HNO) AS KEY, A.RCT_CODE, A.HDATE, A.CT_CODE, C.CT_NAME, 0 AMT, SUM(D.VAT) VAT, 
					 SUM(D.AMT2) AMT2,C.CT_LINK,C.CT_LINK2,  
					B.PRNAME, A.HNO, A.HGU,A.DZ_LINK, A.TMSTYLE, A.ACSTYLE, '21107001' FN_CD, '25500' FN_LINK,'2'|| A.HNO SPERP_CPYID,
					CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '11'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '17'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '31'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '14'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '11'
						 WHEN A.TMSTYLE='O' AND A.ACSTYLE='4' THEN '16'
						 WHEN A.TMSTYLE='O' AND A.ACSTYLE<>'4' THEN '12'
						 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='1' THEN '13'
						 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='2' THEN '18'
						 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='3' THEN '32'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE>'3' THEN '13'
					END  GU1,
					'C' CHA, --'D'차변 'C'대변,,
					'2' GU2
			 FROM TMH A, TMS B, CT C,  
				  ( 
				   SELECT  A.TMHID, (C.BAS_OP4) AS FN_CD, nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
						   SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT,SUM(A.AMT) AS AMT2
				   FROM PD13 A 
						LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE 
						LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%' 
						LEFT JOIN FN D ON C.BAS_OP4 = D.FN_CD1 || D.FN_CD2
				   WHERE A.TMHID IS NOT NULL 
					 AND A.STATE = '8' 
					 --AND SUBSTR(A.TMHID, 5,8) BETWEEN '20220729' AND '20220729' 
					 AND SUBSTR(A.TMHID, 5,8) >= TO_CHAR(sysdate - 45,'YYYYMMDD')
				   GROUP BY A.TMHID, C.BAS_OP4,nvl(D.FN_LINK2,D.FN_LINK)
				  ) D 
			 WHERE A.RCT_CODE = '1000' 
			   --AND A.HDATE BETWEEN '20220729' AND '20220729' 
			   AND A.HDATE >= TO_CHAR(sysdate - 45,'YYYYMMDD')
			   AND A.TCHK <> 'Y'  
			   AND A.RCTYPE = 'C'  
			   AND A.HGU = '13'  
			   AND A.RCT_CODE = B.RCT_CODE 
			   AND A.HDATE = B.HDATE 
			   AND A.HNO = B.HNO 
			   AND A.HGU = B.HGU 
			   AND B.SNO = 1 
			   AND A.CT_CODE = C.CT_CODE (+)
			   AND SUBSTR(D.TMHID, 0, 4) = A.RCT_CODE 
			   AND SUBSTR(D.TMHID, 5, 8) = A.HDATE 
			   AND SUBSTR(D.TMHID, 13, 2) = A.HGU 
			   AND SUBSTR(D.TMHID, 15, 4) = A.HNO 
			   AND (D.AMT + D.VAT) <> 0 
			   GROUP BY (A.HDATE || A.HNO),A.RCT_CODE,A.HDATE, A.CT_CODE, C.CT_NAME,C.CT_LINK,C.CT_LINK2,  
					B.PRNAME, A.HNO, A.HGU, C.CT_LINK, A.DZ_LINK, A.TMSTYLE, A.ACSTYLE,'2'|| A.HNO,
					A.TMSTYLE,A.ACSTYLE
			UNION ALL
			--11105001  외상매출금
			SELECT (A.HDATE || A.HNO) AS KEY, A.RCT_CODE, A.HDATE, A.CT_CODE, C.CT_NAME,  SUM(D.AMT+D.VAT) AMT, 0 VAT, 
					0 AMT2,C.CT_LINK,C.CT_LINK2,  
					B.PRNAME, A.HNO, A.HGU,A.DZ_LINK, A.TMSTYLE, A.ACSTYLE, '11105001' FN_CD, '10800' FN_LINK,'2'|| A.HNO SPERP_CPYID,
					CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '11'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '17'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '31'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '14'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '11'
						 WHEN A.TMSTYLE='O' AND A.ACSTYLE='4' THEN '16'
						 WHEN A.TMSTYLE='O' AND A.ACSTYLE<>'4' THEN '12'
						 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='1' THEN '13'
						 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='2' THEN '18'
						 WHEN A.TMSTYLE='Z' AND A.ACSTYLE='3' THEN '32'
						 WHEN A.TMSTYLE='S' AND A.ACSTYLE>'3' THEN '13'
					END  GU1,
					'D' CHA, --'D'차변 'C'대변,,
					'3' GU2
			 FROM TMH A, TMS B, CT C,  
				  ( 
				   SELECT  A.TMHID, (C.BAS_OP4) AS FN_CD, nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
						   SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT
				   FROM PD13 A 
						LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE 
						LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%' 
						LEFT JOIN FN D ON C.BAS_OP4 = D.FN_CD1 || D.FN_CD2
				   WHERE A.TMHID IS NOT NULL 
					 AND A.STATE = '8' 
					 --AND SUBSTR(A.TMHID, 5,8) BETWEEN '20220729' AND '20220729' 
					 AND SUBSTR(A.TMHID, 5,8) >= TO_CHAR(sysdate - 45,'YYYYMMDD')
				   GROUP BY A.TMHID, C.BAS_OP4,nvl(D.FN_LINK2,D.FN_LINK)
				  ) D 
			 WHERE A.RCT_CODE = '1000' 
			   --AND A.HDATE BETWEEN '20220729' AND '20220729' 
			   AND A.HDATE >= TO_CHAR(sysdate - 45,'YYYYMMDD')
			   AND A.TCHK <> 'Y'  
			   AND A.RCTYPE = 'C'  
			   AND A.HGU = '13'  
			   AND A.RCT_CODE = B.RCT_CODE 
			   AND A.HDATE = B.HDATE 
			   AND A.HNO = B.HNO 
			   AND A.HGU = B.HGU 
			   AND B.SNO = 1 
			   AND A.CT_CODE = C.CT_CODE (+)
			   AND SUBSTR(D.TMHID, 0, 4) = A.RCT_CODE 
			   AND SUBSTR(D.TMHID, 5, 8) = A.HDATE 
			   AND SUBSTR(D.TMHID, 13, 2) = A.HGU 
			   AND SUBSTR(D.TMHID, 15, 4) = A.HNO 
			   AND (D.AMT + D.VAT) <> 0 
			   GROUP BY (A.HDATE || A.HNO),A.RCT_CODE,A.HDATE, A.CT_CODE, C.CT_NAME,C.CT_LINK,C.CT_LINK2,  
					B.PRNAME, A.HNO, A.HGU, C.CT_LINK, A.DZ_LINK, A.TMSTYLE, A.ACSTYLE,'2'|| A.HNO,
					A.TMSTYLE,A.ACSTYLE
		)ORDER BY HDATE,HNO,HGU,GU2,FN_CD  "
;

$n = 0;
$n2 = 0;
$SNO = 0;
$rows = $dbconn_e->query_rows($SQL);
//echo json_encode($rows, JSON_UNESCAPED_UNICODE);

//print_r($rows);

//exit;

	if($rows){
		foreach($rows as $key => $row) {
			//echo $row['KEY']."/".$row['CT_CODE']."/".$row['CT_NAME']."\n";
			//echo $row['FN_LINK']."/".$row['CHA_NAME']."/".$row['CUAMT']."\n";
			$arr_query[$n]="DELETE FROM SAUTODOCUD
							 WHERE IN_DT = '".$row['HDATE']."'
							   AND IN_SQ =  '".$row['SPERP_CPYID']."'
							  AND LN_SQ =  '".$row['SNO']."'
							   AND ISU_DT =  '00000000' 
							   AND CO_CD =  '2005' AND IN_DIV_CD = '1000' ";
			$n++;
			//$del = $dbconn_d->iud_query($del_query);

//print($del_query);



		//더존 회계 계정 자료
		$SQL = "SELECT A.ACCT_CD, A.ACCT_NM, A.TRCD_TY, A.TRNM_TY, A.DEPTCD_TY, A.PJTCD_TY, 
					A.CTNB_TY, A.FRDT_TY, A.TODT_TY, A.QT_TY, A.AM_TY, A.RT_TY, A.DEAL_TY, A.USER1_TY, A.USER2_TY 
					FROM VA_AUTOSACCT A 
						WHERE A.CO_CD = '2005' 
						AND A.ACCT_NM <> '회사설정계정'
						AND A.ACCT_CD= '".$row['FN_LINK']."'";
//print($SQL);

		$arrRow = $dbconn_d->query_row($SQL);

//print_r($arrRow);
//exit;

		//더존 입력 되었는지 확인
		$SQL = " SELECT DISTINCT A.IN_DT, A.IN_SQ, A.ISU_DT
					FROM SAUTODOCUD A
					WHERE A.CO_CD = '2005' AND A.IN_DIV_CD = '1000'
						AND A.IN_DT= '".$row['HDATE']."'
						AND A.IN_SQ= '".$row['SPERP_CPYID']."'
					    AND A.ISU_DT <>  '00000000' ";

		$arrRow2 = $dbconn_d->query_rows($SQL);

//print_r($arrRow2);
//exit;

		if(!$arrRow2){

			 $arr_query[$n] = "
				INSERT INTO SAUTODOCUD
				(IN_DT,IN_SQ,LN_SQ,CO_CD,IN_DIV_CD,LOGIC_CD,ISU_DT,ISU_SQ,DIV_CD,DEPT_CD,EMP_CD,ACCT_CD,DRCR_FG,
				 ACCT_AM,RMK_NB,RMK_DC,ATTR_CD,
				TRCD_TY,TRNM_TY,DEPTCD_TY,PJTCD_TY,CTNB_TY,FRDT_TY,TODT_TY,QT_TY,AM_TY,RT_TY,DEAL_TY,USER1_TY,USER2_TY,
				TR_CD,TR_NM,CT_DEPT,DEPT_NM,PJT_CD,PJT_NM,CT_NB,FR_DT,TO_DT,CT_QT,CT_AM,CT_RT,CT_DEAL,DEAL_NM,
				CT_USER1,USER1_NM,CT_USER2,USER2_NM,EXCH_TY,EXCH_AM,PAYMENT,ISU_NM,ENDORS_NM,
				BILL_FG1,BILL_FG2,DUMMY1,DUMMY2,DUMMY3,INSERT_DT,EX_FG,
				TR_NMK,DEPT_NMK,PJT_NMK,DEAL_NMK,USER1_NMK,USER2_NMK,RMK_DCK,ISU_DOC,ISU_DOCK,PRS_FG)  VALUES
				('".$row['HDATE']."','".$row['SPERP_CPYID']."','".$row['SNO']."','2005','1000','31','00000000',0,'','','',
				'".$row['FN_LINK']."','".$row['CHA_NAME']."',".$row['CUAMT'].",
				0,'".$row['PRNAME']."','',
				'".$arrRow['TRCD_TY']."','".$arrRow['TRNM_TY']."','".$arrRow['DEPTCD_TY']."','".$arrRow['PJTCD_TY']."',
				'".$arrRow['CTNB_TY']."','".$arrRow['FRDT_TY']."','".$arrRow['TODT_TY']."','".$arrRow['QT_TY']."',
				'".$arrRow['AM_TY']."','".$arrRow['RT_TY']."',	'".$arrRow['DEAL_TY']."','".$arrRow['USER1_TY']."','".$arrRow['USER2_TY']."',
				'".$row['CT_LINK']."','".$row['CT_NAME']."','','',";

				if(($row['CHA'] == 'C' && $row['FN_LINK'] =='25500') ||
					($row['CHA'] == 'D' && $row['FN_LINK'] =='13500')){ 
						 $arr_query[$n] .= " '1000','(주)포스뱅크본사','','".$row['HDATE']."','',0,".$row['AMT2'].",0,
												'".$row['GU1']."','".$row['GU1_NAME']."',";
					} else {
						 $arr_query[$n] .= " '','','','".$row['HDATE']."','".$row['HDATE']."',0,0,0,'0','',";
					}
			
				 $arr_query[$n] .= " '','','','','',0,'','','','','','','','',GETDATE(),'','','','','','','','','".$row['PRNAME']."','','')";

//print_r($arr_query);

			 $arr_query2[$n2] = "
					 UPDATE TMH
						SET DZ_LINK = '".$row['SPERP_CPYID']."'
					 WHERE RCT_CODE = '1000' 
					   AND HDATE = '".$row['HDATE']."'
					   AND HNO = '".$row['HNO']."'
					   AND HGU = '13'";

					$n2++;
					$n++;
					$SNO++;
			}
		}
	}

//echo $HDATE."\n";
//print_r($data);
//print_r($arr_query);
	if($arr_query){
		$rs = $dbconn_d->iud_query($arr_query);
		if(!$rs) echo " iud_query error";
		else echo"처리 - ".sizeof($arr_query)."\n";
	}

	if($arr_query2){
		$rs2 = $dbconn_e->iud_query($arr_query2);
		if(!$rs2) echo " iud_query error";
		else echo"처리 - ".sizeof($arr_query2)."\n";
	}

	if($rs) echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";	//쿼리 결과 값
	if($rs2) echo json_encode($rs2, JSON_UNESCAPED_UNICODE)."\n";	//쿼리 결과 값

	echo"처리 - ".sizeof($arr_query)."\n";
	
	echo date("Y-m-d H:i:s")." - 전송 끝\n";

	echo "---------------------------\n";

	## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(86400, "더존 전송 완료");



?>
