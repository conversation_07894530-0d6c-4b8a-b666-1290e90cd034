<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Calculation_Function</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_Calculation_Function"><span class="description">Create a new PHPExcel_Calculation_Function</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_getCategory" title="getCategory :: Get Category (represented by CATEGORY_*)"><span class="description">Get Category (represented by CATEGORY_*)</span><pre>getCategory()</pre></a></li>
<li class="method public "><a href="#method_getExcelName" title="getExcelName :: Get Excel name"><span class="description">Get Excel name</span><pre>getExcelName()</pre></a></li>
<li class="method public "><a href="#method_getPHPExcelName" title="getPHPExcelName :: Get PHPExcel name"><span class="description">Get PHPExcel name</span><pre>getPHPExcelName()</pre></a></li>
<li class="method public "><a href="#method_setCategory" title="setCategory :: Set Category (represented by CATEGORY_*)"><span class="description">Set Category (represented by CATEGORY_*)</span><pre>setCategory()</pre></a></li>
<li class="method public "><a href="#method_setExcelName" title="setExcelName :: Set Excel name"><span class="description">Set Excel name</span><pre>setExcelName()</pre></a></li>
<li class="method public "><a href="#method_setPHPExcelName" title="setPHPExcelName :: Set PHPExcel name"><span class="description">Set PHPExcel name</span><pre>setPHPExcelName()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__category" title="$_category :: Category (represented by CATEGORY_*)"><span class="description"></span><pre>$_category</pre></a></li>
<li class="property private "><a href="#property__excelName" title="$_excelName :: Excel name"><span class="description"></span><pre>$_excelName</pre></a></li>
<li class="property private "><a href="#property__phpExcelName" title="$_phpExcelName :: PHPExcel name"><span class="description"></span><pre>$_phpExcelName</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_CATEGORY_CUBE" title="CATEGORY_CUBE :: "><span class="description">CATEGORY_CUBE</span><pre>CATEGORY_CUBE</pre></a></li>
<li class="constant  "><a href="#constant_CATEGORY_DATABASE" title="CATEGORY_DATABASE :: "><span class="description">CATEGORY_DATABASE</span><pre>CATEGORY_DATABASE</pre></a></li>
<li class="constant  "><a href="#constant_CATEGORY_DATE_AND_TIME" title="CATEGORY_DATE_AND_TIME :: "><span class="description">CATEGORY_DATE_AND_TIME</span><pre>CATEGORY_DATE_AND_TIME</pre></a></li>
<li class="constant  "><a href="#constant_CATEGORY_ENGINEERING" title="CATEGORY_ENGINEERING :: "><span class="description">CATEGORY_ENGINEERING</span><pre>CATEGORY_ENGINEERING</pre></a></li>
<li class="constant  "><a href="#constant_CATEGORY_FINANCIAL" title="CATEGORY_FINANCIAL :: "><span class="description">CATEGORY_FINANCIAL</span><pre>CATEGORY_FINANCIAL</pre></a></li>
<li class="constant  "><a href="#constant_CATEGORY_INFORMATION" title="CATEGORY_INFORMATION :: "><span class="description">CATEGORY_INFORMATION</span><pre>CATEGORY_INFORMATION</pre></a></li>
<li class="constant  "><a href="#constant_CATEGORY_LOGICAL" title="CATEGORY_LOGICAL :: "><span class="description">CATEGORY_LOGICAL</span><pre>CATEGORY_LOGICAL</pre></a></li>
<li class="constant  "><a href="#constant_CATEGORY_LOOKUP_AND_REFERENCE" title="CATEGORY_LOOKUP_AND_REFERENCE :: "><span class="description">CATEGORY_LOOKUP_AND_REFERENCE</span><pre>CATEGORY_LOOKUP_AND_REFERENCE</pre></a></li>
<li class="constant  "><a href="#constant_CATEGORY_MATH_AND_TRIG" title="CATEGORY_MATH_AND_TRIG :: "><span class="description">CATEGORY_MATH_AND_TRIG</span><pre>CATEGORY_MATH_AND_TRIG</pre></a></li>
<li class="constant  "><a href="#constant_CATEGORY_STATISTICAL" title="CATEGORY_STATISTICAL :: "><span class="description">CATEGORY_STATISTICAL</span><pre>CATEGORY_STATISTICAL</pre></a></li>
<li class="constant  "><a href="#constant_CATEGORY_TEXT_AND_DATA" title="CATEGORY_TEXT_AND_DATA :: "><span class="description">CATEGORY_TEXT_AND_DATA</span><pre>CATEGORY_TEXT_AND_DATA</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Calculation_Function"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Calculation_Function.html">PHPExcel_Calculation_Function</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Calculation_Function</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Calculation.html">PHPExcel_Calculation</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_Calculation_Function</h2>
<pre>__construct(string $pCategory, string $pExcelName, string $pPHPExcelName) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pCategory</h4>
<code>string</code><p>Category (represented by CATEGORY_*)</p>
</div>
<div class="subelement argument">
<h4>$pExcelName</h4>
<code>string</code><p>Excel function name</p></div>
<div class="subelement argument">
<h4>$pPHPExcelName</h4>
<code>string</code><p>PHPExcel function mapping</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Calculation_Exception.html">\PHPExcel_Calculation_Exception</a></code></th>
<td></td>
</tr></table>
</div></div>
</div>
<a id="method_getCategory"></a><div class="element clickable method public method_getCategory" data-toggle="collapse" data-target=".method_getCategory .collapse">
<h2>Get Category (represented by CATEGORY_*)</h2>
<pre>getCategory() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getExcelName"></a><div class="element clickable method public method_getExcelName" data-toggle="collapse" data-target=".method_getExcelName .collapse">
<h2>Get Excel name</h2>
<pre>getExcelName() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getPHPExcelName"></a><div class="element clickable method public method_getPHPExcelName" data-toggle="collapse" data-target=".method_getPHPExcelName .collapse">
<h2>Get PHPExcel name</h2>
<pre>getPHPExcelName() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_setCategory"></a><div class="element clickable method public method_setCategory" data-toggle="collapse" data-target=".method_setCategory .collapse">
<h2>Set Category (represented by CATEGORY_*)</h2>
<pre>setCategory(string $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Calculation_Exception.html">\PHPExcel_Calculation_Exception</a></code></th>
<td></td>
</tr></table>
</div></div>
</div>
<a id="method_setExcelName"></a><div class="element clickable method public method_setExcelName" data-toggle="collapse" data-target=".method_setExcelName .collapse">
<h2>Set Excel name</h2>
<pre>setExcelName(string $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
</div></div>
</div>
<a id="method_setPHPExcelName"></a><div class="element clickable method public method_setPHPExcelName" data-toggle="collapse" data-target=".method_setPHPExcelName .collapse">
<h2>Set PHPExcel name</h2>
<pre>setPHPExcelName(string $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__category"> </a><div class="element clickable property private property__category" data-toggle="collapse" data-target=".property__category .collapse">
<h2></h2>
<pre>$_category : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__excelName"> </a><div class="element clickable property private property__excelName" data-toggle="collapse" data-target=".property__excelName .collapse">
<h2></h2>
<pre>$_excelName : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__phpExcelName"> </a><div class="element clickable property private property__phpExcelName" data-toggle="collapse" data-target=".property__phpExcelName .collapse">
<h2></h2>
<pre>$_phpExcelName : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_CATEGORY_CUBE"> </a><div class="element clickable constant  constant_CATEGORY_CUBE" data-toggle="collapse" data-target=".constant_CATEGORY_CUBE .collapse">
<h2>CATEGORY_CUBE</h2>
<pre>CATEGORY_CUBE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CATEGORY_DATABASE"> </a><div class="element clickable constant  constant_CATEGORY_DATABASE" data-toggle="collapse" data-target=".constant_CATEGORY_DATABASE .collapse">
<h2>CATEGORY_DATABASE</h2>
<pre>CATEGORY_DATABASE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CATEGORY_DATE_AND_TIME"> </a><div class="element clickable constant  constant_CATEGORY_DATE_AND_TIME" data-toggle="collapse" data-target=".constant_CATEGORY_DATE_AND_TIME .collapse">
<h2>CATEGORY_DATE_AND_TIME</h2>
<pre>CATEGORY_DATE_AND_TIME </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CATEGORY_ENGINEERING"> </a><div class="element clickable constant  constant_CATEGORY_ENGINEERING" data-toggle="collapse" data-target=".constant_CATEGORY_ENGINEERING .collapse">
<h2>CATEGORY_ENGINEERING</h2>
<pre>CATEGORY_ENGINEERING </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CATEGORY_FINANCIAL"> </a><div class="element clickable constant  constant_CATEGORY_FINANCIAL" data-toggle="collapse" data-target=".constant_CATEGORY_FINANCIAL .collapse">
<h2>CATEGORY_FINANCIAL</h2>
<pre>CATEGORY_FINANCIAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CATEGORY_INFORMATION"> </a><div class="element clickable constant  constant_CATEGORY_INFORMATION" data-toggle="collapse" data-target=".constant_CATEGORY_INFORMATION .collapse">
<h2>CATEGORY_INFORMATION</h2>
<pre>CATEGORY_INFORMATION </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CATEGORY_LOGICAL"> </a><div class="element clickable constant  constant_CATEGORY_LOGICAL" data-toggle="collapse" data-target=".constant_CATEGORY_LOGICAL .collapse">
<h2>CATEGORY_LOGICAL</h2>
<pre>CATEGORY_LOGICAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CATEGORY_LOOKUP_AND_REFERENCE"> </a><div class="element clickable constant  constant_CATEGORY_LOOKUP_AND_REFERENCE" data-toggle="collapse" data-target=".constant_CATEGORY_LOOKUP_AND_REFERENCE .collapse">
<h2>CATEGORY_LOOKUP_AND_REFERENCE</h2>
<pre>CATEGORY_LOOKUP_AND_REFERENCE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CATEGORY_MATH_AND_TRIG"> </a><div class="element clickable constant  constant_CATEGORY_MATH_AND_TRIG" data-toggle="collapse" data-target=".constant_CATEGORY_MATH_AND_TRIG .collapse">
<h2>CATEGORY_MATH_AND_TRIG</h2>
<pre>CATEGORY_MATH_AND_TRIG </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CATEGORY_STATISTICAL"> </a><div class="element clickable constant  constant_CATEGORY_STATISTICAL" data-toggle="collapse" data-target=".constant_CATEGORY_STATISTICAL .collapse">
<h2>CATEGORY_STATISTICAL</h2>
<pre>CATEGORY_STATISTICAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CATEGORY_TEXT_AND_DATA"> </a><div class="element clickable constant  constant_CATEGORY_TEXT_AND_DATA" data-toggle="collapse" data-target=".constant_CATEGORY_TEXT_AND_DATA .collapse">
<h2>CATEGORY_TEXT_AND_DATA</h2>
<pre>CATEGORY_TEXT_AND_DATA </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:34Z.<br></footer></div>
</div>
</body>
</html>
