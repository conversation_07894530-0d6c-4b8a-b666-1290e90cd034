#!/usr//bin/php -q
<?php
	// 0 12 * * * php -q /home/<USER>/open_api/exchange_rate.sh
	# 한국수출입은행 Open Api 적용 (https://www.koreaexim.go.kr)
	# 키발급일 : 2023.11.08 유효기간 2년
	$ROOT_PATH = "/home/<USER>";
	$_SERVER['DOCUMENT_ROOT'] = $ROOT_PATH;
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/func.php");

	$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
	if(empty($dbconn_sperp_posbank->success)) {
		echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
	}

	$arrDate = [];
	for($n=7;$n>=0;$n--){
		$ndate = date("Ymd", strtotime ("-".$n." day"));
		$week = date("w", strtotime ("-".$n." day"));
		if(!in_array($week,array("0","6"))){
			$SQL = "select count(*) from EXCHANGE_RATE where HDATE='".$ndate."'";
			$chk = $dbconn_sperp_posbank->query_one($SQL);
			if(!$chk){
				$arrDate[] = $ndate;
			}
		}
	}

	//$HDATE = date('Ymd');
	//$HDATE = "********";
	//$HDATE = date("Ymd", strtotime ("-4 day"));

	$arr_query2 = [];
	if($arrDate){
		foreach($arrDate as $key => $HDATE) {

			echo date("Y-m-d H:i:s")." - 환율 저장 시작 [".$HDATE."]\n";

			$url = "https://www.koreaexim.go.kr/site/program/financial/exchangeJSON";
			$arr_head[0] = "content-type:application/json";

			#$arr_body['authkey'] = "QQpP3GZeDjJQAIw66fnqctdCyq47wy8q";
			$arr_body['authkey'] = "Hq2dXOl231gmI0jSIjM3LbqCe363uiPt";
			$arr_body['searchdate'] = $HDATE;
			$arr_body['data'] = "AP01";
			$arr_query = [];
			$data = make_curl_get($url, $arr_head, $arr_body);
//print_r($arr_body);
//echo"\n";
//print_r($data);
//echo"\n";
			if($data['result']){
				foreach($data['result'] as $key => $row) {
					if($row['result']){
						$arr_query[] = "
									MERGE INTO EXCHANGE_RATE
										USING DUAL
											ON (HDATE = '".$HDATE."' AND CUR_UNIT = '".$row['cur_unit']."')
										WHEN MATCHED THEN
											UPDATE SET
												CUR_NM = '".$row['cur_nm']."'
												,TTB = decode(nvl(TTB,0),0,'".num_replace($row['ttb'])."',TTB)
												,TTS = decode(nvl(TTS,0),0,'".num_replace($row['tts'])."',TTS)
												,DEAL_BAS_R = decode(nvl(DEAL_BAS_R,0),0,'".num_replace($row['deal_bas_r'])."',DEAL_BAS_R)
												,BKPR = decode(nvl(BKPR,0),0,'".num_replace($row['bkpr'])."',BKPR)
												,YY_EFEE_R = decode(nvl(YY_EFEE_R,0),0,'".num_replace($row['yy_efee_r'])."',YY_EFEE_R)
												,TEN_DD_EFEE_R = decode(nvl(TEN_DD_EFEE_R,0),0,'".num_replace($row['ten_dd_efee_r'])."',TEN_DD_EFEE_R)
												,KFTC_DEAL_BAS_R = decode(nvl(KFTC_DEAL_BAS_R,0),0,'".num_replace($row['kftc_deal_bas_r'])."',KFTC_DEAL_BAS_R)
												,KFTC_BKPR = decode(nvl(KFTC_BKPR,0),0,'".num_replace($row['kftc_bkpr'])."',KFTC_BKPR)
												,REG_DATE = SYSDATE
										WHEN NOT MATCHED THEN
											INSERT (HDATE,CUR_UNIT,CUR_NM,TTB,TTS,DEAL_BAS_R,BKPR,YY_EFEE_R,TEN_DD_EFEE_R,KFTC_DEAL_BAS_R,KFTC_BKPR,REG_IDATE,REG_DATE)
											VALUES ('".$HDATE."','".$row['cur_unit']."','".$row['cur_nm']."','".num_replace($row['ttb'])."','".num_replace($row['tts'])."','".num_replace($row['deal_bas_r'])."','".num_replace($row['bkpr'])."','".num_replace($row['yy_efee_r'])."','".num_replace($row['ten_dd_efee_r'])."','".num_replace($row['kftc_deal_bas_r'])."','".num_replace($row['kftc_bkpr'])."',SYSDATE,SYSDATE)
									";
					}
				}
			}else{
				echo"처리 - API데이타 못 불러옴\n";
			}

		//echo $HDATE."\n";
		//print_r($data);
			if($arr_query){
				$arr = $dbconn_sperp_posbank->iud_query($arr_query);
				if(!$arr) echo " iud_query error";
				else echo"처리 - ".sizeof($arr_query)."\n";

				$arr_query2 = array_merge($arr_query2, $arr_query); //배열 합치기
			}

			echo date("Y-m-d H:i:s")." - 환율 저장 끝\n";

		}
	}


	# 개발용 DB 저장
	$dbconn_sperp_test = new DBController($db['sperp_test']);
	if(empty($dbconn_sperp_test->success)) {
		echo "dbconn error [" . $db['sperp_test']['host'] . "] 데이터베이스 연결 실패";
	}
	if($arr_query2){
		$arr = $dbconn_sperp_test->iud_query($arr_query2);
		if(!$arr) echo " iud_query error";
		else echo"처리 - ".sizeof($arr_query2)."\n";
	}

	echo date("Y-m-d H:i:s")." - 개발용 환율 저장 끝\n";


	## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(7200, "ERP 환율 저장(9시~19시 2시간간격)"); // 2시간 간격
