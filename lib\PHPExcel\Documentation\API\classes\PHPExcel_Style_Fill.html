<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Style_Fill</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public inherited"><a href="#method___clone" title="__clone :: Implement PHP __clone to create a deep clone, not just a shallow copy."><span class="description">Implement PHP __clone to create a deep clone, not just a shallow copy.</span><pre>__clone()</pre></a></li>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_Style_Fill"><span class="description">Create a new PHPExcel_Style_Fill</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_applyFromArray" title="applyFromArray :: Apply styles from array"><span class="description">Apply styles from array</span><pre>applyFromArray()</pre></a></li>
<li class="method public inherited"><a href="#method_bindParent" title="bindParent :: Bind parent."><span class="description">Bind parent.</span><pre>bindParent()</pre></a></li>
<li class="method public inherited"><a href="#method_getActiveCell" title="getActiveCell :: Get the currently active cell coordinate in currently active sheet."><span class="description">Get the currently active cell coordinate in currently active sheet.</span><pre>getActiveCell()</pre></a></li>
<li class="method public inherited"><a href="#method_getActiveSheet" title="getActiveSheet :: Get the currently active sheet."><span class="description">Get the currently active sheet.</span><pre>getActiveSheet()</pre></a></li>
<li class="method public "><a href="#method_getEndColor" title="getEndColor :: Get End Color"><span class="description">Get End Color</span><pre>getEndColor()</pre></a></li>
<li class="method public "><a href="#method_getFillType" title="getFillType :: Get Fill Type"><span class="description">Get Fill Type</span><pre>getFillType()</pre></a></li>
<li class="method public "><a href="#method_getHashCode" title="getHashCode :: Get hash code"><span class="description">Get hash code</span><pre>getHashCode()</pre></a></li>
<li class="method public inherited"><a href="#method_getIsSupervisor" title="getIsSupervisor :: Is this a supervisor or a cell style component?"><span class="description">Is this a supervisor or a cell style component?</span><pre>getIsSupervisor()</pre></a></li>
<li class="method public "><a href="#method_getRotation" title="getRotation :: Get Rotation"><span class="description">Get Rotation</span><pre>getRotation()</pre></a></li>
<li class="method public inherited"><a href="#method_getSelectedCells" title="getSelectedCells :: Get the currently active cell coordinate in currently active sheet."><span class="description">Get the currently active cell coordinate in currently active sheet.</span><pre>getSelectedCells()</pre></a></li>
<li class="method public "><a href="#method_getSharedComponent" title="getSharedComponent :: Get the shared style component for the currently active cell in currently active sheet."><span class="description">Get the shared style component for the currently active cell in currently active sheet.</span><pre>getSharedComponent()</pre></a></li>
<li class="method public "><a href="#method_getStartColor" title="getStartColor :: Get Start Color"><span class="description">Get Start Color</span><pre>getStartColor()</pre></a></li>
<li class="method public "><a href="#method_getStyleArray" title="getStyleArray :: Build style array from subcomponents"><span class="description">Build style array from subcomponents</span><pre>getStyleArray()</pre></a></li>
<li class="method public "><a href="#method_setEndColor" title="setEndColor :: Set End Color"><span class="description">Set End Color</span><pre>setEndColor()</pre></a></li>
<li class="method public "><a href="#method_setFillType" title="setFillType :: Set Fill Type"><span class="description">Set Fill Type</span><pre>setFillType()</pre></a></li>
<li class="method public "><a href="#method_setRotation" title="setRotation :: Set Rotation"><span class="description">Set Rotation</span><pre>setRotation()</pre></a></li>
<li class="method public "><a href="#method_setStartColor" title="setStartColor :: Set Start Color"><span class="description">Set Start Color</span><pre>setStartColor()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header protected">» Protected
                    <ul>
<li class="property protected "><a href="#property__endColor" title="$_endColor :: End color"><span class="description"></span><pre>$_endColor</pre></a></li>
<li class="property protected "><a href="#property__fillType" title="$_fillType :: Fill type"><span class="description"></span><pre>$_fillType</pre></a></li>
<li class="property protected inherited"><a href="#property__isSupervisor" title="$_isSupervisor :: Supervisor?"><span class="description"></span><pre>$_isSupervisor</pre></a></li>
<li class="property protected inherited"><a href="#property__parent" title="$_parent :: Parent."><span class="description"></span><pre>$_parent</pre></a></li>
<li class="property protected "><a href="#property__rotation" title="$_rotation :: Rotation"><span class="description"></span><pre>$_rotation</pre></a></li>
<li class="property protected "><a href="#property__startColor" title="$_startColor :: Start color"><span class="description"></span><pre>$_startColor</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_FILL_GRADIENT_LINEAR" title="FILL_GRADIENT_LINEAR :: "><span class="description">FILL_GRADIENT_LINEAR</span><pre>FILL_GRADIENT_LINEAR</pre></a></li>
<li class="constant  "><a href="#constant_FILL_GRADIENT_PATH" title="FILL_GRADIENT_PATH :: "><span class="description">FILL_GRADIENT_PATH</span><pre>FILL_GRADIENT_PATH</pre></a></li>
<li class="constant  "><a href="#constant_FILL_NONE" title="FILL_NONE :: "><span class="description">FILL_NONE</span><pre>FILL_NONE</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_DARKDOWN" title="FILL_PATTERN_DARKDOWN :: "><span class="description">FILL_PATTERN_DARKDOWN</span><pre>FILL_PATTERN_DARKDOWN</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_DARKGRAY" title="FILL_PATTERN_DARKGRAY :: "><span class="description">FILL_PATTERN_DARKGRAY</span><pre>FILL_PATTERN_DARKGRAY</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_DARKGRID" title="FILL_PATTERN_DARKGRID :: "><span class="description">FILL_PATTERN_DARKGRID</span><pre>FILL_PATTERN_DARKGRID</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_DARKHORIZONTAL" title="FILL_PATTERN_DARKHORIZONTAL :: "><span class="description">FILL_PATTERN_DARKHORIZONTAL</span><pre>FILL_PATTERN_DARKHORIZONTAL</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_DARKTRELLIS" title="FILL_PATTERN_DARKTRELLIS :: "><span class="description">FILL_PATTERN_DARKTRELLIS</span><pre>FILL_PATTERN_DARKTRELLIS</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_DARKUP" title="FILL_PATTERN_DARKUP :: "><span class="description">FILL_PATTERN_DARKUP</span><pre>FILL_PATTERN_DARKUP</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_DARKVERTICAL" title="FILL_PATTERN_DARKVERTICAL :: "><span class="description">FILL_PATTERN_DARKVERTICAL</span><pre>FILL_PATTERN_DARKVERTICAL</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_GRAY0625" title="FILL_PATTERN_GRAY0625 :: "><span class="description">FILL_PATTERN_GRAY0625</span><pre>FILL_PATTERN_GRAY0625</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_GRAY125" title="FILL_PATTERN_GRAY125 :: "><span class="description">FILL_PATTERN_GRAY125</span><pre>FILL_PATTERN_GRAY125</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_LIGHTDOWN" title="FILL_PATTERN_LIGHTDOWN :: "><span class="description">FILL_PATTERN_LIGHTDOWN</span><pre>FILL_PATTERN_LIGHTDOWN</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_LIGHTGRAY" title="FILL_PATTERN_LIGHTGRAY :: "><span class="description">FILL_PATTERN_LIGHTGRAY</span><pre>FILL_PATTERN_LIGHTGRAY</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_LIGHTGRID" title="FILL_PATTERN_LIGHTGRID :: "><span class="description">FILL_PATTERN_LIGHTGRID</span><pre>FILL_PATTERN_LIGHTGRID</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_LIGHTHORIZONTAL" title="FILL_PATTERN_LIGHTHORIZONTAL :: "><span class="description">FILL_PATTERN_LIGHTHORIZONTAL</span><pre>FILL_PATTERN_LIGHTHORIZONTAL</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_LIGHTTRELLIS" title="FILL_PATTERN_LIGHTTRELLIS :: "><span class="description">FILL_PATTERN_LIGHTTRELLIS</span><pre>FILL_PATTERN_LIGHTTRELLIS</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_LIGHTUP" title="FILL_PATTERN_LIGHTUP :: "><span class="description">FILL_PATTERN_LIGHTUP</span><pre>FILL_PATTERN_LIGHTUP</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_LIGHTVERTICAL" title="FILL_PATTERN_LIGHTVERTICAL :: "><span class="description">FILL_PATTERN_LIGHTVERTICAL</span><pre>FILL_PATTERN_LIGHTVERTICAL</pre></a></li>
<li class="constant  "><a href="#constant_FILL_PATTERN_MEDIUMGRAY" title="FILL_PATTERN_MEDIUMGRAY :: "><span class="description">FILL_PATTERN_MEDIUMGRAY</span><pre>FILL_PATTERN_MEDIUMGRAY</pre></a></li>
<li class="constant  "><a href="#constant_FILL_SOLID" title="FILL_SOLID :: "><span class="description">FILL_SOLID</span><pre>FILL_SOLID</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Style_Fill"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Style_Fill.html">PHPExcel_Style_Fill</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Style_Fill</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Style.html">PHPExcel_Style</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___clone"></a><div class="element clickable method public method___clone" data-toggle="collapse" data-target=".method___clone .collapse">
<h2>Implement PHP __clone to create a deep clone, not just a shallow copy.</h2>
<pre>__clone() </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::__clone()</td>
</tr></table>
</div></div>
</div>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_Style_Fill</h2>
<pre>__construct(boolean $isSupervisor, boolean $isConditional) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$isSupervisor</h4>
<code>boolean</code><p>Flag indicating if this is a supervisor or not
								Leave this value at default unless you understand exactly what
									its ramifications are</p></div>
<div class="subelement argument">
<h4>$isConditional</h4>
<code>boolean</code><p>Flag indicating if this is a conditional style or not
								Leave this value at default unless you understand exactly what
									its ramifications are</p></div>
</div></div>
</div>
<a id="method_applyFromArray"></a><div class="element clickable method public method_applyFromArray" data-toggle="collapse" data-target=".method_applyFromArray .collapse">
<h2>Apply styles from array</h2>
<pre>applyFromArray(array $pStyles) : <a href="../classes/PHPExcel_Style_Fill.html">\PHPExcel_Style_Fill</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><pre><code>$objPHPExcel->getActiveSheet()->getStyle('B2')->getFill()->applyFromArray(
    array(
        'type'     => PHPExcel_Style_Fill::FILL_GRADIENT_LINEAR,
        'rotation'   => 0,
        'startcolor' => array(
            'rgb' => '000000'
        ),
        'endcolor'   => array(
            'argb' => 'FFFFFFFF'
        )
    )
);
</code></pre></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pStyles</h4>
<code>array</code><p>Array containing style information</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Fill.html">\PHPExcel_Style_Fill</a></code></div>
</div></div>
</div>
<a id="method_bindParent"></a><div class="element clickable method public method_bindParent" data-toggle="collapse" data-target=".method_bindParent .collapse">
<h2>Bind parent.</h2>
<pre>bindParent(<a href="../classes/PHPExcel.html">\PHPExcel</a> $parent, $parentPropertyName) : <a href="../classes/PHPExcel_Style_Supervisor.html">\PHPExcel_Style_Supervisor</a></pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used for supervisor</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::bindParent()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$parent</h4>
<code><a href="../classes/PHPExcel.html">\PHPExcel</a></code>
</div>
<div class="subelement argument"><h4>$parentPropertyName</h4></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Supervisor.html">\PHPExcel_Style_Supervisor</a></code></div>
</div></div>
</div>
<a id="method_getActiveCell"></a><div class="element clickable method public method_getActiveCell" data-toggle="collapse" data-target=".method_getActiveCell .collapse">
<h2>Get the currently active cell coordinate in currently active sheet.</h2>
<pre>getActiveCell() : string</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used for supervisor</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::getActiveCell()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>E.g. 'A1'</div>
</div></div>
</div>
<a id="method_getActiveSheet"></a><div class="element clickable method public method_getActiveSheet" data-toggle="collapse" data-target=".method_getActiveSheet .collapse">
<h2>Get the currently active sheet.</h2>
<pre>getActiveSheet() : <a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used for supervisor</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::getActiveSheet()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code></div>
</div></div>
</div>
<a id="method_getEndColor"></a><div class="element clickable method public method_getEndColor" data-toggle="collapse" data-target=".method_getEndColor .collapse">
<h2>Get End Color</h2>
<pre>getEndColor() : <a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></code></div>
</div></div>
</div>
<a id="method_getFillType"></a><div class="element clickable method public method_getFillType" data-toggle="collapse" data-target=".method_getFillType .collapse">
<h2>Get Fill Type</h2>
<pre>getFillType() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getHashCode"></a><div class="element clickable method public method_getHashCode" data-toggle="collapse" data-target=".method_getHashCode .collapse">
<h2>Get hash code</h2>
<pre>getHashCode() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Hash code</div>
</div></div>
</div>
<a id="method_getIsSupervisor"></a><div class="element clickable method public method_getIsSupervisor" data-toggle="collapse" data-target=".method_getIsSupervisor .collapse">
<h2>Is this a supervisor or a cell style component?</h2>
<pre>getIsSupervisor() : boolean</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::getIsSupervisor()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getRotation"></a><div class="element clickable method public method_getRotation" data-toggle="collapse" data-target=".method_getRotation .collapse">
<h2>Get Rotation</h2>
<pre>getRotation() : double</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>double</code></div>
</div></div>
</div>
<a id="method_getSelectedCells"></a><div class="element clickable method public method_getSelectedCells" data-toggle="collapse" data-target=".method_getSelectedCells .collapse">
<h2>Get the currently active cell coordinate in currently active sheet.</h2>
<pre>getSelectedCells() : string</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used for supervisor</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::getSelectedCells()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>E.g. 'A1'</div>
</div></div>
</div>
<a id="method_getSharedComponent"></a><div class="element clickable method public method_getSharedComponent" data-toggle="collapse" data-target=".method_getSharedComponent .collapse">
<h2>Get the shared style component for the currently active cell in currently active sheet.</h2>
<pre>getSharedComponent() : <a href="../classes/PHPExcel_Style_Fill.html">\PHPExcel_Style_Fill</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used for style supervisor</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Fill.html">\PHPExcel_Style_Fill</a></code></div>
</div></div>
</div>
<a id="method_getStartColor"></a><div class="element clickable method public method_getStartColor" data-toggle="collapse" data-target=".method_getStartColor .collapse">
<h2>Get Start Color</h2>
<pre>getStartColor() : <a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></code></div>
</div></div>
</div>
<a id="method_getStyleArray"></a><div class="element clickable method public method_getStyleArray" data-toggle="collapse" data-target=".method_getStyleArray .collapse">
<h2>Build style array from subcomponents</h2>
<pre>getStyleArray(array $array) : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$array</h4>
<code>array</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>array</code></div>
</div></div>
</div>
<a id="method_setEndColor"></a><div class="element clickable method public method_setEndColor" data-toggle="collapse" data-target=".method_setEndColor .collapse">
<h2>Set End Color</h2>
<pre>setEndColor(\PHPExcel_Style_Color $pValue) : <a href="../classes/PHPExcel_Style_Fill.html">\PHPExcel_Style_Fill</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code><a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></code>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Fill.html">\PHPExcel_Style_Fill</a></code></div>
</div></div>
</div>
<a id="method_setFillType"></a><div class="element clickable method public method_setFillType" data-toggle="collapse" data-target=".method_setFillType .collapse">
<h2>Set Fill Type</h2>
<pre>setFillType(string $pValue) : <a href="../classes/PHPExcel_Style_Fill.html">\PHPExcel_Style_Fill</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>PHPExcel_Style_Fill fill type</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Fill.html">\PHPExcel_Style_Fill</a></code></div>
</div></div>
</div>
<a id="method_setRotation"></a><div class="element clickable method public method_setRotation" data-toggle="collapse" data-target=".method_setRotation .collapse">
<h2>Set Rotation</h2>
<pre>setRotation(double $pValue) : <a href="../classes/PHPExcel_Style_Fill.html">\PHPExcel_Style_Fill</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>double</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Fill.html">\PHPExcel_Style_Fill</a></code></div>
</div></div>
</div>
<a id="method_setStartColor"></a><div class="element clickable method public method_setStartColor" data-toggle="collapse" data-target=".method_setStartColor .collapse">
<h2>Set Start Color</h2>
<pre>setStartColor(\PHPExcel_Style_Color $pValue) : <a href="../classes/PHPExcel_Style_Fill.html">\PHPExcel_Style_Fill</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code><a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></code>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Fill.html">\PHPExcel_Style_Fill</a></code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__endColor"> </a><div class="element clickable property protected property__endColor" data-toggle="collapse" data-target=".property__endColor .collapse">
<h2></h2>
<pre>$_endColor : <a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__fillType"> </a><div class="element clickable property protected property__fillType" data-toggle="collapse" data-target=".property__fillType .collapse">
<h2></h2>
<pre>$_fillType : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__isSupervisor"> </a><div class="element clickable property protected property__isSupervisor" data-toggle="collapse" data-target=".property__isSupervisor .collapse">
<h2></h2>
<pre>$_isSupervisor : boolean</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::$$_isSupervisor</td>
</tr></table>
</div></div>
</div>
<a id="property__parent"> </a><div class="element clickable property protected property__parent" data-toggle="collapse" data-target=".property__parent .collapse">
<h2></h2>
<pre>$_parent : <a href="../classes/PHPExcel_Style.html">\PHPExcel_Style</a></pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used for supervisor</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::$$_parent</td>
</tr></table>
</div></div>
</div>
<a id="property__rotation"> </a><div class="element clickable property protected property__rotation" data-toggle="collapse" data-target=".property__rotation .collapse">
<h2></h2>
<pre>$_rotation : double</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__startColor"> </a><div class="element clickable property protected property__startColor" data-toggle="collapse" data-target=".property__startColor .collapse">
<h2></h2>
<pre>$_startColor : <a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_FILL_GRADIENT_LINEAR"> </a><div class="element clickable constant  constant_FILL_GRADIENT_LINEAR" data-toggle="collapse" data-target=".constant_FILL_GRADIENT_LINEAR .collapse">
<h2>FILL_GRADIENT_LINEAR</h2>
<pre>FILL_GRADIENT_LINEAR </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_GRADIENT_PATH"> </a><div class="element clickable constant  constant_FILL_GRADIENT_PATH" data-toggle="collapse" data-target=".constant_FILL_GRADIENT_PATH .collapse">
<h2>FILL_GRADIENT_PATH</h2>
<pre>FILL_GRADIENT_PATH </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_NONE"> </a><div class="element clickable constant  constant_FILL_NONE" data-toggle="collapse" data-target=".constant_FILL_NONE .collapse">
<h2>FILL_NONE</h2>
<pre>FILL_NONE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_DARKDOWN"> </a><div class="element clickable constant  constant_FILL_PATTERN_DARKDOWN" data-toggle="collapse" data-target=".constant_FILL_PATTERN_DARKDOWN .collapse">
<h2>FILL_PATTERN_DARKDOWN</h2>
<pre>FILL_PATTERN_DARKDOWN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_DARKGRAY"> </a><div class="element clickable constant  constant_FILL_PATTERN_DARKGRAY" data-toggle="collapse" data-target=".constant_FILL_PATTERN_DARKGRAY .collapse">
<h2>FILL_PATTERN_DARKGRAY</h2>
<pre>FILL_PATTERN_DARKGRAY </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_DARKGRID"> </a><div class="element clickable constant  constant_FILL_PATTERN_DARKGRID" data-toggle="collapse" data-target=".constant_FILL_PATTERN_DARKGRID .collapse">
<h2>FILL_PATTERN_DARKGRID</h2>
<pre>FILL_PATTERN_DARKGRID </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_DARKHORIZONTAL"> </a><div class="element clickable constant  constant_FILL_PATTERN_DARKHORIZONTAL" data-toggle="collapse" data-target=".constant_FILL_PATTERN_DARKHORIZONTAL .collapse">
<h2>FILL_PATTERN_DARKHORIZONTAL</h2>
<pre>FILL_PATTERN_DARKHORIZONTAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_DARKTRELLIS"> </a><div class="element clickable constant  constant_FILL_PATTERN_DARKTRELLIS" data-toggle="collapse" data-target=".constant_FILL_PATTERN_DARKTRELLIS .collapse">
<h2>FILL_PATTERN_DARKTRELLIS</h2>
<pre>FILL_PATTERN_DARKTRELLIS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_DARKUP"> </a><div class="element clickable constant  constant_FILL_PATTERN_DARKUP" data-toggle="collapse" data-target=".constant_FILL_PATTERN_DARKUP .collapse">
<h2>FILL_PATTERN_DARKUP</h2>
<pre>FILL_PATTERN_DARKUP </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_DARKVERTICAL"> </a><div class="element clickable constant  constant_FILL_PATTERN_DARKVERTICAL" data-toggle="collapse" data-target=".constant_FILL_PATTERN_DARKVERTICAL .collapse">
<h2>FILL_PATTERN_DARKVERTICAL</h2>
<pre>FILL_PATTERN_DARKVERTICAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_GRAY0625"> </a><div class="element clickable constant  constant_FILL_PATTERN_GRAY0625" data-toggle="collapse" data-target=".constant_FILL_PATTERN_GRAY0625 .collapse">
<h2>FILL_PATTERN_GRAY0625</h2>
<pre>FILL_PATTERN_GRAY0625 </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_GRAY125"> </a><div class="element clickable constant  constant_FILL_PATTERN_GRAY125" data-toggle="collapse" data-target=".constant_FILL_PATTERN_GRAY125 .collapse">
<h2>FILL_PATTERN_GRAY125</h2>
<pre>FILL_PATTERN_GRAY125 </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_LIGHTDOWN"> </a><div class="element clickable constant  constant_FILL_PATTERN_LIGHTDOWN" data-toggle="collapse" data-target=".constant_FILL_PATTERN_LIGHTDOWN .collapse">
<h2>FILL_PATTERN_LIGHTDOWN</h2>
<pre>FILL_PATTERN_LIGHTDOWN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_LIGHTGRAY"> </a><div class="element clickable constant  constant_FILL_PATTERN_LIGHTGRAY" data-toggle="collapse" data-target=".constant_FILL_PATTERN_LIGHTGRAY .collapse">
<h2>FILL_PATTERN_LIGHTGRAY</h2>
<pre>FILL_PATTERN_LIGHTGRAY </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_LIGHTGRID"> </a><div class="element clickable constant  constant_FILL_PATTERN_LIGHTGRID" data-toggle="collapse" data-target=".constant_FILL_PATTERN_LIGHTGRID .collapse">
<h2>FILL_PATTERN_LIGHTGRID</h2>
<pre>FILL_PATTERN_LIGHTGRID </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_LIGHTHORIZONTAL"> </a><div class="element clickable constant  constant_FILL_PATTERN_LIGHTHORIZONTAL" data-toggle="collapse" data-target=".constant_FILL_PATTERN_LIGHTHORIZONTAL .collapse">
<h2>FILL_PATTERN_LIGHTHORIZONTAL</h2>
<pre>FILL_PATTERN_LIGHTHORIZONTAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_LIGHTTRELLIS"> </a><div class="element clickable constant  constant_FILL_PATTERN_LIGHTTRELLIS" data-toggle="collapse" data-target=".constant_FILL_PATTERN_LIGHTTRELLIS .collapse">
<h2>FILL_PATTERN_LIGHTTRELLIS</h2>
<pre>FILL_PATTERN_LIGHTTRELLIS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_LIGHTUP"> </a><div class="element clickable constant  constant_FILL_PATTERN_LIGHTUP" data-toggle="collapse" data-target=".constant_FILL_PATTERN_LIGHTUP .collapse">
<h2>FILL_PATTERN_LIGHTUP</h2>
<pre>FILL_PATTERN_LIGHTUP </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_LIGHTVERTICAL"> </a><div class="element clickable constant  constant_FILL_PATTERN_LIGHTVERTICAL" data-toggle="collapse" data-target=".constant_FILL_PATTERN_LIGHTVERTICAL .collapse">
<h2>FILL_PATTERN_LIGHTVERTICAL</h2>
<pre>FILL_PATTERN_LIGHTVERTICAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_PATTERN_MEDIUMGRAY"> </a><div class="element clickable constant  constant_FILL_PATTERN_MEDIUMGRAY" data-toggle="collapse" data-target=".constant_FILL_PATTERN_MEDIUMGRAY .collapse">
<h2>FILL_PATTERN_MEDIUMGRAY</h2>
<pre>FILL_PATTERN_MEDIUMGRAY </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_FILL_SOLID"> </a><div class="element clickable constant  constant_FILL_SOLID" data-toggle="collapse" data-target=".constant_FILL_SOLID .collapse">
<h2>FILL_SOLID</h2>
<pre>FILL_SOLID </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:37Z.<br></footer></div>
</div>
</body>
</html>
