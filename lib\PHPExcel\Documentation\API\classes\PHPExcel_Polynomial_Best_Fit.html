<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Polynomial_Best_Fit</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___construct" title="__construct :: Define the regression and calculate the goodness of fit for a set of X and Y data values"><span class="description">Define the regression and calculate the goodness of fit for a set of X and Y data values</span><pre>__construct()</pre></a></li>
<li class="method public inherited"><a href="#method_getBestFitType" title="getBestFitType :: "><span class="description">getBestFitType()
        </span><pre>getBestFitType()</pre></a></li>
<li class="method public "><a href="#method_getCoefficients" title="getCoefficients :: "><span class="description">getCoefficients()
        </span><pre>getCoefficients()</pre></a></li>
<li class="method public inherited"><a href="#method_getCorrelation" title="getCorrelation :: "><span class="description">getCorrelation()
        </span><pre>getCorrelation()</pre></a></li>
<li class="method public inherited"><a href="#method_getCovariance" title="getCovariance :: "><span class="description">getCovariance()
        </span><pre>getCovariance()</pre></a></li>
<li class="method public inherited"><a href="#method_getDFResiduals" title="getDFResiduals :: "><span class="description">getDFResiduals()
        </span><pre>getDFResiduals()</pre></a></li>
<li class="method public "><a href="#method_getEquation" title="getEquation :: Return the Equation of the best-fit line"><span class="description">Return the Equation of the best-fit line</span><pre>getEquation()</pre></a></li>
<li class="method public inherited"><a href="#method_getError" title="getError :: "><span class="description">getError()
        </span><pre>getError()</pre></a></li>
<li class="method public inherited"><a href="#method_getF" title="getF :: "><span class="description">getF()
        </span><pre>getF()</pre></a></li>
<li class="method public inherited"><a href="#method_getGoodnessOfFit" title="getGoodnessOfFit :: Return the goodness of fit for this regression"><span class="description">Return the goodness of fit for this regression</span><pre>getGoodnessOfFit()</pre></a></li>
<li class="method public inherited"><a href="#method_getGoodnessOfFitPercent" title="getGoodnessOfFitPercent :: "><span class="description">getGoodnessOfFitPercent()
        </span><pre>getGoodnessOfFitPercent()</pre></a></li>
<li class="method public inherited"><a href="#method_getIntersect" title="getIntersect :: Return the Value of X where it intersects Y = 0"><span class="description">Return the Value of X where it intersects Y = 0</span><pre>getIntersect()</pre></a></li>
<li class="method public inherited"><a href="#method_getIntersectSE" title="getIntersectSE :: Return the standard error of the Intersect"><span class="description">Return the standard error of the Intersect</span><pre>getIntersectSE()</pre></a></li>
<li class="method public "><a href="#method_getOrder" title="getOrder :: Return the order of this polynomial"><span class="description">Return the order of this polynomial</span><pre>getOrder()</pre></a></li>
<li class="method public inherited"><a href="#method_getSSRegression" title="getSSRegression :: "><span class="description">getSSRegression()
        </span><pre>getSSRegression()</pre></a></li>
<li class="method public inherited"><a href="#method_getSSResiduals" title="getSSResiduals :: "><span class="description">getSSResiduals()
        </span><pre>getSSResiduals()</pre></a></li>
<li class="method public "><a href="#method_getSlope" title="getSlope :: Return the Slope of the line"><span class="description">Return the Slope of the line</span><pre>getSlope()</pre></a></li>
<li class="method public inherited"><a href="#method_getSlopeSE" title="getSlopeSE :: Return the standard error of the Slope"><span class="description">Return the standard error of the Slope</span><pre>getSlopeSE()</pre></a></li>
<li class="method public inherited"><a href="#method_getStdevOfResiduals" title="getStdevOfResiduals :: Return the standard deviation of the residuals for this regression"><span class="description">Return the standard deviation of the residuals for this regression</span><pre>getStdevOfResiduals()</pre></a></li>
<li class="method public "><a href="#method_getValueOfXForY" title="getValueOfXForY :: Return the X-Value for a specified value of Y"><span class="description">Return the X-Value for a specified value of Y</span><pre>getValueOfXForY()</pre></a></li>
<li class="method public "><a href="#method_getValueOfYForX" title="getValueOfYForX :: Return the Y-Value for a specified value of X"><span class="description">Return the Y-Value for a specified value of X</span><pre>getValueOfYForX()</pre></a></li>
<li class="method public inherited"><a href="#method_getXValues" title="getXValues :: Return the original set of X-Values"><span class="description">Return the original set of X-Values</span><pre>getXValues()</pre></a></li>
<li class="method public inherited"><a href="#method_getYBestFitValues" title="getYBestFitValues :: "><span class="description">getYBestFitValues()
        </span><pre>getYBestFitValues()</pre></a></li>
</ul>
</li>
<li class="nav-header protected">» Protected
                    <ul>
<li class="method protected inherited"><a href="#method__calculateGoodnessOfFit" title="_calculateGoodnessOfFit :: "><span class="description">_calculateGoodnessOfFit()
        </span><pre>_calculateGoodnessOfFit()</pre></a></li>
<li class="method protected inherited"><a href="#method__leastSquareFit" title="_leastSquareFit :: "><span class="description">_leastSquareFit()
        </span><pre>_leastSquareFit()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul><li class="method private "><a href="#method__polynomial_regression" title="_polynomial_regression :: Execute the regression and calculate the goodness of fit for a set of X and Y data values"><span class="description">Execute the regression and calculate the goodness of fit for a set of X and Y data values</span><pre>_polynomial_regression()</pre></a></li></ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header protected">» Protected
                    <ul>
<li class="property protected inherited"><a href="#property__DFResiduals" title="$_DFResiduals :: "><span class="description"></span><pre>$_DFResiduals</pre></a></li>
<li class="property protected inherited"><a href="#property__F" title="$_F :: "><span class="description"></span><pre>$_F</pre></a></li>
<li class="property protected inherited"><a href="#property__SSRegression" title="$_SSRegression :: "><span class="description"></span><pre>$_SSRegression</pre></a></li>
<li class="property protected inherited"><a href="#property__SSResiduals" title="$_SSResiduals :: "><span class="description"></span><pre>$_SSResiduals</pre></a></li>
<li class="property protected inherited"><a href="#property__Xoffset" title="$_Xoffset :: "><span class="description"></span><pre>$_Xoffset</pre></a></li>
<li class="property protected inherited"><a href="#property__Yoffset" title="$_Yoffset :: "><span class="description"></span><pre>$_Yoffset</pre></a></li>
<li class="property protected inherited"><a href="#property__adjustToZero" title="$_adjustToZero :: Flag indicating whether values should be adjusted to Y=0"><span class="description"></span><pre>$_adjustToZero</pre></a></li>
<li class="property protected "><a href="#property__bestFitType" title="$_bestFitType :: Algorithm type to use for best-fit
(Name of this trend class)"><span class="description"></span><pre>$_bestFitType</pre></a></li>
<li class="property protected inherited"><a href="#property__correlation" title="$_correlation :: "><span class="description"></span><pre>$_correlation</pre></a></li>
<li class="property protected inherited"><a href="#property__covariance" title="$_covariance :: "><span class="description"></span><pre>$_covariance</pre></a></li>
<li class="property protected inherited"><a href="#property__error" title="$_error :: Indicator flag for a calculation error"><span class="description"></span><pre>$_error</pre></a></li>
<li class="property protected inherited"><a href="#property__goodnessOfFit" title="$_goodnessOfFit :: "><span class="description"></span><pre>$_goodnessOfFit</pre></a></li>
<li class="property protected inherited"><a href="#property__intersect" title="$_intersect :: "><span class="description"></span><pre>$_intersect</pre></a></li>
<li class="property protected inherited"><a href="#property__intersectSE" title="$_intersectSE :: "><span class="description"></span><pre>$_intersectSE</pre></a></li>
<li class="property protected "><a href="#property__order" title="$_order :: Polynomial order"><span class="description"></span><pre>$_order</pre></a></li>
<li class="property protected inherited"><a href="#property__slope" title="$_slope :: "><span class="description"></span><pre>$_slope</pre></a></li>
<li class="property protected inherited"><a href="#property__slopeSE" title="$_slopeSE :: "><span class="description"></span><pre>$_slopeSE</pre></a></li>
<li class="property protected inherited"><a href="#property__stdevOfResiduals" title="$_stdevOfResiduals :: "><span class="description"></span><pre>$_stdevOfResiduals</pre></a></li>
<li class="property protected inherited"><a href="#property__valueCount" title="$_valueCount :: Number of entries in the sets of x- and y-value arrays"><span class="description"></span><pre>$_valueCount</pre></a></li>
<li class="property protected inherited"><a href="#property__xValues" title="$_xValues :: X-value dataseries of values"><span class="description"></span><pre>$_xValues</pre></a></li>
<li class="property protected inherited"><a href="#property__yBestFitValues" title="$_yBestFitValues :: Y-value series of best-fit values"><span class="description"></span><pre>$_yBestFitValues</pre></a></li>
<li class="property protected inherited"><a href="#property__yValues" title="$_yValues :: Y-value dataseries of values"><span class="description"></span><pre>$_yValues</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Polynomial_Best_Fit"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Polynomial_Best_Fit.html">PHPExcel_Polynomial_Best_Fit</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Polynomial_Best_Fit</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Shared.Trend.html">PHPExcel_Shared_Trend</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Define the regression and calculate the goodness of fit for a set of X and Y data values</h2>
<pre>__construct(int $order, float[] $yValues, float[] $xValues, boolean $const) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$order</h4>
<code>int</code><p>Order of Polynomial for this regression</p></div>
<div class="subelement argument">
<h4>$yValues</h4>
<code>float[]</code><p>The set of Y-values for this regression</p>
</div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>float[]</code><p>The set of X-values for this regression</p>
</div>
<div class="subelement argument">
<h4>$const</h4>
<code>boolean</code>
</div>
</div></div>
</div>
<a id="method_getBestFitType"></a><div class="element clickable method public method_getBestFitType" data-toggle="collapse" data-target=".method_getBestFitType .collapse">
<h2>getBestFitType()
        </h2>
<pre>getBestFitType() </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getBestFitType()</td>
</tr></table>
</div></div>
</div>
<a id="method_getCoefficients"></a><div class="element clickable method public method_getCoefficients" data-toggle="collapse" data-target=".method_getCoefficients .collapse">
<h2>getCoefficients()
        </h2>
<pre>getCoefficients($dp) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$dp</h4></div>
</div></div>
</div>
<a id="method_getCorrelation"></a><div class="element clickable method public method_getCorrelation" data-toggle="collapse" data-target=".method_getCorrelation .collapse">
<h2>getCorrelation()
        </h2>
<pre>getCorrelation($dp) </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getCorrelation()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$dp</h4></div>
</div></div>
</div>
<a id="method_getCovariance"></a><div class="element clickable method public method_getCovariance" data-toggle="collapse" data-target=".method_getCovariance .collapse">
<h2>getCovariance()
        </h2>
<pre>getCovariance($dp) </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getCovariance()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$dp</h4></div>
</div></div>
</div>
<a id="method_getDFResiduals"></a><div class="element clickable method public method_getDFResiduals" data-toggle="collapse" data-target=".method_getDFResiduals .collapse">
<h2>getDFResiduals()
        </h2>
<pre>getDFResiduals($dp) </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getDFResiduals()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$dp</h4></div>
</div></div>
</div>
<a id="method_getEquation"></a><div class="element clickable method public method_getEquation" data-toggle="collapse" data-target=".method_getEquation .collapse">
<h2>Return the Equation of the best-fit line</h2>
<pre>getEquation(int $dp) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dp</h4>
<code>int</code><p>Number of places of decimal precision to display</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getError"></a><div class="element clickable method public method_getError" data-toggle="collapse" data-target=".method_getError .collapse">
<h2>getError()
        </h2>
<pre>getError() </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getError()</td>
</tr></table>
</div></div>
</div>
<a id="method_getF"></a><div class="element clickable method public method_getF" data-toggle="collapse" data-target=".method_getF .collapse">
<h2>getF()
        </h2>
<pre>getF($dp) </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getF()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$dp</h4></div>
</div></div>
</div>
<a id="method_getGoodnessOfFit"></a><div class="element clickable method public method_getGoodnessOfFit" data-toggle="collapse" data-target=".method_getGoodnessOfFit .collapse">
<h2>Return the goodness of fit for this regression</h2>
<pre>getGoodnessOfFit(int $dp) : float</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getGoodnessOfFit()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dp</h4>
<code>int</code><p>Number of places of decimal precision to return</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_getGoodnessOfFitPercent"></a><div class="element clickable method public method_getGoodnessOfFitPercent" data-toggle="collapse" data-target=".method_getGoodnessOfFitPercent .collapse">
<h2>getGoodnessOfFitPercent()
        </h2>
<pre>getGoodnessOfFitPercent($dp) </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getGoodnessOfFitPercent()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$dp</h4></div>
</div></div>
</div>
<a id="method_getIntersect"></a><div class="element clickable method public method_getIntersect" data-toggle="collapse" data-target=".method_getIntersect .collapse">
<h2>Return the Value of X where it intersects Y = 0</h2>
<pre>getIntersect(int $dp) : string</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getIntersect()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dp</h4>
<code>int</code><p>Number of places of decimal precision to display</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getIntersectSE"></a><div class="element clickable method public method_getIntersectSE" data-toggle="collapse" data-target=".method_getIntersectSE .collapse">
<h2>Return the standard error of the Intersect</h2>
<pre>getIntersectSE(int $dp) : string</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getIntersectSE()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dp</h4>
<code>int</code><p>Number of places of decimal precision to display</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getOrder"></a><div class="element clickable method public method_getOrder" data-toggle="collapse" data-target=".method_getOrder .collapse">
<h2>Return the order of this polynomial</h2>
<pre>getOrder() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_getSSRegression"></a><div class="element clickable method public method_getSSRegression" data-toggle="collapse" data-target=".method_getSSRegression .collapse">
<h2>getSSRegression()
        </h2>
<pre>getSSRegression($dp) </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getSSRegression()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$dp</h4></div>
</div></div>
</div>
<a id="method_getSSResiduals"></a><div class="element clickable method public method_getSSResiduals" data-toggle="collapse" data-target=".method_getSSResiduals .collapse">
<h2>getSSResiduals()
        </h2>
<pre>getSSResiduals($dp) </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getSSResiduals()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$dp</h4></div>
</div></div>
</div>
<a id="method_getSlope"></a><div class="element clickable method public method_getSlope" data-toggle="collapse" data-target=".method_getSlope .collapse">
<h2>Return the Slope of the line</h2>
<pre>getSlope(int $dp) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dp</h4>
<code>int</code><p>Number of places of decimal precision to display</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getSlopeSE"></a><div class="element clickable method public method_getSlopeSE" data-toggle="collapse" data-target=".method_getSlopeSE .collapse">
<h2>Return the standard error of the Slope</h2>
<pre>getSlopeSE(int $dp) : string</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getSlopeSE()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dp</h4>
<code>int</code><p>Number of places of decimal precision to display</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getStdevOfResiduals"></a><div class="element clickable method public method_getStdevOfResiduals" data-toggle="collapse" data-target=".method_getStdevOfResiduals .collapse">
<h2>Return the standard deviation of the residuals for this regression</h2>
<pre>getStdevOfResiduals(int $dp) : float</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getStdevOfResiduals()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dp</h4>
<code>int</code><p>Number of places of decimal precision to return</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_getValueOfXForY"></a><div class="element clickable method public method_getValueOfXForY" data-toggle="collapse" data-target=".method_getValueOfXForY .collapse">
<h2>Return the X-Value for a specified value of Y</h2>
<pre>getValueOfXForY(float $yValue) : float</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$yValue</h4>
<code>float</code><p>Y-Value</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>X-Value</div>
</div></div>
</div>
<a id="method_getValueOfYForX"></a><div class="element clickable method public method_getValueOfYForX" data-toggle="collapse" data-target=".method_getValueOfYForX .collapse">
<h2>Return the Y-Value for a specified value of X</h2>
<pre>getValueOfYForX(float $xValue) : float</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$xValue</h4>
<code>float</code><p>X-Value</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>Y-Value</div>
</div></div>
</div>
<a id="method_getXValues"></a><div class="element clickable method public method_getXValues" data-toggle="collapse" data-target=".method_getXValues .collapse">
<h2>Return the original set of X-Values</h2>
<pre>getXValues() : float[]</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getXValues()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response">
<code>float[]</code>X-Values</div>
</div></div>
</div>
<a id="method_getYBestFitValues"></a><div class="element clickable method public method_getYBestFitValues" data-toggle="collapse" data-target=".method_getYBestFitValues .collapse">
<h2>getYBestFitValues()
        </h2>
<pre>getYBestFitValues() </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::getYBestFitValues()</td>
</tr></table>
</div></div>
</div>
<a id="method__calculateGoodnessOfFit"></a><div class="element clickable method protected method__calculateGoodnessOfFit" data-toggle="collapse" data-target=".method__calculateGoodnessOfFit .collapse">
<h2>_calculateGoodnessOfFit()
        </h2>
<pre>_calculateGoodnessOfFit($sumX, $sumY, $sumX2, $sumY2, $sumXY, $meanX, $meanY, $const) </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::_calculateGoodnessOfFit()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$sumX</h4></div>
<div class="subelement argument"><h4>$sumY</h4></div>
<div class="subelement argument"><h4>$sumX2</h4></div>
<div class="subelement argument"><h4>$sumY2</h4></div>
<div class="subelement argument"><h4>$sumXY</h4></div>
<div class="subelement argument"><h4>$meanX</h4></div>
<div class="subelement argument"><h4>$meanY</h4></div>
<div class="subelement argument"><h4>$const</h4></div>
</div></div>
</div>
<a id="method__leastSquareFit"></a><div class="element clickable method protected method__leastSquareFit" data-toggle="collapse" data-target=".method__leastSquareFit .collapse">
<h2>_leastSquareFit()
        </h2>
<pre>_leastSquareFit($yValues, $xValues, $const) </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::_leastSquareFit()</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$yValues</h4></div>
<div class="subelement argument"><h4>$xValues</h4></div>
<div class="subelement argument"><h4>$const</h4></div>
</div></div>
</div>
<a id="method__polynomial_regression"></a><div class="element clickable method private method__polynomial_regression" data-toggle="collapse" data-target=".method__polynomial_regression .collapse">
<h2>Execute the regression and calculate the goodness of fit for a set of X and Y data values</h2>
<pre>_polynomial_regression(int $order, float[] $yValues, float[] $xValues, boolean $const) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$order</h4>
<code>int</code><p>Order of Polynomial for this regression</p></div>
<div class="subelement argument">
<h4>$yValues</h4>
<code>float[]</code><p>The set of Y-values for this regression</p>
</div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>float[]</code><p>The set of X-values for this regression</p>
</div>
<div class="subelement argument">
<h4>$const</h4>
<code>boolean</code>
</div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__DFResiduals"> </a><div class="element clickable property protected property__DFResiduals" data-toggle="collapse" data-target=".property__DFResiduals .collapse">
<h2></h2>
<pre>$_DFResiduals </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_DFResiduals</td>
</tr></table>
</div></div>
</div>
<a id="property__F"> </a><div class="element clickable property protected property__F" data-toggle="collapse" data-target=".property__F .collapse">
<h2></h2>
<pre>$_F </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_F</td>
</tr></table>
</div></div>
</div>
<a id="property__SSRegression"> </a><div class="element clickable property protected property__SSRegression" data-toggle="collapse" data-target=".property__SSRegression .collapse">
<h2></h2>
<pre>$_SSRegression </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_SSRegression</td>
</tr></table>
</div></div>
</div>
<a id="property__SSResiduals"> </a><div class="element clickable property protected property__SSResiduals" data-toggle="collapse" data-target=".property__SSResiduals .collapse">
<h2></h2>
<pre>$_SSResiduals </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_SSResiduals</td>
</tr></table>
</div></div>
</div>
<a id="property__Xoffset"> </a><div class="element clickable property protected property__Xoffset" data-toggle="collapse" data-target=".property__Xoffset .collapse">
<h2></h2>
<pre>$_Xoffset </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_Xoffset</td>
</tr></table>
</div></div>
</div>
<a id="property__Yoffset"> </a><div class="element clickable property protected property__Yoffset" data-toggle="collapse" data-target=".property__Yoffset .collapse">
<h2></h2>
<pre>$_Yoffset </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_Yoffset</td>
</tr></table>
</div></div>
</div>
<a id="property__adjustToZero"> </a><div class="element clickable property protected property__adjustToZero" data-toggle="collapse" data-target=".property__adjustToZero .collapse">
<h2></h2>
<pre>$_adjustToZero : boolean</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_adjustToZero</td>
</tr></table>
</div></div>
</div>
<a id="property__bestFitType"> </a><div class="element clickable property protected property__bestFitType" data-toggle="collapse" data-target=".property__bestFitType .collapse">
<h2></h2>
<pre>$_bestFitType : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__correlation"> </a><div class="element clickable property protected property__correlation" data-toggle="collapse" data-target=".property__correlation .collapse">
<h2></h2>
<pre>$_correlation </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_correlation</td>
</tr></table>
</div></div>
</div>
<a id="property__covariance"> </a><div class="element clickable property protected property__covariance" data-toggle="collapse" data-target=".property__covariance .collapse">
<h2></h2>
<pre>$_covariance </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_covariance</td>
</tr></table>
</div></div>
</div>
<a id="property__error"> </a><div class="element clickable property protected property__error" data-toggle="collapse" data-target=".property__error .collapse">
<h2></h2>
<pre>$_error : boolean</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_error</td>
</tr></table>
</div></div>
</div>
<a id="property__goodnessOfFit"> </a><div class="element clickable property protected property__goodnessOfFit" data-toggle="collapse" data-target=".property__goodnessOfFit .collapse">
<h2></h2>
<pre>$_goodnessOfFit </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_goodnessOfFit</td>
</tr></table>
</div></div>
</div>
<a id="property__intersect"> </a><div class="element clickable property protected property__intersect" data-toggle="collapse" data-target=".property__intersect .collapse">
<h2></h2>
<pre>$_intersect </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_intersect</td>
</tr></table>
</div></div>
</div>
<a id="property__intersectSE"> </a><div class="element clickable property protected property__intersectSE" data-toggle="collapse" data-target=".property__intersectSE .collapse">
<h2></h2>
<pre>$_intersectSE </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_intersectSE</td>
</tr></table>
</div></div>
</div>
<a id="property__order"> </a><div class="element clickable property protected property__order" data-toggle="collapse" data-target=".property__order .collapse">
<h2></h2>
<pre>$_order : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>protected</th>
<td></td>
</tr></table>
</div></div>
</div>
<a id="property__slope"> </a><div class="element clickable property protected property__slope" data-toggle="collapse" data-target=".property__slope .collapse">
<h2></h2>
<pre>$_slope </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_slope</td>
</tr></table>
</div></div>
</div>
<a id="property__slopeSE"> </a><div class="element clickable property protected property__slopeSE" data-toggle="collapse" data-target=".property__slopeSE .collapse">
<h2></h2>
<pre>$_slopeSE </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_slopeSE</td>
</tr></table>
</div></div>
</div>
<a id="property__stdevOfResiduals"> </a><div class="element clickable property protected property__stdevOfResiduals" data-toggle="collapse" data-target=".property__stdevOfResiduals .collapse">
<h2></h2>
<pre>$_stdevOfResiduals </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_stdevOfResiduals</td>
</tr></table>
</div></div>
</div>
<a id="property__valueCount"> </a><div class="element clickable property protected property__valueCount" data-toggle="collapse" data-target=".property__valueCount .collapse">
<h2></h2>
<pre>$_valueCount : int</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_valueCount</td>
</tr></table>
</div></div>
</div>
<a id="property__xValues"> </a><div class="element clickable property protected property__xValues" data-toggle="collapse" data-target=".property__xValues .collapse">
<h2></h2>
<pre>$_xValues : float[]</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_xValues</td>
</tr></table>
</div></div>
</div>
<a id="property__yBestFitValues"> </a><div class="element clickable property protected property__yBestFitValues" data-toggle="collapse" data-target=".property__yBestFitValues .collapse">
<h2></h2>
<pre>$_yBestFitValues : float[]</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_yBestFitValues</td>
</tr></table>
</div></div>
</div>
<a id="property__yValues"> </a><div class="element clickable property protected property__yValues" data-toggle="collapse" data-target=".property__yValues .collapse">
<h2></h2>
<pre>$_yValues : float[]</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Best_Fit::$$_yValues</td>
</tr></table>
</div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:37Z.<br></footer></div>
</div>
</body>
</html>
