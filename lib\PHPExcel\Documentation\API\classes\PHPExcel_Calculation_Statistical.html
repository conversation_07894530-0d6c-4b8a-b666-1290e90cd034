<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Calculation_Statistical</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_AVEDEV" title="AVEDEV :: AVEDEV"><span class="description">AVEDEV</span><pre>AVEDEV()</pre></a></li>
<li class="method public "><a href="#method_AVERAGE" title="AVERAGE :: AVERAGE"><span class="description">AVERAGE</span><pre>AVERAGE()</pre></a></li>
<li class="method public "><a href="#method_AVERAGEA" title="AVERAGEA :: AVERAGEA"><span class="description">AVERAGEA</span><pre>AVERAGEA()</pre></a></li>
<li class="method public "><a href="#method_AVERAGEIF" title="AVERAGEIF :: AVERAGEIF"><span class="description">AVERAGEIF</span><pre>AVERAGEIF()</pre></a></li>
<li class="method public "><a href="#method_BETADIST" title="BETADIST :: BETADIST"><span class="description">BETADIST</span><pre>BETADIST()</pre></a></li>
<li class="method public "><a href="#method_BETAINV" title="BETAINV :: BETAINV"><span class="description">BETAINV</span><pre>BETAINV()</pre></a></li>
<li class="method public "><a href="#method_BINOMDIST" title="BINOMDIST :: BINOMDIST"><span class="description">BINOMDIST</span><pre>BINOMDIST()</pre></a></li>
<li class="method public "><a href="#method_CHIDIST" title="CHIDIST :: CHIDIST"><span class="description">CHIDIST</span><pre>CHIDIST()</pre></a></li>
<li class="method public "><a href="#method_CHIINV" title="CHIINV :: CHIINV"><span class="description">CHIINV</span><pre>CHIINV()</pre></a></li>
<li class="method public "><a href="#method_CONFIDENCE" title="CONFIDENCE :: CONFIDENCE"><span class="description">CONFIDENCE</span><pre>CONFIDENCE()</pre></a></li>
<li class="method public "><a href="#method_CORREL" title="CORREL :: CORREL"><span class="description">CORREL</span><pre>CORREL()</pre></a></li>
<li class="method public "><a href="#method_COUNT" title="COUNT :: COUNT"><span class="description">COUNT</span><pre>COUNT()</pre></a></li>
<li class="method public "><a href="#method_COUNTA" title="COUNTA :: COUNTA"><span class="description">COUNTA</span><pre>COUNTA()</pre></a></li>
<li class="method public "><a href="#method_COUNTBLANK" title="COUNTBLANK :: COUNTBLANK"><span class="description">COUNTBLANK</span><pre>COUNTBLANK()</pre></a></li>
<li class="method public "><a href="#method_COUNTIF" title="COUNTIF :: COUNTIF"><span class="description">COUNTIF</span><pre>COUNTIF()</pre></a></li>
<li class="method public "><a href="#method_COVAR" title="COVAR :: COVAR"><span class="description">COVAR</span><pre>COVAR()</pre></a></li>
<li class="method public "><a href="#method_CRITBINOM" title="CRITBINOM :: CRITBINOM"><span class="description">CRITBINOM</span><pre>CRITBINOM()</pre></a></li>
<li class="method public "><a href="#method_DEVSQ" title="DEVSQ :: DEVSQ"><span class="description">DEVSQ</span><pre>DEVSQ()</pre></a></li>
<li class="method public "><a href="#method_EXPONDIST" title="EXPONDIST :: EXPONDIST"><span class="description">EXPONDIST</span><pre>EXPONDIST()</pre></a></li>
<li class="method public "><a href="#method_FISHER" title="FISHER :: FISHER"><span class="description">FISHER</span><pre>FISHER()</pre></a></li>
<li class="method public "><a href="#method_FISHERINV" title="FISHERINV :: FISHERINV"><span class="description">FISHERINV</span><pre>FISHERINV()</pre></a></li>
<li class="method public "><a href="#method_FORECAST" title="FORECAST :: FORECAST"><span class="description">FORECAST</span><pre>FORECAST()</pre></a></li>
<li class="method public "><a href="#method_GAMMADIST" title="GAMMADIST :: GAMMADIST"><span class="description">GAMMADIST</span><pre>GAMMADIST()</pre></a></li>
<li class="method public "><a href="#method_GAMMAINV" title="GAMMAINV :: GAMMAINV"><span class="description">GAMMAINV</span><pre>GAMMAINV()</pre></a></li>
<li class="method public "><a href="#method_GAMMALN" title="GAMMALN :: GAMMALN"><span class="description">GAMMALN</span><pre>GAMMALN()</pre></a></li>
<li class="method public "><a href="#method_GEOMEAN" title="GEOMEAN :: GEOMEAN"><span class="description">GEOMEAN</span><pre>GEOMEAN()</pre></a></li>
<li class="method public "><a href="#method_GROWTH" title="GROWTH :: GROWTH"><span class="description">GROWTH</span><pre>GROWTH()</pre></a></li>
<li class="method public "><a href="#method_HARMEAN" title="HARMEAN :: HARMEAN"><span class="description">HARMEAN</span><pre>HARMEAN()</pre></a></li>
<li class="method public "><a href="#method_HYPGEOMDIST" title="HYPGEOMDIST :: HYPGEOMDIST"><span class="description">HYPGEOMDIST</span><pre>HYPGEOMDIST()</pre></a></li>
<li class="method public "><a href="#method_INTERCEPT" title="INTERCEPT :: INTERCEPT"><span class="description">INTERCEPT</span><pre>INTERCEPT()</pre></a></li>
<li class="method public "><a href="#method_KURT" title="KURT :: KURT"><span class="description">KURT</span><pre>KURT()</pre></a></li>
<li class="method public "><a href="#method_LARGE" title="LARGE :: LARGE"><span class="description">LARGE</span><pre>LARGE()</pre></a></li>
<li class="method public "><a href="#method_LINEST" title="LINEST :: LINEST"><span class="description">LINEST</span><pre>LINEST()</pre></a></li>
<li class="method public "><a href="#method_LOGEST" title="LOGEST :: LOGEST"><span class="description">LOGEST</span><pre>LOGEST()</pre></a></li>
<li class="method public "><a href="#method_LOGINV" title="LOGINV :: LOGINV"><span class="description">LOGINV</span><pre>LOGINV()</pre></a></li>
<li class="method public "><a href="#method_LOGNORMDIST" title="LOGNORMDIST :: LOGNORMDIST"><span class="description">LOGNORMDIST</span><pre>LOGNORMDIST()</pre></a></li>
<li class="method public "><a href="#method_MAX" title="MAX :: MAX"><span class="description">MAX</span><pre>MAX()</pre></a></li>
<li class="method public "><a href="#method_MAXA" title="MAXA :: MAXA"><span class="description">MAXA</span><pre>MAXA()</pre></a></li>
<li class="method public "><a href="#method_MAXIF" title="MAXIF :: MAXIF"><span class="description">MAXIF</span><pre>MAXIF()</pre></a></li>
<li class="method public "><a href="#method_MEDIAN" title="MEDIAN :: MEDIAN"><span class="description">MEDIAN</span><pre>MEDIAN()</pre></a></li>
<li class="method public "><a href="#method_MIN" title="MIN :: MIN"><span class="description">MIN</span><pre>MIN()</pre></a></li>
<li class="method public "><a href="#method_MINA" title="MINA :: MINA"><span class="description">MINA</span><pre>MINA()</pre></a></li>
<li class="method public "><a href="#method_MINIF" title="MINIF :: MINIF"><span class="description">MINIF</span><pre>MINIF()</pre></a></li>
<li class="method public "><a href="#method_MODE" title="MODE :: MODE"><span class="description">MODE</span><pre>MODE()</pre></a></li>
<li class="method public "><a href="#method_NEGBINOMDIST" title="NEGBINOMDIST :: NEGBINOMDIST"><span class="description">NEGBINOMDIST</span><pre>NEGBINOMDIST()</pre></a></li>
<li class="method public "><a href="#method_NORMDIST" title="NORMDIST :: NORMDIST"><span class="description">NORMDIST</span><pre>NORMDIST()</pre></a></li>
<li class="method public "><a href="#method_NORMINV" title="NORMINV :: NORMINV"><span class="description">NORMINV</span><pre>NORMINV()</pre></a></li>
<li class="method public "><a href="#method_NORMSDIST" title="NORMSDIST :: NORMSDIST"><span class="description">NORMSDIST</span><pre>NORMSDIST()</pre></a></li>
<li class="method public "><a href="#method_NORMSINV" title="NORMSINV :: NORMSINV"><span class="description">NORMSINV</span><pre>NORMSINV()</pre></a></li>
<li class="method public "><a href="#method_PERCENTILE" title="PERCENTILE :: PERCENTILE"><span class="description">PERCENTILE</span><pre>PERCENTILE()</pre></a></li>
<li class="method public "><a href="#method_PERCENTRANK" title="PERCENTRANK :: PERCENTRANK"><span class="description">PERCENTRANK</span><pre>PERCENTRANK()</pre></a></li>
<li class="method public "><a href="#method_PERMUT" title="PERMUT :: PERMUT"><span class="description">PERMUT</span><pre>PERMUT()</pre></a></li>
<li class="method public "><a href="#method_POISSON" title="POISSON :: POISSON"><span class="description">POISSON</span><pre>POISSON()</pre></a></li>
<li class="method public "><a href="#method_QUARTILE" title="QUARTILE :: QUARTILE"><span class="description">QUARTILE</span><pre>QUARTILE()</pre></a></li>
<li class="method public "><a href="#method_RANK" title="RANK :: RANK"><span class="description">RANK</span><pre>RANK()</pre></a></li>
<li class="method public "><a href="#method_RSQ" title="RSQ :: RSQ"><span class="description">RSQ</span><pre>RSQ()</pre></a></li>
<li class="method public "><a href="#method_SKEW" title="SKEW :: SKEW"><span class="description">SKEW</span><pre>SKEW()</pre></a></li>
<li class="method public "><a href="#method_SLOPE" title="SLOPE :: SLOPE"><span class="description">SLOPE</span><pre>SLOPE()</pre></a></li>
<li class="method public "><a href="#method_SMALL" title="SMALL :: SMALL"><span class="description">SMALL</span><pre>SMALL()</pre></a></li>
<li class="method public "><a href="#method_STANDARDIZE" title="STANDARDIZE :: STANDARDIZE"><span class="description">STANDARDIZE</span><pre>STANDARDIZE()</pre></a></li>
<li class="method public "><a href="#method_STDEV" title="STDEV :: STDEV"><span class="description">STDEV</span><pre>STDEV()</pre></a></li>
<li class="method public "><a href="#method_STDEVA" title="STDEVA :: STDEVA"><span class="description">STDEVA</span><pre>STDEVA()</pre></a></li>
<li class="method public "><a href="#method_STDEVP" title="STDEVP :: STDEVP"><span class="description">STDEVP</span><pre>STDEVP()</pre></a></li>
<li class="method public "><a href="#method_STDEVPA" title="STDEVPA :: STDEVPA"><span class="description">STDEVPA</span><pre>STDEVPA()</pre></a></li>
<li class="method public "><a href="#method_STEYX" title="STEYX :: STEYX"><span class="description">STEYX</span><pre>STEYX()</pre></a></li>
<li class="method public "><a href="#method_TDIST" title="TDIST :: TDIST"><span class="description">TDIST</span><pre>TDIST()</pre></a></li>
<li class="method public "><a href="#method_TINV" title="TINV :: TINV"><span class="description">TINV</span><pre>TINV()</pre></a></li>
<li class="method public "><a href="#method_TREND" title="TREND :: TREND"><span class="description">TREND</span><pre>TREND()</pre></a></li>
<li class="method public "><a href="#method_TRIMMEAN" title="TRIMMEAN :: TRIMMEAN"><span class="description">TRIMMEAN</span><pre>TRIMMEAN()</pre></a></li>
<li class="method public "><a href="#method_VARA" title="VARA :: VARA"><span class="description">VARA</span><pre>VARA()</pre></a></li>
<li class="method public "><a href="#method_VARFunc" title="VARFunc :: VARFunc"><span class="description">VARFunc</span><pre>VARFunc()</pre></a></li>
<li class="method public "><a href="#method_VARP" title="VARP :: VARP"><span class="description">VARP</span><pre>VARP()</pre></a></li>
<li class="method public "><a href="#method_VARPA" title="VARPA :: VARPA"><span class="description">VARPA</span><pre>VARPA()</pre></a></li>
<li class="method public "><a href="#method_WEIBULL" title="WEIBULL :: WEIBULL"><span class="description">WEIBULL</span><pre>WEIBULL()</pre></a></li>
<li class="method public "><a href="#method_ZTEST" title="ZTEST :: ZTEST"><span class="description">ZTEST</span><pre>ZTEST()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="method private "><a href="#method__beta" title="_beta :: Beta function."><span class="description">Beta function.</span><pre>_beta()</pre></a></li>
<li class="method private "><a href="#method__betaFraction" title="_betaFraction :: Evaluates of continued fraction part of incomplete beta function."><span class="description">Evaluates of continued fraction part of incomplete beta function.</span><pre>_betaFraction()</pre></a></li>
<li class="method private "><a href="#method__checkTrendArrays" title="_checkTrendArrays :: "><span class="description">_checkTrendArrays()
        </span><pre>_checkTrendArrays()</pre></a></li>
<li class="method private "><a href="#method__gamma" title="_gamma :: "><span class="description">_gamma()
        </span><pre>_gamma()</pre></a></li>
<li class="method private "><a href="#method__incompleteBeta" title="_incompleteBeta :: Incomplete beta function"><span class="description">Incomplete beta function</span><pre>_incompleteBeta()</pre></a></li>
<li class="method private "><a href="#method__incompleteGamma" title="_incompleteGamma :: "><span class="description">_incompleteGamma()
        </span><pre>_incompleteGamma()</pre></a></li>
<li class="method private "><a href="#method__inverse_ncdf" title="_inverse_ncdf :: "><span class="description">_inverse_ncdf()
        </span><pre>_inverse_ncdf()</pre></a></li>
<li class="method private "><a href="#method__inverse_ncdf2" title="_inverse_ncdf2 :: "><span class="description">_inverse_ncdf2()
        </span><pre>_inverse_ncdf2()</pre></a></li>
<li class="method private "><a href="#method__inverse_ncdf3" title="_inverse_ncdf3 :: "><span class="description">_inverse_ncdf3()
        </span><pre>_inverse_ncdf3()</pre></a></li>
<li class="method private "><a href="#method__logBeta" title="_logBeta :: The natural logarithm of the beta function."><span class="description">The natural logarithm of the beta function.</span><pre>_logBeta()</pre></a></li>
<li class="method private "><a href="#method__logGamma" title="_logGamma :: "><span class="description">_logGamma()
        </span><pre>_logGamma()</pre></a></li>
<li class="method private "><a href="#method__modeCalc" title="_modeCalc :: "><span class="description">_modeCalc()
        </span><pre>_modeCalc()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__logBetaCache_p" title="$_logBetaCache_p :: "><span class="description"></span><pre>$_logBetaCache_p</pre></a></li>
<li class="property private "><a href="#property__logBetaCache_q" title="$_logBetaCache_q :: "><span class="description"></span><pre>$_logBetaCache_q</pre></a></li>
<li class="property private "><a href="#property__logBetaCache_result" title="$_logBetaCache_result :: "><span class="description"></span><pre>$_logBetaCache_result</pre></a></li>
<li class="property private "><a href="#property__logGammaCache_result" title="$_logGammaCache_result :: "><span class="description"></span><pre>$_logGammaCache_result</pre></a></li>
<li class="property private "><a href="#property__logGammaCache_x" title="$_logGammaCache_x :: "><span class="description"></span><pre>$_logGammaCache_x</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Calculation_Statistical"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Calculation_Statistical.html">PHPExcel_Calculation_Statistical</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Calculation_Statistical</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Calculation.html">PHPExcel_Calculation</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_AVEDEV"></a><div class="element clickable method public method_AVEDEV" data-toggle="collapse" data-target=".method_AVEDEV .collapse">
<h2>AVEDEV</h2>
<pre>AVEDEV() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the average of the absolute deviations of data points from their mean.
AVEDEV is a measure of the variability in a data set.</p>

<p>Excel Function:
    AVEDEV(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_AVERAGE"></a><div class="element clickable method public method_AVERAGE" data-toggle="collapse" data-target=".method_AVERAGE .collapse">
<h2>AVERAGE</h2>
<pre>AVERAGE() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the average (arithmetic mean) of the arguments</p>

<p>Excel Function:
    AVERAGE(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_AVERAGEA"></a><div class="element clickable method public method_AVERAGEA" data-toggle="collapse" data-target=".method_AVERAGEA .collapse">
<h2>AVERAGEA</h2>
<pre>AVERAGEA() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the average of its arguments, including numbers, text, and logical values</p>

<p>Excel Function:
    AVERAGEA(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_AVERAGEIF"></a><div class="element clickable method public method_AVERAGEIF" data-toggle="collapse" data-target=".method_AVERAGEIF .collapse">
<h2>AVERAGEIF</h2>
<pre>AVERAGEIF($aArgs, string $condition, mixed[] $averageArgs) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the average value from a range of cells that contain numbers within the list of arguments</p>

<p>Excel Function:
    AVERAGEIF(value1[,value2[, ...]],condition)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$aArgs</h4></div>
<div class="subelement argument">
<h4>$condition</h4>
<code>string</code><p>The criteria that defines which cells will be checked.</p></div>
<div class="subelement argument">
<h4>$averageArgs</h4>
<code>mixed[]</code><p>Data values</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_BETADIST"></a><div class="element clickable method public method_BETADIST" data-toggle="collapse" data-target=".method_BETADIST .collapse">
<h2>BETADIST</h2>
<pre>BETADIST(float $value, float $alpha, float $beta, $rMin, $rMax) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the beta distribution.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code><p>Value at which you want to evaluate the distribution</p></div>
<div class="subelement argument">
<h4>$alpha</h4>
<code>float</code><p>Parameter to the distribution</p></div>
<div class="subelement argument">
<h4>$beta</h4>
<code>float</code><p>Parameter to the distribution</p></div>
<div class="subelement argument"><h4>$rMin</h4></div>
<div class="subelement argument"><h4>$rMax</h4></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_BETAINV"></a><div class="element clickable method public method_BETAINV" data-toggle="collapse" data-target=".method_BETAINV .collapse">
<h2>BETAINV</h2>
<pre>BETAINV(float $probability, float $alpha, float $beta, float $rMin, float $rMax) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the inverse of the beta distribution.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$probability</h4>
<code>float</code><p>Probability at which you want to evaluate the distribution</p></div>
<div class="subelement argument">
<h4>$alpha</h4>
<code>float</code><p>Parameter to the distribution</p></div>
<div class="subelement argument">
<h4>$beta</h4>
<code>float</code><p>Parameter to the distribution</p></div>
<div class="subelement argument">
<h4>$rMin</h4>
<code>float</code><p>Minimum value</p></div>
<div class="subelement argument">
<h4>$rMax</h4>
<code>float</code><p>Maximum value</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_BINOMDIST"></a><div class="element clickable method public method_BINOMDIST" data-toggle="collapse" data-target=".method_BINOMDIST .collapse">
<h2>BINOMDIST</h2>
<pre>BINOMDIST(float $value, float $trials, float $probability, boolean $cumulative) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the individual term binomial distribution probability. Use BINOMDIST in problems with
    a fixed number of tests or trials, when the outcomes of any trial are only success or failure,
    when trials are independent, and when the probability of success is constant throughout the
    experiment. For example, BINOMDIST can calculate the probability that two of the next three
    babies born are male.</p></div>
<table class="table table-bordered"><tr>
<th>todo</th>
<td>Cumulative distribution function</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code><p>Number of successes in trials</p></div>
<div class="subelement argument">
<h4>$trials</h4>
<code>float</code><p>Number of trials</p></div>
<div class="subelement argument">
<h4>$probability</h4>
<code>float</code><p>Probability of success on each trial</p></div>
<div class="subelement argument">
<h4>$cumulative</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_CHIDIST"></a><div class="element clickable method public method_CHIDIST" data-toggle="collapse" data-target=".method_CHIDIST .collapse">
<h2>CHIDIST</h2>
<pre>CHIDIST(float $value, float $degrees) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the one-tailed probability of the chi-squared distribution.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code><p>Value for the function</p></div>
<div class="subelement argument">
<h4>$degrees</h4>
<code>float</code><p>degrees of freedom</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_CHIINV"></a><div class="element clickable method public method_CHIINV" data-toggle="collapse" data-target=".method_CHIINV .collapse">
<h2>CHIINV</h2>
<pre>CHIINV(float $probability, float $degrees) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the one-tailed probability of the chi-squared distribution.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$probability</h4>
<code>float</code><p>Probability for the function</p></div>
<div class="subelement argument">
<h4>$degrees</h4>
<code>float</code><p>degrees of freedom</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_CONFIDENCE"></a><div class="element clickable method public method_CONFIDENCE" data-toggle="collapse" data-target=".method_CONFIDENCE .collapse">
<h2>CONFIDENCE</h2>
<pre>CONFIDENCE(float $alpha, float $stdDev, float $size) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the confidence interval for a population mean</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$alpha</h4>
<code>float</code>
</div>
<div class="subelement argument">
<h4>$stdDev</h4>
<code>float</code><p>Standard Deviation</p></div>
<div class="subelement argument">
<h4>$size</h4>
<code>float</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_CORREL"></a><div class="element clickable method public method_CORREL" data-toggle="collapse" data-target=".method_CORREL .collapse">
<h2>CORREL</h2>
<pre>CORREL(array $yValues, array $xValues) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns covariance, the average of the products of deviations for each data point pair.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$yValues</h4>
<code>array</code><p>of mixed		Data Series Y</p></div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>array</code><p>of mixed		Data Series X</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_COUNT"></a><div class="element clickable method public method_COUNT" data-toggle="collapse" data-target=".method_COUNT .collapse">
<h2>COUNT</h2>
<pre>COUNT() : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Counts the number of cells that contain numbers within the list of arguments</p>

<p>Excel Function:
    COUNT(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_COUNTA"></a><div class="element clickable method public method_COUNTA" data-toggle="collapse" data-target=".method_COUNTA .collapse">
<h2>COUNTA</h2>
<pre>COUNTA() : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Counts the number of cells that are not empty within the list of arguments</p>

<p>Excel Function:
    COUNTA(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_COUNTBLANK"></a><div class="element clickable method public method_COUNTBLANK" data-toggle="collapse" data-target=".method_COUNTBLANK .collapse">
<h2>COUNTBLANK</h2>
<pre>COUNTBLANK() : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Counts the number of empty cells within the list of arguments</p>

<p>Excel Function:
    COUNTBLANK(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_COUNTIF"></a><div class="element clickable method public method_COUNTIF" data-toggle="collapse" data-target=".method_COUNTIF .collapse">
<h2>COUNTIF</h2>
<pre>COUNTIF($aArgs, string $condition) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Counts the number of cells that contain numbers within the list of arguments</p>

<p>Excel Function:
    COUNTIF(value1[,value2[, ...]],condition)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$aArgs</h4></div>
<div class="subelement argument">
<h4>$condition</h4>
<code>string</code><p>The criteria that defines which cells will be counted.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_COVAR"></a><div class="element clickable method public method_COVAR" data-toggle="collapse" data-target=".method_COVAR .collapse">
<h2>COVAR</h2>
<pre>COVAR(array $yValues, array $xValues) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns covariance, the average of the products of deviations for each data point pair.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$yValues</h4>
<code>array</code><p>of mixed		Data Series Y</p></div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>array</code><p>of mixed		Data Series X</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_CRITBINOM"></a><div class="element clickable method public method_CRITBINOM" data-toggle="collapse" data-target=".method_CRITBINOM .collapse">
<h2>CRITBINOM</h2>
<pre>CRITBINOM(float $trials, float $probability, float $alpha) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the smallest value for which the cumulative binomial distribution is greater
    than or equal to a criterion value</p>

<p>See http://support.microsoft.com/kb/828117/ for details of the algorithm used</p></div>
<table class="table table-bordered"><tr>
<th>todo</th>
<td>Warning. This implementation differs from the algorithm detailed on the MS
		web site in that $CumPGuessMinus1 = $CumPGuess - 1 rather than $CumPGuess - $PGuess
		This eliminates a potential endless loop error, but may have an adverse affect on the
		accuracy of the function (although all my tests have so far returned correct results).</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$trials</h4>
<code>float</code><p>number of Bernoulli trials</p></div>
<div class="subelement argument">
<h4>$probability</h4>
<code>float</code><p>probability of a success on each trial</p></div>
<div class="subelement argument">
<h4>$alpha</h4>
<code>float</code><p>criterion value</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_DEVSQ"></a><div class="element clickable method public method_DEVSQ" data-toggle="collapse" data-target=".method_DEVSQ .collapse">
<h2>DEVSQ</h2>
<pre>DEVSQ() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the sum of squares of deviations of data points from their sample mean.</p>

<p>Excel Function:
    DEVSQ(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_EXPONDIST"></a><div class="element clickable method public method_EXPONDIST" data-toggle="collapse" data-target=".method_EXPONDIST .collapse">
<h2>EXPONDIST</h2>
<pre>EXPONDIST(float $value, float $lambda, boolean $cumulative) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the exponential distribution. Use EXPONDIST to model the time between events,
    such as how long an automated bank teller takes to deliver cash. For example, you can
    use EXPONDIST to determine the probability that the process takes at most 1 minute.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code><p>Value of the function</p></div>
<div class="subelement argument">
<h4>$lambda</h4>
<code>float</code><p>The parameter value</p></div>
<div class="subelement argument">
<h4>$cumulative</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_FISHER"></a><div class="element clickable method public method_FISHER" data-toggle="collapse" data-target=".method_FISHER .collapse">
<h2>FISHER</h2>
<pre>FISHER(float $value) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the Fisher transformation at x. This transformation produces a function that
    is normally distributed rather than skewed. Use this function to perform hypothesis
    testing on the correlation coefficient.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_FISHERINV"></a><div class="element clickable method public method_FISHERINV" data-toggle="collapse" data-target=".method_FISHERINV .collapse">
<h2>FISHERINV</h2>
<pre>FISHERINV(float $value) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the inverse of the Fisher transformation. Use this transformation when
    analyzing correlations between ranges or arrays of data. If y = FISHER(x), then
    FISHERINV(y) = x.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_FORECAST"></a><div class="element clickable method public method_FORECAST" data-toggle="collapse" data-target=".method_FORECAST .collapse">
<h2>FORECAST</h2>
<pre>FORECAST(float $xValue, array $yValues, array $xValues) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Calculates, or predicts, a future value by using existing values. The predicted value is a y-value for a given x-value.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$xValue</h4>
<code>float</code><p>Value of X for which we want to find Y</p></div>
<div class="subelement argument">
<h4>$yValues</h4>
<code>array</code><p>of mixed		Data Series Y</p></div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>array</code><p>of mixed		Data Series X</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_GAMMADIST"></a><div class="element clickable method public method_GAMMADIST" data-toggle="collapse" data-target=".method_GAMMADIST .collapse">
<h2>GAMMADIST</h2>
<pre>GAMMADIST(float $value, float $a, float $b, boolean $cumulative) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the gamma distribution.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code><p>Value at which you want to evaluate the distribution</p></div>
<div class="subelement argument">
<h4>$a</h4>
<code>float</code><p>Parameter to the distribution</p></div>
<div class="subelement argument">
<h4>$b</h4>
<code>float</code><p>Parameter to the distribution</p></div>
<div class="subelement argument">
<h4>$cumulative</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_GAMMAINV"></a><div class="element clickable method public method_GAMMAINV" data-toggle="collapse" data-target=".method_GAMMAINV .collapse">
<h2>GAMMAINV</h2>
<pre>GAMMAINV(float $probability, float $alpha, float $beta) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the inverse of the beta distribution.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$probability</h4>
<code>float</code><p>Probability at which you want to evaluate the distribution</p></div>
<div class="subelement argument">
<h4>$alpha</h4>
<code>float</code><p>Parameter to the distribution</p></div>
<div class="subelement argument">
<h4>$beta</h4>
<code>float</code><p>Parameter to the distribution</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_GAMMALN"></a><div class="element clickable method public method_GAMMALN" data-toggle="collapse" data-target=".method_GAMMALN .collapse">
<h2>GAMMALN</h2>
<pre>GAMMALN(float $value) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the natural logarithm of the gamma function.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_GEOMEAN"></a><div class="element clickable method public method_GEOMEAN" data-toggle="collapse" data-target=".method_GEOMEAN .collapse">
<h2>GEOMEAN</h2>
<pre>GEOMEAN() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the geometric mean of an array or range of positive data. For example, you
    can use GEOMEAN to calculate average growth rate given compound interest with
    variable rates.</p>

<p>Excel Function:
    GEOMEAN(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_GROWTH"></a><div class="element clickable method public method_GROWTH" data-toggle="collapse" data-target=".method_GROWTH .collapse">
<h2>GROWTH</h2>
<pre>GROWTH(array $yValues, array $xValues, array $newValues, boolean $const) : array</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns values along a predicted emponential trend</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$yValues</h4>
<code>array</code><p>of mixed		Data Series Y</p></div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>array</code><p>of mixed		Data Series X</p></div>
<div class="subelement argument">
<h4>$newValues</h4>
<code>array</code><p>of mixed		Values of X for which we want to find Y</p></div>
<div class="subelement argument">
<h4>$const</h4>
<code>boolean</code><p>A logical value specifying whether to force the intersect to equal 0.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>array</code>of float</div>
</div></div>
</div>
<a id="method_HARMEAN"></a><div class="element clickable method public method_HARMEAN" data-toggle="collapse" data-target=".method_HARMEAN .collapse">
<h2>HARMEAN</h2>
<pre>HARMEAN() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the harmonic mean of a data set. The harmonic mean is the reciprocal of the
    arithmetic mean of reciprocals.</p>

<p>Excel Function:
    HARMEAN(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_HYPGEOMDIST"></a><div class="element clickable method public method_HYPGEOMDIST" data-toggle="collapse" data-target=".method_HYPGEOMDIST .collapse">
<h2>HYPGEOMDIST</h2>
<pre>HYPGEOMDIST(float $sampleSuccesses, float $sampleNumber, float $populationSuccesses, float $populationNumber) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the hypergeometric distribution. HYPGEOMDIST returns the probability of a given number of
sample successes, given the sample size, population successes, and population size.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$sampleSuccesses</h4>
<code>float</code><p>Number of successes in the sample</p></div>
<div class="subelement argument">
<h4>$sampleNumber</h4>
<code>float</code><p>Size of the sample</p></div>
<div class="subelement argument">
<h4>$populationSuccesses</h4>
<code>float</code><p>Number of successes in the population</p></div>
<div class="subelement argument">
<h4>$populationNumber</h4>
<code>float</code><p>Population size</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_INTERCEPT"></a><div class="element clickable method public method_INTERCEPT" data-toggle="collapse" data-target=".method_INTERCEPT .collapse">
<h2>INTERCEPT</h2>
<pre>INTERCEPT(array $yValues, array $xValues) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Calculates the point at which a line will intersect the y-axis by using existing x-values and y-values.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$yValues</h4>
<code>array</code><p>of mixed		Data Series Y</p></div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>array</code><p>of mixed		Data Series X</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_KURT"></a><div class="element clickable method public method_KURT" data-toggle="collapse" data-target=".method_KURT .collapse">
<h2>KURT</h2>
<pre>KURT() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the kurtosis of a data set. Kurtosis characterizes the relative peakedness
or flatness of a distribution compared with the normal distribution. Positive
kurtosis indicates a relatively peaked distribution. Negative kurtosis indicates a
relatively flat distribution.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_LARGE"></a><div class="element clickable method public method_LARGE" data-toggle="collapse" data-target=".method_LARGE .collapse">
<h2>LARGE</h2>
<pre>LARGE() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the nth largest value in a data set. You can use this function to
    select a value based on its relative standing.</p>

<p>Excel Function:
    LARGE(value1[,value2[, ...]],entry)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_LINEST"></a><div class="element clickable method public method_LINEST" data-toggle="collapse" data-target=".method_LINEST .collapse">
<h2>LINEST</h2>
<pre>LINEST(array $yValues, array $xValues, boolean $const, boolean $stats) : array</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Calculates the statistics for a line by using the "least squares" method to calculate a straight line that best fits your data,
    and then returns an array that describes the line.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$yValues</h4>
<code>array</code><p>of mixed		Data Series Y</p></div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>array</code><p>of mixed		Data Series X</p></div>
<div class="subelement argument">
<h4>$const</h4>
<code>boolean</code><p>A logical value specifying whether to force the intersect to equal 0.</p></div>
<div class="subelement argument">
<h4>$stats</h4>
<code>boolean</code><p>A logical value specifying whether to return additional regression statistics.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>array</code></div>
</div></div>
</div>
<a id="method_LOGEST"></a><div class="element clickable method public method_LOGEST" data-toggle="collapse" data-target=".method_LOGEST .collapse">
<h2>LOGEST</h2>
<pre>LOGEST(array $yValues, array $xValues, boolean $const, boolean $stats) : array</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Calculates an exponential curve that best fits the X and Y data series,
    and then returns an array that describes the line.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$yValues</h4>
<code>array</code><p>of mixed		Data Series Y</p></div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>array</code><p>of mixed		Data Series X</p></div>
<div class="subelement argument">
<h4>$const</h4>
<code>boolean</code><p>A logical value specifying whether to force the intersect to equal 0.</p></div>
<div class="subelement argument">
<h4>$stats</h4>
<code>boolean</code><p>A logical value specifying whether to return additional regression statistics.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>array</code></div>
</div></div>
</div>
<a id="method_LOGINV"></a><div class="element clickable method public method_LOGINV" data-toggle="collapse" data-target=".method_LOGINV .collapse">
<h2>LOGINV</h2>
<pre>LOGINV(float $probability, float $mean, float $stdDev) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the inverse of the normal cumulative distribution</p></div>
<table class="table table-bordered"><tr>
<th>todo</th>
<td>Try implementing P J Acklam's refinement algorithm for greater
		accuracy if I can get my head round the mathematics
		(as described at) http://home.online.no/~pjacklam/notes/invnorm/</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$probability</h4>
<code>float</code>
</div>
<div class="subelement argument">
<h4>$mean</h4>
<code>float</code>
</div>
<div class="subelement argument">
<h4>$stdDev</h4>
<code>float</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_LOGNORMDIST"></a><div class="element clickable method public method_LOGNORMDIST" data-toggle="collapse" data-target=".method_LOGNORMDIST .collapse">
<h2>LOGNORMDIST</h2>
<pre>LOGNORMDIST(float $value, float $mean, float $stdDev) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the cumulative lognormal distribution of x, where ln(x) is normally distributed
with parameters mean and standard_dev.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code>
</div>
<div class="subelement argument">
<h4>$mean</h4>
<code>float</code>
</div>
<div class="subelement argument">
<h4>$stdDev</h4>
<code>float</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_MAX"></a><div class="element clickable method public method_MAX" data-toggle="collapse" data-target=".method_MAX .collapse">
<h2>MAX</h2>
<pre>MAX() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>MAX returns the value of the element of the values passed that has the highest value,
    with negative numbers considered smaller than positive numbers.</p>

<p>Excel Function:
    MAX(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_MAXA"></a><div class="element clickable method public method_MAXA" data-toggle="collapse" data-target=".method_MAXA .collapse">
<h2>MAXA</h2>
<pre>MAXA() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the greatest value in a list of arguments, including numbers, text, and logical values</p>

<p>Excel Function:
    MAXA(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_MAXIF"></a><div class="element clickable method public method_MAXIF" data-toggle="collapse" data-target=".method_MAXIF .collapse">
<h2>MAXIF</h2>
<pre>MAXIF($aArgs, string $condition, $sumArgs) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Counts the maximum value within a range of cells that contain numbers within the list of arguments</p>

<p>Excel Function:
    MAXIF(value1[,value2[, ...]],condition)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$aArgs</h4></div>
<div class="subelement argument">
<h4>$condition</h4>
<code>string</code><p>The criteria that defines which cells will be checked.</p></div>
<div class="subelement argument"><h4>$sumArgs</h4></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_MEDIAN"></a><div class="element clickable method public method_MEDIAN" data-toggle="collapse" data-target=".method_MEDIAN .collapse">
<h2>MEDIAN</h2>
<pre>MEDIAN() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the median of the given numbers. The median is the number in the middle of a set of numbers.</p>

<p>Excel Function:
    MEDIAN(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_MIN"></a><div class="element clickable method public method_MIN" data-toggle="collapse" data-target=".method_MIN .collapse">
<h2>MIN</h2>
<pre>MIN() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>MIN returns the value of the element of the values passed that has the smallest value,
    with negative numbers considered smaller than positive numbers.</p>

<p>Excel Function:
    MIN(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_MINA"></a><div class="element clickable method public method_MINA" data-toggle="collapse" data-target=".method_MINA .collapse">
<h2>MINA</h2>
<pre>MINA() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the smallest value in a list of arguments, including numbers, text, and logical values</p>

<p>Excel Function:
    MINA(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_MINIF"></a><div class="element clickable method public method_MINIF" data-toggle="collapse" data-target=".method_MINIF .collapse">
<h2>MINIF</h2>
<pre>MINIF($aArgs, string $condition, $sumArgs) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the minimum value within a range of cells that contain numbers within the list of arguments</p>

<p>Excel Function:
    MINIF(value1[,value2[, ...]],condition)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$aArgs</h4></div>
<div class="subelement argument">
<h4>$condition</h4>
<code>string</code><p>The criteria that defines which cells will be checked.</p></div>
<div class="subelement argument"><h4>$sumArgs</h4></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_MODE"></a><div class="element clickable method public method_MODE" data-toggle="collapse" data-target=".method_MODE .collapse">
<h2>MODE</h2>
<pre>MODE() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the most frequently occurring, or repetitive, value in an array or range of data</p>

<p>Excel Function:
    MODE(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_NEGBINOMDIST"></a><div class="element clickable method public method_NEGBINOMDIST" data-toggle="collapse" data-target=".method_NEGBINOMDIST .collapse">
<h2>NEGBINOMDIST</h2>
<pre>NEGBINOMDIST(float $failures, float $successes, float $probability) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the negative binomial distribution. NEGBINOMDIST returns the probability that
    there will be number_f failures before the number_s-th success, when the constant
    probability of a success is probability_s. This function is similar to the binomial
    distribution, except that the number of successes is fixed, and the number of trials is
    variable. Like the binomial, trials are assumed to be independent.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$failures</h4>
<code>float</code><p>Number of Failures</p></div>
<div class="subelement argument">
<h4>$successes</h4>
<code>float</code><p>Threshold number of Successes</p></div>
<div class="subelement argument">
<h4>$probability</h4>
<code>float</code><p>Probability of success on each trial</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_NORMDIST"></a><div class="element clickable method public method_NORMDIST" data-toggle="collapse" data-target=".method_NORMDIST .collapse">
<h2>NORMDIST</h2>
<pre>NORMDIST(float $value, float $mean, float $stdDev, boolean $cumulative) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the normal distribution for the specified mean and standard deviation. This
function has a very wide range of applications in statistics, including hypothesis
testing.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code>
</div>
<div class="subelement argument">
<h4>$mean</h4>
<code>float</code><p>Mean Value</p></div>
<div class="subelement argument">
<h4>$stdDev</h4>
<code>float</code><p>Standard Deviation</p></div>
<div class="subelement argument">
<h4>$cumulative</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_NORMINV"></a><div class="element clickable method public method_NORMINV" data-toggle="collapse" data-target=".method_NORMINV .collapse">
<h2>NORMINV</h2>
<pre>NORMINV($probability, float $mean, float $stdDev) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the inverse of the normal cumulative distribution for the specified mean and standard deviation.</p></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$probability</h4></div>
<div class="subelement argument">
<h4>$mean</h4>
<code>float</code><p>Mean Value</p></div>
<div class="subelement argument">
<h4>$stdDev</h4>
<code>float</code><p>Standard Deviation</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_NORMSDIST"></a><div class="element clickable method public method_NORMSDIST" data-toggle="collapse" data-target=".method_NORMSDIST .collapse">
<h2>NORMSDIST</h2>
<pre>NORMSDIST(float $value) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the standard normal cumulative distribution function. The distribution has
a mean of 0 (zero) and a standard deviation of one. Use this function in place of a
table of standard normal curve areas.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_NORMSINV"></a><div class="element clickable method public method_NORMSINV" data-toggle="collapse" data-target=".method_NORMSINV .collapse">
<h2>NORMSINV</h2>
<pre>NORMSINV(float $value) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the inverse of the standard normal cumulative distribution</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_PERCENTILE"></a><div class="element clickable method public method_PERCENTILE" data-toggle="collapse" data-target=".method_PERCENTILE .collapse">
<h2>PERCENTILE</h2>
<pre>PERCENTILE() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the nth percentile of values in a range..</p>

<p>Excel Function:
    PERCENTILE(value1[,value2[, ...]],entry)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_PERCENTRANK"></a><div class="element clickable method public method_PERCENTRANK" data-toggle="collapse" data-target=".method_PERCENTRANK .collapse">
<h2>PERCENTRANK</h2>
<pre>PERCENTRANK(array $valueSet, \number $value, \number $significance) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the rank of a value in a data set as a percentage of the data set.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$valueSet</h4>
<code>array</code><p>of number		An array of, or a reference to, a list of numbers.</p></div>
<div class="subelement argument">
<h4>$value</h4>
<code>\number</code><p>The number whose rank you want to find.</p></div>
<div class="subelement argument">
<h4>$significance</h4>
<code>\number</code><p>The number of significant digits for the returned percentage value.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_PERMUT"></a><div class="element clickable method public method_PERMUT" data-toggle="collapse" data-target=".method_PERMUT .collapse">
<h2>PERMUT</h2>
<pre>PERMUT(int $numObjs, int $numInSet) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the number of permutations for a given number of objects that can be
    selected from number objects. A permutation is any set or subset of objects or
    events where internal order is significant. Permutations are different from
    combinations, for which the internal order is not significant. Use this function
    for lottery-style probability calculations.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$numObjs</h4>
<code>int</code><p>Number of different objects</p></div>
<div class="subelement argument">
<h4>$numInSet</h4>
<code>int</code><p>Number of objects in each permutation</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Number of permutations</div>
</div></div>
</div>
<a id="method_POISSON"></a><div class="element clickable method public method_POISSON" data-toggle="collapse" data-target=".method_POISSON .collapse">
<h2>POISSON</h2>
<pre>POISSON(float $value, float $mean, boolean $cumulative) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the Poisson distribution. A common application of the Poisson distribution
is predicting the number of events over a specific time, such as the number of
cars arriving at a toll plaza in 1 minute.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code>
</div>
<div class="subelement argument">
<h4>$mean</h4>
<code>float</code><p>Mean Value</p></div>
<div class="subelement argument">
<h4>$cumulative</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_QUARTILE"></a><div class="element clickable method public method_QUARTILE" data-toggle="collapse" data-target=".method_QUARTILE .collapse">
<h2>QUARTILE</h2>
<pre>QUARTILE() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the quartile of a data set.</p>

<p>Excel Function:
    QUARTILE(value1[,value2[, ...]],entry)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_RANK"></a><div class="element clickable method public method_RANK" data-toggle="collapse" data-target=".method_RANK .collapse">
<h2>RANK</h2>
<pre>RANK(\number $value, array $valueSet, mixed $order) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the rank of a number in a list of numbers.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>\number</code><p>The number whose rank you want to find.</p></div>
<div class="subelement argument">
<h4>$valueSet</h4>
<code>array</code><p>of number		An array of, or a reference to, a list of numbers.</p></div>
<div class="subelement argument">
<h4>$order</h4>
<code>mixed</code><p>Order to sort the values in the value set</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_RSQ"></a><div class="element clickable method public method_RSQ" data-toggle="collapse" data-target=".method_RSQ .collapse">
<h2>RSQ</h2>
<pre>RSQ(array $yValues, array $xValues) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the square of the Pearson product moment correlation coefficient through data points in known_y's and known_x's.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$yValues</h4>
<code>array</code><p>of mixed		Data Series Y</p></div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>array</code><p>of mixed		Data Series X</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_SKEW"></a><div class="element clickable method public method_SKEW" data-toggle="collapse" data-target=".method_SKEW .collapse">
<h2>SKEW</h2>
<pre>SKEW() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the skewness of a distribution. Skewness characterizes the degree of asymmetry
of a distribution around its mean. Positive skewness indicates a distribution with an
asymmetric tail extending toward more positive values. Negative skewness indicates a
distribution with an asymmetric tail extending toward more negative values.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_SLOPE"></a><div class="element clickable method public method_SLOPE" data-toggle="collapse" data-target=".method_SLOPE .collapse">
<h2>SLOPE</h2>
<pre>SLOPE(array $yValues, array $xValues) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the slope of the linear regression line through data points in known_y's and known_x's.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$yValues</h4>
<code>array</code><p>of mixed		Data Series Y</p></div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>array</code><p>of mixed		Data Series X</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_SMALL"></a><div class="element clickable method public method_SMALL" data-toggle="collapse" data-target=".method_SMALL .collapse">
<h2>SMALL</h2>
<pre>SMALL() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the nth smallest value in a data set. You can use this function to
    select a value based on its relative standing.</p>

<p>Excel Function:
    SMALL(value1[,value2[, ...]],entry)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_STANDARDIZE"></a><div class="element clickable method public method_STANDARDIZE" data-toggle="collapse" data-target=".method_STANDARDIZE .collapse">
<h2>STANDARDIZE</h2>
<pre>STANDARDIZE(float $value, float $mean, float $stdDev) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns a normalized value from a distribution characterized by mean and standard_dev.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code><p>Value to normalize</p></div>
<div class="subelement argument">
<h4>$mean</h4>
<code>float</code><p>Mean Value</p></div>
<div class="subelement argument">
<h4>$stdDev</h4>
<code>float</code><p>Standard Deviation</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>Standardized value</div>
</div></div>
</div>
<a id="method_STDEV"></a><div class="element clickable method public method_STDEV" data-toggle="collapse" data-target=".method_STDEV .collapse">
<h2>STDEV</h2>
<pre>STDEV() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Estimates standard deviation based on a sample. The standard deviation is a measure of how
    widely values are dispersed from the average value (the mean).</p>

<p>Excel Function:
    STDEV(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_STDEVA"></a><div class="element clickable method public method_STDEVA" data-toggle="collapse" data-target=".method_STDEVA .collapse">
<h2>STDEVA</h2>
<pre>STDEVA() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Estimates standard deviation based on a sample, including numbers, text, and logical values</p>

<p>Excel Function:
    STDEVA(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_STDEVP"></a><div class="element clickable method public method_STDEVP" data-toggle="collapse" data-target=".method_STDEVP .collapse">
<h2>STDEVP</h2>
<pre>STDEVP() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Calculates standard deviation based on the entire population</p>

<p>Excel Function:
    STDEVP(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_STDEVPA"></a><div class="element clickable method public method_STDEVPA" data-toggle="collapse" data-target=".method_STDEVPA .collapse">
<h2>STDEVPA</h2>
<pre>STDEVPA() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Calculates standard deviation based on the entire population, including numbers, text, and logical values</p>

<p>Excel Function:
    STDEVPA(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_STEYX"></a><div class="element clickable method public method_STEYX" data-toggle="collapse" data-target=".method_STEYX .collapse">
<h2>STEYX</h2>
<pre>STEYX(array $yValues, array $xValues) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the standard error of the predicted y-value for each x in the regression.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$yValues</h4>
<code>array</code><p>of mixed		Data Series Y</p></div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>array</code><p>of mixed		Data Series X</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_TDIST"></a><div class="element clickable method public method_TDIST" data-toggle="collapse" data-target=".method_TDIST .collapse">
<h2>TDIST</h2>
<pre>TDIST(float $value, float $degrees, float $tails) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the probability of Student's T distribution.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code><p>Value for the function</p></div>
<div class="subelement argument">
<h4>$degrees</h4>
<code>float</code><p>degrees of freedom</p></div>
<div class="subelement argument">
<h4>$tails</h4>
<code>float</code><p>number of tails (1 or 2)</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_TINV"></a><div class="element clickable method public method_TINV" data-toggle="collapse" data-target=".method_TINV .collapse">
<h2>TINV</h2>
<pre>TINV(float $probability, float $degrees) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the one-tailed probability of the chi-squared distribution.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$probability</h4>
<code>float</code><p>Probability for the function</p></div>
<div class="subelement argument">
<h4>$degrees</h4>
<code>float</code><p>degrees of freedom</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_TREND"></a><div class="element clickable method public method_TREND" data-toggle="collapse" data-target=".method_TREND .collapse">
<h2>TREND</h2>
<pre>TREND(array $yValues, array $xValues, array $newValues, boolean $const) : array</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns values along a linear trend</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$yValues</h4>
<code>array</code><p>of mixed		Data Series Y</p></div>
<div class="subelement argument">
<h4>$xValues</h4>
<code>array</code><p>of mixed		Data Series X</p></div>
<div class="subelement argument">
<h4>$newValues</h4>
<code>array</code><p>of mixed		Values of X for which we want to find Y</p></div>
<div class="subelement argument">
<h4>$const</h4>
<code>boolean</code><p>A logical value specifying whether to force the intersect to equal 0.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>array</code>of float</div>
</div></div>
</div>
<a id="method_TRIMMEAN"></a><div class="element clickable method public method_TRIMMEAN" data-toggle="collapse" data-target=".method_TRIMMEAN .collapse">
<h2>TRIMMEAN</h2>
<pre>TRIMMEAN() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the mean of the interior of a data set. TRIMMEAN calculates the mean
    taken by excluding a percentage of data points from the top and bottom tails
    of a data set.</p>

<p>Excel Function:
    TRIMEAN(value1[,value2[, ...]],$discard)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_VARA"></a><div class="element clickable method public method_VARA" data-toggle="collapse" data-target=".method_VARA .collapse">
<h2>VARA</h2>
<pre>VARA() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Estimates variance based on a sample, including numbers, text, and logical values</p>

<p>Excel Function:
    VARA(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_VARFunc"></a><div class="element clickable method public method_VARFunc" data-toggle="collapse" data-target=".method_VARFunc .collapse">
<h2>VARFunc</h2>
<pre>VARFunc() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Estimates variance based on a sample.</p>

<p>Excel Function:
    VAR(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_VARP"></a><div class="element clickable method public method_VARP" data-toggle="collapse" data-target=".method_VARP .collapse">
<h2>VARP</h2>
<pre>VARP() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Calculates variance based on the entire population</p>

<p>Excel Function:
    VARP(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_VARPA"></a><div class="element clickable method public method_VARPA" data-toggle="collapse" data-target=".method_VARPA .collapse">
<h2>VARPA</h2>
<pre>VARPA() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Calculates variance based on the entire population, including numbers, text, and logical values</p>

<p>Excel Function:
    VARPA(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Statistical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_WEIBULL"></a><div class="element clickable method public method_WEIBULL" data-toggle="collapse" data-target=".method_WEIBULL .collapse">
<h2>WEIBULL</h2>
<pre>WEIBULL(float $value, float $alpha, float $beta, boolean $cumulative) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the Weibull distribution. Use this distribution in reliability
analysis, such as calculating a device's mean time to failure.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code>
</div>
<div class="subelement argument">
<h4>$alpha</h4>
<code>float</code><p>Alpha Parameter</p></div>
<div class="subelement argument">
<h4>$beta</h4>
<code>float</code><p>Beta Parameter</p></div>
<div class="subelement argument">
<h4>$cumulative</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_ZTEST"></a><div class="element clickable method public method_ZTEST" data-toggle="collapse" data-target=".method_ZTEST .collapse">
<h2>ZTEST</h2>
<pre>ZTEST(float $dataSet, float $m0, float $sigma) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the Weibull distribution. Use this distribution in reliability
analysis, such as calculating a device's mean time to failure.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dataSet</h4>
<code>float</code>
</div>
<div class="subelement argument">
<h4>$m0</h4>
<code>float</code><p>Alpha Parameter</p></div>
<div class="subelement argument">
<h4>$sigma</h4>
<code>float</code><p>Beta Parameter</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method__beta"></a><div class="element clickable method private method__beta" data-toggle="collapse" data-target=".method__beta .collapse">
<h2>Beta function.</h2>
<pre>_beta(\p $p, \q $q) : </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>author</th>
<td><a href="">Jaco van Kooten</a></td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$p</h4>
<code>\p</code><p>require p>0</p>
</div>
<div class="subelement argument">
<h4>$q</h4>
<code>\q</code><p>require q>0</p>
</div>
<h3>Returns</h3>
<div class="subelement response">if p<=0, q<=0 or p+q>2.55E305 to avoid errors and over/underflow</div>
</div></div>
</div>
<a id="method__betaFraction"></a><div class="element clickable method private method__betaFraction" data-toggle="collapse" data-target=".method__betaFraction .collapse">
<h2>Evaluates of continued fraction part of incomplete beta function.</h2>
<pre>_betaFraction($x, $p, $q) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Based on an idea from Numerical Recipes (W.H. Press et al, 1992).</p></div>
<table class="table table-bordered"><tr>
<th>author</th>
<td><a href="">Jaco van Kooten</a></td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$x</h4></div>
<div class="subelement argument"><h4>$p</h4></div>
<div class="subelement argument"><h4>$q</h4></div>
</div></div>
</div>
<a id="method__checkTrendArrays"></a><div class="element clickable method private method__checkTrendArrays" data-toggle="collapse" data-target=".method__checkTrendArrays .collapse">
<h2>_checkTrendArrays()
        </h2>
<pre>_checkTrendArrays($array1, $array2) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$array1</h4></div>
<div class="subelement argument"><h4>$array2</h4></div>
</div></div>
</div>
<a id="method__gamma"></a><div class="element clickable method private method__gamma" data-toggle="collapse" data-target=".method__gamma .collapse">
<h2>_gamma()
        </h2>
<pre>_gamma($data) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$data</h4></div>
</div></div>
</div>
<a id="method__incompleteBeta"></a><div class="element clickable method private method__incompleteBeta" data-toggle="collapse" data-target=".method__incompleteBeta .collapse">
<h2>Incomplete beta function</h2>
<pre>_incompleteBeta(\x $x, \p $p, \q $q) : </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>author</th>
<td><a href="">Jaco van Kooten</a></td>
</tr>
<tr>
<th>author</th>
<td><a href="">Paul Meagher
The computation is based on formulas from Numerical Recipes, Chapter 6.4 (W.H. Press et al, 1992).</a></td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$x</h4>
<code>\x</code><p>require 0<=x<=1</p>
</div>
<div class="subelement argument">
<h4>$p</h4>
<code>\p</code><p>require p>0</p>
</div>
<div class="subelement argument">
<h4>$q</h4>
<code>\q</code><p>require q>0</p>
</div>
<h3>Returns</h3>
<div class="subelement response">if x<0, p<=0, q<=0 or p+q>2.55E305 and 1 if x>1 to avoid errors and over/underflow</div>
</div></div>
</div>
<a id="method__incompleteGamma"></a><div class="element clickable method private method__incompleteGamma" data-toggle="collapse" data-target=".method__incompleteGamma .collapse">
<h2>_incompleteGamma()
        </h2>
<pre>_incompleteGamma($a, $x) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$a</h4></div>
<div class="subelement argument"><h4>$x</h4></div>
</div></div>
</div>
<a id="method__inverse_ncdf"></a><div class="element clickable method private method__inverse_ncdf" data-toggle="collapse" data-target=".method__inverse_ncdf .collapse">
<h2>_inverse_ncdf()
        </h2>
<pre>_inverse_ncdf($p) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p</h4></div>
</div></div>
</div>
<a id="method__inverse_ncdf2"></a><div class="element clickable method private method__inverse_ncdf2" data-toggle="collapse" data-target=".method__inverse_ncdf2 .collapse">
<h2>_inverse_ncdf2()
        </h2>
<pre>_inverse_ncdf2($prob) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$prob</h4></div>
</div></div>
</div>
<a id="method__inverse_ncdf3"></a><div class="element clickable method private method__inverse_ncdf3" data-toggle="collapse" data-target=".method__inverse_ncdf3 .collapse">
<h2>_inverse_ncdf3()
        </h2>
<pre>_inverse_ncdf3($p) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p</h4></div>
</div></div>
</div>
<a id="method__logBeta"></a><div class="element clickable method private method__logBeta" data-toggle="collapse" data-target=".method__logBeta .collapse">
<h2>The natural logarithm of the beta function.</h2>
<pre>_logBeta(\p $p, \q $q) : </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>author</th>
<td><a href="">Jaco van Kooten</a></td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$p</h4>
<code>\p</code><p>require p>0</p>
</div>
<div class="subelement argument">
<h4>$q</h4>
<code>\q</code><p>require q>0</p>
</div>
<h3>Returns</h3>
<div class="subelement response">if p<=0, q<=0 or p+q>2.55E305 to avoid errors and over/underflow</div>
</div></div>
</div>
<a id="method__logGamma"></a><div class="element clickable method private method__logGamma" data-toggle="collapse" data-target=".method__logGamma .collapse">
<h2>_logGamma()
        </h2>
<pre>_logGamma($x) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$x</h4></div>
</div></div>
</div>
<a id="method__modeCalc"></a><div class="element clickable method private method__modeCalc" data-toggle="collapse" data-target=".method__modeCalc .collapse">
<h2>_modeCalc()
        </h2>
<pre>_modeCalc($data) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$data</h4></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__logBetaCache_p"> </a><div class="element clickable property private property__logBetaCache_p" data-toggle="collapse" data-target=".property__logBetaCache_p .collapse">
<h2></h2>
<pre>$_logBetaCache_p </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__logBetaCache_q"> </a><div class="element clickable property private property__logBetaCache_q" data-toggle="collapse" data-target=".property__logBetaCache_q .collapse">
<h2></h2>
<pre>$_logBetaCache_q </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__logBetaCache_result"> </a><div class="element clickable property private property__logBetaCache_result" data-toggle="collapse" data-target=".property__logBetaCache_result .collapse">
<h2></h2>
<pre>$_logBetaCache_result </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__logGammaCache_result"> </a><div class="element clickable property private property__logGammaCache_result" data-toggle="collapse" data-target=".property__logGammaCache_result .collapse">
<h2></h2>
<pre>$_logGammaCache_result </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__logGammaCache_x"> </a><div class="element clickable property private property__logGammaCache_x" data-toggle="collapse" data-target=".property__logGammaCache_x .collapse">
<h2></h2>
<pre>$_logGammaCache_x </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:34Z.<br></footer></div>
</div>
</body>
</html>
