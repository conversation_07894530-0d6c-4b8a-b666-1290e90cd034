<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Calculation_FormulaParser</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_Calculation_FormulaParser"><span class="description">Create a new PHPExcel_Calculation_FormulaParser</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_getFormula" title="getFormula :: Get Formula"><span class="description">Get Formula</span><pre>getFormula()</pre></a></li>
<li class="method public "><a href="#method_getToken" title="getToken :: Get Token"><span class="description">Get Token</span><pre>getToken()</pre></a></li>
<li class="method public "><a href="#method_getTokenCount" title="getTokenCount :: Get Token count"><span class="description">Get Token count</span><pre>getTokenCount()</pre></a></li>
<li class="method public "><a href="#method_getTokens" title="getTokens :: Get Tokens"><span class="description">Get Tokens</span><pre>getTokens()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul><li class="method private "><a href="#method__parseToTokens" title="_parseToTokens :: Parse to tokens"><span class="description">Parse to tokens</span><pre>_parseToTokens()</pre></a></li></ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__formula" title="$_formula :: Formula"><span class="description"></span><pre>$_formula</pre></a></li>
<li class="property private "><a href="#property__tokens" title="$_tokens :: Tokens"><span class="description"></span><pre>$_tokens</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_BRACE_CLOSE" title="BRACE_CLOSE :: "><span class="description">BRACE_CLOSE</span><pre>BRACE_CLOSE</pre></a></li>
<li class="constant  "><a href="#constant_BRACE_OPEN" title="BRACE_OPEN :: "><span class="description">BRACE_OPEN</span><pre>BRACE_OPEN</pre></a></li>
<li class="constant  "><a href="#constant_BRACKET_CLOSE" title="BRACKET_CLOSE :: "><span class="description">BRACKET_CLOSE</span><pre>BRACKET_CLOSE</pre></a></li>
<li class="constant  "><a href="#constant_BRACKET_OPEN" title="BRACKET_OPEN :: "><span class="description">BRACKET_OPEN</span><pre>BRACKET_OPEN</pre></a></li>
<li class="constant  "><a href="#constant_COMMA" title="COMMA :: "><span class="description">COMMA</span><pre>COMMA</pre></a></li>
<li class="constant  "><a href="#constant_ERROR_START" title="ERROR_START :: "><span class="description">ERROR_START</span><pre>ERROR_START</pre></a></li>
<li class="constant  "><a href="#constant_OPERATORS_INFIX" title="OPERATORS_INFIX :: "><span class="description">OPERATORS_INFIX</span><pre>OPERATORS_INFIX</pre></a></li>
<li class="constant  "><a href="#constant_OPERATORS_POSTFIX" title="OPERATORS_POSTFIX :: "><span class="description">OPERATORS_POSTFIX</span><pre>OPERATORS_POSTFIX</pre></a></li>
<li class="constant  "><a href="#constant_OPERATORS_SN" title="OPERATORS_SN :: "><span class="description">OPERATORS_SN</span><pre>OPERATORS_SN</pre></a></li>
<li class="constant  "><a href="#constant_PAREN_CLOSE" title="PAREN_CLOSE :: "><span class="description">PAREN_CLOSE</span><pre>PAREN_CLOSE</pre></a></li>
<li class="constant  "><a href="#constant_PAREN_OPEN" title="PAREN_OPEN :: "><span class="description">PAREN_OPEN</span><pre>PAREN_OPEN</pre></a></li>
<li class="constant  "><a href="#constant_QUOTE_DOUBLE" title="QUOTE_DOUBLE :: "><span class="description">QUOTE_DOUBLE</span><pre>QUOTE_DOUBLE</pre></a></li>
<li class="constant  "><a href="#constant_QUOTE_SINGLE" title="QUOTE_SINGLE :: "><span class="description">QUOTE_SINGLE</span><pre>QUOTE_SINGLE</pre></a></li>
<li class="constant  "><a href="#constant_SEMICOLON" title="SEMICOLON :: "><span class="description">SEMICOLON</span><pre>SEMICOLON</pre></a></li>
<li class="constant  "><a href="#constant_WHITESPACE" title="WHITESPACE :: "><span class="description">WHITESPACE</span><pre>WHITESPACE</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Calculation_FormulaParser"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Calculation_FormulaParser.html">PHPExcel_Calculation_FormulaParser</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Calculation_FormulaParser</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Calculation.html">PHPExcel_Calculation</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_Calculation_FormulaParser</h2>
<pre>__construct(string $pFormula) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pFormula</h4>
<code>string</code><p>Formula to parse</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Calculation_Exception.html">\PHPExcel_Calculation_Exception</a></code></th>
<td></td>
</tr></table>
</div></div>
</div>
<a id="method_getFormula"></a><div class="element clickable method public method_getFormula" data-toggle="collapse" data-target=".method_getFormula .collapse">
<h2>Get Formula</h2>
<pre>getFormula() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getToken"></a><div class="element clickable method public method_getToken" data-toggle="collapse" data-target=".method_getToken .collapse">
<h2>Get Token</h2>
<pre>getToken(int $pId) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pId</h4>
<code>int</code><p>Token id</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Calculation_Exception.html">\PHPExcel_Calculation_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getTokenCount"></a><div class="element clickable method public method_getTokenCount" data-toggle="collapse" data-target=".method_getTokenCount .collapse">
<h2>Get Token count</h2>
<pre>getTokenCount() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getTokens"></a><div class="element clickable method public method_getTokens" data-toggle="collapse" data-target=".method_getTokens .collapse">
<h2>Get Tokens</h2>
<pre>getTokens() : <a href="PHPExcel.Calculation.FormulaToken.html#%5CPHPExcel_Calculation_FormulaToken">\PHPExcel_Calculation_FormulaToken[]</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="PHPExcel.Calculation.FormulaToken.html#%5CPHPExcel_Calculation_FormulaToken">\PHPExcel_Calculation_FormulaToken[]</a></code></div>
</div></div>
</div>
<a id="method__parseToTokens"></a><div class="element clickable method private method__parseToTokens" data-toggle="collapse" data-target=".method__parseToTokens .collapse">
<h2>Parse to tokens</h2>
<pre>_parseToTokens() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__formula"> </a><div class="element clickable property private property__formula" data-toggle="collapse" data-target=".property__formula .collapse">
<h2></h2>
<pre>$_formula : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__tokens"> </a><div class="element clickable property private property__tokens" data-toggle="collapse" data-target=".property__tokens .collapse">
<h2></h2>
<pre>$_tokens : <a href="PHPExcel.Calculation.FormulaToken.html#%5CPHPExcel_Calculation_FormulaToken">\PHPExcel_Calculation_FormulaToken[]</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_BRACE_CLOSE"> </a><div class="element clickable constant  constant_BRACE_CLOSE" data-toggle="collapse" data-target=".constant_BRACE_CLOSE .collapse">
<h2>BRACE_CLOSE</h2>
<pre>BRACE_CLOSE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BRACE_OPEN"> </a><div class="element clickable constant  constant_BRACE_OPEN" data-toggle="collapse" data-target=".constant_BRACE_OPEN .collapse">
<h2>BRACE_OPEN</h2>
<pre>BRACE_OPEN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BRACKET_CLOSE"> </a><div class="element clickable constant  constant_BRACKET_CLOSE" data-toggle="collapse" data-target=".constant_BRACKET_CLOSE .collapse">
<h2>BRACKET_CLOSE</h2>
<pre>BRACKET_CLOSE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BRACKET_OPEN"> </a><div class="element clickable constant  constant_BRACKET_OPEN" data-toggle="collapse" data-target=".constant_BRACKET_OPEN .collapse">
<h2>BRACKET_OPEN</h2>
<pre>BRACKET_OPEN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COMMA"> </a><div class="element clickable constant  constant_COMMA" data-toggle="collapse" data-target=".constant_COMMA .collapse">
<h2>COMMA</h2>
<pre>COMMA </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_ERROR_START"> </a><div class="element clickable constant  constant_ERROR_START" data-toggle="collapse" data-target=".constant_ERROR_START .collapse">
<h2>ERROR_START</h2>
<pre>ERROR_START </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATORS_INFIX"> </a><div class="element clickable constant  constant_OPERATORS_INFIX" data-toggle="collapse" data-target=".constant_OPERATORS_INFIX .collapse">
<h2>OPERATORS_INFIX</h2>
<pre>OPERATORS_INFIX </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATORS_POSTFIX"> </a><div class="element clickable constant  constant_OPERATORS_POSTFIX" data-toggle="collapse" data-target=".constant_OPERATORS_POSTFIX .collapse">
<h2>OPERATORS_POSTFIX</h2>
<pre>OPERATORS_POSTFIX </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATORS_SN"> </a><div class="element clickable constant  constant_OPERATORS_SN" data-toggle="collapse" data-target=".constant_OPERATORS_SN .collapse">
<h2>OPERATORS_SN</h2>
<pre>OPERATORS_SN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_PAREN_CLOSE"> </a><div class="element clickable constant  constant_PAREN_CLOSE" data-toggle="collapse" data-target=".constant_PAREN_CLOSE .collapse">
<h2>PAREN_CLOSE</h2>
<pre>PAREN_CLOSE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_PAREN_OPEN"> </a><div class="element clickable constant  constant_PAREN_OPEN" data-toggle="collapse" data-target=".constant_PAREN_OPEN .collapse">
<h2>PAREN_OPEN</h2>
<pre>PAREN_OPEN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_QUOTE_DOUBLE"> </a><div class="element clickable constant  constant_QUOTE_DOUBLE" data-toggle="collapse" data-target=".constant_QUOTE_DOUBLE .collapse">
<h2>QUOTE_DOUBLE</h2>
<pre>QUOTE_DOUBLE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_QUOTE_SINGLE"> </a><div class="element clickable constant  constant_QUOTE_SINGLE" data-toggle="collapse" data-target=".constant_QUOTE_SINGLE .collapse">
<h2>QUOTE_SINGLE</h2>
<pre>QUOTE_SINGLE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_SEMICOLON"> </a><div class="element clickable constant  constant_SEMICOLON" data-toggle="collapse" data-target=".constant_SEMICOLON .collapse">
<h2>SEMICOLON</h2>
<pre>SEMICOLON </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_WHITESPACE"> </a><div class="element clickable constant  constant_WHITESPACE" data-toggle="collapse" data-target=".constant_WHITESPACE .collapse">
<h2>WHITESPACE</h2>
<pre>WHITESPACE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:34Z.<br></footer></div>
</div>
</body>
</html>
