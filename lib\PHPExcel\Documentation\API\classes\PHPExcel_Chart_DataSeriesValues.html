<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Chart_DataSeriesValues</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_Chart_DataSeriesValues object"><span class="description">Create a new PHPExcel_Chart_DataSeriesValues object</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_getDataSource" title="getDataSource :: Get Series Data Source (formula)"><span class="description">Get Series Data Source (formula)</span><pre>getDataSource()</pre></a></li>
<li class="method public "><a href="#method_getDataType" title="getDataType :: Get Series Data Type"><span class="description">Get Series Data Type</span><pre>getDataType()</pre></a></li>
<li class="method public "><a href="#method_getDataValue" title="getDataValue :: Get the first Series Data value"><span class="description">Get the first Series Data value</span><pre>getDataValue()</pre></a></li>
<li class="method public "><a href="#method_getDataValues" title="getDataValues :: Get Series Data Values"><span class="description">Get Series Data Values</span><pre>getDataValues()</pre></a></li>
<li class="method public "><a href="#method_getFormatCode" title="getFormatCode :: Get Series Format Code"><span class="description">Get Series Format Code</span><pre>getFormatCode()</pre></a></li>
<li class="method public "><a href="#method_getPointCount" title="getPointCount :: Get Series Point Count"><span class="description">Get Series Point Count</span><pre>getPointCount()</pre></a></li>
<li class="method public "><a href="#method_getPointMarker" title="getPointMarker :: Get Point Marker"><span class="description">Get Point Marker</span><pre>getPointMarker()</pre></a></li>
<li class="method public "><a href="#method_isMultiLevelSeries" title="isMultiLevelSeries :: Identify if the Data Series is a multi-level or a simple series"><span class="description">Identify if the Data Series is a multi-level or a simple series</span><pre>isMultiLevelSeries()</pre></a></li>
<li class="method public "><a href="#method_multiLevelCount" title="multiLevelCount :: Return the level count of a multi-level Data Series"><span class="description">Return the level count of a multi-level Data Series</span><pre>multiLevelCount()</pre></a></li>
<li class="method public "><a href="#method_refresh" title="refresh :: "><span class="description">refresh()
        </span><pre>refresh()</pre></a></li>
<li class="method public "><a href="#method_setDataSource" title="setDataSource :: Set Series Data Source (formula)"><span class="description">Set Series Data Source (formula)</span><pre>setDataSource()</pre></a></li>
<li class="method public "><a href="#method_setDataType" title="setDataType :: Set Series Data Type"><span class="description">Set Series Data Type</span><pre>setDataType()</pre></a></li>
<li class="method public "><a href="#method_setDataValues" title="setDataValues :: Set Series Data Values"><span class="description">Set Series Data Values</span><pre>setDataValues()</pre></a></li>
<li class="method public "><a href="#method_setFormatCode" title="setFormatCode :: Set Series Format Code"><span class="description">Set Series Format Code</span><pre>setFormatCode()</pre></a></li>
<li class="method public "><a href="#method_setPointMarker" title="setPointMarker :: Set Point Marker"><span class="description">Set Point Marker</span><pre>setPointMarker()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul><li class="method private "><a href="#method__stripNulls" title="_stripNulls :: "><span class="description">_stripNulls()
        </span><pre>_stripNulls()</pre></a></li></ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__dataSource" title="$_dataSource :: Series Data Source"><span class="description"></span><pre>$_dataSource</pre></a></li>
<li class="property private "><a href="#property__dataType" title="$_dataType :: Series Data Type"><span class="description"></span><pre>$_dataType</pre></a></li>
<li class="property private "><a href="#property__dataTypeValues" title="$_dataTypeValues :: "><span class="description"></span><pre>$_dataTypeValues</pre></a></li>
<li class="property private "><a href="#property__dataValues" title="$_dataValues :: Data Values"><span class="description"></span><pre>$_dataValues</pre></a></li>
<li class="property private "><a href="#property__formatCode" title="$_formatCode :: Format Code"><span class="description"></span><pre>$_formatCode</pre></a></li>
<li class="property private "><a href="#property__marker" title="$_marker :: Series Point Marker"><span class="description"></span><pre>$_marker</pre></a></li>
<li class="property private "><a href="#property__pointCount" title="$_pointCount :: Point Count (The number of datapoints in the dataseries)"><span class="description"></span><pre>$_pointCount</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_DATASERIES_TYPE_NUMBER" title="DATASERIES_TYPE_NUMBER :: "><span class="description">DATASERIES_TYPE_NUMBER</span><pre>DATASERIES_TYPE_NUMBER</pre></a></li>
<li class="constant  "><a href="#constant_DATASERIES_TYPE_STRING" title="DATASERIES_TYPE_STRING :: "><span class="description">DATASERIES_TYPE_STRING</span><pre>DATASERIES_TYPE_STRING</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Chart_DataSeriesValues"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Chart_DataSeriesValues.html">PHPExcel_Chart_DataSeriesValues</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Chart_DataSeriesValues</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Chart.html">PHPExcel_Chart</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_Chart_DataSeriesValues object</h2>
<pre>__construct($dataType, $dataSource, $formatCode, $pointCount, $dataValues, $marker) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$dataType</h4></div>
<div class="subelement argument"><h4>$dataSource</h4></div>
<div class="subelement argument"><h4>$formatCode</h4></div>
<div class="subelement argument"><h4>$pointCount</h4></div>
<div class="subelement argument"><h4>$dataValues</h4></div>
<div class="subelement argument"><h4>$marker</h4></div>
</div></div>
</div>
<a id="method_getDataSource"></a><div class="element clickable method public method_getDataSource" data-toggle="collapse" data-target=".method_getDataSource .collapse">
<h2>Get Series Data Source (formula)</h2>
<pre>getDataSource() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getDataType"></a><div class="element clickable method public method_getDataType" data-toggle="collapse" data-target=".method_getDataType .collapse">
<h2>Get Series Data Type</h2>
<pre>getDataType() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getDataValue"></a><div class="element clickable method public method_getDataValue" data-toggle="collapse" data-target=".method_getDataValue .collapse">
<h2>Get the first Series Data value</h2>
<pre>getDataValue() : mixed</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>mixed</code></div>
</div></div>
</div>
<a id="method_getDataValues"></a><div class="element clickable method public method_getDataValues" data-toggle="collapse" data-target=".method_getDataValues .collapse">
<h2>Get Series Data Values</h2>
<pre>getDataValues() : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>array</code>of mixed</div>
</div></div>
</div>
<a id="method_getFormatCode"></a><div class="element clickable method public method_getFormatCode" data-toggle="collapse" data-target=".method_getFormatCode .collapse">
<h2>Get Series Format Code</h2>
<pre>getFormatCode() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getPointCount"></a><div class="element clickable method public method_getPointCount" data-toggle="collapse" data-target=".method_getPointCount .collapse">
<h2>Get Series Point Count</h2>
<pre>getPointCount() : integer</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>integer</code></div>
</div></div>
</div>
<a id="method_getPointMarker"></a><div class="element clickable method public method_getPointMarker" data-toggle="collapse" data-target=".method_getPointMarker .collapse">
<h2>Get Point Marker</h2>
<pre>getPointMarker() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_isMultiLevelSeries"></a><div class="element clickable method public method_isMultiLevelSeries" data-toggle="collapse" data-target=".method_isMultiLevelSeries .collapse">
<h2>Identify if the Data Series is a multi-level or a simple series</h2>
<pre>isMultiLevelSeries() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_multiLevelCount"></a><div class="element clickable method public method_multiLevelCount" data-toggle="collapse" data-target=".method_multiLevelCount .collapse">
<h2>Return the level count of a multi-level Data Series</h2>
<pre>multiLevelCount() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_refresh"></a><div class="element clickable method public method_refresh" data-toggle="collapse" data-target=".method_refresh .collapse">
<h2>refresh()
        </h2>
<pre>refresh(\PHPExcel_Worksheet $worksheet, $flatten) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$worksheet</h4></div>
<div class="subelement argument"><h4>$flatten</h4></div>
</div></div>
</div>
<a id="method_setDataSource"></a><div class="element clickable method public method_setDataSource" data-toggle="collapse" data-target=".method_setDataSource .collapse">
<h2>Set Series Data Source (formula)</h2>
<pre>setDataSource(string $dataSource, $refreshDataValues) : <a href="../classes/PHPExcel_Chart_DataSeriesValues.html">\PHPExcel_Chart_DataSeriesValues</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dataSource</h4>
<code>string</code>
</div>
<div class="subelement argument"><h4>$refreshDataValues</h4></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_DataSeriesValues.html">\PHPExcel_Chart_DataSeriesValues</a></code></div>
</div></div>
</div>
<a id="method_setDataType"></a><div class="element clickable method public method_setDataType" data-toggle="collapse" data-target=".method_setDataType .collapse">
<h2>Set Series Data Type</h2>
<pre>setDataType(string $dataType) : <a href="../classes/PHPExcel_Chart_DataSeriesValues.html">\PHPExcel_Chart_DataSeriesValues</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dataType</h4>
<code>string</code><p>Datatype of this data series
							Typical values are:
								PHPExcel_Chart_DataSeriesValues::DATASERIES_TYPE_STRING
									Normally used for axis point values
								PHPExcel_Chart_DataSeriesValues::DATASERIES_TYPE_NUMBER
									Normally used for chart data values</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_DataSeriesValues.html">\PHPExcel_Chart_DataSeriesValues</a></code></div>
</div></div>
</div>
<a id="method_setDataValues"></a><div class="element clickable method public method_setDataValues" data-toggle="collapse" data-target=".method_setDataValues .collapse">
<h2>Set Series Data Values</h2>
<pre>setDataValues(array $dataValues, boolean $refreshDataSource) : <a href="../classes/PHPExcel_Chart_DataSeriesValues.html">\PHPExcel_Chart_DataSeriesValues</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dataValues</h4>
<code>array</code>
</div>
<div class="subelement argument">
<h4>$refreshDataSource</h4>
<code>boolean</code><p>TRUE - refresh the value of _dataSource based on the values of $dataValues
                FALSE - don't change the value of _dataSource</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_DataSeriesValues.html">\PHPExcel_Chart_DataSeriesValues</a></code></div>
</div></div>
</div>
<a id="method_setFormatCode"></a><div class="element clickable method public method_setFormatCode" data-toggle="collapse" data-target=".method_setFormatCode .collapse">
<h2>Set Series Format Code</h2>
<pre>setFormatCode(string $formatCode) : <a href="../classes/PHPExcel_Chart_DataSeriesValues.html">\PHPExcel_Chart_DataSeriesValues</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$formatCode</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_DataSeriesValues.html">\PHPExcel_Chart_DataSeriesValues</a></code></div>
</div></div>
</div>
<a id="method_setPointMarker"></a><div class="element clickable method public method_setPointMarker" data-toggle="collapse" data-target=".method_setPointMarker .collapse">
<h2>Set Point Marker</h2>
<pre>setPointMarker(string $marker) : <a href="../classes/PHPExcel_Chart_DataSeriesValues.html">\PHPExcel_Chart_DataSeriesValues</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$marker</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_DataSeriesValues.html">\PHPExcel_Chart_DataSeriesValues</a></code></div>
</div></div>
</div>
<a id="method__stripNulls"></a><div class="element clickable method private method__stripNulls" data-toggle="collapse" data-target=".method__stripNulls .collapse">
<h2>_stripNulls()
        </h2>
<pre>_stripNulls($var) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$var</h4></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__dataSource"> </a><div class="element clickable property private property__dataSource" data-toggle="collapse" data-target=".property__dataSource .collapse">
<h2></h2>
<pre>$_dataSource : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__dataType"> </a><div class="element clickable property private property__dataType" data-toggle="collapse" data-target=".property__dataType .collapse">
<h2></h2>
<pre>$_dataType : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__dataTypeValues"> </a><div class="element clickable property private property__dataTypeValues" data-toggle="collapse" data-target=".property__dataTypeValues .collapse">
<h2></h2>
<pre>$_dataTypeValues </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__dataValues"> </a><div class="element clickable property private property__dataValues" data-toggle="collapse" data-target=".property__dataValues .collapse">
<h2></h2>
<pre>$_dataValues : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__formatCode"> </a><div class="element clickable property private property__formatCode" data-toggle="collapse" data-target=".property__formatCode .collapse">
<h2></h2>
<pre>$_formatCode : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__marker"> </a><div class="element clickable property private property__marker" data-toggle="collapse" data-target=".property__marker .collapse">
<h2></h2>
<pre>$_marker : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__pointCount"> </a><div class="element clickable property private property__pointCount" data-toggle="collapse" data-target=".property__pointCount .collapse">
<h2></h2>
<pre>$_pointCount : integer</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_DATASERIES_TYPE_NUMBER"> </a><div class="element clickable constant  constant_DATASERIES_TYPE_NUMBER" data-toggle="collapse" data-target=".constant_DATASERIES_TYPE_NUMBER .collapse">
<h2>DATASERIES_TYPE_NUMBER</h2>
<pre>DATASERIES_TYPE_NUMBER </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_DATASERIES_TYPE_STRING"> </a><div class="element clickable constant  constant_DATASERIES_TYPE_STRING" data-toggle="collapse" data-target=".constant_DATASERIES_TYPE_STRING .collapse">
<h2>DATASERIES_TYPE_STRING</h2>
<pre>DATASERIES_TYPE_STRING </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:34Z.<br></footer></div>
</div>
</body>
</html>
