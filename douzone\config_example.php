<?php
/**
 * 더존 API 연동 설정 파일 예시
 */

return [
    // 더존 API 설정
    'douzon' => [
        'api_base_url' => 'https://your-douzon-server.com',
        'api_key' => 'your-api-key-here',
        'company_code' => '1000', // 회사코드
        'division_code' => '1000', // 회계단위
        'timeout' => 30
    ],
    
    // 데이터베이스 설정
    'database' => [
        'host' => 'localhost',
        'dbname' => 'your_database',
        'username' => 'your_username',
        'password' => 'your_password',
        'charset' => 'utf8'
    ],
    
    // 로그 설정
    'logging' => [
        'log_file' => '/var/log/douzon_api.log',
        'error_file' => '/var/log/douzon_error.log',
        'debug_mode' => true
    ],
    
    // 배치 처리 설정
    'batch' => [
        'max_items_per_request' => 100, // 한 번에 처리할 최대 건수
        'retry_attempts' => 3, // 실패시 재시도 횟수
        'retry_delay' => 5 // 재시도 대기시간(초)
    ]
];
?>