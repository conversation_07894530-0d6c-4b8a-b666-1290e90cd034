<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_String</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_ControlCharacterOOXML2PHP" title="ControlCharacterOOXML2PHP :: Convert from OpenXML escaped control character to PHP control character"><span class="description">Convert from OpenXML escaped control character to PHP control character</span><pre>ControlCharacterOOXML2PHP()</pre></a></li>
<li class="method public "><a href="#method_ControlCharacterPHP2OOXML" title="ControlCharacterPHP2OOXML :: Convert from PHP control character to OpenXML escaped control character"><span class="description">Convert from PHP control character to OpenXML escaped control character</span><pre>ControlCharacterPHP2OOXML()</pre></a></li>
<li class="method public "><a href="#method_ConvertEncoding" title="ConvertEncoding :: Convert string from one encoding to another."><span class="description">Convert string from one encoding to another.</span><pre>ConvertEncoding()</pre></a></li>
<li class="method public "><a href="#method_CountCharacters" title="CountCharacters :: Get character count."><span class="description">Get character count.</span><pre>CountCharacters()</pre></a></li>
<li class="method public "><a href="#method_FormatNumber" title="FormatNumber :: Formats a numeric value as a string for output in various output writers forcing
point as decimal separator in case locale is other than English."><span class="description">Formats a numeric value as a string for output in various output writers forcing
point as decimal separator in case locale is other than English.</span><pre>FormatNumber()</pre></a></li>
<li class="method public "><a href="#method_IsUTF8" title="IsUTF8 :: Check if a string contains UTF8 data"><span class="description">Check if a string contains UTF8 data</span><pre>IsUTF8()</pre></a></li>
<li class="method public "><a href="#method_SYLKtoUTF8" title="SYLKtoUTF8 :: Convert SYLK encoded string to UTF-8"><span class="description">Convert SYLK encoded string to UTF-8</span><pre>SYLKtoUTF8()</pre></a></li>
<li class="method public "><a href="#method_SanitizeUTF8" title="SanitizeUTF8 :: Try to sanitize UTF8, stripping invalid byte sequences."><span class="description">Try to sanitize UTF8, stripping invalid byte sequences.</span><pre>SanitizeUTF8()</pre></a></li>
<li class="method public "><a href="#method_StrToLower" title="StrToLower :: Convert a UTF-8 encoded string to lower case"><span class="description">Convert a UTF-8 encoded string to lower case</span><pre>StrToLower()</pre></a></li>
<li class="method public "><a href="#method_StrToTitle" title="StrToTitle :: Convert a UTF-8 encoded string to title/proper case
   (uppercase every first character in each word, lower case all other characters)"><span class="description">Convert a UTF-8 encoded string to title/proper case
   (uppercase every first character in each word, lower case all other characters)</span><pre>StrToTitle()</pre></a></li>
<li class="method public "><a href="#method_StrToUpper" title="StrToUpper :: Convert a UTF-8 encoded string to upper case"><span class="description">Convert a UTF-8 encoded string to upper case</span><pre>StrToUpper()</pre></a></li>
<li class="method public "><a href="#method_Substring" title="Substring :: Get a substring of a UTF-8 encoded string."><span class="description">Get a substring of a UTF-8 encoded string.</span><pre>Substring()</pre></a></li>
<li class="method public "><a href="#method_UTF8toBIFF8UnicodeLong" title="UTF8toBIFF8UnicodeLong :: Converts a UTF-8 string into BIFF8 Unicode string data (16-bit string length)
Writes the string using uncompressed notation, no rich text, no Asian phonetics
If mbstring extension is not available, ASCII is assumed, and compressed notation is used
although this will give wrong results for non-ASCII strings
see OpenOffice.org's Documentation of the Microsoft Excel File Format, sect."><span class="description">Converts a UTF-8 string into BIFF8 Unicode string data (16-bit string length)
Writes the string using uncompressed notation, no rich text, no Asian phonetics
If mbstring extension is not available, ASCII is assumed, and compressed notation is used
although this will give wrong results for non-ASCII strings
see OpenOffice.org's Documentation of the Microsoft Excel File Format, sect.</span><pre>UTF8toBIFF8UnicodeLong()</pre></a></li>
<li class="method public "><a href="#method_UTF8toBIFF8UnicodeShort" title="UTF8toBIFF8UnicodeShort :: Converts a UTF-8 string into BIFF8 Unicode string data (8-bit string length)
Writes the string using uncompressed notation, no rich text, no Asian phonetics
If mbstring extension is not available, ASCII is assumed, and compressed notation is used
although this will give wrong results for non-ASCII strings
see OpenOffice.org's Documentation of the Microsoft Excel File Format, sect."><span class="description">Converts a UTF-8 string into BIFF8 Unicode string data (8-bit string length)
Writes the string using uncompressed notation, no rich text, no Asian phonetics
If mbstring extension is not available, ASCII is assumed, and compressed notation is used
although this will give wrong results for non-ASCII strings
see OpenOffice.org's Documentation of the Microsoft Excel File Format, sect.</span><pre>UTF8toBIFF8UnicodeShort()</pre></a></li>
<li class="method public "><a href="#method_buildCharacterSets" title="buildCharacterSets :: "><span class="description">buildCharacterSets()
        </span><pre>buildCharacterSets()</pre></a></li>
<li class="method public "><a href="#method_convertToNumberIfFraction" title="convertToNumberIfFraction :: Identify whether a string contains a fractional numeric value,
   and convert it to a numeric if it is"><span class="description">Identify whether a string contains a fractional numeric value,
   and convert it to a numeric if it is</span><pre>convertToNumberIfFraction()</pre></a></li>
<li class="method public "><a href="#method_getCurrencyCode" title="getCurrencyCode :: Get the currency code."><span class="description">Get the currency code.</span><pre>getCurrencyCode()</pre></a></li>
<li class="method public "><a href="#method_getDecimalSeparator" title="getDecimalSeparator :: Get the decimal separator."><span class="description">Get the decimal separator.</span><pre>getDecimalSeparator()</pre></a></li>
<li class="method public "><a href="#method_getIsIconvEnabled" title="getIsIconvEnabled :: Get whether iconv extension is available"><span class="description">Get whether iconv extension is available</span><pre>getIsIconvEnabled()</pre></a></li>
<li class="method public "><a href="#method_getIsMbstringEnabled" title="getIsMbstringEnabled :: Get whether mbstring extension is available"><span class="description">Get whether mbstring extension is available</span><pre>getIsMbstringEnabled()</pre></a></li>
<li class="method public "><a href="#method_getThousandsSeparator" title="getThousandsSeparator :: Get the thousands separator."><span class="description">Get the thousands separator.</span><pre>getThousandsSeparator()</pre></a></li>
<li class="method public "><a href="#method_setCurrencyCode" title="setCurrencyCode :: Set the currency code."><span class="description">Set the currency code.</span><pre>setCurrencyCode()</pre></a></li>
<li class="method public "><a href="#method_setDecimalSeparator" title="setDecimalSeparator :: Set the decimal separator."><span class="description">Set the decimal separator.</span><pre>setDecimalSeparator()</pre></a></li>
<li class="method public "><a href="#method_setThousandsSeparator" title="setThousandsSeparator :: Set the thousands separator."><span class="description">Set the thousands separator.</span><pre>setThousandsSeparator()</pre></a></li>
<li class="method public "><a href="#method_testStringAsNumeric" title="testStringAsNumeric :: Retrieve any leading numeric part of a string, or return the full string if no leading numeric
(handles basic integer or float, but not exponent or non decimal)"><span class="description">Retrieve any leading numeric part of a string, or return the full string if no leading numeric
(handles basic integer or float, but not exponent or non decimal)</span><pre>testStringAsNumeric()</pre></a></li>
<li class="method public "><a href="#method_utf16_decode" title="utf16_decode :: Decode UTF-16 encoded strings."><span class="description">Decode UTF-16 encoded strings.</span><pre>utf16_decode()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="method private "><a href="#method__buildControlCharacters" title="_buildControlCharacters :: Build control characters array"><span class="description">Build control characters array</span><pre>_buildControlCharacters()</pre></a></li>
<li class="method private "><a href="#method__buildSYLKCharacters" title="_buildSYLKCharacters :: Build SYLK characters array"><span class="description">Build SYLK characters array</span><pre>_buildSYLKCharacters()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__SYLKCharacters" title="$_SYLKCharacters :: SYLK Characters array"><span class="description"></span><pre>$_SYLKCharacters</pre></a></li>
<li class="property private "><a href="#property__controlCharacters" title="$_controlCharacters :: Control characters array"><span class="description"></span><pre>$_controlCharacters</pre></a></li>
<li class="property private "><a href="#property__currencyCode" title="$_currencyCode :: Currency code"><span class="description"></span><pre>$_currencyCode</pre></a></li>
<li class="property private "><a href="#property__decimalSeparator" title="$_decimalSeparator :: Decimal separator"><span class="description"></span><pre>$_decimalSeparator</pre></a></li>
<li class="property private "><a href="#property__isIconvEnabled" title="$_isIconvEnabled :: Is iconv extension avalable?"><span class="description"></span><pre>$_isIconvEnabled</pre></a></li>
<li class="property private "><a href="#property__isMbstringEnabled" title="$_isMbstringEnabled :: Is mbstring extension avalable?"><span class="description"></span><pre>$_isMbstringEnabled</pre></a></li>
<li class="property private "><a href="#property__thousandsSeparator" title="$_thousandsSeparator :: Thousands separator"><span class="description"></span><pre>$_thousandsSeparator</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul><li class="constant  "><a href="#constant_STRING_REGEXP_FRACTION" title="STRING_REGEXP_FRACTION :: "><span class="description">STRING_REGEXP_FRACTION</span><pre>STRING_REGEXP_FRACTION</pre></a></li></ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_String"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_String.html">PHPExcel_Shared_String</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Shared_String</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Shared.html">PHPExcel_Shared</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_ControlCharacterOOXML2PHP"></a><div class="element clickable method public method_ControlCharacterOOXML2PHP" data-toggle="collapse" data-target=".method_ControlCharacterOOXML2PHP .collapse">
<h2>Convert from OpenXML escaped control character to PHP control character</h2>
<pre>ControlCharacterOOXML2PHP(string $value) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><h2>Excel 2007 team:</h2>

<p>That's correct, control characters are stored directly in the shared-strings table.
We do encode characters that cannot be represented in XML using the following escape sequence:
<em>xHHHH</em> where H represents a hexadecimal character in the character's value...
So you could end up with something like <em>x0008</em> in a string (either in a cell value (<v>)
element or in the shared string <t> element.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code><p>Value to unescape</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_ControlCharacterPHP2OOXML"></a><div class="element clickable method public method_ControlCharacterPHP2OOXML" data-toggle="collapse" data-target=".method_ControlCharacterPHP2OOXML .collapse">
<h2>Convert from PHP control character to OpenXML escaped control character</h2>
<pre>ControlCharacterPHP2OOXML(string $value) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><h2>Excel 2007 team:</h2>

<p>That's correct, control characters are stored directly in the shared-strings table.
We do encode characters that cannot be represented in XML using the following escape sequence:
<em>xHHHH</em> where H represents a hexadecimal character in the character's value...
So you could end up with something like <em>x0008</em> in a string (either in a cell value (<v>)
element or in the shared string <t> element.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code><p>Value to escape</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_ConvertEncoding"></a><div class="element clickable method public method_ConvertEncoding" data-toggle="collapse" data-target=".method_ConvertEncoding .collapse">
<h2>Convert string from one encoding to another.</h2>
<pre>ConvertEncoding(string $value, string $to, string $from) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>First try mbstring, then iconv, finally strlen</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$to</h4>
<code>string</code><p>Encoding to convert to, e.g. 'UTF-8'</p>
</div>
<div class="subelement argument">
<h4>$from</h4>
<code>string</code><p>Encoding to convert from, e.g. 'UTF-16LE'</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_CountCharacters"></a><div class="element clickable method public method_CountCharacters" data-toggle="collapse" data-target=".method_CountCharacters .collapse">
<h2>Get character count.</h2>
<pre>CountCharacters(string $value, string $enc) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>First try mbstring, then iconv, finally strlen</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$enc</h4>
<code>string</code><p>Encoding</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Character count</div>
</div></div>
</div>
<a id="method_FormatNumber"></a><div class="element clickable method public method_FormatNumber" data-toggle="collapse" data-target=".method_FormatNumber .collapse">
<h2>Formats a numeric value as a string for output in various output writers forcing
point as decimal separator in case locale is other than English.</h2>
<pre>FormatNumber(mixed $value) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_IsUTF8"></a><div class="element clickable method public method_IsUTF8" data-toggle="collapse" data-target=".method_IsUTF8 .collapse">
<h2>Check if a string contains UTF8 data</h2>
<pre>IsUTF8(string $value) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_SYLKtoUTF8"></a><div class="element clickable method public method_SYLKtoUTF8" data-toggle="collapse" data-target=".method_SYLKtoUTF8 .collapse">
<h2>Convert SYLK encoded string to UTF-8</h2>
<pre>SYLKtoUTF8(string $pValue) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>UTF-8 encoded string</div>
</div></div>
</div>
<a id="method_SanitizeUTF8"></a><div class="element clickable method public method_SanitizeUTF8" data-toggle="collapse" data-target=".method_SanitizeUTF8 .collapse">
<h2>Try to sanitize UTF8, stripping invalid byte sequences.</h2>
<pre>SanitizeUTF8(string $value) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Not perfect. Does not surrogate characters.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_StrToLower"></a><div class="element clickable method public method_StrToLower" data-toggle="collapse" data-target=".method_StrToLower .collapse">
<h2>Convert a UTF-8 encoded string to lower case</h2>
<pre>StrToLower(string $pValue) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>UTF-8 encoded string</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_StrToTitle"></a><div class="element clickable method public method_StrToTitle" data-toggle="collapse" data-target=".method_StrToTitle .collapse">
<h2>Convert a UTF-8 encoded string to title/proper case
   (uppercase every first character in each word, lower case all other characters)</h2>
<pre>StrToTitle(string $pValue) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>UTF-8 encoded string</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_StrToUpper"></a><div class="element clickable method public method_StrToUpper" data-toggle="collapse" data-target=".method_StrToUpper .collapse">
<h2>Convert a UTF-8 encoded string to upper case</h2>
<pre>StrToUpper(string $pValue) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>UTF-8 encoded string</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_Substring"></a><div class="element clickable method public method_Substring" data-toggle="collapse" data-target=".method_Substring .collapse">
<h2>Get a substring of a UTF-8 encoded string.</h2>
<pre>Substring(string $pValue, int $pStart, int $pLength) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>First try mbstring, then iconv, finally strlen</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>UTF-8 encoded string</p>
</div>
<div class="subelement argument">
<h4>$pStart</h4>
<code>int</code><p>Start offset</p></div>
<div class="subelement argument">
<h4>$pLength</h4>
<code>int</code><p>Maximum number of characters in substring</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_UTF8toBIFF8UnicodeLong"></a><div class="element clickable method public method_UTF8toBIFF8UnicodeLong" data-toggle="collapse" data-target=".method_UTF8toBIFF8UnicodeLong .collapse">
<h2>Converts a UTF-8 string into BIFF8 Unicode string data (16-bit string length)
Writes the string using uncompressed notation, no rich text, no Asian phonetics
If mbstring extension is not available, ASCII is assumed, and compressed notation is used
although this will give wrong results for non-ASCII strings
see OpenOffice.org's Documentation of the Microsoft Excel File Format, sect.</h2>
<pre>UTF8toBIFF8UnicodeLong(string $value) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>2.5.3</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code><p>UTF-8 encoded string</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_UTF8toBIFF8UnicodeShort"></a><div class="element clickable method public method_UTF8toBIFF8UnicodeShort" data-toggle="collapse" data-target=".method_UTF8toBIFF8UnicodeShort .collapse">
<h2>Converts a UTF-8 string into BIFF8 Unicode string data (8-bit string length)
Writes the string using uncompressed notation, no rich text, no Asian phonetics
If mbstring extension is not available, ASCII is assumed, and compressed notation is used
although this will give wrong results for non-ASCII strings
see OpenOffice.org's Documentation of the Microsoft Excel File Format, sect.</h2>
<pre>UTF8toBIFF8UnicodeShort(string $value, mixed[] $arrcRuns) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>2.5.3</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code><p>UTF-8 encoded string</p>
</div>
<div class="subelement argument">
<h4>$arrcRuns</h4>
<code>mixed[]</code><p>Details of rich text runs in $value</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_buildCharacterSets"></a><div class="element clickable method public method_buildCharacterSets" data-toggle="collapse" data-target=".method_buildCharacterSets .collapse">
<h2>buildCharacterSets()
        </h2>
<pre>buildCharacterSets() </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_convertToNumberIfFraction"></a><div class="element clickable method public method_convertToNumberIfFraction" data-toggle="collapse" data-target=".method_convertToNumberIfFraction .collapse">
<h2>Identify whether a string contains a fractional numeric value,
   and convert it to a numeric if it is</h2>
<pre>convertToNumberIfFraction(string $operand) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$operand</h4>
<code>string</code><p>&$operand string value to test</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getCurrencyCode"></a><div class="element clickable method public method_getCurrencyCode" data-toggle="collapse" data-target=".method_getCurrencyCode .collapse">
<h2>Get the currency code.</h2>
<pre>getCurrencyCode() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>If it has not yet been set explicitly, try to obtain the
    symbol information from locale.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getDecimalSeparator"></a><div class="element clickable method public method_getDecimalSeparator" data-toggle="collapse" data-target=".method_getDecimalSeparator .collapse">
<h2>Get the decimal separator.</h2>
<pre>getDecimalSeparator() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>If it has not yet been set explicitly, try to obtain number
formatting information from locale.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getIsIconvEnabled"></a><div class="element clickable method public method_getIsIconvEnabled" data-toggle="collapse" data-target=".method_getIsIconvEnabled .collapse">
<h2>Get whether iconv extension is available</h2>
<pre>getIsIconvEnabled() : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getIsMbstringEnabled"></a><div class="element clickable method public method_getIsMbstringEnabled" data-toggle="collapse" data-target=".method_getIsMbstringEnabled .collapse">
<h2>Get whether mbstring extension is available</h2>
<pre>getIsMbstringEnabled() : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getThousandsSeparator"></a><div class="element clickable method public method_getThousandsSeparator" data-toggle="collapse" data-target=".method_getThousandsSeparator .collapse">
<h2>Get the thousands separator.</h2>
<pre>getThousandsSeparator() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>If it has not yet been set explicitly, try to obtain number
formatting information from locale.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_setCurrencyCode"></a><div class="element clickable method public method_setCurrencyCode" data-toggle="collapse" data-target=".method_setCurrencyCode .collapse">
<h2>Set the currency code.</h2>
<pre>setCurrencyCode(string $pValue) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used by PHPExcel_Style_NumberFormat::toFormattedString()
    to format output by PHPExcel_Writer_HTML and PHPExcel_Writer_PDF</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>Character for currency code</p></div>
</div></div>
</div>
<a id="method_setDecimalSeparator"></a><div class="element clickable method public method_setDecimalSeparator" data-toggle="collapse" data-target=".method_setDecimalSeparator .collapse">
<h2>Set the decimal separator.</h2>
<pre>setDecimalSeparator(string $pValue) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used by PHPExcel_Style_NumberFormat::toFormattedString()
to format output by PHPExcel_Writer_HTML and PHPExcel_Writer_PDF</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>Character for decimal separator</p></div>
</div></div>
</div>
<a id="method_setThousandsSeparator"></a><div class="element clickable method public method_setThousandsSeparator" data-toggle="collapse" data-target=".method_setThousandsSeparator .collapse">
<h2>Set the thousands separator.</h2>
<pre>setThousandsSeparator(string $pValue) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used by PHPExcel_Style_NumberFormat::toFormattedString()
to format output by PHPExcel_Writer_HTML and PHPExcel_Writer_PDF</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>Character for thousands separator</p></div>
</div></div>
</div>
<a id="method_testStringAsNumeric"></a><div class="element clickable method public method_testStringAsNumeric" data-toggle="collapse" data-target=".method_testStringAsNumeric .collapse">
<h2>Retrieve any leading numeric part of a string, or return the full string if no leading numeric
(handles basic integer or float, but not exponent or non decimal)</h2>
<pre>testStringAsNumeric(string $value) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>string or only the leading numeric part of the string</div>
</div></div>
</div>
<a id="method_utf16_decode"></a><div class="element clickable method public method_utf16_decode" data-toggle="collapse" data-target=".method_utf16_decode .collapse">
<h2>Decode UTF-16 encoded strings.</h2>
<pre>utf16_decode(string $str, $bom_be) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Can handle both BOM'ed data and un-BOM'ed data.
Assumes Big-Endian byte order if no BOM is available.
This function was taken from http://php.net/manual/en/function.utf8-decode.php
and $bom_be parameter added.</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>version</th>
<td>0.2 / 2010-05-13</td>
</tr>
<tr>
<th>author</th>
<td><a href="">Rasmus Andersson {@link http://rasmusandersson.se/}</a></td>
</tr>
<tr>
<th>author</th>
<td><a href="">vadik56</a></td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$str</h4>
<code>string</code><p>UTF-16 encoded data to decode.</p>
</div>
<div class="subelement argument"><h4>$bom_be</h4></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>UTF-8 / ISO encoded data.</div>
</div></div>
</div>
<a id="method__buildControlCharacters"></a><div class="element clickable method private method__buildControlCharacters" data-toggle="collapse" data-target=".method__buildControlCharacters .collapse">
<h2>Build control characters array</h2>
<pre>_buildControlCharacters() </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__buildSYLKCharacters"></a><div class="element clickable method private method__buildSYLKCharacters" data-toggle="collapse" data-target=".method__buildSYLKCharacters .collapse">
<h2>Build SYLK characters array</h2>
<pre>_buildSYLKCharacters() </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__SYLKCharacters"> </a><div class="element clickable property private property__SYLKCharacters" data-toggle="collapse" data-target=".property__SYLKCharacters .collapse">
<h2></h2>
<pre>$_SYLKCharacters </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>$var array</p></div></div></div>
</div>
<a id="property__controlCharacters"> </a><div class="element clickable property private property__controlCharacters" data-toggle="collapse" data-target=".property__controlCharacters .collapse">
<h2></h2>
<pre>$_controlCharacters : string[]</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__currencyCode"> </a><div class="element clickable property private property__currencyCode" data-toggle="collapse" data-target=".property__currencyCode .collapse">
<h2></h2>
<pre>$_currencyCode : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__decimalSeparator"> </a><div class="element clickable property private property__decimalSeparator" data-toggle="collapse" data-target=".property__decimalSeparator .collapse">
<h2></h2>
<pre>$_decimalSeparator : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__isIconvEnabled"> </a><div class="element clickable property private property__isIconvEnabled" data-toggle="collapse" data-target=".property__isIconvEnabled .collapse">
<h2></h2>
<pre>$_isIconvEnabled : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__isMbstringEnabled"> </a><div class="element clickable property private property__isMbstringEnabled" data-toggle="collapse" data-target=".property__isMbstringEnabled .collapse">
<h2></h2>
<pre>$_isMbstringEnabled : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__thousandsSeparator"> </a><div class="element clickable property private property__thousandsSeparator" data-toggle="collapse" data-target=".property__thousandsSeparator .collapse">
<h2></h2>
<pre>$_thousandsSeparator : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_STRING_REGEXP_FRACTION"> </a><div class="element clickable constant  constant_STRING_REGEXP_FRACTION" data-toggle="collapse" data-target=".constant_STRING_REGEXP_FRACTION .collapse">
<h2>STRING_REGEXP_FRACTION</h2>
<pre>STRING_REGEXP_FRACTION </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:37Z.<br></footer></div>
</div>
</body>
</html>
