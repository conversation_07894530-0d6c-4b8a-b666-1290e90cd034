#!/usr/local/bin/php -q
<?php
	// 0 9 * * * php -q /home/<USER>/sperp/verify_mant.psh
	# 개발이원화 검증 2개월 지연 알림
	$ROOT_PATH = "/home/<USER>";
	include($ROOT_PATH . "/inc/func.php");
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/Encode.php");
	include($ROOT_PATH . "/inc/func_state.php");

	$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
	if(empty($dbconn_sperp_posbank->success)) {
		echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
	}

	$dbconn_posbank_intra = new DBController($db['posbank_intra']);
	if(empty($dbconn_posbank_intra->success)) {
		echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패";
	}
	/**********************************************************/

	if(in_array(date('w'),array("0","6"))){
		echo date("Y-m-d") . " - 휴무일\n";
		## 스케즐 처리 상황 intra DB에 저장
		crontab_execution(86400, "개발이원화 검증 2개월 지연 알림");
		exit;
	}

	$SQL = "SELECT NAME FROM HOLIDAY_DATA WHERE to_char(HDATE,'YYYYMMDD')='".date('Ymd')."'";
	$HOLIDAY_NM = $dbconn_sperp_posbank->query_one($SQL);
	if($HOLIDAY_NM){
		echo date("Y-m-d") . " - 휴무일(".$HOLIDAY_NM.")\n";
		## 스케즐 처리 상황 intra DB에 저장
		crontab_execution(86400, "개발이원화 검증 2개월 지연 알림");
		exit;
	}

	$Checkday = date("Ymd", strtotime ("-2 month"));

	$arr_STATE = arr_VERIFY_MANT_STATE(); // 상태
	$arr_A_LEVEL = arr_VERIFY_MANT_A_LEVEL(); // 검증등급
	$arr_A_GUBUN = arr_VERIFY_MANT_A_GUBUN(); // 검증구분

	$STATE_Case =  Case_Convert("STATE",$arr_STATE);
	$A_LEVEL_Case =  Case_Convert("A_LEVEL",$arr_A_LEVEL);
	$A_GUBUN_Case =  Case_Convert("A_GUBUN",$arr_A_GUBUN);

	$SQL = "select 
			RCT_CODE||HDATE||HNO HID
			,A_LEVEL
			,".$A_LEVEL_Case." A_LEVEL_NM
			,A_GUBUN
			,".$A_GUBUN_Case." A_GUBUN_NM
			,A_PCOMPANY
			,A_ITEM_BAS
			,F_BAS(A_ITEM_BAS,'NAME') A_ITEM_BAS_NM
			,STATE
			,".$STATE_Case." STATE_NM
			,A_MDATE
			,to_char(to_date(A_MDATE,'YYYY-MM-DD'),'YYYY-MM-DD') A_MDATE2
			,A_STNAME
		from VERIFY_MANT
		where 
			STATE not in ('E','9') 
			and A_MDATE<'".$Checkday."'
			and NVL(B_STATE,'1')<>'2' 
			and NVL(C_STATE,'1')<>'2' 
			and NVL(C2_STATE,'1')<>'2' 
			and NVL(D_STATE,'1')<>'2' 
			and NVL(D2_STATE,'1')<>'2' 
			and NVL(E_STATE,'1')<>'2'
		order by HID ";
	$arrRow = $dbconn_sperp_posbank->query_rows($SQL);

	// 발송 대상자
	$SQL2 = "SELECT BAS_OP4 as STCODE FROM BAS WHERE BAS_CODE='E074'";
	$stcode_str = $dbconn_sperp_posbank->query_one($SQL2);



	//$stcode_str = "100695";
	$arr_ST = explode(",", $stcode_str);

	$style = "font-size:12px;line-height:25px;border:1px solid #333333;padding:3px;line-height:120%;";
	$style .= "text-overflow:ellipsis; table-layout:fixed;  overflow-x:hidden; overflow-y:hidden; white-space:nowrap;";
	$title = "[개발이원화 검증 2개월 지연] " . sizeof($arrRow) . "건";
	$content = "<div>\n";
	$content .= "<div style=\"font-size:12px;line-height:50px;\"><b>개발이원화 검증처리가 2개월 이상 지연된 내역</b></div>\n";
	$content .= "<div><a href=\"https://www.spqms.co.kr/?pageCode=MTI0MjI\" target=\"_blank\">[개발/이원화 검증 관리 보러가기]</a></div>";
	$content .= "<table>";
	$content .= "<tr>";
	$content .= "<th style=\"".$style."width:20px;\">No</th>";
	$content .= "<th style=\"".$style."width:110px;\">고유번호</th>";
	$content .= "<th style=\"".$style."width:25px;\">검증<br>등급</th>";
	$content .= "<th style=\"".$style."width:35px;\">검증<br>구분</th>";
	$content .= "<th style=\"".$style."\">제조사</th>";
	$content .= "<th style=\"".$style."width:80px;\">검증<br>품목</th>";
	$content .= "<th style=\"".$style."width:70px;\">상태</th>";
	$content .= "<th style=\"".$style."width:70px;\">의뢰일자</th>";
	$content .= "<th style=\"".$style."\">의뢰자</th>";
	$content .= "</tr>\n";
	if($arrRow){
		foreach($arrRow as $key => $row) {
			$link = "https://www.spqms.co.kr/?pageCode=MTI0MjI=&searHID=".$row['HID']."";

			$content .= "<tr>";
			$content .= "<td style=\"".$style." text-align:center;\">".($key+1)."</td>";
			$content .= "<td style=\"".$style." text-align:center;\"><a href=\"".$link."\" target=\"_blank\">".$row['HID']."</a></td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['A_LEVEL_NM']."</td>";
			$content .= "<td style=\"".$style." letter-spacing:-1px; text-align:center;\">".$row['A_GUBUN_NM']."</td>";
			$content .= "<td style=\"".$style."\">".$row['A_PCOMPANY']."</td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['A_ITEM_BAS_NM']."</td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['STATE_NM']."</td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['A_MDATE2']."</td>";
			$content .= "<td style=\"".$style."\">".$row['A_STNAME']."</td>";
			$content .= "</tr>\n";
		}
	}
	$content .= "</table>\n";
	$content .= "</div>";


	//인트라넷 업무연락 보내는 함수(ERP 사원코드)
	$rs = intra_send_erp('',$arr_ST,$title,$content);
	echo date("Y-m-d H:i:s")." 업무연락 발송 \n";
	echo $title . "\n";
	echo "(".$stcode_str.") \n";
	echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";



	##### End. 2024-07-02. 신규 스케줄링
	###########################################


	## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(86400, "개발이원화 검증 2개월 지연 알림");

	echo date("Y-m-d H:i:s")." - 끝\n";
?>
