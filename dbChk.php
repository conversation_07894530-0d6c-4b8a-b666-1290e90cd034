<?php
@ini_set('display_error', 'On');
@ini_get('include_path');
@error_reporting(E_ALL^E_WARNING^E_NOTICE);
#phpinfo();

$serverName = "210.220.188.250";
$connectionOptions = array(
    "Database" => "DZICUBE",
    "Uid" => "dzicube",
    "PWD" => "ejwhs123$"
);
//Establishes the connection
$conn = sqlsrv_connect($serverName, $connectionOptions);
if($conn)   echo "250 Mssql Connected!"; else echo "MSSQL fail";


echo "<hr>";


$mysqli = new mysqli("210.220.188.51","root","dhfksTl33$$","erp");

// Check connection
if ($mysqli -> connect_errno) {
  echo "51 Failed to connect to MySQL: " . $mysqli -> connect_error;
  exit();
}else{
  echo "Mysql 51 connected";
}

echo "<hr>";


$mysqli = new mysqli("***************","root","dhfksTl33$$","posbankintra");

// Check connection
if ($mysqli -> connect_errno) {
  echo "147 Failed to connect to MySQL: " . $mysqli -> connect_error;
  exit();
}else{
  echo "Mysql 147 connected";
}

echo "<hr>";


$db['sperp_posbank']['host'] = "**************/ORCL";
$db['sperp_posbank']['userid'] = "PBKADMIN";
$db['sperp_posbank']['password'] = "ALSUDHKTJRFB";
$db['sperp_posbank']['database'] = "PBKADMIN";
$db['sperp_posbank']['charset'] = "utf8";
$db['sperp_posbank']['dbdriver'] = "oracle";


$conn = oci_connect($db['sperp_posbank']['userid'], $db['sperp_posbank']['password'], $db['sperp_posbank']['host'], $db['sperp_posbank']['charset']);

if (!$conn) {
	$e = oci_error();
	 print_r($e);
//    trigger_error(htmlentities($e['message'], ENT_QUOTES), E_USER_ERROR);
}else{
	echo "ORACLE 32 connected";
}



?>
