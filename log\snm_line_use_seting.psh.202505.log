<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250501 | sEdate : 20250612<br>
<br>
 MAX_DAY  : 20250611<br>
 DIFF_DAY  : 42<br>
 20250501 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250611 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 이미 등록되어있음......<br>
 20250611 이미 등록되어있음......<br>
 20250611 이미 등록되어있음......<br>
 20250611 이미 등록되어있음......<br>
 20250611 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250501001<br>
 입력 SLUS_ID  : 20250501002<br>
 입력 SLUS_ID  : 20250501003<br>
 입력 SLUS_ID  : 20250501004<br>
 입력 SLUS_ID  : 20250501005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250502 | sEdate : 20250613<br>
<br>
 MAX_DAY  : 20250612<br>
 DIFF_DAY  : 42<br>
 20250502 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250612 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 이미 등록되어있음......<br>
 20250612 이미 등록되어있음......<br>
 20250612 이미 등록되어있음......<br>
 20250612 이미 등록되어있음......<br>
 20250612 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250502001<br>
 입력 SLUS_ID  : 20250502002<br>
 입력 SLUS_ID  : 20250502003<br>
 입력 SLUS_ID  : 20250502004<br>
 입력 SLUS_ID  : 20250502005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250503 | sEdate : 20250614<br>
<br>
 MAX_DAY  : 20250613<br>
 DIFF_DAY  : 42<br>
 20250503 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 이미 등록되어있음......<br>
 20250613 이미 등록되어있음......<br>
 20250613 이미 등록되어있음......<br>
 20250613 이미 등록되어있음......<br>
 20250613 이미 등록되어있음......<br>
 20250614 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250504 | sEdate : 20250615<br>
<br>
 MAX_DAY  : 20250613<br>
 DIFF_DAY  : 42<br>
 20250504 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 이미 등록되어있음......<br>
 20250613 이미 등록되어있음......<br>
 20250613 이미 등록되어있음......<br>
 20250613 이미 등록되어있음......<br>
 20250613 이미 등록되어있음......<br>
 20250614 주말일 경우 패스.....<br>
 20250615 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250505 | sEdate : 20250616<br>
<br>
 MAX_DAY  : 20250613<br>
 DIFF_DAY  : 42<br>
 20250505 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250613 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 이미 등록되어있음......<br>
 20250613 이미 등록되어있음......<br>
 20250613 이미 등록되어있음......<br>
 20250613 이미 등록되어있음......<br>
 20250613 이미 등록되어있음......<br>
 20250614 주말일 경우 패스.....<br>
 20250615 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250505001<br>
 입력 SLUS_ID  : 20250505002<br>
 입력 SLUS_ID  : 20250505003<br>
 입력 SLUS_ID  : 20250505004<br>
 입력 SLUS_ID  : 20250505005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250506 | sEdate : 20250617<br>
<br>
 MAX_DAY  : 20250616<br>
 DIFF_DAY  : 42<br>
 20250506 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250616 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 이미 등록되어있음......<br>
 20250616 이미 등록되어있음......<br>
 20250616 이미 등록되어있음......<br>
 20250616 이미 등록되어있음......<br>
 20250616 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250506001<br>
 입력 SLUS_ID  : 20250506002<br>
 입력 SLUS_ID  : 20250506003<br>
 입력 SLUS_ID  : 20250506004<br>
 입력 SLUS_ID  : 20250506005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250507 | sEdate : 20250618<br>
<br>
 MAX_DAY  : 20250617<br>
 DIFF_DAY  : 42<br>
 20250507 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250617 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 이미 등록되어있음......<br>
 20250617 이미 등록되어있음......<br>
 20250617 이미 등록되어있음......<br>
 20250617 이미 등록되어있음......<br>
 20250617 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250507001<br>
 입력 SLUS_ID  : 20250507002<br>
 입력 SLUS_ID  : 20250507003<br>
 입력 SLUS_ID  : 20250507004<br>
 입력 SLUS_ID  : 20250507005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250508 | sEdate : 20250619<br>
<br>
 MAX_DAY  : 20250618<br>
 DIFF_DAY  : 42<br>
 20250508 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250618 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 이미 등록되어있음......<br>
 20250618 이미 등록되어있음......<br>
 20250618 이미 등록되어있음......<br>
 20250618 이미 등록되어있음......<br>
 20250618 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250508001<br>
 입력 SLUS_ID  : 20250508002<br>
 입력 SLUS_ID  : 20250508003<br>
 입력 SLUS_ID  : 20250508004<br>
 입력 SLUS_ID  : 20250508005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250509 | sEdate : 20250620<br>
<br>
 MAX_DAY  : 20250619<br>
 DIFF_DAY  : 42<br>
 20250509 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250619 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 이미 등록되어있음......<br>
 20250619 이미 등록되어있음......<br>
 20250619 이미 등록되어있음......<br>
 20250619 이미 등록되어있음......<br>
 20250619 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250509001<br>
 입력 SLUS_ID  : 20250509002<br>
 입력 SLUS_ID  : 20250509003<br>
 입력 SLUS_ID  : 20250509004<br>
 입력 SLUS_ID  : 20250509005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250510 | sEdate : 20250621<br>
<br>
 MAX_DAY  : 20250620<br>
 DIFF_DAY  : 42<br>
 20250510 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 이미 등록되어있음......<br>
 20250620 이미 등록되어있음......<br>
 20250620 이미 등록되어있음......<br>
 20250620 이미 등록되어있음......<br>
 20250620 이미 등록되어있음......<br>
 20250621 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250511 | sEdate : 20250622<br>
<br>
 MAX_DAY  : 20250620<br>
 DIFF_DAY  : 42<br>
 20250511 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 이미 등록되어있음......<br>
 20250620 이미 등록되어있음......<br>
 20250620 이미 등록되어있음......<br>
 20250620 이미 등록되어있음......<br>
 20250620 이미 등록되어있음......<br>
 20250621 주말일 경우 패스.....<br>
 20250622 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250512 | sEdate : 20250623<br>
<br>
 MAX_DAY  : 20250620<br>
 DIFF_DAY  : 42<br>
 20250512 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250620 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 이미 등록되어있음......<br>
 20250620 이미 등록되어있음......<br>
 20250620 이미 등록되어있음......<br>
 20250620 이미 등록되어있음......<br>
 20250620 이미 등록되어있음......<br>
 20250621 주말일 경우 패스.....<br>
 20250622 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250512001<br>
 입력 SLUS_ID  : 20250512002<br>
 입력 SLUS_ID  : 20250512003<br>
 입력 SLUS_ID  : 20250512004<br>
 입력 SLUS_ID  : 20250512005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250513 | sEdate : 20250624<br>
<br>
 MAX_DAY  : 20250623<br>
 DIFF_DAY  : 42<br>
 20250513 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250623 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 이미 등록되어있음......<br>
 20250623 이미 등록되어있음......<br>
 20250623 이미 등록되어있음......<br>
 20250623 이미 등록되어있음......<br>
 20250623 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250513001<br>
 입력 SLUS_ID  : 20250513002<br>
 입력 SLUS_ID  : 20250513003<br>
 입력 SLUS_ID  : 20250513004<br>
 입력 SLUS_ID  : 20250513005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250514 | sEdate : 20250625<br>
<br>
 MAX_DAY  : 20250624<br>
 DIFF_DAY  : 42<br>
 20250514 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250624 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 이미 등록되어있음......<br>
 20250624 이미 등록되어있음......<br>
 20250624 이미 등록되어있음......<br>
 20250624 이미 등록되어있음......<br>
 20250624 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250514001<br>
 입력 SLUS_ID  : 20250514002<br>
 입력 SLUS_ID  : 20250514003<br>
 입력 SLUS_ID  : 20250514004<br>
 입력 SLUS_ID  : 20250514005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250515 | sEdate : 20250626<br>
<br>
 MAX_DAY  : 20250625<br>
 DIFF_DAY  : 42<br>
 20250515 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250625 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 이미 등록되어있음......<br>
 20250625 이미 등록되어있음......<br>
 20250625 이미 등록되어있음......<br>
 20250625 이미 등록되어있음......<br>
 20250625 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250515001<br>
 입력 SLUS_ID  : 20250515002<br>
 입력 SLUS_ID  : 20250515003<br>
 입력 SLUS_ID  : 20250515004<br>
 입력 SLUS_ID  : 20250515005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250516 | sEdate : 20250627<br>
<br>
 MAX_DAY  : 20250626<br>
 DIFF_DAY  : 42<br>
 20250516 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250626 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 이미 등록되어있음......<br>
 20250626 이미 등록되어있음......<br>
 20250626 이미 등록되어있음......<br>
 20250626 이미 등록되어있음......<br>
 20250626 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250516001<br>
 입력 SLUS_ID  : 20250516002<br>
 입력 SLUS_ID  : 20250516003<br>
 입력 SLUS_ID  : 20250516004<br>
 입력 SLUS_ID  : 20250516005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250517 | sEdate : 20250628<br>
<br>
 MAX_DAY  : 20250627<br>
 DIFF_DAY  : 42<br>
 20250517 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 이미 등록되어있음......<br>
 20250627 이미 등록되어있음......<br>
 20250627 이미 등록되어있음......<br>
 20250627 이미 등록되어있음......<br>
 20250627 이미 등록되어있음......<br>
 20250628 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250518 | sEdate : 20250629<br>
<br>
 MAX_DAY  : 20250627<br>
 DIFF_DAY  : 42<br>
 20250518 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 이미 등록되어있음......<br>
 20250627 이미 등록되어있음......<br>
 20250627 이미 등록되어있음......<br>
 20250627 이미 등록되어있음......<br>
 20250627 이미 등록되어있음......<br>
 20250628 주말일 경우 패스.....<br>
 20250629 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250519 | sEdate : 20250630<br>
<br>
 MAX_DAY  : 20250627<br>
 DIFF_DAY  : 42<br>
 20250519 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250627 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 이미 등록되어있음......<br>
 20250627 이미 등록되어있음......<br>
 20250627 이미 등록되어있음......<br>
 20250627 이미 등록되어있음......<br>
 20250627 이미 등록되어있음......<br>
 20250628 주말일 경우 패스.....<br>
 20250629 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250519001<br>
 입력 SLUS_ID  : 20250519002<br>
 입력 SLUS_ID  : 20250519003<br>
 입력 SLUS_ID  : 20250519004<br>
 입력 SLUS_ID  : 20250519005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250520 | sEdate : 20250701<br>
<br>
 MAX_DAY  : 20250630<br>
 DIFF_DAY  : 42<br>
 20250520 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250630 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 이미 등록되어있음......<br>
 20250630 이미 등록되어있음......<br>
 20250630 이미 등록되어있음......<br>
 20250630 이미 등록되어있음......<br>
 20250630 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250520001<br>
 입력 SLUS_ID  : 20250520002<br>
 입력 SLUS_ID  : 20250520003<br>
 입력 SLUS_ID  : 20250520004<br>
 입력 SLUS_ID  : 20250520005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250521 | sEdate : 20250702<br>
<br>
 MAX_DAY  : 20250701<br>
 DIFF_DAY  : 42<br>
 20250521 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250701 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 이미 등록되어있음......<br>
 20250701 이미 등록되어있음......<br>
 20250701 이미 등록되어있음......<br>
 20250701 이미 등록되어있음......<br>
 20250701 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250521001<br>
 입력 SLUS_ID  : 20250521002<br>
 입력 SLUS_ID  : 20250521003<br>
 입력 SLUS_ID  : 20250521004<br>
 입력 SLUS_ID  : 20250521005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250522 | sEdate : 20250703<br>
<br>
 MAX_DAY  : 20250702<br>
 DIFF_DAY  : 42<br>
 20250522 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250702 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 이미 등록되어있음......<br>
 20250702 이미 등록되어있음......<br>
 20250702 이미 등록되어있음......<br>
 20250702 이미 등록되어있음......<br>
 20250702 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250522001<br>
 입력 SLUS_ID  : 20250522002<br>
 입력 SLUS_ID  : 20250522003<br>
 입력 SLUS_ID  : 20250522004<br>
 입력 SLUS_ID  : 20250522005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250523 | sEdate : 20250704<br>
<br>
 MAX_DAY  : 20250703<br>
 DIFF_DAY  : 42<br>
 20250523 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250703 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 이미 등록되어있음......<br>
 20250703 이미 등록되어있음......<br>
 20250703 이미 등록되어있음......<br>
 20250703 이미 등록되어있음......<br>
 20250703 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250523001<br>
 입력 SLUS_ID  : 20250523002<br>
 입력 SLUS_ID  : 20250523003<br>
 입력 SLUS_ID  : 20250523004<br>
 입력 SLUS_ID  : 20250523005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250524 | sEdate : 20250705<br>
<br>
 MAX_DAY  : 20250704<br>
 DIFF_DAY  : 42<br>
 20250524 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 이미 등록되어있음......<br>
 20250704 이미 등록되어있음......<br>
 20250704 이미 등록되어있음......<br>
 20250704 이미 등록되어있음......<br>
 20250704 이미 등록되어있음......<br>
 20250705 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250525 | sEdate : 20250706<br>
<br>
 MAX_DAY  : 20250704<br>
 DIFF_DAY  : 42<br>
 20250525 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 이미 등록되어있음......<br>
 20250704 이미 등록되어있음......<br>
 20250704 이미 등록되어있음......<br>
 20250704 이미 등록되어있음......<br>
 20250704 이미 등록되어있음......<br>
 20250705 주말일 경우 패스.....<br>
 20250706 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250526 | sEdate : 20250707<br>
<br>
 MAX_DAY  : 20250704<br>
 DIFF_DAY  : 42<br>
 20250526 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250704 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 이미 등록되어있음......<br>
 20250704 이미 등록되어있음......<br>
 20250704 이미 등록되어있음......<br>
 20250704 이미 등록되어있음......<br>
 20250704 이미 등록되어있음......<br>
 20250705 주말일 경우 패스.....<br>
 20250706 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250526001<br>
 입력 SLUS_ID  : 20250526002<br>
 입력 SLUS_ID  : 20250526003<br>
 입력 SLUS_ID  : 20250526004<br>
 입력 SLUS_ID  : 20250526005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250527 | sEdate : 20250708<br>
<br>
 MAX_DAY  : 20250707<br>
 DIFF_DAY  : 42<br>
 20250527 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250707 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 이미 등록되어있음......<br>
 20250707 이미 등록되어있음......<br>
 20250707 이미 등록되어있음......<br>
 20250707 이미 등록되어있음......<br>
 20250707 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250527001<br>
 입력 SLUS_ID  : 20250527002<br>
 입력 SLUS_ID  : 20250527003<br>
 입력 SLUS_ID  : 20250527004<br>
 입력 SLUS_ID  : 20250527005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250528 | sEdate : 20250709<br>
<br>
 MAX_DAY  : 20250708<br>
 DIFF_DAY  : 42<br>
 20250528 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250708 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 이미 등록되어있음......<br>
 20250708 이미 등록되어있음......<br>
 20250708 이미 등록되어있음......<br>
 20250708 이미 등록되어있음......<br>
 20250708 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250528001<br>
 입력 SLUS_ID  : 20250528002<br>
 입력 SLUS_ID  : 20250528003<br>
 입력 SLUS_ID  : 20250528004<br>
 입력 SLUS_ID  : 20250528005