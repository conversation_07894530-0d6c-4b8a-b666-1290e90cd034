<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Comment</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___clone" title="__clone :: Implement PHP __clone to create a deep clone, not just a shallow copy."><span class="description">Implement PHP __clone to create a deep clone, not just a shallow copy.</span><pre>__clone()</pre></a></li>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_Comment"><span class="description">Create a new PHPExcel_Comment</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method___toString" title="__toString :: Convert to string"><span class="description">Convert to string</span><pre>__toString()</pre></a></li>
<li class="method public "><a href="#method_getAlignment" title="getAlignment :: Get Alignment"><span class="description">Get Alignment</span><pre>getAlignment()</pre></a></li>
<li class="method public "><a href="#method_getAuthor" title="getAuthor :: Get Author"><span class="description">Get Author</span><pre>getAuthor()</pre></a></li>
<li class="method public "><a href="#method_getFillColor" title="getFillColor :: Get fill color"><span class="description">Get fill color</span><pre>getFillColor()</pre></a></li>
<li class="method public "><a href="#method_getHashCode" title="getHashCode :: Get hash code"><span class="description">Get hash code</span><pre>getHashCode()</pre></a></li>
<li class="method public "><a href="#method_getHeight" title="getHeight :: Get comment height (CSS style, i.e."><span class="description">Get comment height (CSS style, i.e.</span><pre>getHeight()</pre></a></li>
<li class="method public "><a href="#method_getMarginLeft" title="getMarginLeft :: Get left margin (CSS style, i.e."><span class="description">Get left margin (CSS style, i.e.</span><pre>getMarginLeft()</pre></a></li>
<li class="method public "><a href="#method_getMarginTop" title="getMarginTop :: Get top margin (CSS style, i.e."><span class="description">Get top margin (CSS style, i.e.</span><pre>getMarginTop()</pre></a></li>
<li class="method public "><a href="#method_getText" title="getText :: Get Rich text comment"><span class="description">Get Rich text comment</span><pre>getText()</pre></a></li>
<li class="method public "><a href="#method_getVisible" title="getVisible :: Is the comment visible by default?"><span class="description">Is the comment visible by default?</span><pre>getVisible()</pre></a></li>
<li class="method public "><a href="#method_getWidth" title="getWidth :: Get comment width (CSS style, i.e."><span class="description">Get comment width (CSS style, i.e.</span><pre>getWidth()</pre></a></li>
<li class="method public "><a href="#method_setAlignment" title="setAlignment :: Set Alignment"><span class="description">Set Alignment</span><pre>setAlignment()</pre></a></li>
<li class="method public "><a href="#method_setAuthor" title="setAuthor :: Set Author"><span class="description">Set Author</span><pre>setAuthor()</pre></a></li>
<li class="method public "><a href="#method_setHeight" title="setHeight :: Set comment height (CSS style, i.e."><span class="description">Set comment height (CSS style, i.e.</span><pre>setHeight()</pre></a></li>
<li class="method public "><a href="#method_setMarginLeft" title="setMarginLeft :: Set left margin (CSS style, i.e."><span class="description">Set left margin (CSS style, i.e.</span><pre>setMarginLeft()</pre></a></li>
<li class="method public "><a href="#method_setMarginTop" title="setMarginTop :: Set top margin (CSS style, i.e."><span class="description">Set top margin (CSS style, i.e.</span><pre>setMarginTop()</pre></a></li>
<li class="method public "><a href="#method_setText" title="setText :: Set Rich text comment"><span class="description">Set Rich text comment</span><pre>setText()</pre></a></li>
<li class="method public "><a href="#method_setVisible" title="setVisible :: Set comment default visibility"><span class="description">Set comment default visibility</span><pre>setVisible()</pre></a></li>
<li class="method public "><a href="#method_setWidth" title="setWidth :: Set comment width (CSS style, i.e."><span class="description">Set comment width (CSS style, i.e.</span><pre>setWidth()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__alignment" title="$_alignment :: Alignment"><span class="description"></span><pre>$_alignment</pre></a></li>
<li class="property private "><a href="#property__author" title="$_author :: Author"><span class="description"></span><pre>$_author</pre></a></li>
<li class="property private "><a href="#property__fillColor" title="$_fillColor :: Comment fill color"><span class="description"></span><pre>$_fillColor</pre></a></li>
<li class="property private "><a href="#property__height" title="$_height :: Comment height (CSS style, i.e."><span class="description"></span><pre>$_height</pre></a></li>
<li class="property private "><a href="#property__marginLeft" title="$_marginLeft :: Left margin (CSS style, i.e."><span class="description"></span><pre>$_marginLeft</pre></a></li>
<li class="property private "><a href="#property__marginTop" title="$_marginTop :: Top margin (CSS style, i.e."><span class="description"></span><pre>$_marginTop</pre></a></li>
<li class="property private "><a href="#property__text" title="$_text :: Rich text comment"><span class="description"></span><pre>$_text</pre></a></li>
<li class="property private "><a href="#property__visible" title="$_visible :: Visible"><span class="description"></span><pre>$_visible</pre></a></li>
<li class="property private "><a href="#property__width" title="$_width :: Comment width (CSS style, i.e."><span class="description"></span><pre>$_width</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Comment"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Comment.html">PHPExcel_Comment</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Comment</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.html">PHPExcel</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___clone"></a><div class="element clickable method public method___clone" data-toggle="collapse" data-target=".method___clone .collapse">
<h2>Implement PHP __clone to create a deep clone, not just a shallow copy.</h2>
<pre>__clone() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_Comment</h2>
<pre>__construct() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
</div></div>
</div>
<a id="method___toString"></a><div class="element clickable method public method___toString" data-toggle="collapse" data-target=".method___toString .collapse">
<h2>Convert to string</h2>
<pre>__toString() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getAlignment"></a><div class="element clickable method public method_getAlignment" data-toggle="collapse" data-target=".method_getAlignment .collapse">
<h2>Get Alignment</h2>
<pre>getAlignment() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getAuthor"></a><div class="element clickable method public method_getAuthor" data-toggle="collapse" data-target=".method_getAuthor .collapse">
<h2>Get Author</h2>
<pre>getAuthor() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getFillColor"></a><div class="element clickable method public method_getFillColor" data-toggle="collapse" data-target=".method_getFillColor .collapse">
<h2>Get fill color</h2>
<pre>getFillColor() : <a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></code></div>
</div></div>
</div>
<a id="method_getHashCode"></a><div class="element clickable method public method_getHashCode" data-toggle="collapse" data-target=".method_getHashCode .collapse">
<h2>Get hash code</h2>
<pre>getHashCode() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Hash code</div>
</div></div>
</div>
<a id="method_getHeight"></a><div class="element clickable method public method_getHeight" data-toggle="collapse" data-target=".method_getHeight .collapse">
<h2>Get comment height (CSS style, i.e.</h2>
<pre>getHeight() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>XXpx or YYpt)</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getMarginLeft"></a><div class="element clickable method public method_getMarginLeft" data-toggle="collapse" data-target=".method_getMarginLeft .collapse">
<h2>Get left margin (CSS style, i.e.</h2>
<pre>getMarginLeft() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>XXpx or YYpt)</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getMarginTop"></a><div class="element clickable method public method_getMarginTop" data-toggle="collapse" data-target=".method_getMarginTop .collapse">
<h2>Get top margin (CSS style, i.e.</h2>
<pre>getMarginTop() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>XXpx or YYpt)</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getText"></a><div class="element clickable method public method_getText" data-toggle="collapse" data-target=".method_getText .collapse">
<h2>Get Rich text comment</h2>
<pre>getText() : <a href="../classes/PHPExcel_RichText.html">\PHPExcel_RichText</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_RichText.html">\PHPExcel_RichText</a></code></div>
</div></div>
</div>
<a id="method_getVisible"></a><div class="element clickable method public method_getVisible" data-toggle="collapse" data-target=".method_getVisible .collapse">
<h2>Is the comment visible by default?</h2>
<pre>getVisible() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getWidth"></a><div class="element clickable method public method_getWidth" data-toggle="collapse" data-target=".method_getWidth .collapse">
<h2>Get comment width (CSS style, i.e.</h2>
<pre>getWidth() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>XXpx or YYpt)</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_setAlignment"></a><div class="element clickable method public method_setAlignment" data-toggle="collapse" data-target=".method_setAlignment .collapse">
<h2>Set Alignment</h2>
<pre>setAlignment(string $pValue) : <a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></code></div>
</div></div>
</div>
<a id="method_setAuthor"></a><div class="element clickable method public method_setAuthor" data-toggle="collapse" data-target=".method_setAuthor .collapse">
<h2>Set Author</h2>
<pre>setAuthor(string $pValue) : <a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></code></div>
</div></div>
</div>
<a id="method_setHeight"></a><div class="element clickable method public method_setHeight" data-toggle="collapse" data-target=".method_setHeight .collapse">
<h2>Set comment height (CSS style, i.e.</h2>
<pre>setHeight(string $value) : <a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>XXpx or YYpt)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></code></div>
</div></div>
</div>
<a id="method_setMarginLeft"></a><div class="element clickable method public method_setMarginLeft" data-toggle="collapse" data-target=".method_setMarginLeft .collapse">
<h2>Set left margin (CSS style, i.e.</h2>
<pre>setMarginLeft(string $value) : <a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>XXpx or YYpt)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></code></div>
</div></div>
</div>
<a id="method_setMarginTop"></a><div class="element clickable method public method_setMarginTop" data-toggle="collapse" data-target=".method_setMarginTop .collapse">
<h2>Set top margin (CSS style, i.e.</h2>
<pre>setMarginTop(string $value) : <a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>XXpx or YYpt)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></code></div>
</div></div>
</div>
<a id="method_setText"></a><div class="element clickable method public method_setText" data-toggle="collapse" data-target=".method_setText .collapse">
<h2>Set Rich text comment</h2>
<pre>setText(\PHPExcel_RichText $pValue) : <a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code><a href="../classes/PHPExcel_RichText.html">\PHPExcel_RichText</a></code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></code></div>
</div></div>
</div>
<a id="method_setVisible"></a><div class="element clickable method public method_setVisible" data-toggle="collapse" data-target=".method_setVisible .collapse">
<h2>Set comment default visibility</h2>
<pre>setVisible(boolean $value) : <a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></code></div>
</div></div>
</div>
<a id="method_setWidth"></a><div class="element clickable method public method_setWidth" data-toggle="collapse" data-target=".method_setWidth .collapse">
<h2>Set comment width (CSS style, i.e.</h2>
<pre>setWidth(string $value) : <a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>XXpx or YYpt)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Comment.html">\PHPExcel_Comment</a></code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__alignment"> </a><div class="element clickable property private property__alignment" data-toggle="collapse" data-target=".property__alignment .collapse">
<h2></h2>
<pre>$_alignment : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__author"> </a><div class="element clickable property private property__author" data-toggle="collapse" data-target=".property__author .collapse">
<h2></h2>
<pre>$_author : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__fillColor"> </a><div class="element clickable property private property__fillColor" data-toggle="collapse" data-target=".property__fillColor .collapse">
<h2></h2>
<pre>$_fillColor : <a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__height"> </a><div class="element clickable property private property__height" data-toggle="collapse" data-target=".property__height .collapse">
<h2></h2>
<pre>$_height : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>XXpx or YYpt)</p></div></div></div>
</div>
<a id="property__marginLeft"> </a><div class="element clickable property private property__marginLeft" data-toggle="collapse" data-target=".property__marginLeft .collapse">
<h2></h2>
<pre>$_marginLeft : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>XXpx or YYpt)</p></div></div></div>
</div>
<a id="property__marginTop"> </a><div class="element clickable property private property__marginTop" data-toggle="collapse" data-target=".property__marginTop .collapse">
<h2></h2>
<pre>$_marginTop : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>XXpx or YYpt)</p></div></div></div>
</div>
<a id="property__text"> </a><div class="element clickable property private property__text" data-toggle="collapse" data-target=".property__text .collapse">
<h2></h2>
<pre>$_text : <a href="../classes/PHPExcel_RichText.html">\PHPExcel_RichText</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__visible"> </a><div class="element clickable property private property__visible" data-toggle="collapse" data-target=".property__visible .collapse">
<h2></h2>
<pre>$_visible : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__width"> </a><div class="element clickable property private property__width" data-toggle="collapse" data-target=".property__width .collapse">
<h2></h2>
<pre>$_width : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>XXpx or YYpt)</p></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:35Z.<br></footer></div>
</div>
</body>
</html>
