#!/usr/local/bin/php -q
<?
// 0 9 * * * php -q /home/<USER>/sperp/doch.psh
# 불량이슈 처리기한 알림
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/inc/Encode.php");

$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
if(empty($dbconn_sperp_posbank->success)) {
	echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
}

$dbconn_posbank_intra = new DBController($db['posbank_intra']);
if(empty($dbconn_posbank_intra->success)) {
	echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패";
}
/**********************************************************/


//$date = date("Y-m-d");
$date = date("Y-m-d", strtotime ("+14 day")); // 2019-10-24 2주전 알림으로 변경 jjs
//$date = "2023-01-14";

$SQL = "SELECT 
				H.RCT_CODE||H.HDATE||H.HNO HID,H.HCODE,H.TITLE,H.CONTENT,H.REG_ISTCODE,D.NAME,D.VAL,H.GU,F_BAS(H.GU,'NAME') GU_NM
				,(SELECT BAS_OP5 FROM BAS WHERE BAS_CODE=H.GU) ASSING_ST
			FROM DOCH H, DOCD2 D 
			WHERE D.RCT_CODE=H.RCT_CODE AND D.HDATE=H.HDATE AND D.HNO=H.HNO AND D.VAL_TYPE='D' AND D.ALARM='Y' AND D.VAL='".$date."' AND H.STATE='1' ";
$arrRow = $dbconn_sperp_posbank->query_rows($SQL);

$arr_hp = "";
if($arrRow){
	foreach($arrRow as $key => $row) { 
		unset($arr_hp);
		if($row['ASSING_ST']){
			switch($row['GU']){
				case "EG01": $page="MTE0MTM="; break;  // 측정설비
				case "EG02": $page="MTE0MTQ="; break;  // 생산시설
				case "EG03": $page="MTE0MjA="; break;  // 생산환경
				case "EG04": $page="MTE0MjE="; break;  // 내부심사
				case "EG05": $page="MTE0MjI="; break;  // 교육훈련
				case "EG06": $page="MTE0MjM="; break;  // 신제품개발
				case "EG07": $page="MTE0MzE="; break;  // 시정조치
				case "EG08": $page="MTE0MzI="; break;  // 예방조치
				case "EG09": $page="MTE0NDI="; break;  // ISO표준문서
				case "EG10": $page="MTE1NDQ="; break;  // 교육훈련(계획)
				default: $page="MTE0MTM="; break;
			}
			$title = "[spQMS] 문서관리 - " . $row['GU_NM'] . " 알람내역이 있습니다. (" . $row['HCODE'] . ")";
			$regdate = strftime("%Y-%m-%d-%H-%M");
			$content = "<div style=\"font-size:12px;\">";
			$content .= "<div><b>[".$row['GU_NM']."]</b></div>";
			$content .= "<div>문서번호 : ".$row['HCODE']."</div>";
			$content .= "<div>제목 : ".$row['TITLE']."</div>";
			$content .= "<div>내용 : ".nl2br($row['CONTENT'])."</div>";
			$content .= "<div><a href=\"https://i.posbank.com/sperp_login.html?https://www.spqms.co.kr/?pageCode=${page}&searKey=H.HCODE&searKeyword=$row[HCODE]&HID=$row[HID]\" target=\"_blank\">[문서관리 보러가기]</a></div>";
			$content .= "</div>";

			$Kakao_msg = "문서관리(" . $row['GU_NM'] . ") 알람내역이 있습니다.\n\n";
			$Kakao_msg .= "[문서번호]\n" . $row['HCODE'] . "\n\n";
			$Kakao_msg .= "[제목]\n" . $row['TITLE'] . "\n\n";
			$Kakao_msg .= "[내용]\n" . $row['CONTENT'] . "\n\n";

			$Kakao_msg2 = "[포스뱅크 QMS 알림]\n\n";
			$Kakao_msg2 .= "문서관리(" . $row['GU_NM'] . ") 알람내역이 있습니다.\n\n";
			$Kakao_msg2 .= "[구분]\n".$row['GU_NM']."\n\n";
			$Kakao_msg2 .= "[문서번호]\n" . $row['HCODE'] . "\n\n";
			$Kakao_msg2 .= "[제목]\n" . $row['TITLE'] . "\n\n";
			$Kakao_msg2 .= "[내용]\n" . $row['CONTENT'] . "\n\n";
			$Kakao_msg2 .= "https://www.spqms.co.kr/etc/kakao_link.html?mode=doch&HID=".$row['HID'];
			$Kakao_TemCode = "posbank_000016";

			$sms_msg = "[spQMS]\n문서관리(" . $row['GU_NM'] . ") 알람내역이 있습니다.";

//$row['REG_ISTCODE'] = '100695';
//$row['ASSING_ST'] = '100695';

			$SQL2 = "SELECT PRS_NUM,PRS_NM,PART_CODE,PART_NM FROM PRS_MASTER WHERE FLAG='1' AND ERP_CODE='".$row['REG_ISTCODE']."'";
			$prs_row = $dbconn_posbank_intra->query_row($SQL2);

			$hdate = date("Ymd");
			$query = "INSERT INTO TRANS (PRS_NUM, PRS_NM, PART_CODE, PART_NM, `DIV`, TITLE, HDATE, IN_DT, FILE, CONTENT, OPEN_YN, wr_content_File, PB_LINK) 
							VALUES ('".$prs_row['PRS_NUM']."', '".$prs_row['PRS_NM']."', '".$prs_row['PART_CODE']."', '".$prs_row['PART_NM']."', '1', '".$title."', '".$hdate."', '".$regdate."', '', '".$content."', '2', '', 'posbank')";
			$rs = $dbconn_posbank_intra->iud_query($query);
//echo $query."\n\n";
			$REG_NO = $dbconn_posbank_intra->insert_id();

			$arr_ST = explode(",", $row['ASSING_ST']);
			$SQL3 = "SELECT PRS_NUM,PRS_NM,PART_CODE,PART_NM,HP FROM PRS_MASTER WHERE FLAG='1' AND IFNULL(ERP_CODE,'')<>'' AND ERP_CODE IN ('".implode("','",$arr_ST)."')";
			$SQL3_rows = $dbconn_posbank_intra->query_rows($SQL3);
			if($SQL3_rows){
				foreach($SQL3_rows as $key3 => $row3) {

					// 분류함 자동 분류
					$kdCode = "";
					$SQL4 = "select KD_CODE,TITLE_KEYWORD from TRANS_KIND_SET where PRS_NUM='".$prs_row['PRS_NUM']."' and ifnull(TITLE_KEYWORD,'')<>'' order by KD_CODE desc";
					$SQL4_rows = $dbconn_posbank_intra->query_rows($SQL4);
					if($SQL4_rows){
						foreach($SQL4_rows as $key4 => $row4) {
							$arr_KEYWORD = explode("|", $row4['TITLE_KEYWORD']);
							if($arr_KEYWORD){
								foreach($arr_KEYWORD as $keyword) {
									if($keyword){
										if(stripos($title, $keyword) !== false) $kdCode = $row4['KD_CODE'];
									}
								}
							}
						}
					}

					// 받는사람
					$query = "INSERT INTO TRANS_TARGET (`REG_NO`, `PRS_NUM`, `PRS_NM`, `KD_CODE`) 
									VALUES ('".$REG_NO."', '".$row3['PRS_NUM']."', '".$row3['PRS_NM']."', '".$kdCode."')";
					$rs = $dbconn_posbank_intra->iud_query($query);
//echo $query."\n\n";
					//if($row[ASSING_ST]) $arr_hp[$row3[HP]] = $row3[HP]."/".$row3[PRS_NM];
					$arr_hp[$row3['HP']] = $row3['HP']."/".$row3['PRS_NM'];
					echo date("Y-m-d H:i:s")." - 문서관리(" . $row['GU_NM'] . ") : ".$row['HCODE']." - ".$row3['PRS_NM']." 업무연락 정상발송\n";
				}
			}

			if($arr_ST){
				// 구글워크스테이션 웹훅 보내기 2023-01-17 jjs
				$goolgework_Params = [];
				$goolgework_Params['PROGRAM'] = "sperp";
				$goolgework_Params['GU'] = "qms";
				$goolgework_Params['ID'] = $arr_ST;
				$goolgework_Params['PREVIEW'] = "문서관리(" . $row['GU_NM'] . ") 알람내역이 있습니다.";
				$goolgework_Params['TITLE'] = "문서관리";
				$goolgework_Params['LINK_NM'] = "확인하러가기";
				//$goolgework_Params['LINK'] = "https://www.spqms.co.kr/?pageCode=MTE0MzM=&&searKey=H.RCT_CODE||H.HDATE||H.HNO&searKeyword=".$row['HID'];
				$goolgework_Params['LINK'] = "https://www.spqms.co.kr/etc/kakao_link.html?mode=doch&HID=".$row['HID'];
				$goolgework_Params['MESSAGE'][] = ["<b>구분 : </b><font color='#555555'>".$row['GU_NM']."</font>"];
				$goolgework_Params['MESSAGE'][] = ["<b>문서번호 : </b><font color='#555555'>".$row['HCODE']."</font>"];
				$goolgework_Params['MESSAGE'][] = ["<b>제목 : </b><font color='#555555'>".$row['TITLE']."</font>"];
				$goolgework_Params['MESSAGE'][] = ["<b>내용 : </b><font color='#555555'>".mb_substr($row['CONTENT'], 0, 400, 'euc-kr')."</font>"];
				$rs = goolgework_send($goolgework_Params);
				echo date("Y-m-d H:i:s")." - 문서관리 웹훅 정상발송\n";
			}else{
				echo date("Y-m-d H:i:s")." - 문서관리 정상처리\n";
			}

//			if($arr_hp){
//				$hp = implode(";",$arr_hp);
//				//sms_send2($hp,"[spQMS]\문서관리(" . $row[GU_NM] . ") 알람내역이 있습니다.");
//
//				if($Kakao_TemCode){ // 알림톡
//					unset($KakaoTalk_Params);
//					$KakaoTalk_Params['SMS_ID'] = "posbank2";
//					$KakaoTalk_Params['dispatchPhone'] = "1588-6335";
//					$KakaoTalk_Params['msgType'] = "1008";
//					$KakaoTalk_Params['temCode'] = $Kakao_TemCode;
//					$KakaoTalk_Params['receptionPhone'] = $hp;
//					$KakaoTalk_Params['imgUrl'] = "";
//					$KakaoTalk_Params['imgLink'] = "";
//					$KakaoTalk_Params['toMsg'] = $Kakao_msg2;
//					$KakaoTalk_Params['etc1'] = $sms_msg; // 친구톡 실패시 문자 발송 내용
//					$rs = KakaoTalk_send($KakaoTalk_Params);
//				}else{ // 친구톡
//					unset($KakaoTalk_Params);
//					$KakaoTalk_Params['msgType'] = "1009";
//					$KakaoTalk_Params['receptionPhone'] = $hp;
//					$KakaoTalk_Params['imgUrl'] = "https://mud-kage.kakao.com/dn/eYgum/btqxWZ3IFzr/z3R4SPRVIfcper33qGDYP0/img_l.png";
//					$KakaoTalk_Params['imgLink'] = "https://www.spqms.co.kr/etc/kakao_link.html?mode=doch&HID=".$row[HID];
//					$KakaoTalk_Params['toMsg'] = $Kakao_msg;
//					$KakaoTalk_Params['etc1'] = $sms_msg; // 친구톡 실패시 묹자 발송 내용
//					KakaoTalk_send($KakaoTalk_Params);
//				}
//				echo date("Y-m-d H:i:s")." - 문서관리 카카오톡 정상발송\n";
//			}else{
//				echo date("Y-m-d H:i:s")." - 문서관리 정상처리\n";
//			}
		}
	}
}else{
	echo date("Y-m-d H:i:s")." - 문서관리 정상처리\n";
}


## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(86400, "ERP 문서관리 알람");
?>
