<br>
 date_NOW : 20250701<br>
 date_30D : 20250929<br>
 date_2M : 20250901<br>
 date_3M : 20251001<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250901' AND '20251001' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250702<br>
 date_30D : 20250930<br>
 date_2M : 20250902<br>
 date_3M : 20251002<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250902' AND '20251002' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250703<br>
 date_30D : 20251001<br>
 date_2M : 20250903<br>
 date_3M : 20251003<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250903' AND '20251003' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250704<br>
 date_30D : 20251002<br>
 date_2M : 20250904<br>
 date_3M : 20251004<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250904' AND '20251004' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-07-05 - 휴무일
2025-07-06 - 휴무일
<br>
 date_NOW : 20250707<br>
 date_30D : 20251005<br>
 date_2M : 20250907<br>
 date_3M : 20251007<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250907' AND '20251007' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250708<br>
 date_30D : 20251006<br>
 date_2M : 20250908<br>
 date_3M : 20251008<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250908' AND '20251008' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250709<br>
 date_30D : 20251007<br>
 date_2M : 20250909<br>
 date_3M : 20251009<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250909' AND '20251009' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250710<br>
 date_30D : 20251008<br>
 date_2M : 20250910<br>
 date_3M : 20251010<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250910' AND '20251010' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250711<br>
 date_30D : 20251009<br>
 date_2M : 20250911<br>
 date_3M : 20251011<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250911' AND '20251011' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-07-12 - 휴무일
2025-07-13 - 휴무일
<br>
 date_NOW : 20250714<br>
 date_30D : 20251012<br>
 date_2M : 20250914<br>
 date_3M : 20251014<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250914' AND '20251014' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 50
    [VAN_NAME] => 파이서브코리아(FDK)(FDK)
    [POS_NAME] => Anyshop e2
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => #####SR-F20IF101FDPOSBNKPOPS1001
    [EXPIRE_YMD] => 2025-10-14
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '50' <br>
 date_NOW : 20250715<br>
 date_30D : 20251013<br>
 date_2M : 20250915<br>
 date_3M : 20251015<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250915' AND '20251015' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250716<br>
 date_30D : 20251014<br>
 date_2M : 20250916<br>
 date_3M : 20251016<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250916' AND '20251016' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250717<br>
 date_30D : 20251015<br>
 date_2M : 20250917<br>
 date_3M : 20251017<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250917' AND '20251017' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250718<br>
 date_30D : 20251016<br>
 date_2M : 20250918<br>
 date_3M : 20251018<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250918' AND '20251018' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-07-19 - 휴무일
2025-07-20 - 휴무일
<br>
 date_NOW : 20250721<br>
 date_30D : 20251019<br>
 date_2M : 20250921<br>
 date_3M : 20251021<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250921' AND '20251021' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250722<br>
 date_30D : 20251020<br>
 date_2M : 20250922<br>
 date_3M : 20251022<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250922' AND '20251022' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250723<br>
 date_30D : 20251021<br>
 date_2M : 20250923<br>
 date_3M : 20251023<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250923' AND '20251023' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250724<br>
 date_30D : 20251022<br>
 date_2M : 20250924<br>
 date_3M : 20251024<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250924' AND '20251024' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250725<br>
 date_30D : 20251023<br>
 date_2M : 20250925<br>
 date_3M : 20251025<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250925' AND '20251025' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-07-26 - 휴무일
2025-07-27 - 휴무일
<br>
 date_NOW : 20250728<br>
 date_30D : 20251026<br>
 date_2M : 20250928<br>
 date_3M : 20251028<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250928' AND '20251028' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250729<br>
 date_30D : 20251027<br>
 date_2M : 20250929<br>
 date_3M : 20251029<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250929' AND '20251029' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250730<br>
 date_30D : 20251028<br>
 date_2M : 20250930<br>
 date_3M : 20251030<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250930' AND '20251030' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250731<br>
 date_30D : 20251029<br>
 date_2M : 20251001<br>
 date_3M : 20251031<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20251001' AND '20251031' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
