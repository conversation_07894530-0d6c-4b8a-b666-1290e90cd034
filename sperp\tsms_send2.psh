#!/usr/local/bin/php -q
<?
# ARS 접수 데이타 알림톡 발송
# 5분마다 실행
echo date("Y-m-d H:i:s")." - 실행 \n";
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

$dbconn_sperp_ars = new DBController($db['sperp_ars']);
if(empty($dbconn_sperp_ars->success)) {
	echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
}
/**********************************************************/



// 0 9 * * * php -q /sms_Check/tsms_send.psh
# CID 알림톡 발송
//$ROOT_PATH = "/home/<USER>";
//$_SERVER['DOCUMENT_ROOT'] = $ROOT_PATH;
//include($ROOT_PATH . "/inc/pb_encode.php");
//include($ROOT_PATH . "/inc/dbcon_ars.html"); // $ars_conn
//include($ROOT_PATH . "/inc/func.php");
$arr_hp1 = [];
$arr_hp2 = [];
$SQL = "
	SELECT
		BAS_CODE,BAS_OP5
	FROM
		BAS
	WHERE
		(BAS_CODE LIKE 'SN%' or BAS_CODE LIKE 'SD%')
		AND ifnull(BAS_OP5,'')<>'' ";
$arrRow = $dbconn_sperp_ars->query_rows($SQL);
if($arrRow){
	foreach($arrRow as $key => $row) {
		if(substr($row['BAS_CODE'],0,2)=="SN") $arr_hp1[] = trim($row['BAS_OP5']);
		if(substr($row['BAS_CODE'],0,2)=="SD") $arr_hp2[] = trim($row['BAS_OP5']);
	}
}
echo json_encode($arr_hp1, JSON_PRETTY_PRINT|JSON_UNESCAPED_UNICODE)."\n\n";
echo json_encode($arr_hp2, JSON_PRETTY_PRINT|JSON_UNESCAPED_UNICODE)."\n\n";
exit;

$hp1 = "";
$hp2 = "";
if($arr_hp1) $hp1 = implode(";",$arr_hp1);
if($arr_hp2) $hp2 = implode(";",$arr_hp2);
$SQL2 = "
	SELECT
		NO, PTYPE, REG_DATA, ARSTYPE, date_format(REG_DATE,'%H:%i') REG_DATE
	FROM
		T_SMS
	WHERE
		SMS_YN='0'
	ORDER BY NO DESC
";
$arrRow2 = $dbconn_sperp_ars->query_rows($SQL2);
if($arrRow2){
	foreach($arrRow2 as $key2 => $row2) {
		$sms_yn[$row2['NO']] = "1";
		if($row2['PTYPE'] == "1"){ // 야간/공휴일일때만
			if($hp1){
				$srr_MSG1[$key2] .= $row2['REG_DATA'] . " - [" . $row2['REG_DATE'] . "]";
				$sms_yn[$row2['NO']] = "2";
			}
		}
		if($row2['PTYPE'] == "0"){ // 주간일때
			if($hp2){
				$srr_MSG2[$key2] .= $row2['REG_DATA'] . " - [" . $row2['REG_DATE'] . "]";
				$sms_yn[$row2['NO']] = "2";
			}
		}
	}
}
echo json_encode($srr_MSG1, JSON_PRETTY_PRINT|JSON_UNESCAPED_UNICODE)."\n\n";
exit;


if($hp1 && $srr_MSG1){
	$setParams['SMS_ID'] = "posbank";
	$setParams['msgType'] = "1008";
	$setParams['dispatchPhone'] = "1588-6312";
	$setParams['receptionPhone'] = $hp1;
	$setParams['temCode'] = "posbank_000003";
	$setParams['toMsg'] = "[당직긴급콜]\n" . implode("\n",$srr_MSG1);
	$rs = KakaoTalk_send($setParams);
	echo $hp1 . "/" .$setParams['toMsg'] . "\n";
}
if($hp2 && $srr_MSG2){
	$setParams['SMS_ID'] = "posbank";
	$setParams['msgType'] = "1008";
	$setParams['dispatchPhone'] = "1588-6312";
	$setParams['receptionPhone'] = $hp2;
	$setParams['temCode'] = "posbank_000003";
	$setParams['toMsg'] = "[당직긴급콜]\n" . implode("\n",$srr_MSG2);
	$rs = KakaoTalk_send($setParams);
	echo $hp2 . "/" .$setParams['toMsg'] . "\n";
}
if($sms_yn){
	foreach($sms_yn as $key => $value) {
		$query = "update T_SMS set SMS_YN = '".$value."' where NO='".$key."'";
		//echo $query . "\n";
		$rs = mysqlQuery($query,$ars_conn);
	}
}
echo date("Y-m-d H:i:s")." - 알림톡 발송\n";
echo "---------------------------\n";
// 스케줄 처리 상황 intra DB에 저장
crontab_execution_pbs(300, "CID 알림톡 발송");
mysql_close($ars_conn);
?>