#!/usr/bin/php -q
<?
// 17 3 * * * php -q /home/<USER>/sperp/hanmaeum_webbasket_log_del.sh
# 한마음공동체 DB의 WEBBASKET_LOG 테이블의 10일 지난 데이터 삭제 처리
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

echo date("Y-m-d H:i:s")." - 실행 \n";

$dbconn_sperp_hanmaeum = new DBController($db['sperp_hanmaeum']);
if(empty($dbconn_sperp_hanmaeum->success)) {
	echo "dbconn error [" . $db['sperp_hanmaeum']['host'] . "] 데이터베이스 연결 실패";
}
/**********************************************************/


// 10일 전
$tendays_ago = date("Y-m-d", strtotime ("-10 day"));

$query="
	DELETE FROM 
		WEBBASKET_LOG 
	WHERE 
		TO_CHAR(DDATE,'YYYY-MM-DD') < '".$tendays_ago."' ";
$rs = $dbconn_sperp_hanmaeum->iud_query($query);

echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
echo date("Y-m-d H:i:s")." - 종료\n";

## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(86400, "ERP 한마음공동체 장바구니 데이터 삭제");
?>