#!/usr/bin/php -q
<?php 
	// 0 9 * * 1-5 php -q /home/<USER>/sperp/as_delay_mail_send.psh
	# 품목 마이너스 재고 현황 (업무연락)
	$ROOT_PATH = "/home/<USER>";
	include($ROOT_PATH . "/inc/func.php");
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/Encode.php");

	$dbconn = new DBController($db['sperp_posbank']);
	if(empty($dbconn->success)) {
		echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
	}
 
    echo "\n[".date('Y-m-d H:i:s')."]\n";

    $TODAY = date('Y-m-d H:i');
 // ************************************************************** [창고재고] 품목 마이너스 재고 현황 **************************************************************
    $SQL = "   
        SELECT B.PR_CODE,B.HCT_CODE,B.QTY,C.QTY IV_QTY,PR.PR_NAME,HCT.HCT_NAME,C.PR_CODE IV_CHK, PR.PR_STD
        FROM ( 
            SELECT PR_CODE,HCT_CODE,SUM(QTY) QTY
                FROM ( 
                    SELECT PR_CODE,HCT_CODE,QTY 
                    From BIV
                    WHERE RCT_CODE='1000' AND BIV_YYYY='2007'
                    UNION ALL 
                    SELECT PR_CODE,HCT_CODE,SUM(QTY) QTY 
                    FROM PD23 
                    WHERE RCT_CODE='1000' and HDATE>='********' AND STATE <> '0' 
                    GROUP BY PR_CODE,HCT_CODE
                    UNION ALL 
                    SELECT PR_CODE,HCT_CODE,SUM(QTY)*-1 QTY
                    FROM PD13
                    WHERE RCT_CODE='1000' and HDATE>='********' AND STATE <> '0' AND NVL(POS_GUBUN,'0') <> '1' 
                    GROUP BY PR_CODE,HCT_CODE  
                    UNION ALL 
                    SELECT D.PR_CODE,D.HCT_CODE,SUM(D.OQTY)*-1 QTY
                    FROM PH13R H,PD13R D 
                    WHERE H.RCT_CODE='1000' and H.HDATE=D.HDATE and H.HNO=D.HNO and H.HDATE>='********' AND H.STATE <> '0' 
                    GROUP BY D.PR_CODE,D.HCT_CODE
                    UNION ALL 
                    SELECT D.PR_CODE,H.HCT_CODE,SUM(D.QTY)*-1 QTY
                    FROM POSH H,POSD D 
                    WHERE H.RCT_CODE='1000' AND H.HDATE>='********' 
                    AND H.STATE<>'0' AND H.RCT_CODE=D.RCT_CODE AND H.HDATE=D.HDATE AND H.POS_CODE=D.POS_CODE AND H.HNO=D.HNO 
                    GROUP BY D.PR_CODE,H.HCT_CODE  
                    UNION ALL 
                    SELECT PR_CODE,HCT_CODE,SUM(DECODE(GUBUN,'J',EQTY,'R',EQTY * -1)) QTY 
                    FROM PJ31  
                    WHERE RCT_CODE='1000' and HDATE>='********' AND STATE <> '0' 
                    GROUP BY PR_CODE,HCT_CODE 
                    UNION ALL 
                    SELECT PR_CODE,HCT_CODE,SUM(DECODE(GUBUN,'J',SQTY,'R',SQTY * -1)) * -1 QTY
                    FROM PD31  
                    WHERE RCT_CODE='1000' and HDATE>='********' AND STATE <> '0' 
                    GROUP BY PR_CODE,HCT_CODE
                    UNION ALL 
                    SELECT PR_CODE,OHCT_CODE HCT_CODE,SUM(DECODE(HIO,'I',QTY,'O',QTY *-1)) QTY
                    FROM PD99  
                    WHERE RCT_CODE='1000' and HDATE>='********' AND STATE <> '0' 
                    GROUP BY PR_CODE,OHCT_CODE
                    UNION ALL 
                    SELECT PR_CODE,NHCT_CODE HCT_CODE,SUM(DECODE(HIO,'O',QTY,'I',QTY * -1)) QTY
                    FROM PD99  
                    WHERE NRCT_CODE='1000' and HDATE>='********' AND STATE <> '0' AND HGU IN ('2','3') 
                    GROUP BY PR_CODE,NHCT_CODE 
                ) A
                group by PR_CODE,HCT_CODE
            ) B
            left join IV C on C.RCT_CODE='1000' and B.HCT_CODE=C.HCT_CODE and B.PR_CODE=C.PR_CODE 
            left join PR on B.PR_CODE=PR.PR_CODE 
            left join HCT on B.HCT_CODE=HCT.HCT_CODE 
        where nvl(B.QTY,0)<>nvl(C.QTY,0) -- AND PR.PR_IVCK='0'  -- and HCT.HCT_IV='Y'
        order by B.PR_CODE
    ";
    $QTYrows = $dbconn->query_rows($SQL); 

    // 재고 수정 
    $sql_str2 = null;
    $iv_rs = null;
    if(!empty($QTYrows)){
        $sql_str2 = "BEGIN ";
        foreach($QTYrows as $key => $row) {
            $sql_str = "update IV set QTY=".$row['QTY']." where RCT_CODE='1000' and HCT_CODE='".$row['HCT_CODE']."' and PR_CODE='".$row['PR_CODE']."';";
            if(!$row['IV_CHK']) $sql_str = "insert into IV (RCT_CODE,HCT_CODE,PR_CODE,QTY) values ('".$RCT_CODE."','".$row['HCT_CODE']."','".$row['PR_CODE']."','".$row['QTY']."');";

            $sql_str2 .= $sql_str."\n";
        }
        $sql_str2 .= "END ; ";
        $iv_rs = $dbconn->iud_query($sql_str2);
        
        // 재고수정이후 재 검색
        $QTYrows = $dbconn->query_rows($SQL); 
    }


    // 업무연락 내용
    // TH
    $content = '
        <p style="text-align: center;" align="center">
            <span style="font-size: 18.6667px; color:#0075c8; border-bottom:1px solid #0075c8;"><b>품목 마이너스 재고 현황('.$TODAY.')</b></span>
        </p>
        <br>
        <table border="0" cellpadding="0" cellspacing="0" style="border:1px solid #cccccc; border-left:0; border-bottom:0;" class="__se_tbl">
            <tbody>
                <tr>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 50px; height: 24px;  background-color: #EAF0F4; " class="">
                        <p style="text-align: center; "><b>NO.</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 100px; height: 24px;  background-color: #EAF0F4; " class="">
                        <p style="text-align: center; "><b>품목코드</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 500px; height: 24px;  background-color: #EAF0F4; " class="">
                        <p style="text-align: center; "><b>품목명</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 400px; height: 24px;  background-color: #EAF0F4; " class="">
                        <p style="text-align: center; "><b>규격</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 100px; height: 24px;  background-color: #EAF0F4; " class="">
                        <p style="text-align: center; "><b>재고</b></p>
                    </td>
                </tr>
    ';

    //TD
    if (!empty($QTYrows)) {  
        $no = 0;
        foreach ($QTYrows as $row) { 
            $no++;
            $content .= '
                <tr>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 50px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">'.$no.'</p>
                    </td> 
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 100px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">'.$row['PR_CODE'].'</p>
                    </td> 
                    <td style="padding:10px; border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 500px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: left;">'.$row['PR_NAME'].'</p>
                    </td> 
                    <td style="padding:10px; border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 400px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: left;">'.$row['PR_STD'].'</p>
                    </td> 
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 100px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">'.$row['QTY'].'</p>
                    </td> 
                </tr>
            ';
        }
    } else {
        $content .= '<tr>
                    <td  colspan="6" style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">해당 항목이 없습니다.</p>
                    </td> 
                </tr>
        ';
    }

    $content .= '
            </tbody>
        </table>
        <br>    
    ';
        

    //수신자
    $arr_ST = alarm_st("E0A1");
    // $arr_ST = ["101141"];
    $intra_Params = [];
    $intra_Params['gu'] = "erp";
    $intra_Params['from'] = "";
    $intra_Params['to'] = $arr_ST;
    $intra_Params['title'] = "[창고재고] 품목 마이너스 재고 현황 ".$TODAY;
    $intra_Params['content'] = $content;
    $rs3 = intra_send_curl($intra_Params);

    echo "수신자              : " . json_encode($intra_Params['to'], true) . "\n";
    echo "인트라넷 업무연락 rs : " . json_encode($rs3, true) . "\n";
    if($sql_str2) echo "재고수정 SQL  : " . json_encode($sql_str2, true) . "\n";
    if($iv_rs) echo "재고수정 IV_RS  : " . json_encode($iv_rs, true) . "\n";
    // echo "구글워크스테이션  rs : " . json_encode($rs3, true) . "\n";

	##### End. 2025.07.28 KSH(101141) 품목 마이너스 재고 현황 (업무연락)
	###########################################
    
	// ## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(86400, "품목 마이너스 재고 현황 (업무연락)");

	echo date("Y-m-d H:i:s")." - 끝\n";


?>
