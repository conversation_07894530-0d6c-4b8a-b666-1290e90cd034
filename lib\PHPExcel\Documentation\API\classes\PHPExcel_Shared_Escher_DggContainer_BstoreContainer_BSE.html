<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_getBlip" title="getBlip :: Get the BLIP"><span class="description">Get the BLIP</span><pre>getBlip()</pre></a></li>
<li class="method public "><a href="#method_getBlipType" title="getBlipType :: Get the BLIP type"><span class="description">Get the BLIP type</span><pre>getBlipType()</pre></a></li>
<li class="method public "><a href="#method_setBlip" title="setBlip :: Set the BLIP"><span class="description">Set the BLIP</span><pre>setBlip()</pre></a></li>
<li class="method public "><a href="#method_setBlipType" title="setBlipType :: Set the BLIP type"><span class="description">Set the BLIP type</span><pre>setBlipType()</pre></a></li>
<li class="method public "><a href="#method_setParent" title="setParent :: Set parent BLIP Store Entry Container"><span class="description">Set parent BLIP Store Entry Container</span><pre>setParent()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__blip" title="$_blip :: The BLIP (Big Large Image or Picture)"><span class="description"></span><pre>$_blip</pre></a></li>
<li class="property private "><a href="#property__blipType" title="$_blipType :: The BLIP type"><span class="description"></span><pre>$_blipType</pre></a></li>
<li class="property private "><a href="#property__parent" title="$_parent :: The parent BLIP Store Entry Container"><span class="description"></span><pre>$_parent</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_BLIPTYPE_CMYKJPEG" title="BLIPTYPE_CMYKJPEG :: "><span class="description">BLIPTYPE_CMYKJPEG</span><pre>BLIPTYPE_CMYKJPEG</pre></a></li>
<li class="constant  "><a href="#constant_BLIPTYPE_DIB" title="BLIPTYPE_DIB :: "><span class="description">BLIPTYPE_DIB</span><pre>BLIPTYPE_DIB</pre></a></li>
<li class="constant  "><a href="#constant_BLIPTYPE_EMF" title="BLIPTYPE_EMF :: "><span class="description">BLIPTYPE_EMF</span><pre>BLIPTYPE_EMF</pre></a></li>
<li class="constant  "><a href="#constant_BLIPTYPE_ERROR" title="BLIPTYPE_ERROR :: "><span class="description">BLIPTYPE_ERROR</span><pre>BLIPTYPE_ERROR</pre></a></li>
<li class="constant  "><a href="#constant_BLIPTYPE_JPEG" title="BLIPTYPE_JPEG :: "><span class="description">BLIPTYPE_JPEG</span><pre>BLIPTYPE_JPEG</pre></a></li>
<li class="constant  "><a href="#constant_BLIPTYPE_PICT" title="BLIPTYPE_PICT :: "><span class="description">BLIPTYPE_PICT</span><pre>BLIPTYPE_PICT</pre></a></li>
<li class="constant  "><a href="#constant_BLIPTYPE_PNG" title="BLIPTYPE_PNG :: "><span class="description">BLIPTYPE_PNG</span><pre>BLIPTYPE_PNG</pre></a></li>
<li class="constant  "><a href="#constant_BLIPTYPE_TIFF" title="BLIPTYPE_TIFF :: "><span class="description">BLIPTYPE_TIFF</span><pre>BLIPTYPE_TIFF</pre></a></li>
<li class="constant  "><a href="#constant_BLIPTYPE_UNKNOWN" title="BLIPTYPE_UNKNOWN :: "><span class="description">BLIPTYPE_UNKNOWN</span><pre>BLIPTYPE_UNKNOWN</pre></a></li>
<li class="constant  "><a href="#constant_BLIPTYPE_WMF" title="BLIPTYPE_WMF :: "><span class="description">BLIPTYPE_WMF</span><pre>BLIPTYPE_WMF</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE.html">PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Shared.Escher.html">PHPExcel_Shared_Escher</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_getBlip"></a><div class="element clickable method public method_getBlip" data-toggle="collapse" data-target=".method_getBlip .collapse">
<h2>Get the BLIP</h2>
<pre>getBlip() : <a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE_Blip.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE_Blip</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE_Blip.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE_Blip</a></code></div>
</div></div>
</div>
<a id="method_getBlipType"></a><div class="element clickable method public method_getBlipType" data-toggle="collapse" data-target=".method_getBlipType .collapse">
<h2>Get the BLIP type</h2>
<pre>getBlipType() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_setBlip"></a><div class="element clickable method public method_setBlip" data-toggle="collapse" data-target=".method_setBlip .collapse">
<h2>Set the BLIP</h2>
<pre>setBlip(<a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE_Blip.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE_Blip</a> $blip) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$blip</h4>
<code><a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE_Blip.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE_Blip</a></code>
</div>
</div></div>
</div>
<a id="method_setBlipType"></a><div class="element clickable method public method_setBlipType" data-toggle="collapse" data-target=".method_setBlipType .collapse">
<h2>Set the BLIP type</h2>
<pre>setBlipType(int $blipType) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$blipType</h4>
<code>int</code>
</div>
</div></div>
</div>
<a id="method_setParent"></a><div class="element clickable method public method_setParent" data-toggle="collapse" data-target=".method_setParent .collapse">
<h2>Set parent BLIP Store Entry Container</h2>
<pre>setParent(<a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer</a> $parent) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$parent</h4>
<code><a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer</a></code>
</div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__blip"> </a><div class="element clickable property private property__blip" data-toggle="collapse" data-target=".property__blip .collapse">
<h2></h2>
<pre>$_blip : <a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE_Blip.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer_BSE_Blip</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__blipType"> </a><div class="element clickable property private property__blipType" data-toggle="collapse" data-target=".property__blipType .collapse">
<h2></h2>
<pre>$_blipType : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__parent"> </a><div class="element clickable property private property__parent" data-toggle="collapse" data-target=".property__parent .collapse">
<h2></h2>
<pre>$_parent : <a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_BLIPTYPE_CMYKJPEG"> </a><div class="element clickable constant  constant_BLIPTYPE_CMYKJPEG" data-toggle="collapse" data-target=".constant_BLIPTYPE_CMYKJPEG .collapse">
<h2>BLIPTYPE_CMYKJPEG</h2>
<pre>BLIPTYPE_CMYKJPEG </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BLIPTYPE_DIB"> </a><div class="element clickable constant  constant_BLIPTYPE_DIB" data-toggle="collapse" data-target=".constant_BLIPTYPE_DIB .collapse">
<h2>BLIPTYPE_DIB</h2>
<pre>BLIPTYPE_DIB </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BLIPTYPE_EMF"> </a><div class="element clickable constant  constant_BLIPTYPE_EMF" data-toggle="collapse" data-target=".constant_BLIPTYPE_EMF .collapse">
<h2>BLIPTYPE_EMF</h2>
<pre>BLIPTYPE_EMF </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BLIPTYPE_ERROR"> </a><div class="element clickable constant  constant_BLIPTYPE_ERROR" data-toggle="collapse" data-target=".constant_BLIPTYPE_ERROR .collapse">
<h2>BLIPTYPE_ERROR</h2>
<pre>BLIPTYPE_ERROR </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BLIPTYPE_JPEG"> </a><div class="element clickable constant  constant_BLIPTYPE_JPEG" data-toggle="collapse" data-target=".constant_BLIPTYPE_JPEG .collapse">
<h2>BLIPTYPE_JPEG</h2>
<pre>BLIPTYPE_JPEG </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BLIPTYPE_PICT"> </a><div class="element clickable constant  constant_BLIPTYPE_PICT" data-toggle="collapse" data-target=".constant_BLIPTYPE_PICT .collapse">
<h2>BLIPTYPE_PICT</h2>
<pre>BLIPTYPE_PICT </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BLIPTYPE_PNG"> </a><div class="element clickable constant  constant_BLIPTYPE_PNG" data-toggle="collapse" data-target=".constant_BLIPTYPE_PNG .collapse">
<h2>BLIPTYPE_PNG</h2>
<pre>BLIPTYPE_PNG </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BLIPTYPE_TIFF"> </a><div class="element clickable constant  constant_BLIPTYPE_TIFF" data-toggle="collapse" data-target=".constant_BLIPTYPE_TIFF .collapse">
<h2>BLIPTYPE_TIFF</h2>
<pre>BLIPTYPE_TIFF </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BLIPTYPE_UNKNOWN"> </a><div class="element clickable constant  constant_BLIPTYPE_UNKNOWN" data-toggle="collapse" data-target=".constant_BLIPTYPE_UNKNOWN .collapse">
<h2>BLIPTYPE_UNKNOWN</h2>
<pre>BLIPTYPE_UNKNOWN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BLIPTYPE_WMF"> </a><div class="element clickable constant  constant_BLIPTYPE_WMF" data-toggle="collapse" data-target=".constant_BLIPTYPE_WMF .collapse">
<h2>BLIPTYPE_WMF</h2>
<pre>BLIPTYPE_WMF </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:36Z.<br></footer></div>
</div>
</body>
</html>
