#!/usr/local/bin/php -q
<?
# 피드백 업무연락 중 확인 안한 건 알림
# 매일 9시 실행
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

$dbconn = new DBController($db['posbank_intra']);
if(empty($dbconn->success)) {
	echo "[" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패입니다.";
}

$dbconn2 = new DBController($db['chrome_push']);
if(empty($dbconn2->success)) {
	echo "[" . $db['chrome_push']['host'] . "] 데이터베이스 연결 실패입니다.";
}
/**********************************************************/

$SQL = "SELECT now() FROM dual";
$SQL = "test";
$aa = $dbconn->query_one($SQL);

echo $aa . "\n";
exit;


## 스케즐 처리 상황 intra DB에 저장
crontab_execution(86400, "인트라넷 업무연락 미확인 알림");

echo date("Y-m-d H:i:s")." - 끝\n";
?>
