#!/usr/local/bin/php -q
<?php
// 0 9 * * * php -q /home/<USER>/sperp/ic_reader_shape.psh
// /home/<USER>/sperp/ic_reader_shape.sh


# IC리더기형상변경현황 만료일 알람
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/inc/Encode.php");

$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
if(empty($dbconn_sperp_posbank->success)) {
	echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
}

$dbconn_posbank_intra = new DBController($db['posbank_intra']);
if(empty($dbconn_posbank_intra->success)) {
	echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패";
}
/**********************************************************/

if(in_array(date('w'),array("0","6"))){
	echo date("Y-m-d") . " - 휴무일\n";
	## 스케즐 처리 상황 intra DB에 저장 
	crontab_execution(86400, "IC리더기형상변경현황 > 인증 만료일 알람");
	exit;
}



$SQL = "SELECT NAME FROM HOLIDAY_DATA WHERE to_char(HDATE,'YYYYMMDD')='".date('Ymd')."'";
$HOLIDAY_NM = $dbconn_sperp_posbank->query_one($SQL);
if($HOLIDAY_NM){
	echo date("Y-m-d") . " - 휴무일(".$HOLIDAY_NM.")\n";
	## 스케즐 처리 상황 intra DB에 저장 
	crontab_execution(86400, "IC리더기형상변경현황 > 인증 만료일 알림");
	exit;
}



$date_NOW = date("Ymd");
$date_30D = date("Ymd", strtotime ("+90 day"));
$date_2M = date("Ymd", strtotime ("+2 month"));
$date_3M = date("Ymd", strtotime ("+3 month"));


echo "<br>\n date_NOW : ".$date_NOW;
echo "<br>\n date_30D : ".$date_30D;
echo "<br>\n date_2M : ".$date_2M;
echo "<br>\n date_3M : ".$date_3M;


// CERT_STATE = 인증상태(0=유효:기한 내,1=임박:기한이 30일 이하 남았을때,2=만료:기한 지났을때)
$sql01 = "update IC_READER_SHAPE set CERT_STATE = 2  WHERE CERT_STATE in (0,1) AND EXPIRE_YMD < '".$date_NOW."' ";
$rs = $dbconn_sperp_posbank->iud_query($sql01);



// $sql = "SELECT IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(MANAGE_CODE, 'NAME') AS MANAGE_NAME, F_BAS(EXAM_CODE, 'NAME') AS EXAM_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, CASE WHEN CERT_TYPE='1' THEN '리더' WHEN CERT_TYPE='2' THEN '일체형포스' ELSE '포스' END AS CERT_TYPE_NAME, CASE WHEN CERT_STATE='2' THEN '만료' WHEN CERT_STATE='1' THEN '임박' ELSE '유효' END AS CERT_STATE_NAME, CERT_STATE, ITEM_NAME, ITEM_NUMBER, CERT_NUMBER, TO_CHAR(TO_DATE(CERT_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS CERT_YMD, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
// FROM IC_READER_SHAPE WHERE CERT_STATE in (0,1) AND CERT_STATE = 1 AND rownum = 1";

$sql = "
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '".$date_2M."' AND '".$date_3M."' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
";
$row = $dbconn_sperp_posbank->query_row($sql);
echo "<br>\n sql :".$sql;
print_r($row);

$arr_ST = array();
$sql3 = "SELECT BAS_OP4 AS STCODE FROM BAS WHERE BAS_CODE = 'E059'";
$row_bas = $dbconn_sperp_posbank->query_row($sql3);
// print_r($row_bas);
$arr_ST = explode(",", $row_bas['STCODE']);

// print_r($arr_ST);
// exit;


if ($row) {


	$title = "[IC리더기형상변경현황] 보안 인증 만료기한이 3개월전입니다.";
	$sms_msg = "[IC리더기형상변경현황]\n 보안인증 만료기한이 3개월전입니다.";
	$regdate = strftime("%Y-%m-%d-%H-%M");

	$content = "<div style=\"font-size:12px;\">";
	$content .= "<div style=\"clear:both;\"><br></div>";
	$content .= "<div><b>[IC 리더기 형상변경 현황]</b></div>";
	$content .= "<div style=\"clear:both;\"><br></div>";
	$content .= "<div>---------------------------------</div>";
	$content .= "<div style=\"clear:both;\"><br><br></div>";
	$content .= "<div style=\"clear:both;padding:5px;\"><div style=\"float:left;width:100px;\">VAN</div><div style=\"float:left;\">".$row['VAN_NAME']."</div></div>";
	$content .= "<div style=\"clear:both;padding:5px;\"><div style=\"float:left;width:100px;\">포스장비</div><div style=\"float:left;\">".$row['POS_NAME']."</div></div>";
	$content .= "<div style=\"clear:both;padding:5px;\"><div style=\"float:left;width:100px;\">품목명</div><div style=\"float:left;\">".nl2br($row['ITEM_NAME'])."</div></div>";
	$content .= "<div style=\"clear:both;padding:5px;\"><div style=\"float:left;width:100px;\">식별번호</div><div style=\"float:left;\">".nl2br($row['ITEM_NUMBER'])."</div></div>";
	$content .= "<div style=\"clear:both;padding:5px;\"><div style=\"float:left;width:100px;\">만료일자</div><div style=\"float:left;\">".nl2br($row['EXPIRE_YMD'])."</div></div>";
	$content .= "<div style=\"clear:both;\"><br><br></div>";
	$content .= "<div style=\"clear:both;\">---------------------------------</div>";
	$content .= "<div style=\"clear:both;\"><br></div>";
	$content .= "<div><a href=\"https://erp.posbank.com/?pageCode=MTE5MTc=\" target=\"_blank\">[IC리더기형상변경현황 보러가기]</a></div>";
	$content .= "<div style=\"clear:both;\"><br></div>";
	$content .= "</div>";

// 100994,100695
//$arr_ST = array('100695'); // 장진식
// $arr_ST = array('100994','100695'); // 신현주, 장진식
// 100968,100395,100119,100125,100940,100956,100641,100741,100718,100720,100721,100985,100860,100279,100251,100719,100605,100645,100647,100648,101003,100644 국내영업부/연구소부문

	//인트라넷 업무연락 보내는 함수(ERP 사원코드)
	$rs = intra_send_erp('',$arr_ST,$title,$content);


	// 구글워크스테이션 웹훅 보내기 
	$goolgework_Params['PROGRAM'] = "sperp";
	$goolgework_Params['GU'] = "ERP";
	$goolgework_Params['ID'] = $arr_ST;
	$goolgework_Params['PREVIEW'] = $title;
	$goolgework_Params['TITLE'] = "인증만료일수";

	$goolgework_Params['MESSAGE'][] = ["<b>VAN : </b><font color='#555555'>".$row['VAN_NAME']."</font>"];
	$goolgework_Params['MESSAGE'][] = ["<b>포스장비 : </b><font color='#555555'>".$row['POS_NAME']."</font>"];
	$goolgework_Params['MESSAGE'][] = ["<b>품목명 : </b><font color='#555555'>".nl2br($row['ITEM_NAME'])."</font>"];
	$goolgework_Params['MESSAGE'][] = ["<b>식별번호 : </b><font color='#555555'>".nl2br($row['ITEM_NUMBER'])."</font>"];
	$goolgework_Params['MESSAGE'][] = ["<b>만료일자 : </b><font color='#555555'>".nl2br($row['EXPIRE_YMD'])."</font>"];

	$goolgework_Params['LINK_NM'] = "확인하러가기";
	$goolgework_Params['LINK'] = "https://erp.posbank.com/?pageCode=MTE5MTc=";
	$rs = goolgework_send($goolgework_Params);



	$sql04 = "update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '".$row["IC_IDX"]."' ";
echo "<br>\n sql04 : ".$sql04;
	$rs = $dbconn_sperp_posbank->iud_query($sql04);


}


## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(86400, "IC리더기형상변경현황 > 인증 만료일 알람");


?>