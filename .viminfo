# This viminfo file was generated by Vim 8.1.
# You may edit it if you're careful!

# Viminfo version
|1,4

# Value of 'encoding' when this file was written
*encoding=utf-8


# hlsearch on (H) or off (h):
~h
# Last Search Pattern:
~MSle0~/sh

# Last Substitute String:
$

# Command Line History (newest to oldest):
:wq!
|2,0,1750826634,,"wq!"
:set ff=unix
|2,0,1750826625,,"set ff=unix"
:q!
|2,0,1750826615,,"q!"
:q
|2,0,1750826601,,"q"

# Search String History (newest to oldest):
?/sh
|2,1,1693183793,47,"sh"
?/home
|2,1,1672109903,47,"home"
?/spqms
|2,1,1672109900,47,"spqms"
? @$
|2,1,1672109861,,"@$"

# Expression History (newest to oldest):

# Input Line History (newest to oldest):

# Debug Line History (newest to oldest):

# Registers:
""-	CHAR	0
	g
|3,1,36,0,1,0,1750826608,"g"

# File marks:
'0  6  9  ~/sperp/as_delay_mail_send.sh
|4,48,6,9,1750826634,"~/sperp/as_delay_mail_send.sh"
'1  6  103  ~/sperp/as_delay_mail_send.sh
|4,49,6,103,1750826615,"~/sperp/as_delay_mail_send.sh"
'2  1  0  ~/log/snm_line_use_seting.psh.20231121.log
|4,50,1,0,1700533784,"~/log/snm_line_use_seting.psh.20231121.log"
'3  200  0  ~/.bash_history
|4,51,200,0,1693184530,"~/.bash_history"
'4  155  0  ~/.bash_history
|4,52,155,0,1693183554,"~/.bash_history"
'5  155  0  ~/.bash_history
|4,53,155,0,1693183554,"~/.bash_history"
'6  54  0  /etc/nginx/nginx.conf
|4,54,54,0,1672109905,"/etc/nginx/nginx.conf"
'7  8  0  /etc/nginx/conf.d
|4,55,8,0,1672109865,"/etc/nginx/conf.d"
'8  47  0  /etc/passwd
|4,56,47,0,1672109848,"/etc/passwd"

# Jumplist (newest first):
-'  6  9  ~/sperp/as_delay_mail_send.sh
|4,39,6,9,1750826634,"~/sperp/as_delay_mail_send.sh"
-'  6  103  ~/sperp/as_delay_mail_send.sh
|4,39,6,103,1750826615,"~/sperp/as_delay_mail_send.sh"
-'  1  0  ~/sperp/as_delay_mail_send.sh
|4,39,1,0,1750826562,"~/sperp/as_delay_mail_send.sh"
-'  1  0  ~/sperp/as_delay_mail_send.sh
|4,39,1,0,1750826562,"~/sperp/as_delay_mail_send.sh"
-'  1  0  ~/log/snm_line_use_seting.psh.20231121.log
|4,39,1,0,1700533784,"~/log/snm_line_use_seting.psh.20231121.log"
-'  1  0  ~/log/snm_line_use_seting.psh.20231121.log
|4,39,1,0,1700533784,"~/log/snm_line_use_seting.psh.20231121.log"
-'  1  0  ~/log/snm_line_use_seting.psh.20231121.log
|4,39,1,0,1700533784,"~/log/snm_line_use_seting.psh.20231121.log"
-'  1  0  ~/log/snm_line_use_seting.psh.20231121.log
|4,39,1,0,1700533784,"~/log/snm_line_use_seting.psh.20231121.log"
-'  200  0  ~/.bash_history
|4,39,200,0,1693184530,"~/.bash_history"
-'  200  0  ~/.bash_history
|4,39,200,0,1693184530,"~/.bash_history"
-'  200  0  ~/.bash_history
|4,39,200,0,1693184530,"~/.bash_history"
-'  200  0  ~/.bash_history
|4,39,200,0,1693184530,"~/.bash_history"
-'  200  0  ~/.bash_history
|4,39,200,0,1693184530,"~/.bash_history"
-'  200  0  ~/.bash_history
|4,39,200,0,1693184530,"~/.bash_history"
-'  200  0  ~/.bash_history
|4,39,200,0,1693184530,"~/.bash_history"
-'  200  0  ~/.bash_history
|4,39,200,0,1693184530,"~/.bash_history"
-'  191  16  ~/.bash_history
|4,39,191,16,1693183836,"~/.bash_history"
-'  191  16  ~/.bash_history
|4,39,191,16,1693183836,"~/.bash_history"
-'  191  16  ~/.bash_history
|4,39,191,16,1693183836,"~/.bash_history"
-'  191  16  ~/.bash_history
|4,39,191,16,1693183836,"~/.bash_history"
-'  191  16  ~/.bash_history
|4,39,191,16,1693183836,"~/.bash_history"
-'  191  16  ~/.bash_history
|4,39,191,16,1693183836,"~/.bash_history"
-'  191  16  ~/.bash_history
|4,39,191,16,1693183836,"~/.bash_history"
-'  191  16  ~/.bash_history
|4,39,191,16,1693183836,"~/.bash_history"
-'  189  16  ~/.bash_history
|4,39,189,16,1693183833,"~/.bash_history"
-'  187  22  ~/.bash_history
|4,39,187,22,1693183833,"~/.bash_history"
-'  186  16  ~/.bash_history
|4,39,186,16,1693183833,"~/.bash_history"
-'  179  17  ~/.bash_history
|4,39,179,17,1693183833,"~/.bash_history"
-'  177  17  ~/.bash_history
|4,39,177,17,1693183833,"~/.bash_history"
-'  176  15  ~/.bash_history
|4,39,176,15,1693183833,"~/.bash_history"
-'  189  16  ~/.bash_history
|4,39,189,16,1693183833,"~/.bash_history"
-'  187  22  ~/.bash_history
|4,39,187,22,1693183833,"~/.bash_history"
-'  186  16  ~/.bash_history
|4,39,186,16,1693183833,"~/.bash_history"
-'  179  17  ~/.bash_history
|4,39,179,17,1693183833,"~/.bash_history"
-'  177  17  ~/.bash_history
|4,39,177,17,1693183833,"~/.bash_history"
-'  176  15  ~/.bash_history
|4,39,176,15,1693183833,"~/.bash_history"
-'  189  16  ~/.bash_history
|4,39,189,16,1693183833,"~/.bash_history"
-'  187  22  ~/.bash_history
|4,39,187,22,1693183833,"~/.bash_history"
-'  186  16  ~/.bash_history
|4,39,186,16,1693183833,"~/.bash_history"
-'  179  17  ~/.bash_history
|4,39,179,17,1693183833,"~/.bash_history"
-'  177  17  ~/.bash_history
|4,39,177,17,1693183833,"~/.bash_history"
-'  176  15  ~/.bash_history
|4,39,176,15,1693183833,"~/.bash_history"
-'  189  16  ~/.bash_history
|4,39,189,16,1693183833,"~/.bash_history"
-'  187  22  ~/.bash_history
|4,39,187,22,1693183833,"~/.bash_history"
-'  186  16  ~/.bash_history
|4,39,186,16,1693183833,"~/.bash_history"
-'  179  17  ~/.bash_history
|4,39,179,17,1693183833,"~/.bash_history"
-'  177  17  ~/.bash_history
|4,39,177,17,1693183833,"~/.bash_history"
-'  176  15  ~/.bash_history
|4,39,176,15,1693183833,"~/.bash_history"
-'  189  16  ~/.bash_history
|4,39,189,16,1693183833,"~/.bash_history"
-'  187  22  ~/.bash_history
|4,39,187,22,1693183833,"~/.bash_history"
-'  186  16  ~/.bash_history
|4,39,186,16,1693183833,"~/.bash_history"
-'  179  17  ~/.bash_history
|4,39,179,17,1693183833,"~/.bash_history"
-'  177  17  ~/.bash_history
|4,39,177,17,1693183833,"~/.bash_history"
-'  176  15  ~/.bash_history
|4,39,176,15,1693183833,"~/.bash_history"
-'  189  16  ~/.bash_history
|4,39,189,16,1693183833,"~/.bash_history"
-'  187  22  ~/.bash_history
|4,39,187,22,1693183833,"~/.bash_history"
-'  186  16  ~/.bash_history
|4,39,186,16,1693183833,"~/.bash_history"
-'  179  17  ~/.bash_history
|4,39,179,17,1693183833,"~/.bash_history"
-'  177  17  ~/.bash_history
|4,39,177,17,1693183833,"~/.bash_history"
-'  176  15  ~/.bash_history
|4,39,176,15,1693183833,"~/.bash_history"
-'  189  16  ~/.bash_history
|4,39,189,16,1693183833,"~/.bash_history"
-'  187  22  ~/.bash_history
|4,39,187,22,1693183833,"~/.bash_history"
-'  186  16  ~/.bash_history
|4,39,186,16,1693183833,"~/.bash_history"
-'  179  17  ~/.bash_history
|4,39,179,17,1693183833,"~/.bash_history"
-'  177  17  ~/.bash_history
|4,39,177,17,1693183833,"~/.bash_history"
-'  176  15  ~/.bash_history
|4,39,176,15,1693183833,"~/.bash_history"
-'  189  16  ~/.bash_history
|4,39,189,16,1693183833,"~/.bash_history"
-'  187  22  ~/.bash_history
|4,39,187,22,1693183833,"~/.bash_history"
-'  186  16  ~/.bash_history
|4,39,186,16,1693183833,"~/.bash_history"
-'  179  17  ~/.bash_history
|4,39,179,17,1693183833,"~/.bash_history"
-'  177  17  ~/.bash_history
|4,39,177,17,1693183833,"~/.bash_history"
-'  176  15  ~/.bash_history
|4,39,176,15,1693183833,"~/.bash_history"
-'  172  18  ~/.bash_history
|4,39,172,18,1693183832,"~/.bash_history"
-'  162  13  ~/.bash_history
|4,39,162,13,1693183832,"~/.bash_history"
-'  160  30  ~/.bash_history
|4,39,160,30,1693183832,"~/.bash_history"
-'  156  16  ~/.bash_history
|4,39,156,16,1693183832,"~/.bash_history"
-'  154  32  ~/.bash_history
|4,39,154,32,1693183832,"~/.bash_history"
-'  150  21  ~/.bash_history
|4,39,150,21,1693183832,"~/.bash_history"
-'  146  39  ~/.bash_history
|4,39,146,39,1693183832,"~/.bash_history"
-'  145  40  ~/.bash_history
|4,39,145,40,1693183832,"~/.bash_history"
-'  144  39  ~/.bash_history
|4,39,144,39,1693183832,"~/.bash_history"
-'  143  40  ~/.bash_history
|4,39,143,40,1693183832,"~/.bash_history"
-'  142  39  ~/.bash_history
|4,39,142,39,1693183832,"~/.bash_history"
-'  141  40  ~/.bash_history
|4,39,141,40,1693183832,"~/.bash_history"
-'  140  39  ~/.bash_history
|4,39,140,39,1693183832,"~/.bash_history"
-'  139  49  ~/.bash_history
|4,39,139,49,1693183832,"~/.bash_history"
-'  138  48  ~/.bash_history
|4,39,138,48,1693183832,"~/.bash_history"
-'  137  49  ~/.bash_history
|4,39,137,49,1693183832,"~/.bash_history"
-'  172  18  ~/.bash_history
|4,39,172,18,1693183832,"~/.bash_history"
-'  162  13  ~/.bash_history
|4,39,162,13,1693183832,"~/.bash_history"
-'  160  30  ~/.bash_history
|4,39,160,30,1693183832,"~/.bash_history"
-'  156  16  ~/.bash_history
|4,39,156,16,1693183832,"~/.bash_history"
-'  154  32  ~/.bash_history
|4,39,154,32,1693183832,"~/.bash_history"
-'  150  21  ~/.bash_history
|4,39,150,21,1693183832,"~/.bash_history"
-'  146  39  ~/.bash_history
|4,39,146,39,1693183832,"~/.bash_history"
-'  145  40  ~/.bash_history
|4,39,145,40,1693183832,"~/.bash_history"
-'  144  39  ~/.bash_history
|4,39,144,39,1693183832,"~/.bash_history"
-'  143  40  ~/.bash_history
|4,39,143,40,1693183832,"~/.bash_history"
-'  142  39  ~/.bash_history
|4,39,142,39,1693183832,"~/.bash_history"
-'  141  40  ~/.bash_history
|4,39,141,40,1693183832,"~/.bash_history"

# History of marks within files (newest to oldest):

> ~/sperp/as_delay_mail_send.sh
	*	1750826628	0
	"	6	9
	^	6	104
	.	6	103
	+	1	11
	+	2	25
	+	3	16
	+	4	15
	+	5	12
	+	6	0
	+	5	12
	+	6	103

> ~/log/snm_line_use_seting.psh.20231121.log
	*	1700533783	0
	"	1	0

> ~/.bash_history
	*	1693183839	0
	"	200	0
	^	155	1
	.	155	0
	+	155	0

> /etc/nginx/nginx.conf
	*	1672109905	0
	"	54	0

> /etc/passwd
	*	1672109846	0
	"	47	0
