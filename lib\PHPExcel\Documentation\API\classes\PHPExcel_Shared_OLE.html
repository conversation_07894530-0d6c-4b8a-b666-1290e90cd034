<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_OLE</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_Asc2Ucs" title="Asc2Ucs :: Utility function to transform ASCII text to Unicode"><span class="description">Utility function to transform ASCII text to Unicode</span><pre>Asc2Ucs()</pre></a></li>
<li class="method public "><a href="#method_LocalDate2OLE" title="LocalDate2OLE :: Utility function
Returns a string for the OLE container with the date given"><span class="description">Utility function
Returns a string for the OLE container with the date given</span><pre>LocalDate2OLE()</pre></a></li>
<li class="method public "><a href="#method_OLE2LocalDate" title="OLE2LocalDate :: Returns a timestamp from an OLE container's date"><span class="description">Returns a timestamp from an OLE container's date</span><pre>OLE2LocalDate()</pre></a></li>
<li class="method public "><a href="#method__getBlockOffset" title="_getBlockOffset :: "><span class="description">_getBlockOffset()
        </span><pre>_getBlockOffset()</pre></a></li>
<li class="method public "><a href="#method__ppsTreeComplete" title="_ppsTreeComplete :: It checks whether the PPS tree is complete (all PPS's read)
starting with the given PPS (not necessarily root)"><span class="description">It checks whether the PPS tree is complete (all PPS's read)
starting with the given PPS (not necessarily root)</span><pre>_ppsTreeComplete()</pre></a></li>
<li class="method public "><a href="#method__readPpsWks" title="_readPpsWks :: Gets information about all PPS's on the OLE container from the PPS WK's
creates an OLE_PPS object for each one."><span class="description">Gets information about all PPS's on the OLE container from the PPS WK's
creates an OLE_PPS object for each one.</span><pre>_readPpsWks()</pre></a></li>
<li class="method public "><a href="#method_getData" title="getData :: Gets data from a PPS
If there is no PPS for the index given, it will return an empty string."><span class="description">Gets data from a PPS
If there is no PPS for the index given, it will return an empty string.</span><pre>getData()</pre></a></li>
<li class="method public "><a href="#method_getDataLength" title="getDataLength :: Gets the data length from a PPS
If there is no PPS for the index given, it will return 0."><span class="description">Gets the data length from a PPS
If there is no PPS for the index given, it will return 0.</span><pre>getDataLength()</pre></a></li>
<li class="method public "><a href="#method_getStream" title="getStream :: Returns a stream for use with fread() etc."><span class="description">Returns a stream for use with fread() etc.</span><pre>getStream()</pre></a></li>
<li class="method public "><a href="#method_isFile" title="isFile :: Checks whether a PPS is a File PPS or not."><span class="description">Checks whether a PPS is a File PPS or not.</span><pre>isFile()</pre></a></li>
<li class="method public "><a href="#method_isRoot" title="isRoot :: Checks whether a PPS is a Root PPS or not."><span class="description">Checks whether a PPS is a Root PPS or not.</span><pre>isRoot()</pre></a></li>
<li class="method public "><a href="#method_ppsTotal" title="ppsTotal :: Gives the total number of PPS's found in the OLE container."><span class="description">Gives the total number of PPS's found in the OLE container.</span><pre>ppsTotal()</pre></a></li>
<li class="method public "><a href="#method_read" title="read :: Reads an OLE container from the contents of the file given."><span class="description">Reads an OLE container from the contents of the file given.</span><pre>read()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="method private "><a href="#method__readInt1" title="_readInt1 :: Reads a signed char."><span class="description">Reads a signed char.</span><pre>_readInt1()</pre></a></li>
<li class="method private "><a href="#method__readInt2" title="_readInt2 :: Reads an unsigned short (2 octets)."><span class="description">Reads an unsigned short (2 octets).</span><pre>_readInt2()</pre></a></li>
<li class="method private "><a href="#method__readInt4" title="_readInt4 :: Reads an unsigned long (4 octets)."><span class="description">Reads an unsigned long (4 octets).</span><pre>_readInt4()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul>
<li class="property public "><a href="#property__file_handle" title="$_file_handle :: The file handle for reading an OLE container"><span class="description"></span><pre>$_file_handle</pre></a></li>
<li class="property public "><a href="#property__list" title="$_list :: Array of PPS's found on the OLE container"><span class="description"></span><pre>$_list</pre></a></li>
<li class="property public "><a href="#property_bbat" title="$bbat :: Big Block Allocation Table"><span class="description"></span><pre>$bbat</pre></a></li>
<li class="property public "><a href="#property_bigBlockSize" title="$bigBlockSize :: Size of big blocks."><span class="description"></span><pre>$bigBlockSize</pre></a></li>
<li class="property public "><a href="#property_root" title="$root :: Root directory of OLE container"><span class="description"></span><pre>$root</pre></a></li>
<li class="property public "><a href="#property_sbat" title="$sbat :: Short Block Allocation Table"><span class="description"></span><pre>$sbat</pre></a></li>
<li class="property public "><a href="#property_smallBlockSize" title="$smallBlockSize :: Size of small blocks."><span class="description"></span><pre>$smallBlockSize</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_OLE_DATA_SIZE_SMALL" title="OLE_DATA_SIZE_SMALL :: "><span class="description">OLE_DATA_SIZE_SMALL</span><pre>OLE_DATA_SIZE_SMALL</pre></a></li>
<li class="constant  "><a href="#constant_OLE_LONG_INT_SIZE" title="OLE_LONG_INT_SIZE :: "><span class="description">OLE_LONG_INT_SIZE</span><pre>OLE_LONG_INT_SIZE</pre></a></li>
<li class="constant  "><a href="#constant_OLE_PPS_SIZE" title="OLE_PPS_SIZE :: "><span class="description">OLE_PPS_SIZE</span><pre>OLE_PPS_SIZE</pre></a></li>
<li class="constant  "><a href="#constant_OLE_PPS_TYPE_DIR" title="OLE_PPS_TYPE_DIR :: "><span class="description">OLE_PPS_TYPE_DIR</span><pre>OLE_PPS_TYPE_DIR</pre></a></li>
<li class="constant  "><a href="#constant_OLE_PPS_TYPE_FILE" title="OLE_PPS_TYPE_FILE :: "><span class="description">OLE_PPS_TYPE_FILE</span><pre>OLE_PPS_TYPE_FILE</pre></a></li>
<li class="constant  "><a href="#constant_OLE_PPS_TYPE_ROOT" title="OLE_PPS_TYPE_ROOT :: "><span class="description">OLE_PPS_TYPE_ROOT</span><pre>OLE_PPS_TYPE_ROOT</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_OLE"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_OLE.html">PHPExcel_Shared_OLE</a>
</li>
</ul>
<div class="element class">
<p class="short_description">OLE package base class.</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>author</th>
<td><a href="mailto:<EMAIL>">Xavier Noguer</a></td>
</tr>
<tr>
<th>author</th>
<td><a href="mailto:<EMAIL>">Christian Schmidt</a></td>
</tr>
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Shared.OLE.html">PHPExcel_Shared_OLE</a></td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_Asc2Ucs"></a><div class="element clickable method public method_Asc2Ucs" data-toggle="collapse" data-target=".method_Asc2Ucs .collapse">
<h2>Utility function to transform ASCII text to Unicode</h2>
<pre>Asc2Ucs(string $ascii) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>static</th>
<td></td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$ascii</h4>
<code>string</code><p>The ASCII string to transform</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>The string in Unicode</div>
</div></div>
</div>
<a id="method_LocalDate2OLE"></a><div class="element clickable method public method_LocalDate2OLE" data-toggle="collapse" data-target=".method_LocalDate2OLE .collapse">
<h2>Utility function
Returns a string for the OLE container with the date given</h2>
<pre>LocalDate2OLE(integer $date) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>static</th>
<td></td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$date</h4>
<code>integer</code><p>A timestamp</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>The string for the OLE container</div>
</div></div>
</div>
<a id="method_OLE2LocalDate"></a><div class="element clickable method public method_OLE2LocalDate" data-toggle="collapse" data-target=".method_OLE2LocalDate .collapse">
<h2>Returns a timestamp from an OLE container's date</h2>
<pre>OLE2LocalDate(integer $string) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>static</th>
<td></td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$string</h4>
<code>integer</code><p>A binary string with the encoded date</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>The timestamp corresponding to the string</div>
</div></div>
</div>
<a id="method__getBlockOffset"></a><div class="element clickable method public method__getBlockOffset" data-toggle="collapse" data-target=".method__getBlockOffset .collapse">
<h2>_getBlockOffset()
        </h2>
<pre>_getBlockOffset(int $blockId) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$blockId</h4>
<code>int</code><p>block id</p></div>
</div></div>
</div>
<a id="method__ppsTreeComplete"></a><div class="element clickable method public method__ppsTreeComplete" data-toggle="collapse" data-target=".method__ppsTreeComplete .collapse">
<h2>It checks whether the PPS tree is complete (all PPS's read)
starting with the given PPS (not necessarily root)</h2>
<pre>_ppsTreeComplete(integer $index) : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$index</h4>
<code>integer</code><p>The index of the PPS from which we are checking</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>boolean</code>Whether the PPS tree for the given PPS is complete</div>
</div></div>
</div>
<a id="method__readPpsWks"></a><div class="element clickable method public method__readPpsWks" data-toggle="collapse" data-target=".method__readPpsWks .collapse">
<h2>Gets information about all PPS's on the OLE container from the PPS WK's
creates an OLE_PPS object for each one.</h2>
<pre>_readPpsWks(integer $blockId) : mixed</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$blockId</h4>
<code>integer</code><p>the block id of the first block</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>true on success, PEAR_Error on failure</div>
</div></div>
</div>
<a id="method_getData"></a><div class="element clickable method public method_getData" data-toggle="collapse" data-target=".method_getData .collapse">
<h2>Gets data from a PPS
If there is no PPS for the index given, it will return an empty string.</h2>
<pre>getData(integer $index, integer $position, integer $length) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>see</th>
<td><a href="">\OLE_PPS_File::getStream()</a></td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$index</h4>
<code>integer</code><p>The index for the PPS</p></div>
<div class="subelement argument">
<h4>$position</h4>
<code>integer</code><p>The position from which to start reading
                         (relative to the PPS)</p>
</div>
<div class="subelement argument">
<h4>$length</h4>
<code>integer</code><p>The amount of bytes to read (at most)</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>The binary string containing the data requested</div>
</div></div>
</div>
<a id="method_getDataLength"></a><div class="element clickable method public method_getDataLength" data-toggle="collapse" data-target=".method_getDataLength .collapse">
<h2>Gets the data length from a PPS
If there is no PPS for the index given, it will return 0.</h2>
<pre>getDataLength(integer $index) : integer</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$index</h4>
<code>integer</code><p>The index for the PPS</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>The amount of bytes in data the PPS has</div>
</div></div>
</div>
<a id="method_getStream"></a><div class="element clickable method public method_getStream" data-toggle="collapse" data-target=".method_getStream .collapse">
<h2>Returns a stream for use with fread() etc.</h2>
<pre>getStream(int | \PPS $blockIdOrPps) : resource</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>External callers should
use PHPExcel_Shared_OLE_PPS_File::getStream().</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$blockIdOrPps</h4>
<code>int</code><code>\PPS</code><p>block id or PPS</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>resource</code>read-only stream</div>
</div></div>
</div>
<a id="method_isFile"></a><div class="element clickable method public method_isFile" data-toggle="collapse" data-target=".method_isFile .collapse">
<h2>Checks whether a PPS is a File PPS or not.</h2>
<pre>isFile(integer $index) : bool</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>If there is no PPS for the index given, it will return false.</p></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$index</h4>
<code>integer</code><p>The index for the PPS</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>bool</code>true if it's a File PPS, false otherwise</div>
</div></div>
</div>
<a id="method_isRoot"></a><div class="element clickable method public method_isRoot" data-toggle="collapse" data-target=".method_isRoot .collapse">
<h2>Checks whether a PPS is a Root PPS or not.</h2>
<pre>isRoot(integer $index) : bool</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>If there is no PPS for the index given, it will return false.</p></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$index</h4>
<code>integer</code><p>The index for the PPS.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>bool</code>true if it's a Root PPS, false otherwise</div>
</div></div>
</div>
<a id="method_ppsTotal"></a><div class="element clickable method public method_ppsTotal" data-toggle="collapse" data-target=".method_ppsTotal .collapse">
<h2>Gives the total number of PPS's found in the OLE container.</h2>
<pre>ppsTotal() : integer</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>The total number of PPS's found in the OLE container</div>
</div></div>
</div>
<a id="method_read"></a><div class="element clickable method public method_read" data-toggle="collapse" data-target=".method_read .collapse">
<h2>Reads an OLE container from the contents of the file given.</h2>
<pre>read(string $file) : mixed</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>acces</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$file</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>true on success, PEAR_Error on failure</div>
</div></div>
</div>
<a id="method__readInt1"></a><div class="element clickable method private method__readInt1" data-toggle="collapse" data-target=".method__readInt1 .collapse">
<h2>Reads a signed char.</h2>
<pre>_readInt1(resource $fh) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$fh</h4>
<code>resource</code><p>file handle</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method__readInt2"></a><div class="element clickable method private method__readInt2" data-toggle="collapse" data-target=".method__readInt2 .collapse">
<h2>Reads an unsigned short (2 octets).</h2>
<pre>_readInt2(resource $fh) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$fh</h4>
<code>resource</code><p>file handle</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method__readInt4"></a><div class="element clickable method private method__readInt4" data-toggle="collapse" data-target=".method__readInt4 .collapse">
<h2>Reads an unsigned long (4 octets).</h2>
<pre>_readInt4(resource $fh) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$fh</h4>
<code>resource</code><p>file handle</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__file_handle"> </a><div class="element clickable property public property__file_handle" data-toggle="collapse" data-target=".property__file_handle .collapse">
<h2></h2>
<pre>$_file_handle : resource</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__list"> </a><div class="element clickable property public property__list" data-toggle="collapse" data-target=".property__list .collapse">
<h2></h2>
<pre>$_list : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_bbat"> </a><div class="element clickable property public property_bbat" data-toggle="collapse" data-target=".property_bbat .collapse">
<h2></h2>
<pre>$bbat : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_bigBlockSize"> </a><div class="element clickable property public property_bigBlockSize" data-toggle="collapse" data-target=".property_bigBlockSize .collapse">
<h2></h2>
<pre>$bigBlockSize : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>This is usually 512.</p></div></div></div>
</div>
<a id="property_root"> </a><div class="element clickable property public property_root" data-toggle="collapse" data-target=".property_root .collapse">
<h2></h2>
<pre>$root : \OLE_PPS_Root</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_sbat"> </a><div class="element clickable property public property_sbat" data-toggle="collapse" data-target=".property_sbat .collapse">
<h2></h2>
<pre>$sbat : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_smallBlockSize"> </a><div class="element clickable property public property_smallBlockSize" data-toggle="collapse" data-target=".property_smallBlockSize .collapse">
<h2></h2>
<pre>$smallBlockSize : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>This is usually 64.</p></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_OLE_DATA_SIZE_SMALL"> </a><div class="element clickable constant  constant_OLE_DATA_SIZE_SMALL" data-toggle="collapse" data-target=".constant_OLE_DATA_SIZE_SMALL .collapse">
<h2>OLE_DATA_SIZE_SMALL</h2>
<pre>OLE_DATA_SIZE_SMALL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OLE_LONG_INT_SIZE"> </a><div class="element clickable constant  constant_OLE_LONG_INT_SIZE" data-toggle="collapse" data-target=".constant_OLE_LONG_INT_SIZE .collapse">
<h2>OLE_LONG_INT_SIZE</h2>
<pre>OLE_LONG_INT_SIZE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OLE_PPS_SIZE"> </a><div class="element clickable constant  constant_OLE_PPS_SIZE" data-toggle="collapse" data-target=".constant_OLE_PPS_SIZE .collapse">
<h2>OLE_PPS_SIZE</h2>
<pre>OLE_PPS_SIZE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OLE_PPS_TYPE_DIR"> </a><div class="element clickable constant  constant_OLE_PPS_TYPE_DIR" data-toggle="collapse" data-target=".constant_OLE_PPS_TYPE_DIR .collapse">
<h2>OLE_PPS_TYPE_DIR</h2>
<pre>OLE_PPS_TYPE_DIR </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OLE_PPS_TYPE_FILE"> </a><div class="element clickable constant  constant_OLE_PPS_TYPE_FILE" data-toggle="collapse" data-target=".constant_OLE_PPS_TYPE_FILE .collapse">
<h2>OLE_PPS_TYPE_FILE</h2>
<pre>OLE_PPS_TYPE_FILE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OLE_PPS_TYPE_ROOT"> </a><div class="element clickable constant  constant_OLE_PPS_TYPE_ROOT" data-toggle="collapse" data-target=".constant_OLE_PPS_TYPE_ROOT .collapse">
<h2>OLE_PPS_TYPE_ROOT</h2>
<pre>OLE_PPS_TYPE_ROOT </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:37Z.<br></footer></div>
</div>
</body>
</html>
