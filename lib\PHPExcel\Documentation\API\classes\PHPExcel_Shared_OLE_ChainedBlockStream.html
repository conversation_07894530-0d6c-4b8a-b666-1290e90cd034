<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_OLE_ChainedBlockStream</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_stream_close" title="stream_close :: Implements support for fclose()."><span class="description">Implements support for fclose().</span><pre>stream_close()</pre></a></li>
<li class="method public "><a href="#method_stream_eof" title="stream_eof :: Implements support for feof()."><span class="description">Implements support for feof().</span><pre>stream_eof()</pre></a></li>
<li class="method public "><a href="#method_stream_open" title="stream_open :: Implements support for fopen()."><span class="description">Implements support for fopen().</span><pre>stream_open()</pre></a></li>
<li class="method public "><a href="#method_stream_read" title="stream_read :: Implements support for fread(), fgets() etc."><span class="description">Implements support for fread(), fgets() etc.</span><pre>stream_read()</pre></a></li>
<li class="method public "><a href="#method_stream_seek" title="stream_seek :: Implements support for fseek()."><span class="description">Implements support for fseek().</span><pre>stream_seek()</pre></a></li>
<li class="method public "><a href="#method_stream_stat" title="stream_stat :: Implements support for fstat()."><span class="description">Implements support for fstat().</span><pre>stream_stat()</pre></a></li>
<li class="method public "><a href="#method_stream_tell" title="stream_tell :: Returns the position of the file pointer, i.e."><span class="description">Returns the position of the file pointer, i.e.</span><pre>stream_tell()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul>
<li class="property public "><a href="#property_data" title="$data :: The binary data of the file."><span class="description"></span><pre>$data</pre></a></li>
<li class="property public "><a href="#property_ole" title="$ole :: The OLE container of the file that is being read."><span class="description"></span><pre>$ole</pre></a></li>
<li class="property public "><a href="#property_params" title="$params :: Parameters specified by fopen()."><span class="description"></span><pre>$params</pre></a></li>
<li class="property public "><a href="#property_pos" title="$pos :: The file pointer."><span class="description"></span><pre>$pos</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_OLE_ChainedBlockStream"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_OLE_ChainedBlockStream.html">PHPExcel_Shared_OLE_ChainedBlockStream</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Shared_OLE_ChainedBlockStream</p>
<div class="details">
<div class="long_description"><p>Stream wrapper for reading data stored in an OLE file. Implements methods
for PHP's stream_wrapper_register(). For creating streams using this
wrapper, use PHPExcel_Shared_OLE_PPS_File::getStream().</p></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Shared.OLE.html">PHPExcel_Shared_OLE</a></td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_stream_close"></a><div class="element clickable method public method_stream_close" data-toggle="collapse" data-target=".method_stream_close .collapse">
<h2>Implements support for fclose().</h2>
<pre>stream_close() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_stream_eof"></a><div class="element clickable method public method_stream_eof" data-toggle="collapse" data-target=".method_stream_eof .collapse">
<h2>Implements support for feof().</h2>
<pre>stream_eof() : bool</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>bool</code>TRUE if the file pointer is at EOF; otherwise FALSE</div>
</div></div>
</div>
<a id="method_stream_open"></a><div class="element clickable method public method_stream_open" data-toggle="collapse" data-target=".method_stream_open .collapse">
<h2>Implements support for fopen().</h2>
<pre>stream_open(string $path, string $mode, int $options, string $openedPath) : bool</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>For creating streams using this wrapper, use OLE_PPS_File::getStream().</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$path</h4>
<code>string</code><p>resource name including scheme, e.g.
                                ole-chainedblockstream://oleInstanceId=1</p>
</div>
<div class="subelement argument">
<h4>$mode</h4>
<code>string</code><p>only "r" is supported</p>
</div>
<div class="subelement argument">
<h4>$options</h4>
<code>int</code><p>mask of STREAM_REPORT_ERRORS and STREAM_USE_PATH</p></div>
<div class="subelement argument">
<h4>$openedPath</h4>
<code>string</code><p>&$openedPath    absolute path of the opened stream (out parameter)</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>bool</code>true on success</div>
</div></div>
</div>
<a id="method_stream_read"></a><div class="element clickable method public method_stream_read" data-toggle="collapse" data-target=".method_stream_read .collapse">
<h2>Implements support for fread(), fgets() etc.</h2>
<pre>stream_read(int $count) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$count</h4>
<code>int</code><p>maximum number of bytes to read</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_stream_seek"></a><div class="element clickable method public method_stream_seek" data-toggle="collapse" data-target=".method_stream_seek .collapse">
<h2>Implements support for fseek().</h2>
<pre>stream_seek(int $offset, int $whence) : bool</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$offset</h4>
<code>int</code><p>byte offset</p></div>
<div class="subelement argument">
<h4>$whence</h4>
<code>int</code><p>SEEK_SET, SEEK_CUR or SEEK_END</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>bool</code></div>
</div></div>
</div>
<a id="method_stream_stat"></a><div class="element clickable method public method_stream_stat" data-toggle="collapse" data-target=".method_stream_stat .collapse">
<h2>Implements support for fstat().</h2>
<pre>stream_stat() : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Currently the only supported field is
"size".</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>array</code></div>
</div></div>
</div>
<a id="method_stream_tell"></a><div class="element clickable method public method_stream_tell" data-toggle="collapse" data-target=".method_stream_tell .collapse">
<h2>Returns the position of the file pointer, i.e.</h2>
<pre>stream_tell() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>its offset into the file
stream. Implements support for ftell().</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property_data"> </a><div class="element clickable property public property_data" data-toggle="collapse" data-target=".property_data .collapse">
<h2></h2>
<pre>$data : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_ole"> </a><div class="element clickable property public property_ole" data-toggle="collapse" data-target=".property_ole .collapse">
<h2></h2>
<pre>$ole : \OLE</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_params"> </a><div class="element clickable property public property_params" data-toggle="collapse" data-target=".property_params .collapse">
<h2></h2>
<pre>$params : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_pos"> </a><div class="element clickable property public property_pos" data-toggle="collapse" data-target=".property_pos .collapse">
<h2></h2>
<pre>$pos : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:37Z.<br></footer></div>
</div>
</body>
</html>
