<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Chart_Layout</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_Chart_Layout"><span class="description">Create a new PHPExcel_Chart_Layout</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_getHeight" title="getHeight :: Get Height"><span class="description">Get Height</span><pre>getHeight()</pre></a></li>
<li class="method public "><a href="#method_getLayoutTarget" title="getLayoutTarget :: Get Layout Target"><span class="description">Get Layout Target</span><pre>getLayoutTarget()</pre></a></li>
<li class="method public "><a href="#method_getShowBubbleSize" title="getShowBubbleSize :: Get show bubble size"><span class="description">Get show bubble size</span><pre>getShowBubbleSize()</pre></a></li>
<li class="method public "><a href="#method_getShowCatName" title="getShowCatName :: Get show category name"><span class="description">Get show category name</span><pre>getShowCatName()</pre></a></li>
<li class="method public "><a href="#method_getShowLeaderLines" title="getShowLeaderLines :: Get show leader lines"><span class="description">Get show leader lines</span><pre>getShowLeaderLines()</pre></a></li>
<li class="method public "><a href="#method_getShowLegendKey" title="getShowLegendKey :: Get show legend key"><span class="description">Get show legend key</span><pre>getShowLegendKey()</pre></a></li>
<li class="method public "><a href="#method_getShowPercent" title="getShowPercent :: Get show percentage"><span class="description">Get show percentage</span><pre>getShowPercent()</pre></a></li>
<li class="method public "><a href="#method_getShowSerName" title="getShowSerName :: Get show data series name"><span class="description">Get show data series name</span><pre>getShowSerName()</pre></a></li>
<li class="method public "><a href="#method_getShowVal" title="getShowVal :: Get show value"><span class="description">Get show value</span><pre>getShowVal()</pre></a></li>
<li class="method public "><a href="#method_getWidth" title="getWidth :: Get Width"><span class="description">Get Width</span><pre>getWidth()</pre></a></li>
<li class="method public "><a href="#method_getXMode" title="getXMode :: Get X-Mode"><span class="description">Get X-Mode</span><pre>getXMode()</pre></a></li>
<li class="method public "><a href="#method_getXPosition" title="getXPosition :: Get X-Position"><span class="description">Get X-Position</span><pre>getXPosition()</pre></a></li>
<li class="method public "><a href="#method_getYMode" title="getYMode :: Get Y-Mode"><span class="description">Get Y-Mode</span><pre>getYMode()</pre></a></li>
<li class="method public "><a href="#method_getYPosition" title="getYPosition :: Get Y-Position"><span class="description">Get Y-Position</span><pre>getYPosition()</pre></a></li>
<li class="method public "><a href="#method_setHeight" title="setHeight :: Set Height"><span class="description">Set Height</span><pre>setHeight()</pre></a></li>
<li class="method public "><a href="#method_setLayoutTarget" title="setLayoutTarget :: Set Layout Target"><span class="description">Set Layout Target</span><pre>setLayoutTarget()</pre></a></li>
<li class="method public "><a href="#method_setShowBubbleSize" title="setShowBubbleSize :: Set show bubble size
Specifies that the bubble size should be shown in data labels."><span class="description">Set show bubble size
Specifies that the bubble size should be shown in data labels.</span><pre>setShowBubbleSize()</pre></a></li>
<li class="method public "><a href="#method_setShowCatName" title="setShowCatName :: Set show cat name
Specifies that the category name should be shown in data labels."><span class="description">Set show cat name
Specifies that the category name should be shown in data labels.</span><pre>setShowCatName()</pre></a></li>
<li class="method public "><a href="#method_setShowLeaderLines" title="setShowLeaderLines :: Set show leader lines
Specifies that leader lines should be shown in data labels."><span class="description">Set show leader lines
Specifies that leader lines should be shown in data labels.</span><pre>setShowLeaderLines()</pre></a></li>
<li class="method public "><a href="#method_setShowLegendKey" title="setShowLegendKey :: Set show legend key
Specifies that legend keys should be shown in data labels."><span class="description">Set show legend key
Specifies that legend keys should be shown in data labels.</span><pre>setShowLegendKey()</pre></a></li>
<li class="method public "><a href="#method_setShowPercent" title="setShowPercent :: Set show percentage
Specifies that the percentage should be shown in data labels."><span class="description">Set show percentage
Specifies that the percentage should be shown in data labels.</span><pre>setShowPercent()</pre></a></li>
<li class="method public "><a href="#method_setShowSerName" title="setShowSerName :: Set show ser name
Specifies that the series name should be shown in data labels."><span class="description">Set show ser name
Specifies that the series name should be shown in data labels.</span><pre>setShowSerName()</pre></a></li>
<li class="method public "><a href="#method_setShowVal" title="setShowVal :: Set show val
Specifies that the value should be shown in data labels."><span class="description">Set show val
Specifies that the value should be shown in data labels.</span><pre>setShowVal()</pre></a></li>
<li class="method public "><a href="#method_setWidth" title="setWidth :: Set Width"><span class="description">Set Width</span><pre>setWidth()</pre></a></li>
<li class="method public "><a href="#method_setXMode" title="setXMode :: Set X-Mode"><span class="description">Set X-Mode</span><pre>setXMode()</pre></a></li>
<li class="method public "><a href="#method_setXPosition" title="setXPosition :: Set X-Position"><span class="description">Set X-Position</span><pre>setXPosition()</pre></a></li>
<li class="method public "><a href="#method_setYMode" title="setYMode :: Set Y-Mode"><span class="description">Set Y-Mode</span><pre>setYMode()</pre></a></li>
<li class="method public "><a href="#method_setYPosition" title="setYPosition :: Set Y-Position"><span class="description">Set Y-Position</span><pre>setYPosition()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__height" title="$_height :: height"><span class="description"></span><pre>$_height</pre></a></li>
<li class="property private "><a href="#property__layoutTarget" title="$_layoutTarget :: layoutTarget"><span class="description"></span><pre>$_layoutTarget</pre></a></li>
<li class="property private "><a href="#property__showBubbleSize" title="$_showBubbleSize :: show bubble size"><span class="description"></span><pre>$_showBubbleSize</pre></a></li>
<li class="property private "><a href="#property__showCatName" title="$_showCatName :: show category name
Specifies that the category name should be shown in the data label."><span class="description"></span><pre>$_showCatName</pre></a></li>
<li class="property private "><a href="#property__showLeaderLines" title="$_showLeaderLines :: show leader lines
Specifies that leader lines should be shown for the data label."><span class="description"></span><pre>$_showLeaderLines</pre></a></li>
<li class="property private "><a href="#property__showLegendKey" title="$_showLegendKey :: show legend key
Specifies that legend keys should be shown in data labels"><span class="description"></span><pre>$_showLegendKey</pre></a></li>
<li class="property private "><a href="#property__showPercent" title="$_showPercent :: show percentage
Specifies that the percentage should be shown in the data label."><span class="description"></span><pre>$_showPercent</pre></a></li>
<li class="property private "><a href="#property__showSerName" title="$_showSerName :: show data series name
Specifies that the series name should be shown in the data label."><span class="description"></span><pre>$_showSerName</pre></a></li>
<li class="property private "><a href="#property__showVal" title="$_showVal :: show value
Specifies that the value should be shown in a data label."><span class="description"></span><pre>$_showVal</pre></a></li>
<li class="property private "><a href="#property__width" title="$_width :: width"><span class="description"></span><pre>$_width</pre></a></li>
<li class="property private "><a href="#property__xMode" title="$_xMode :: X Mode"><span class="description"></span><pre>$_xMode</pre></a></li>
<li class="property private "><a href="#property__xPos" title="$_xPos :: X-Position"><span class="description"></span><pre>$_xPos</pre></a></li>
<li class="property private "><a href="#property__yMode" title="$_yMode :: Y Mode"><span class="description"></span><pre>$_yMode</pre></a></li>
<li class="property private "><a href="#property__yPos" title="$_yPos :: Y-Position"><span class="description"></span><pre>$_yPos</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Chart_Layout"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Chart_Layout.html">PHPExcel_Chart_Layout</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Chart_Layout</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Chart.html">PHPExcel_Chart</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_Chart_Layout</h2>
<pre>__construct($layout) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$layout</h4></div>
</div></div>
</div>
<a id="method_getHeight"></a><div class="element clickable method public method_getHeight" data-toggle="collapse" data-target=".method_getHeight .collapse">
<h2>Get Height</h2>
<pre>getHeight() : \number</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>\number</code></div>
</div></div>
</div>
<a id="method_getLayoutTarget"></a><div class="element clickable method public method_getLayoutTarget" data-toggle="collapse" data-target=".method_getLayoutTarget .collapse">
<h2>Get Layout Target</h2>
<pre>getLayoutTarget() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getShowBubbleSize"></a><div class="element clickable method public method_getShowBubbleSize" data-toggle="collapse" data-target=".method_getShowBubbleSize .collapse">
<h2>Get show bubble size</h2>
<pre>getShowBubbleSize() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getShowCatName"></a><div class="element clickable method public method_getShowCatName" data-toggle="collapse" data-target=".method_getShowCatName .collapse">
<h2>Get show category name</h2>
<pre>getShowCatName() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getShowLeaderLines"></a><div class="element clickable method public method_getShowLeaderLines" data-toggle="collapse" data-target=".method_getShowLeaderLines .collapse">
<h2>Get show leader lines</h2>
<pre>getShowLeaderLines() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getShowLegendKey"></a><div class="element clickable method public method_getShowLegendKey" data-toggle="collapse" data-target=".method_getShowLegendKey .collapse">
<h2>Get show legend key</h2>
<pre>getShowLegendKey() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getShowPercent"></a><div class="element clickable method public method_getShowPercent" data-toggle="collapse" data-target=".method_getShowPercent .collapse">
<h2>Get show percentage</h2>
<pre>getShowPercent() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getShowSerName"></a><div class="element clickable method public method_getShowSerName" data-toggle="collapse" data-target=".method_getShowSerName .collapse">
<h2>Get show data series name</h2>
<pre>getShowSerName() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getShowVal"></a><div class="element clickable method public method_getShowVal" data-toggle="collapse" data-target=".method_getShowVal .collapse">
<h2>Get show value</h2>
<pre>getShowVal() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getWidth"></a><div class="element clickable method public method_getWidth" data-toggle="collapse" data-target=".method_getWidth .collapse">
<h2>Get Width</h2>
<pre>getWidth() : \number</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>\number</code></div>
</div></div>
</div>
<a id="method_getXMode"></a><div class="element clickable method public method_getXMode" data-toggle="collapse" data-target=".method_getXMode .collapse">
<h2>Get X-Mode</h2>
<pre>getXMode() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getXPosition"></a><div class="element clickable method public method_getXPosition" data-toggle="collapse" data-target=".method_getXPosition .collapse">
<h2>Get X-Position</h2>
<pre>getXPosition() : \number</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>\number</code></div>
</div></div>
</div>
<a id="method_getYMode"></a><div class="element clickable method public method_getYMode" data-toggle="collapse" data-target=".method_getYMode .collapse">
<h2>Get Y-Mode</h2>
<pre>getYMode() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getYPosition"></a><div class="element clickable method public method_getYPosition" data-toggle="collapse" data-target=".method_getYPosition .collapse">
<h2>Get Y-Position</h2>
<pre>getYPosition() : \number</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>\number</code></div>
</div></div>
</div>
<a id="method_setHeight"></a><div class="element clickable method public method_setHeight" data-toggle="collapse" data-target=".method_setHeight .collapse">
<h2>Set Height</h2>
<pre>setHeight(\Height $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>\Height</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setLayoutTarget"></a><div class="element clickable method public method_setLayoutTarget" data-toggle="collapse" data-target=".method_setLayoutTarget .collapse">
<h2>Set Layout Target</h2>
<pre>setLayoutTarget(\Layout $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>\Layout</code><p>Target $value</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setShowBubbleSize"></a><div class="element clickable method public method_setShowBubbleSize" data-toggle="collapse" data-target=".method_setShowBubbleSize .collapse">
<h2>Set show bubble size
Specifies that the bubble size should be shown in data labels.</h2>
<pre>setShowBubbleSize(boolean $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code><p>Show bubble size</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setShowCatName"></a><div class="element clickable method public method_setShowCatName" data-toggle="collapse" data-target=".method_setShowCatName .collapse">
<h2>Set show cat name
Specifies that the category name should be shown in data labels.</h2>
<pre>setShowCatName(boolean $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code><p>Show cat name</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setShowLeaderLines"></a><div class="element clickable method public method_setShowLeaderLines" data-toggle="collapse" data-target=".method_setShowLeaderLines .collapse">
<h2>Set show leader lines
Specifies that leader lines should be shown in data labels.</h2>
<pre>setShowLeaderLines(boolean $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code><p>Show leader lines</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setShowLegendKey"></a><div class="element clickable method public method_setShowLegendKey" data-toggle="collapse" data-target=".method_setShowLegendKey .collapse">
<h2>Set show legend key
Specifies that legend keys should be shown in data labels.</h2>
<pre>setShowLegendKey(boolean $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code><p>Show legend key</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setShowPercent"></a><div class="element clickable method public method_setShowPercent" data-toggle="collapse" data-target=".method_setShowPercent .collapse">
<h2>Set show percentage
Specifies that the percentage should be shown in data labels.</h2>
<pre>setShowPercent(boolean $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code><p>Show percentage</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setShowSerName"></a><div class="element clickable method public method_setShowSerName" data-toggle="collapse" data-target=".method_setShowSerName .collapse">
<h2>Set show ser name
Specifies that the series name should be shown in data labels.</h2>
<pre>setShowSerName(boolean $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code><p>Show series name</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setShowVal"></a><div class="element clickable method public method_setShowVal" data-toggle="collapse" data-target=".method_setShowVal .collapse">
<h2>Set show val
Specifies that the value should be shown in data labels.</h2>
<pre>setShowVal(boolean $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code><p>Show val</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setWidth"></a><div class="element clickable method public method_setWidth" data-toggle="collapse" data-target=".method_setWidth .collapse">
<h2>Set Width</h2>
<pre>setWidth(\Width $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>\Width</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setXMode"></a><div class="element clickable method public method_setXMode" data-toggle="collapse" data-target=".method_setXMode .collapse">
<h2>Set X-Mode</h2>
<pre>setXMode(\X-Mode $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>\X-Mode</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setXPosition"></a><div class="element clickable method public method_setXPosition" data-toggle="collapse" data-target=".method_setXPosition .collapse">
<h2>Set X-Position</h2>
<pre>setXPosition(\X-Position $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>\X-Position</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setYMode"></a><div class="element clickable method public method_setYMode" data-toggle="collapse" data-target=".method_setYMode .collapse">
<h2>Set Y-Mode</h2>
<pre>setYMode(\Y-Mode $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>\Y-Mode</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<a id="method_setYPosition"></a><div class="element clickable method public method_setYPosition" data-toggle="collapse" data-target=".method_setYPosition .collapse">
<h2>Set Y-Position</h2>
<pre>setYPosition(\Y-Position $value) : <a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>\Y-Position</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Chart_Layout.html">\PHPExcel_Chart_Layout</a></code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__height"> </a><div class="element clickable property private property__height" data-toggle="collapse" data-target=".property__height .collapse">
<h2></h2>
<pre>$_height : float</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__layoutTarget"> </a><div class="element clickable property private property__layoutTarget" data-toggle="collapse" data-target=".property__layoutTarget .collapse">
<h2></h2>
<pre>$_layoutTarget : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__showBubbleSize"> </a><div class="element clickable property private property__showBubbleSize" data-toggle="collapse" data-target=".property__showBubbleSize .collapse">
<h2></h2>
<pre>$_showBubbleSize : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__showCatName"> </a><div class="element clickable property private property__showCatName" data-toggle="collapse" data-target=".property__showCatName .collapse">
<h2></h2>
<pre>$_showCatName : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__showLeaderLines"> </a><div class="element clickable property private property__showLeaderLines" data-toggle="collapse" data-target=".property__showLeaderLines .collapse">
<h2></h2>
<pre>$_showLeaderLines : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__showLegendKey"> </a><div class="element clickable property private property__showLegendKey" data-toggle="collapse" data-target=".property__showLegendKey .collapse">
<h2></h2>
<pre>$_showLegendKey : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__showPercent"> </a><div class="element clickable property private property__showPercent" data-toggle="collapse" data-target=".property__showPercent .collapse">
<h2></h2>
<pre>$_showPercent : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__showSerName"> </a><div class="element clickable property private property__showSerName" data-toggle="collapse" data-target=".property__showSerName .collapse">
<h2></h2>
<pre>$_showSerName : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__showVal"> </a><div class="element clickable property private property__showVal" data-toggle="collapse" data-target=".property__showVal .collapse">
<h2></h2>
<pre>$_showVal : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__width"> </a><div class="element clickable property private property__width" data-toggle="collapse" data-target=".property__width .collapse">
<h2></h2>
<pre>$_width : float</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__xMode"> </a><div class="element clickable property private property__xMode" data-toggle="collapse" data-target=".property__xMode .collapse">
<h2></h2>
<pre>$_xMode : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__xPos"> </a><div class="element clickable property private property__xPos" data-toggle="collapse" data-target=".property__xPos .collapse">
<h2></h2>
<pre>$_xPos : float</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__yMode"> </a><div class="element clickable property private property__yMode" data-toggle="collapse" data-target=".property__yMode .collapse">
<h2></h2>
<pre>$_yMode : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__yPos"> </a><div class="element clickable property private property__yPos" data-toggle="collapse" data-target=".property__yPos .collapse">
<h2></h2>
<pre>$_yPos : float</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:34Z.<br></footer></div>
</div>
</body>
</html>
