2025-02-01 - 휴무일
2025-02-02 - 휴무일
<br>
 date_NOW : 20250203<br>
 date_30D : 20250504<br>
 date_2M : 20250403<br>
 date_3M : 20250503<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250403' AND '20250503' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250204<br>
 date_30D : 20250505<br>
 date_2M : 20250404<br>
 date_3M : 20250504<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250404' AND '20250504' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250205<br>
 date_30D : 20250506<br>
 date_2M : 20250405<br>
 date_3M : 20250505<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250405' AND '20250505' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250206<br>
 date_30D : 20250507<br>
 date_2M : 20250406<br>
 date_3M : 20250506<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250406' AND '20250506' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250207<br>
 date_30D : 20250508<br>
 date_2M : 20250407<br>
 date_3M : 20250507<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250407' AND '20250507' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-02-08 - 휴무일
2025-02-09 - 휴무일
<br>
 date_NOW : 20250210<br>
 date_30D : 20250511<br>
 date_2M : 20250410<br>
 date_3M : 20250510<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250410' AND '20250510' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250211<br>
 date_30D : 20250512<br>
 date_2M : 20250411<br>
 date_3M : 20250511<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250411' AND '20250511' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250212<br>
 date_30D : 20250513<br>
 date_2M : 20250412<br>
 date_3M : 20250512<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250412' AND '20250512' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250213<br>
 date_30D : 20250514<br>
 date_2M : 20250413<br>
 date_3M : 20250513<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250413' AND '20250513' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250214<br>
 date_30D : 20250515<br>
 date_2M : 20250414<br>
 date_3M : 20250514<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250414' AND '20250514' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-02-15 - 휴무일
2025-02-16 - 휴무일
<br>
 date_NOW : 20250217<br>
 date_30D : 20250518<br>
 date_2M : 20250417<br>
 date_3M : 20250517<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250417' AND '20250517' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250218<br>
 date_30D : 20250519<br>
 date_2M : 20250418<br>
 date_3M : 20250518<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250418' AND '20250518' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250219<br>
 date_30D : 20250520<br>
 date_2M : 20250419<br>
 date_3M : 20250519<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250419' AND '20250519' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250220<br>
 date_30D : 20250521<br>
 date_2M : 20250420<br>
 date_3M : 20250520<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250420' AND '20250520' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250221<br>
 date_30D : 20250522<br>
 date_2M : 20250421<br>
 date_3M : 20250521<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250421' AND '20250521' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-02-22 - 휴무일
2025-02-23 - 휴무일
<br>
 date_NOW : 20250224<br>
 date_30D : 20250525<br>
 date_2M : 20250424<br>
 date_3M : 20250524<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250424' AND '20250524' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250225<br>
 date_30D : 20250526<br>
 date_2M : 20250425<br>
 date_3M : 20250525<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250425' AND '20250525' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250226<br>
 date_30D : 20250527<br>
 date_2M : 20250426<br>
 date_3M : 20250526<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250426' AND '20250526' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250227<br>
 date_30D : 20250528<br>
 date_2M : 20250427<br>
 date_3M : 20250527<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250427' AND '20250527' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250228<br>
 date_30D : 20250529<br>
 date_2M : 20250428<br>
 date_3M : 20250528<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250428' AND '20250528' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
