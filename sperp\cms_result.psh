#!/usr/local/bin/php -q
<?
// 0 9 * * * php -q /home/<USER>/sperp/cms_result.psh
# CMS 결과 ERP에 연동
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

$arr_db = [];
$arr_query = [];
$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
if(empty($dbconn_sperp_posbank->success)) {
	echo "dbconn error [" . $db['sperp_posbank']['host'] ." - ". $db['sperp_posbank']['userid'] . "] 데이터베이스 연결 실패 \n";
}

$dbconn_cms1 = new DBController($db['cms1']);
if(empty($dbconn_cms1->success)) {
	echo "dbconn error [" . $db['cms1']['host'] ." - ". $db['cms1']['userid'] . "] 데이터베이스 연결 실패 \n";
}else{
	$arr_db['posbank'] = $dbconn_cms1;
}

$dbconn_cms3 = new DBController($db['cms3']);
if(empty($dbconn_cms3->success)) {
	echo "dbconn error [" . $db['cms3']['host'] ." - ". $db['cms3']['userid'] . "] 데이터베이스 연결 실패 \n";
}else{
	$arr_db['posbank4'] = $dbconn_cms3;
}
/**********************************************************/


echo date("Y-m-d H:i:s")." - CMS RESULT 연동시작 \n";
$n = 0;
foreach($arr_db as $cms_id => $cms_conn) {

	$SQL = "select max(EA22_INX) from CMS_RESULT_LOG where CMS_ID='".$cms_id."'";
	$EA22_INX = $dbconn_sperp_posbank->query_one($SQL);

	$SQL = "select ea22_inx,mem_inx,ext_inx,file_name,bank_code,amount,div_amount,result,result_code,result_msg,send_stat,in_time 
				from file_ea22 
				where ea22_inx>'".$EA22_INX."' 
				order by ea22_inx";
	$arrRow = $cms_conn->query_rows($SQL);
	if($arrRow){
		foreach($arrRow as $key => $row) {
			$arr_query[$n] = "insert into CMS_RESULT_LOG ";
			$arr_query[$n] .= "(CMS_ID,EA22_INX,MEM_INX,EXT_INX,FILE_NAME,BANK_CODE,AMOUNT,DIV_AMOUNT,RESULT,RESULT_CODE,RESULT_MSG,SEND_STAT,IN_TIME) ";
			$arr_query[$n] .= "values ";
			$arr_query[$n] .= "('".$cms_id."','".$row['ea22_inx']."','".$row['mem_inx']."','".$row['ext_inx']."','".$row['file_name']."','".$row['bank_code']."','".$row['amount']."','".$row['div_amount']."','".$row['result']."','".$row['result_code']."','".$row['result_msg']."','".$row['send_stat']."',TO_DATE('".$row['in_time']."','YYYY-MM-DD HH24:MI:SS')) ";
			$n++;
		}
	}
}

if($arr_query){
	$rs = $dbconn_sperp_posbank->iud_query($arr_query);
	if($rs['state']){
		$msg = number_format($rs['count']) . "건 등록";
	}else{
		$msg = "전산장애!! " . $rs['error'];
	}
}else{
	$msg = "없음";
}
echo date("Y-m-d H:i:s") . " - " . $msg . "\n";


## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(21600, "CMS처리결과 ERP에 연동");

echo "================================================== \n";

?>
