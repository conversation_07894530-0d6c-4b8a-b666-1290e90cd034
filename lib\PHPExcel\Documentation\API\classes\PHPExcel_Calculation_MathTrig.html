<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Calculation_MathTrig</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_ATAN2" title="ATAN2 :: ATAN2"><span class="description">ATAN2</span><pre>ATAN2()</pre></a></li>
<li class="method public "><a href="#method_CEILING" title="CEILING :: CEILING"><span class="description">CEILING</span><pre>CEILING()</pre></a></li>
<li class="method public "><a href="#method_COMBIN" title="COMBIN :: COMBIN"><span class="description">COMBIN</span><pre>COMBIN()</pre></a></li>
<li class="method public "><a href="#method_EVEN" title="EVEN :: EVEN"><span class="description">EVEN</span><pre>EVEN()</pre></a></li>
<li class="method public "><a href="#method_FACT" title="FACT :: FACT"><span class="description">FACT</span><pre>FACT()</pre></a></li>
<li class="method public "><a href="#method_FACTDOUBLE" title="FACTDOUBLE :: FACTDOUBLE"><span class="description">FACTDOUBLE</span><pre>FACTDOUBLE()</pre></a></li>
<li class="method public "><a href="#method_FLOOR" title="FLOOR :: FLOOR"><span class="description">FLOOR</span><pre>FLOOR()</pre></a></li>
<li class="method public "><a href="#method_GCD" title="GCD :: GCD"><span class="description">GCD</span><pre>GCD()</pre></a></li>
<li class="method public "><a href="#method_INT" title="INT :: INT"><span class="description">INT</span><pre>INT()</pre></a></li>
<li class="method public "><a href="#method_LCM" title="LCM :: LCM"><span class="description">LCM</span><pre>LCM()</pre></a></li>
<li class="method public "><a href="#method_LOG_BASE" title="LOG_BASE :: LOG_BASE"><span class="description">LOG_BASE</span><pre>LOG_BASE()</pre></a></li>
<li class="method public "><a href="#method_MDETERM" title="MDETERM :: MDETERM"><span class="description">MDETERM</span><pre>MDETERM()</pre></a></li>
<li class="method public "><a href="#method_MINVERSE" title="MINVERSE :: MINVERSE"><span class="description">MINVERSE</span><pre>MINVERSE()</pre></a></li>
<li class="method public "><a href="#method_MMULT" title="MMULT :: MMULT"><span class="description">MMULT</span><pre>MMULT()</pre></a></li>
<li class="method public "><a href="#method_MOD" title="MOD :: MOD"><span class="description">MOD</span><pre>MOD()</pre></a></li>
<li class="method public "><a href="#method_MROUND" title="MROUND :: MROUND"><span class="description">MROUND</span><pre>MROUND()</pre></a></li>
<li class="method public "><a href="#method_MULTINOMIAL" title="MULTINOMIAL :: MULTINOMIAL"><span class="description">MULTINOMIAL</span><pre>MULTINOMIAL()</pre></a></li>
<li class="method public "><a href="#method_ODD" title="ODD :: ODD"><span class="description">ODD</span><pre>ODD()</pre></a></li>
<li class="method public "><a href="#method_POWER" title="POWER :: POWER"><span class="description">POWER</span><pre>POWER()</pre></a></li>
<li class="method public "><a href="#method_PRODUCT" title="PRODUCT :: PRODUCT"><span class="description">PRODUCT</span><pre>PRODUCT()</pre></a></li>
<li class="method public "><a href="#method_QUOTIENT" title="QUOTIENT :: QUOTIENT"><span class="description">QUOTIENT</span><pre>QUOTIENT()</pre></a></li>
<li class="method public "><a href="#method_RAND" title="RAND :: RAND"><span class="description">RAND</span><pre>RAND()</pre></a></li>
<li class="method public "><a href="#method_ROMAN" title="ROMAN :: "><span class="description">ROMAN()
        </span><pre>ROMAN()</pre></a></li>
<li class="method public "><a href="#method_ROUNDDOWN" title="ROUNDDOWN :: ROUNDDOWN"><span class="description">ROUNDDOWN</span><pre>ROUNDDOWN()</pre></a></li>
<li class="method public "><a href="#method_ROUNDUP" title="ROUNDUP :: ROUNDUP"><span class="description">ROUNDUP</span><pre>ROUNDUP()</pre></a></li>
<li class="method public "><a href="#method_SERIESSUM" title="SERIESSUM :: SERIESSUM"><span class="description">SERIESSUM</span><pre>SERIESSUM()</pre></a></li>
<li class="method public "><a href="#method_SIGN" title="SIGN :: SIGN"><span class="description">SIGN</span><pre>SIGN()</pre></a></li>
<li class="method public "><a href="#method_SQRTPI" title="SQRTPI :: SQRTPI"><span class="description">SQRTPI</span><pre>SQRTPI()</pre></a></li>
<li class="method public "><a href="#method_SUBTOTAL" title="SUBTOTAL :: SUBTOTAL"><span class="description">SUBTOTAL</span><pre>SUBTOTAL()</pre></a></li>
<li class="method public "><a href="#method_SUM" title="SUM :: SUM"><span class="description">SUM</span><pre>SUM()</pre></a></li>
<li class="method public "><a href="#method_SUMIF" title="SUMIF :: SUMIF"><span class="description">SUMIF</span><pre>SUMIF()</pre></a></li>
<li class="method public "><a href="#method_SUMPRODUCT" title="SUMPRODUCT :: SUMPRODUCT"><span class="description">SUMPRODUCT</span><pre>SUMPRODUCT()</pre></a></li>
<li class="method public "><a href="#method_SUMSQ" title="SUMSQ :: SUMSQ"><span class="description">SUMSQ</span><pre>SUMSQ()</pre></a></li>
<li class="method public "><a href="#method_SUMX2MY2" title="SUMX2MY2 :: SUMX2MY2"><span class="description">SUMX2MY2</span><pre>SUMX2MY2()</pre></a></li>
<li class="method public "><a href="#method_SUMX2PY2" title="SUMX2PY2 :: SUMX2PY2"><span class="description">SUMX2PY2</span><pre>SUMX2PY2()</pre></a></li>
<li class="method public "><a href="#method_SUMXMY2" title="SUMXMY2 :: SUMXMY2"><span class="description">SUMXMY2</span><pre>SUMXMY2()</pre></a></li>
<li class="method public "><a href="#method_TRUNC" title="TRUNC :: TRUNC"><span class="description">TRUNC</span><pre>TRUNC()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="method private "><a href="#method__factors" title="_factors :: "><span class="description">_factors()
        </span><pre>_factors()</pre></a></li>
<li class="method private "><a href="#method__romanCut" title="_romanCut :: "><span class="description">_romanCut()
        </span><pre>_romanCut()</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Calculation_MathTrig"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Calculation_MathTrig.html">PHPExcel_Calculation_MathTrig</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Calculation_MathTrig</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Calculation.html">PHPExcel_Calculation</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_ATAN2"></a><div class="element clickable method public method_ATAN2" data-toggle="collapse" data-target=".method_ATAN2 .collapse">
<h2>ATAN2</h2>
<pre>ATAN2(float $xCoordinate, float $yCoordinate) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>This function calculates the arc tangent of the two variables x and y. It is similar to
    calculating the arc tangent of y ÷ x, except that the signs of both arguments are used
    to determine the quadrant of the result.
The arctangent is the angle from the x-axis to a line containing the origin (0, 0) and a
    point with coordinates (xCoordinate, yCoordinate). The angle is given in radians between
    -pi and pi, excluding -pi.</p>

<p>Note that the Excel ATAN2() function accepts its arguments in the reverse order to the standard
    PHP atan2() function, so we need to reverse them here before calling the PHP atan() function.</p>

<p>Excel Function:
    ATAN2(xCoordinate,yCoordinate)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$xCoordinate</h4>
<code>float</code><p>The x-coordinate of the point.</p>
</div>
<div class="subelement argument">
<h4>$yCoordinate</h4>
<code>float</code><p>The y-coordinate of the point.</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>The inverse tangent of the specified x- and y-coordinates.</div>
</div></div>
</div>
<a id="method_CEILING"></a><div class="element clickable method public method_CEILING" data-toggle="collapse" data-target=".method_CEILING .collapse">
<h2>CEILING</h2>
<pre>CEILING(float $number, float $significance) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns number rounded up, away from zero, to the nearest multiple of significance.
    For example, if you want to avoid using pennies in your prices and your product is
    priced at $4.42, use the formula =CEILING(4.42,0.05) to round prices up to the
    nearest nickel.</p>

<p>Excel Function:
    CEILING(number[,significance])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$number</h4>
<code>float</code><p>The number you want to round.</p></div>
<div class="subelement argument">
<h4>$significance</h4>
<code>float</code><p>The multiple to which you want to round.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>Rounded Number</div>
</div></div>
</div>
<a id="method_COMBIN"></a><div class="element clickable method public method_COMBIN" data-toggle="collapse" data-target=".method_COMBIN .collapse">
<h2>COMBIN</h2>
<pre>COMBIN(int $numObjs, int $numInSet) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the number of combinations for a given number of items. Use COMBIN to
    determine the total possible number of groups for a given number of items.</p>

<p>Excel Function:
    COMBIN(numObjs,numInSet)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$numObjs</h4>
<code>int</code><p>Number of different objects</p></div>
<div class="subelement argument">
<h4>$numInSet</h4>
<code>int</code><p>Number of objects in each combination</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Number of combinations</div>
</div></div>
</div>
<a id="method_EVEN"></a><div class="element clickable method public method_EVEN" data-toggle="collapse" data-target=".method_EVEN .collapse">
<h2>EVEN</h2>
<pre>EVEN(float $number) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns number rounded up to the nearest even integer.
You can use this function for processing items that come in twos. For example,
    a packing crate accepts rows of one or two items. The crate is full when
    the number of items, rounded up to the nearest two, matches the crate's
    capacity.</p>

<p>Excel Function:
    EVEN(number)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$number</h4>
<code>float</code><p>Number to round</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Rounded Number</div>
</div></div>
</div>
<a id="method_FACT"></a><div class="element clickable method public method_FACT" data-toggle="collapse" data-target=".method_FACT .collapse">
<h2>FACT</h2>
<pre>FACT(float $factVal) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the factorial of a number.
The factorial of a number is equal to 1<em>2</em>3<em>...</em> number.</p>

<p>Excel Function:
    FACT(factVal)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$factVal</h4>
<code>float</code><p>Factorial Value</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Factorial</div>
</div></div>
</div>
<a id="method_FACTDOUBLE"></a><div class="element clickable method public method_FACTDOUBLE" data-toggle="collapse" data-target=".method_FACTDOUBLE .collapse">
<h2>FACTDOUBLE</h2>
<pre>FACTDOUBLE(float $factVal) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the double factorial of a number.</p>

<p>Excel Function:
    FACTDOUBLE(factVal)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$factVal</h4>
<code>float</code><p>Factorial Value</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Double Factorial</div>
</div></div>
</div>
<a id="method_FLOOR"></a><div class="element clickable method public method_FLOOR" data-toggle="collapse" data-target=".method_FLOOR .collapse">
<h2>FLOOR</h2>
<pre>FLOOR(float $number, float $significance) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Rounds number down, toward zero, to the nearest multiple of significance.</p>

<p>Excel Function:
    FLOOR(number[,significance])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$number</h4>
<code>float</code><p>Number to round</p></div>
<div class="subelement argument">
<h4>$significance</h4>
<code>float</code><p>Significance</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>Rounded Number</div>
</div></div>
</div>
<a id="method_GCD"></a><div class="element clickable method public method_GCD" data-toggle="collapse" data-target=".method_GCD .collapse">
<h2>GCD</h2>
<pre>GCD() : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the greatest common divisor of a series of numbers.
The greatest common divisor is the largest integer that divides both
    number1 and number2 without a remainder.</p>

<p>Excel Function:
    GCD(number1[,number2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>Greatest Common Divisor</div>
</div></div>
</div>
<a id="method_INT"></a><div class="element clickable method public method_INT" data-toggle="collapse" data-target=".method_INT .collapse">
<h2>INT</h2>
<pre>INT(float $number) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Casts a floating point value to an integer</p>

<p>Excel Function:
    INT(number)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$number</h4>
<code>float</code><p>Number to cast to an integer</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>Integer value</div>
</div></div>
</div>
<a id="method_LCM"></a><div class="element clickable method public method_LCM" data-toggle="collapse" data-target=".method_LCM .collapse">
<h2>LCM</h2>
<pre>LCM() : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the lowest common multiplier of a series of numbers
The least common multiple is the smallest positive integer that is a multiple
of all integer arguments number1, number2, and so on. Use LCM to add fractions
with different denominators.</p>

<p>Excel Function:
    LCM(number1[,number2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Lowest Common Multiplier</div>
</div></div>
</div>
<a id="method_LOG_BASE"></a><div class="element clickable method public method_LOG_BASE" data-toggle="collapse" data-target=".method_LOG_BASE .collapse">
<h2>LOG_BASE</h2>
<pre>LOG_BASE(float $number, float $base) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the logarithm of a number to a specified base. The default base is 10.</p>

<p>Excel Function:
    LOG(number[,base])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$number</h4>
<code>float</code><p>The positive real number for which you want the logarithm</p></div>
<div class="subelement argument">
<h4>$base</h4>
<code>float</code><p>The base of the logarithm. If base is omitted, it is assumed to be 10.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_MDETERM"></a><div class="element clickable method public method_MDETERM" data-toggle="collapse" data-target=".method_MDETERM .collapse">
<h2>MDETERM</h2>
<pre>MDETERM(array $matrixValues) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the matrix determinant of an array.</p>

<p>Excel Function:
    MDETERM(array)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$matrixValues</h4>
<code>array</code><p>A matrix of values</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_MINVERSE"></a><div class="element clickable method public method_MINVERSE" data-toggle="collapse" data-target=".method_MINVERSE .collapse">
<h2>MINVERSE</h2>
<pre>MINVERSE(array $matrixValues) : array</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the inverse matrix for the matrix stored in an array.</p>

<p>Excel Function:
    MINVERSE(array)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$matrixValues</h4>
<code>array</code><p>A matrix of values</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>array</code></div>
</div></div>
</div>
<a id="method_MMULT"></a><div class="element clickable method public method_MMULT" data-toggle="collapse" data-target=".method_MMULT .collapse">
<h2>MMULT</h2>
<pre>MMULT(array $matrixData1, array $matrixData2) : array</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$matrixData1</h4>
<code>array</code><p>A matrix of values</p></div>
<div class="subelement argument">
<h4>$matrixData2</h4>
<code>array</code><p>A matrix of values</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>array</code></div>
</div></div>
</div>
<a id="method_MOD"></a><div class="element clickable method public method_MOD" data-toggle="collapse" data-target=".method_MOD .collapse">
<h2>MOD</h2>
<pre>MOD(int $a, int $b) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$a</h4>
<code>int</code><p>Dividend</p></div>
<div class="subelement argument">
<h4>$b</h4>
<code>int</code><p>Divisor</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Remainder</div>
</div></div>
</div>
<a id="method_MROUND"></a><div class="element clickable method public method_MROUND" data-toggle="collapse" data-target=".method_MROUND .collapse">
<h2>MROUND</h2>
<pre>MROUND(float $number, int $multiple) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Rounds a number to the nearest multiple of a specified value</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$number</h4>
<code>float</code><p>Number to round</p></div>
<div class="subelement argument">
<h4>$multiple</h4>
<code>int</code><p>Multiple to which you want to round $number</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>Rounded Number</div>
</div></div>
</div>
<a id="method_MULTINOMIAL"></a><div class="element clickable method public method_MULTINOMIAL" data-toggle="collapse" data-target=".method_MULTINOMIAL .collapse">
<h2>MULTINOMIAL</h2>
<pre>MULTINOMIAL() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the ratio of the factorial of a sum of values to the product of factorials.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_ODD"></a><div class="element clickable method public method_ODD" data-toggle="collapse" data-target=".method_ODD .collapse">
<h2>ODD</h2>
<pre>ODD(float $number) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns number rounded up to the nearest odd integer.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$number</h4>
<code>float</code><p>Number to round</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Rounded Number</div>
</div></div>
</div>
<a id="method_POWER"></a><div class="element clickable method public method_POWER" data-toggle="collapse" data-target=".method_POWER .collapse">
<h2>POWER</h2>
<pre>POWER(float $x, float $y) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Computes x raised to the power y.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$x</h4>
<code>float</code>
</div>
<div class="subelement argument">
<h4>$y</h4>
<code>float</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_PRODUCT"></a><div class="element clickable method public method_PRODUCT" data-toggle="collapse" data-target=".method_PRODUCT .collapse">
<h2>PRODUCT</h2>
<pre>PRODUCT() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>PRODUCT returns the product of all the values and cells referenced in the argument list.</p>

<p>Excel Function:
    PRODUCT(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_QUOTIENT"></a><div class="element clickable method public method_QUOTIENT" data-toggle="collapse" data-target=".method_QUOTIENT .collapse">
<h2>QUOTIENT</h2>
<pre>QUOTIENT() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>QUOTIENT function returns the integer portion of a division. Numerator is the divided number
    and denominator is the divisor.</p>

<p>Excel Function:
    QUOTIENT(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_RAND"></a><div class="element clickable method public method_RAND" data-toggle="collapse" data-target=".method_RAND .collapse">
<h2>RAND</h2>
<pre>RAND(int $min, int $max) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$min</h4>
<code>int</code><p>Minimal value</p></div>
<div class="subelement argument">
<h4>$max</h4>
<code>int</code><p>Maximal value</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Random number</div>
</div></div>
</div>
<a id="method_ROMAN"></a><div class="element clickable method public method_ROMAN" data-toggle="collapse" data-target=".method_ROMAN .collapse">
<h2>ROMAN()
        </h2>
<pre>ROMAN($aValue, $style) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$aValue</h4></div>
<div class="subelement argument"><h4>$style</h4></div>
</div></div>
</div>
<a id="method_ROUNDDOWN"></a><div class="element clickable method public method_ROUNDDOWN" data-toggle="collapse" data-target=".method_ROUNDDOWN .collapse">
<h2>ROUNDDOWN</h2>
<pre>ROUNDDOWN(float $number, int $digits) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Rounds a number down to a specified number of decimal places</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$number</h4>
<code>float</code><p>Number to round</p></div>
<div class="subelement argument">
<h4>$digits</h4>
<code>int</code><p>Number of digits to which you want to round $number</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>Rounded Number</div>
</div></div>
</div>
<a id="method_ROUNDUP"></a><div class="element clickable method public method_ROUNDUP" data-toggle="collapse" data-target=".method_ROUNDUP .collapse">
<h2>ROUNDUP</h2>
<pre>ROUNDUP(float $number, int $digits) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Rounds a number up to a specified number of decimal places</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$number</h4>
<code>float</code><p>Number to round</p></div>
<div class="subelement argument">
<h4>$digits</h4>
<code>int</code><p>Number of digits to which you want to round $number</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>Rounded Number</div>
</div></div>
</div>
<a id="method_SERIESSUM"></a><div class="element clickable method public method_SERIESSUM" data-toggle="collapse" data-target=".method_SERIESSUM .collapse">
<h2>SERIESSUM</h2>
<pre>SERIESSUM() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the sum of a power series</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_SIGN"></a><div class="element clickable method public method_SIGN" data-toggle="collapse" data-target=".method_SIGN .collapse">
<h2>SIGN</h2>
<pre>SIGN(float $number) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Determines the sign of a number. Returns 1 if the number is positive, zero (0)
    if the number is 0, and -1 if the number is negative.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$number</h4>
<code>float</code><p>Number to round</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>sign value</div>
</div></div>
</div>
<a id="method_SQRTPI"></a><div class="element clickable method public method_SQRTPI" data-toggle="collapse" data-target=".method_SQRTPI .collapse">
<h2>SQRTPI</h2>
<pre>SQRTPI(float $number) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the square root of (number * pi).</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$number</h4>
<code>float</code><p>Number</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>Square Root of Number * Pi</div>
</div></div>
</div>
<a id="method_SUBTOTAL"></a><div class="element clickable method public method_SUBTOTAL" data-toggle="collapse" data-target=".method_SUBTOTAL .collapse">
<h2>SUBTOTAL</h2>
<pre>SUBTOTAL() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns a subtotal in a list or database.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_SUM"></a><div class="element clickable method public method_SUM" data-toggle="collapse" data-target=".method_SUM .collapse">
<h2>SUM</h2>
<pre>SUM() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>SUM computes the sum of all the values and cells referenced in the argument list.</p>

<p>Excel Function:
    SUM(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_SUMIF"></a><div class="element clickable method public method_SUMIF" data-toggle="collapse" data-target=".method_SUMIF .collapse">
<h2>SUMIF</h2>
<pre>SUMIF($aArgs, string $condition, $sumArgs) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Counts the number of cells that contain numbers within the list of arguments</p>

<p>Excel Function:
    SUMIF(value1[,value2[, ...]],condition)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$aArgs</h4></div>
<div class="subelement argument">
<h4>$condition</h4>
<code>string</code><p>The criteria that defines which cells will be summed.</p></div>
<div class="subelement argument"><h4>$sumArgs</h4></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_SUMPRODUCT"></a><div class="element clickable method public method_SUMPRODUCT" data-toggle="collapse" data-target=".method_SUMPRODUCT .collapse">
<h2>SUMPRODUCT</h2>
<pre>SUMPRODUCT() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Excel Function:
    SUMPRODUCT(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_SUMSQ"></a><div class="element clickable method public method_SUMSQ" data-toggle="collapse" data-target=".method_SUMSQ .collapse">
<h2>SUMSQ</h2>
<pre>SUMSQ() : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>SUMSQ returns the sum of the squares of the arguments</p>

<p>Excel Function:
    SUMSQ(value1[,value2[, ...]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Mathematical and Trigonometric Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_SUMX2MY2"></a><div class="element clickable method public method_SUMX2MY2" data-toggle="collapse" data-target=".method_SUMX2MY2 .collapse">
<h2>SUMX2MY2</h2>
<pre>SUMX2MY2(mixed[] $matrixData1, mixed[] $matrixData2) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$matrixData1</h4>
<code>mixed[]</code><p>Matrix #1</p></div>
<div class="subelement argument">
<h4>$matrixData2</h4>
<code>mixed[]</code><p>Matrix #2</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_SUMX2PY2"></a><div class="element clickable method public method_SUMX2PY2" data-toggle="collapse" data-target=".method_SUMX2PY2 .collapse">
<h2>SUMX2PY2</h2>
<pre>SUMX2PY2(mixed[] $matrixData1, mixed[] $matrixData2) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$matrixData1</h4>
<code>mixed[]</code><p>Matrix #1</p></div>
<div class="subelement argument">
<h4>$matrixData2</h4>
<code>mixed[]</code><p>Matrix #2</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_SUMXMY2"></a><div class="element clickable method public method_SUMXMY2" data-toggle="collapse" data-target=".method_SUMXMY2 .collapse">
<h2>SUMXMY2</h2>
<pre>SUMXMY2(mixed[] $matrixData1, mixed[] $matrixData2) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$matrixData1</h4>
<code>mixed[]</code><p>Matrix #1</p></div>
<div class="subelement argument">
<h4>$matrixData2</h4>
<code>mixed[]</code><p>Matrix #2</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_TRUNC"></a><div class="element clickable method public method_TRUNC" data-toggle="collapse" data-target=".method_TRUNC .collapse">
<h2>TRUNC</h2>
<pre>TRUNC(float $value, int $digits) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Truncates value to the number of fractional digits by number_digits.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>float</code>
</div>
<div class="subelement argument">
<h4>$digits</h4>
<code>int</code>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>Truncated value</div>
</div></div>
</div>
<a id="method__factors"></a><div class="element clickable method private method__factors" data-toggle="collapse" data-target=".method__factors .collapse">
<h2>_factors()
        </h2>
<pre>_factors($value) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$value</h4></div>
</div></div>
</div>
<a id="method__romanCut"></a><div class="element clickable method private method__romanCut" data-toggle="collapse" data-target=".method__romanCut .collapse">
<h2>_romanCut()
        </h2>
<pre>_romanCut($num, $n) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$num</h4></div>
<div class="subelement argument"><h4>$n</h4></div>
</div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:34Z.<br></footer></div>
</div>
</body>
</html>
