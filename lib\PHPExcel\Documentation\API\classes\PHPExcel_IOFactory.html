<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_IOFactory</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_addSearchLocation" title="addSearchLocation :: Add search location"><span class="description">Add search location</span><pre>addSearchLocation()</pre></a></li>
<li class="method public "><a href="#method_createReader" title="createReader :: Create PHPExcel_Reader_IReader"><span class="description">Create PHPExcel_Reader_IReader</span><pre>createReader()</pre></a></li>
<li class="method public "><a href="#method_createReaderForFile" title="createReaderForFile :: Create PHPExcel_Reader_IReader for file using automatic PHPExcel_Reader_IReader resolution"><span class="description">Create PHPExcel_Reader_IReader for file using automatic PHPExcel_Reader_IReader resolution</span><pre>createReaderForFile()</pre></a></li>
<li class="method public "><a href="#method_createWriter" title="createWriter :: Create PHPExcel_Writer_IWriter"><span class="description">Create PHPExcel_Writer_IWriter</span><pre>createWriter()</pre></a></li>
<li class="method public "><a href="#method_getSearchLocations" title="getSearchLocations :: Get search locations"><span class="description">Get search locations</span><pre>getSearchLocations()</pre></a></li>
<li class="method public "><a href="#method_identify" title="identify :: Identify file type using automatic PHPExcel_Reader_IReader resolution"><span class="description">Identify file type using automatic PHPExcel_Reader_IReader resolution</span><pre>identify()</pre></a></li>
<li class="method public "><a href="#method_load" title="load :: Loads PHPExcel from file using automatic PHPExcel_Reader_IReader resolution"><span class="description">Loads PHPExcel from file using automatic PHPExcel_Reader_IReader resolution</span><pre>load()</pre></a></li>
<li class="method public "><a href="#method_setSearchLocations" title="setSearchLocations :: Set search locations"><span class="description">Set search locations</span><pre>setSearchLocations()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul><li class="method private "><a href="#method___construct" title="__construct :: Private constructor for PHPExcel_IOFactory"><span class="description">Private constructor for PHPExcel_IOFactory</span><pre>__construct()</pre></a></li></ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__autoResolveClasses" title="$_autoResolveClasses :: Autoresolve classes"><span class="description"></span><pre>$_autoResolveClasses</pre></a></li>
<li class="property private "><a href="#property__searchLocations" title="$_searchLocations :: Search locations"><span class="description"></span><pre>$_searchLocations</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_IOFactory"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_IOFactory.html">PHPExcel_IOFactory</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_IOFactory</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.html">PHPExcel</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_addSearchLocation"></a><div class="element clickable method public method_addSearchLocation" data-toggle="collapse" data-target=".method_addSearchLocation .collapse">
<h2>Add search location</h2>
<pre>addSearchLocation(string $type, string $location, string $classname) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>static</th>
<td></td>
</tr>
<tr>
<th>access</th>
<td>public</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$type</h4>
<code>string</code><p>Example: IWriter</p></div>
<div class="subelement argument">
<h4>$location</h4>
<code>string</code><p>Example: PHPExcel/Writer/{0}.php</p>
</div>
<div class="subelement argument">
<h4>$classname</h4>
<code>string</code><p>Example: PHPExcel_Writer_{0}</p>
</div>
</div></div>
</div>
<a id="method_createReader"></a><div class="element clickable method public method_createReader" data-toggle="collapse" data-target=".method_createReader .collapse">
<h2>Create PHPExcel_Reader_IReader</h2>
<pre>createReader(string $readerType) : <a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>static</th>
<td></td>
</tr>
<tr>
<th>access</th>
<td>public</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$readerType</h4>
<code>string</code><p>Example: Excel2007</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Reader_Exception.html">\PHPExcel_Reader_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></code></div>
</div></div>
</div>
<a id="method_createReaderForFile"></a><div class="element clickable method public method_createReaderForFile" data-toggle="collapse" data-target=".method_createReaderForFile .collapse">
<h2>Create PHPExcel_Reader_IReader for file using automatic PHPExcel_Reader_IReader resolution</h2>
<pre>createReaderForFile(string $pFilename) : <a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>static</th>
<td></td>
</tr>
<tr>
<th>access</th>
<td>public</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pFilename</h4>
<code>string</code><p>The name of the spreadsheet file</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Reader_Exception.html">\PHPExcel_Reader_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Reader_IReader.html">\PHPExcel_Reader_IReader</a></code></div>
</div></div>
</div>
<a id="method_createWriter"></a><div class="element clickable method public method_createWriter" data-toggle="collapse" data-target=".method_createWriter .collapse">
<h2>Create PHPExcel_Writer_IWriter</h2>
<pre>createWriter(\PHPExcel $phpExcel, string $writerType) : <a href="../classes/PHPExcel_Writer_IWriter.html">\PHPExcel_Writer_IWriter</a></pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>static</th>
<td></td>
</tr>
<tr>
<th>access</th>
<td>public</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$phpExcel</h4>
<code><a href="../classes/PHPExcel.html">\PHPExcel</a></code>
</div>
<div class="subelement argument">
<h4>$writerType</h4>
<code>string</code><p>Example: Excel2007</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Reader_Exception.html">\PHPExcel_Reader_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Writer_IWriter.html">\PHPExcel_Writer_IWriter</a></code></div>
</div></div>
</div>
<a id="method_getSearchLocations"></a><div class="element clickable method public method_getSearchLocations" data-toggle="collapse" data-target=".method_getSearchLocations .collapse">
<h2>Get search locations</h2>
<pre>getSearchLocations() : array</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>static</th>
<td></td>
</tr>
<tr>
<th>access</th>
<td>public</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response"><code>array</code></div>
</div></div>
</div>
<a id="method_identify"></a><div class="element clickable method public method_identify" data-toggle="collapse" data-target=".method_identify .collapse">
<h2>Identify file type using automatic PHPExcel_Reader_IReader resolution</h2>
<pre>identify(string $pFilename) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>static</th>
<td></td>
</tr>
<tr>
<th>access</th>
<td>public</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pFilename</h4>
<code>string</code><p>The name of the spreadsheet file to identify</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Reader_Exception.html">\PHPExcel_Reader_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_load"></a><div class="element clickable method public method_load" data-toggle="collapse" data-target=".method_load .collapse">
<h2>Loads PHPExcel from file using automatic PHPExcel_Reader_IReader resolution</h2>
<pre>load(string $pFilename) : <a href="../classes/PHPExcel.html">\PHPExcel</a></pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>static</th>
<td></td>
</tr>
<tr>
<th>access</th>
<td>public</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pFilename</h4>
<code>string</code><p>The name of the spreadsheet file</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Reader_Exception.html">\PHPExcel_Reader_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel.html">\PHPExcel</a></code></div>
</div></div>
</div>
<a id="method_setSearchLocations"></a><div class="element clickable method public method_setSearchLocations" data-toggle="collapse" data-target=".method_setSearchLocations .collapse">
<h2>Set search locations</h2>
<pre>setSearchLocations(array $value) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>static</th>
<td></td>
</tr>
<tr>
<th>access</th>
<td>public</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>array</code>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Reader_Exception.html">\PHPExcel_Reader_Exception</a></code></th>
<td></td>
</tr></table>
</div></div>
</div>
<a id="method___construct"></a><div class="element clickable method private method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Private constructor for PHPExcel_IOFactory</h2>
<pre>__construct() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__autoResolveClasses"> </a><div class="element clickable property private property__autoResolveClasses" data-toggle="collapse" data-target=".property__autoResolveClasses .collapse">
<h2></h2>
<pre>$_autoResolveClasses : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>private</td>
</tr>
<tr>
<th>static</th>
<td></td>
</tr>
</table>
</div></div>
</div>
<a id="property__searchLocations"> </a><div class="element clickable property private property__searchLocations" data-toggle="collapse" data-target=".property__searchLocations .collapse">
<h2></h2>
<pre>$_searchLocations : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>private</td>
</tr>
<tr>
<th>static</th>
<td></td>
</tr>
</table>
</div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:35Z.<br></footer></div>
</div>
</body>
</html>
