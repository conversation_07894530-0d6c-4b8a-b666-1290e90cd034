<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Calculation_Logical</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list"><li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_FALSE" title="FALSE :: FALSE"><span class="description">FALSE</span><pre>FALSE()</pre></a></li>
<li class="method public "><a href="#method_IFERROR" title="IFERROR :: IFERROR"><span class="description">IFERROR</span><pre>IFERROR()</pre></a></li>
<li class="method public "><a href="#method_LOGICAL_AND" title="LOGICAL_AND :: LOGICAL_AND"><span class="description">LOGICAL_AND</span><pre>LOGICAL_AND()</pre></a></li>
<li class="method public "><a href="#method_LOGICAL_OR" title="LOGICAL_OR :: LOGICAL_OR"><span class="description">LOGICAL_OR</span><pre>LOGICAL_OR()</pre></a></li>
<li class="method public "><a href="#method_NOT" title="NOT :: NOT"><span class="description">NOT</span><pre>NOT()</pre></a></li>
<li class="method public "><a href="#method_STATEMENT_IF" title="STATEMENT_IF :: STATEMENT_IF"><span class="description">STATEMENT_IF</span><pre>STATEMENT_IF()</pre></a></li>
<li class="method public "><a href="#method_TRUE" title="TRUE :: TRUE"><span class="description">TRUE</span><pre>TRUE()</pre></a></li>
</ul>
</li></ul>
</div>
<div class="span8">
<a id="\PHPExcel_Calculation_Logical"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Calculation_Logical.html">PHPExcel_Calculation_Logical</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Calculation_Logical</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Calculation.html">PHPExcel_Calculation</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_FALSE"></a><div class="element clickable method public method_FALSE" data-toggle="collapse" data-target=".method_FALSE .collapse">
<h2>FALSE</h2>
<pre>FALSE() : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the boolean FALSE.</p>

<p>Excel Function:
    =FALSE()</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Logical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>boolean</code>False</div>
</div></div>
</div>
<a id="method_IFERROR"></a><div class="element clickable method public method_IFERROR" data-toggle="collapse" data-target=".method_IFERROR .collapse">
<h2>IFERROR</h2>
<pre>IFERROR(mixed $testValue, mixed $errorpart) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Excel Function:
    =IFERROR(testValue,errorpart)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Logical Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$testValue</h4>
<code>mixed</code><p>Value to check, is also the value returned when no error</p></div>
<div class="subelement argument">
<h4>$errorpart</h4>
<code>mixed</code><p>Value to return when testValue is an error condition</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>The value of errorpart or testValue determined by error condition</div>
</div></div>
</div>
<a id="method_LOGICAL_AND"></a><div class="element clickable method public method_LOGICAL_AND" data-toggle="collapse" data-target=".method_LOGICAL_AND .collapse">
<h2>LOGICAL_AND</h2>
<pre>LOGICAL_AND() : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns boolean TRUE if all its arguments are TRUE; returns FALSE if one or more argument is FALSE.</p>

<p>Excel Function:
    =AND(logical1[,logical2[, ...]])</p>

<pre><code>The arguments must evaluate to logical values such as TRUE or FALSE, or the arguments must be arrays
    or references that contain logical values.

Boolean arguments are treated as True or False as appropriate
Integer or floating point arguments are treated as True, except for 0 or 0.0 which are False
If any argument value is a string, or a Null, the function returns a #VALUE! error, unless the string holds
    the value TRUE or FALSE, in which case it is evaluated as the corresponding boolean value
</code></pre></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Logical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>boolean</code>The logical AND of the arguments.</div>
</div></div>
</div>
<a id="method_LOGICAL_OR"></a><div class="element clickable method public method_LOGICAL_OR" data-toggle="collapse" data-target=".method_LOGICAL_OR .collapse">
<h2>LOGICAL_OR</h2>
<pre>LOGICAL_OR() : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns boolean TRUE if any argument is TRUE; returns FALSE if all arguments are FALSE.</p>

<p>Excel Function:
    =OR(logical1[,logical2[, ...]])</p>

<pre><code>The arguments must evaluate to logical values such as TRUE or FALSE, or the arguments must be arrays
    or references that contain logical values.

Boolean arguments are treated as True or False as appropriate
Integer or floating point arguments are treated as True, except for 0 or 0.0 which are False
If any argument value is a string, or a Null, the function returns a #VALUE! error, unless the string holds
    the value TRUE or FALSE, in which case it is evaluated as the corresponding boolean value
</code></pre></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Logical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>boolean</code>The logical OR of the arguments.</div>
</div></div>
</div>
<a id="method_NOT"></a><div class="element clickable method public method_NOT" data-toggle="collapse" data-target=".method_NOT .collapse">
<h2>NOT</h2>
<pre>NOT(mixed $logical) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the boolean inverse of the argument.</p>

<p>Excel Function:
    =NOT(logical)</p>

<pre><code>The argument must evaluate to a logical value such as TRUE or FALSE

Boolean arguments are treated as True or False as appropriate
Integer or floating point arguments are treated as True, except for 0 or 0.0 which are False
If any argument value is a string, or a Null, the function returns a #VALUE! error, unless the string holds
    the value TRUE or FALSE, in which case it is evaluated as the corresponding boolean value
</code></pre></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Logical Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$logical</h4>
<code>mixed</code><p>A value or expression that can be evaluated to TRUE or FALSE</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>boolean</code>The boolean inverse of the argument.</div>
</div></div>
</div>
<a id="method_STATEMENT_IF"></a><div class="element clickable method public method_STATEMENT_IF" data-toggle="collapse" data-target=".method_STATEMENT_IF .collapse">
<h2>STATEMENT_IF</h2>
<pre>STATEMENT_IF(mixed $condition, mixed $returnIfTrue, mixed $returnIfFalse) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns one value if a condition you specify evaluates to TRUE and another value if it evaluates to FALSE.</p>

<p>Excel Function:
    =IF(condition[,returnIfTrue[,returnIfFalse]])</p>

<pre><code>Condition is any value or expression that can be evaluated to TRUE or FALSE.
    For example, A10=100 is a logical expression; if the value in cell A10 is equal to 100,
    the expression evaluates to TRUE. Otherwise, the expression evaluates to FALSE.
    This argument can use any comparison calculation operator.
ReturnIfTrue is the value that is returned if condition evaluates to TRUE.
    For example, if this argument is the text string "Within budget" and the condition argument evaluates to TRUE,
    then the IF function returns the text "Within budget"
    If condition is TRUE and ReturnIfTrue is blank, this argument returns 0 (zero). To display the word TRUE, use
    the logical value TRUE for this argument.
    ReturnIfTrue can be another formula.
ReturnIfFalse is the value that is returned if condition evaluates to FALSE.
    For example, if this argument is the text string "Over budget" and the condition argument evaluates to FALSE,
    then the IF function returns the text "Over budget".
    If condition is FALSE and ReturnIfFalse is omitted, then the logical value FALSE is returned.
    If condition is FALSE and ReturnIfFalse is blank, then the value 0 (zero) is returned.
    ReturnIfFalse can be another formula.
</code></pre></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Logical Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$condition</h4>
<code>mixed</code><p>Condition to evaluate</p></div>
<div class="subelement argument">
<h4>$returnIfTrue</h4>
<code>mixed</code><p>Value to return when condition is true</p></div>
<div class="subelement argument">
<h4>$returnIfFalse</h4>
<code>mixed</code><p>Optional value to return when condition is false</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>The value of returnIfTrue or returnIfFalse determined by condition</div>
</div></div>
</div>
<a id="method_TRUE"></a><div class="element clickable method public method_TRUE" data-toggle="collapse" data-target=".method_TRUE .collapse">
<h2>TRUE</h2>
<pre>TRUE() : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the boolean TRUE.</p>

<p>Excel Function:
    =TRUE()</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Logical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>boolean</code>True</div>
</div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:34Z.<br></footer></div>
</div>
</body>
</html>
