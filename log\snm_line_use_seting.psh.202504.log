<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250401 | sEdate : 20250513<br>
<br>
 MAX_DAY  : 20250512<br>
 DIFF_DAY  : 42<br>
 20250401 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250512 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 이미 등록되어있음......<br>
 20250512 이미 등록되어있음......<br>
 20250512 이미 등록되어있음......<br>
 20250512 이미 등록되어있음......<br>
 20250512 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250401001<br>
 입력 SLUS_ID  : 20250401002<br>
 입력 SLUS_ID  : 20250401003<br>
 입력 SLUS_ID  : 20250401004<br>
 입력 SLUS_ID  : 20250401005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250402 | sEdate : 20250514<br>
<br>
 MAX_DAY  : 20250513<br>
 DIFF_DAY  : 42<br>
 20250402 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250513 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 이미 등록되어있음......<br>
 20250513 이미 등록되어있음......<br>
 20250513 이미 등록되어있음......<br>
 20250513 이미 등록되어있음......<br>
 20250513 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250402001<br>
 입력 SLUS_ID  : 20250402002<br>
 입력 SLUS_ID  : 20250402003<br>
 입력 SLUS_ID  : 20250402004<br>
 입력 SLUS_ID  : 20250402005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250403 | sEdate : 20250515<br>
<br>
 MAX_DAY  : 20250514<br>
 DIFF_DAY  : 42<br>
 20250403 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250514 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 이미 등록되어있음......<br>
 20250514 이미 등록되어있음......<br>
 20250514 이미 등록되어있음......<br>
 20250514 이미 등록되어있음......<br>
 20250514 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250403001<br>
 입력 SLUS_ID  : 20250403002<br>
 입력 SLUS_ID  : 20250403003<br>
 입력 SLUS_ID  : 20250403004<br>
 입력 SLUS_ID  : 20250403005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250404 | sEdate : 20250516<br>
<br>
 MAX_DAY  : 20250515<br>
 DIFF_DAY  : 42<br>
 20250404 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250515 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 이미 등록되어있음......<br>
 20250515 이미 등록되어있음......<br>
 20250515 이미 등록되어있음......<br>
 20250515 이미 등록되어있음......<br>
 20250515 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250404001<br>
 입력 SLUS_ID  : 20250404002<br>
 입력 SLUS_ID  : 20250404003<br>
 입력 SLUS_ID  : 20250404004<br>
 입력 SLUS_ID  : 20250404005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250405 | sEdate : 20250517<br>
<br>
 MAX_DAY  : 20250516<br>
 DIFF_DAY  : 42<br>
 20250405 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 이미 등록되어있음......<br>
 20250516 이미 등록되어있음......<br>
 20250516 이미 등록되어있음......<br>
 20250516 이미 등록되어있음......<br>
 20250516 이미 등록되어있음......<br>
 20250517 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250406 | sEdate : 20250518<br>
<br>
 MAX_DAY  : 20250516<br>
 DIFF_DAY  : 42<br>
 20250406 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 이미 등록되어있음......<br>
 20250516 이미 등록되어있음......<br>
 20250516 이미 등록되어있음......<br>
 20250516 이미 등록되어있음......<br>
 20250516 이미 등록되어있음......<br>
 20250517 주말일 경우 패스.....<br>
 20250518 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250407 | sEdate : 20250519<br>
<br>
 MAX_DAY  : 20250516<br>
 DIFF_DAY  : 42<br>
 20250407 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250516 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 이미 등록되어있음......<br>
 20250516 이미 등록되어있음......<br>
 20250516 이미 등록되어있음......<br>
 20250516 이미 등록되어있음......<br>
 20250516 이미 등록되어있음......<br>
 20250517 주말일 경우 패스.....<br>
 20250518 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250407001<br>
 입력 SLUS_ID  : 20250407002<br>
 입력 SLUS_ID  : 20250407003<br>
 입력 SLUS_ID  : 20250407004<br>
 입력 SLUS_ID  : 20250407005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250408 | sEdate : 20250520<br>
<br>
 MAX_DAY  : 20250519<br>
 DIFF_DAY  : 42<br>
 20250408 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250519 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 이미 등록되어있음......<br>
 20250519 이미 등록되어있음......<br>
 20250519 이미 등록되어있음......<br>
 20250519 이미 등록되어있음......<br>
 20250519 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250408001<br>
 입력 SLUS_ID  : 20250408002<br>
 입력 SLUS_ID  : 20250408003<br>
 입력 SLUS_ID  : 20250408004<br>
 입력 SLUS_ID  : 20250408005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250409 | sEdate : 20250521<br>
<br>
 MAX_DAY  : 20250520<br>
 DIFF_DAY  : 42<br>
 20250409 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250520 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 이미 등록되어있음......<br>
 20250520 이미 등록되어있음......<br>
 20250520 이미 등록되어있음......<br>
 20250520 이미 등록되어있음......<br>
 20250520 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250409001<br>
 입력 SLUS_ID  : 20250409002<br>
 입력 SLUS_ID  : 20250409003<br>
 입력 SLUS_ID  : 20250409004<br>
 입력 SLUS_ID  : 20250409005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250410 | sEdate : 20250522<br>
<br>
 MAX_DAY  : 20250521<br>
 DIFF_DAY  : 42<br>
 20250410 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250411 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250521 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 이미 등록되어있음......<br>
 20250521 이미 등록되어있음......<br>
 20250521 이미 등록되어있음......<br>
 20250521 이미 등록되어있음......<br>
 20250521 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250410001<br>
 입력 SLUS_ID  : 20250410002<br>
 입력 SLUS_ID  : 20250410003<br>
 입력 SLUS_ID  : 20250410004<br>
 입력 SLUS_ID  : 20250410005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250411 | sEdate : 20250523<br>
<br>
 MAX_DAY  : 20250522<br>
 DIFF_DAY  : 42<br>
 20250411 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250412 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250522 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 이미 등록되어있음......<br>
 20250522 이미 등록되어있음......<br>
 20250522 이미 등록되어있음......<br>
 20250522 이미 등록되어있음......<br>
 20250522 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250411001<br>
 입력 SLUS_ID  : 20250411002<br>
 입력 SLUS_ID  : 20250411003<br>
 입력 SLUS_ID  : 20250411004<br>
 입력 SLUS_ID  : 20250411005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250412 | sEdate : 20250524<br>
<br>
 MAX_DAY  : 20250523<br>
 DIFF_DAY  : 42<br>
 20250412 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250413 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 이미 등록되어있음......<br>
 20250523 이미 등록되어있음......<br>
 20250523 이미 등록되어있음......<br>
 20250523 이미 등록되어있음......<br>
 20250523 이미 등록되어있음......<br>
 20250524 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250413 | sEdate : 20250525<br>
<br>
 MAX_DAY  : 20250523<br>
 DIFF_DAY  : 42<br>
 20250413 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250414 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 이미 등록되어있음......<br>
 20250523 이미 등록되어있음......<br>
 20250523 이미 등록되어있음......<br>
 20250523 이미 등록되어있음......<br>
 20250523 이미 등록되어있음......<br>
 20250524 주말일 경우 패스.....<br>
 20250525 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250414 | sEdate : 20250526<br>
<br>
 MAX_DAY  : 20250523<br>
 DIFF_DAY  : 42<br>
 20250414 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250415 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250523 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 이미 등록되어있음......<br>
 20250523 이미 등록되어있음......<br>
 20250523 이미 등록되어있음......<br>
 20250523 이미 등록되어있음......<br>
 20250523 이미 등록되어있음......<br>
 20250524 주말일 경우 패스.....<br>
 20250525 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250414001<br>
 입력 SLUS_ID  : 20250414002<br>
 입력 SLUS_ID  : 20250414003<br>
 입력 SLUS_ID  : 20250414004<br>
 입력 SLUS_ID  : 20250414005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250415 | sEdate : 20250527<br>
<br>
 MAX_DAY  : 20250526<br>
 DIFF_DAY  : 42<br>
 20250415 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250416 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250526 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 이미 등록되어있음......<br>
 20250526 이미 등록되어있음......<br>
 20250526 이미 등록되어있음......<br>
 20250526 이미 등록되어있음......<br>
 20250526 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250415001<br>
 입력 SLUS_ID  : 20250415002<br>
 입력 SLUS_ID  : 20250415003<br>
 입력 SLUS_ID  : 20250415004<br>
 입력 SLUS_ID  : 20250415005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250416 | sEdate : 20250528<br>
<br>
 MAX_DAY  : 20250527<br>
 DIFF_DAY  : 42<br>
 20250416 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250417 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250527 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 이미 등록되어있음......<br>
 20250527 이미 등록되어있음......<br>
 20250527 이미 등록되어있음......<br>
 20250527 이미 등록되어있음......<br>
 20250527 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250416001<br>
 입력 SLUS_ID  : 20250416002<br>
 입력 SLUS_ID  : 20250416003<br>
 입력 SLUS_ID  : 20250416004<br>
 입력 SLUS_ID  : 20250416005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250417 | sEdate : 20250529<br>
<br>
 MAX_DAY  : 20250528<br>
 DIFF_DAY  : 42<br>
 20250417 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250418 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250528 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 이미 등록되어있음......<br>
 20250528 이미 등록되어있음......<br>
 20250528 이미 등록되어있음......<br>
 20250528 이미 등록되어있음......<br>
 20250528 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250417001<br>
 입력 SLUS_ID  : 20250417002<br>
 입력 SLUS_ID  : 20250417003<br>
 입력 SLUS_ID  : 20250417004<br>
 입력 SLUS_ID  : 20250417005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250418 | sEdate : 20250530<br>
<br>
 MAX_DAY  : 20250529<br>
 DIFF_DAY  : 42<br>
 20250418 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250419 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250529 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 이미 등록되어있음......<br>
 20250529 이미 등록되어있음......<br>
 20250529 이미 등록되어있음......<br>
 20250529 이미 등록되어있음......<br>
 20250529 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250418001<br>
 입력 SLUS_ID  : 20250418002<br>
 입력 SLUS_ID  : 20250418003<br>
 입력 SLUS_ID  : 20250418004<br>
 입력 SLUS_ID  : 20250418005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250419 | sEdate : 20250531<br>
<br>
 MAX_DAY  : 20250530<br>
 DIFF_DAY  : 42<br>
 20250419 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250420 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 이미 등록되어있음......<br>
 20250530 이미 등록되어있음......<br>
 20250530 이미 등록되어있음......<br>
 20250530 이미 등록되어있음......<br>
 20250530 이미 등록되어있음......<br>
 20250531 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250420 | sEdate : 20250601<br>
<br>
 MAX_DAY  : 20250530<br>
 DIFF_DAY  : 42<br>
 20250420 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250421 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 이미 등록되어있음......<br>
 20250530 이미 등록되어있음......<br>
 20250530 이미 등록되어있음......<br>
 20250530 이미 등록되어있음......<br>
 20250530 이미 등록되어있음......<br>
 20250531 주말일 경우 패스.....<br>
 20250601 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250421 | sEdate : 20250602<br>
<br>
 MAX_DAY  : 20250530<br>
 DIFF_DAY  : 42<br>
 20250421 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250422 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250530 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 이미 등록되어있음......<br>
 20250530 이미 등록되어있음......<br>
 20250530 이미 등록되어있음......<br>
 20250530 이미 등록되어있음......<br>
 20250530 이미 등록되어있음......<br>
 20250531 주말일 경우 패스.....<br>
 20250601 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250421001<br>
 입력 SLUS_ID  : 20250421002<br>
 입력 SLUS_ID  : 20250421003<br>
 입력 SLUS_ID  : 20250421004<br>
 입력 SLUS_ID  : 20250421005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250422 | sEdate : 20250603<br>
<br>
 MAX_DAY  : 20250602<br>
 DIFF_DAY  : 42<br>
 20250422 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250423 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 이미 등록되어있음......<br>
 20250602 이미 등록되어있음......<br>
 20250602 이미 등록되어있음......<br>
 20250602 이미 등록되어있음......<br>
 20250602 이미 등록되어있음......<br>
 대통령선거일 휴무일인 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250423 | sEdate : 20250604<br>
<br>
 MAX_DAY  : 20250602<br>
 DIFF_DAY  : 42<br>
 20250423 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250424 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250602 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 이미 등록되어있음......<br>
 20250602 이미 등록되어있음......<br>
 20250602 이미 등록되어있음......<br>
 20250602 이미 등록되어있음......<br>
 20250602 이미 등록되어있음......<br>
 대통령선거일 휴무일인 경우 패스.....<br>
 입력 SLUS_ID  : 20250423001<br>
 입력 SLUS_ID  : 20250423002<br>
 입력 SLUS_ID  : 20250423003<br>
 입력 SLUS_ID  : 20250423004<br>
 입력 SLUS_ID  : 20250423005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250424 | sEdate : 20250605<br>
<br>
 MAX_DAY  : 20250604<br>
 DIFF_DAY  : 42<br>
 20250424 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250425 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250604 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 이미 등록되어있음......<br>
 20250604 이미 등록되어있음......<br>
 20250604 이미 등록되어있음......<br>
 20250604 이미 등록되어있음......<br>
 20250604 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250424001<br>
 입력 SLUS_ID  : 20250424002<br>
 입력 SLUS_ID  : 20250424003<br>
 입력 SLUS_ID  : 20250424004<br>
 입력 SLUS_ID  : 20250424005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250425 | sEdate : 20250606<br>
<br>
 MAX_DAY  : 20250605<br>
 DIFF_DAY  : 42<br>
 20250425 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250426 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 현충일 휴무일인 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250426 | sEdate : 20250607<br>
<br>
 MAX_DAY  : 20250605<br>
 DIFF_DAY  : 42<br>
 20250426 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250427 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 현충일 휴무일인 경우 패스.....<br>
 20250607 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250427 | sEdate : 20250608<br>
<br>
 MAX_DAY  : 20250605<br>
 DIFF_DAY  : 42<br>
 20250427 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250428 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 현충일 휴무일인 경우 패스.....<br>
 20250607 주말일 경우 패스.....<br>
 20250608 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250428 | sEdate : 20250609<br>
<br>
 MAX_DAY  : 20250605<br>
 DIFF_DAY  : 42<br>
 20250428 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250429 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250605 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 20250605 이미 등록되어있음......<br>
 현충일 휴무일인 경우 패스.....<br>
 20250607 주말일 경우 패스.....<br>
 20250608 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250428001<br>
 입력 SLUS_ID  : 20250428002<br>
 입력 SLUS_ID  : 20250428003<br>
 입력 SLUS_ID  : 20250428004<br>
 입력 SLUS_ID  : 20250428005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250429 | sEdate : 20250610<br>
<br>
 MAX_DAY  : 20250609<br>
 DIFF_DAY  : 42<br>
 20250429 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250430 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250609 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 이미 등록되어있음......<br>
 20250609 이미 등록되어있음......<br>
 20250609 이미 등록되어있음......<br>
 20250609 이미 등록되어있음......<br>
 20250609 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250429001<br>
 입력 SLUS_ID  : 20250429002<br>
 입력 SLUS_ID  : 20250429003<br>
 입력 SLUS_ID  : 20250429004<br>
 입력 SLUS_ID  : 20250429005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250430 | sEdate : 20250611<br>
<br>
 MAX_DAY  : 20250610<br>
 DIFF_DAY  : 42<br>
 20250430 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250501 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250502 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250503 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250504 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250505 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250506 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250507 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250508 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250509 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250510 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250511 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250512 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250513 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250514 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250515 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250516 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250517 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250518 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250519 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250520 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250521 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250522 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250523 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250524 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250525 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250526 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250527 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250528 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250529 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250530 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250531 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250601 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250610 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 이미 등록되어있음......<br>
 20250610 이미 등록되어있음......<br>
 20250610 이미 등록되어있음......<br>
 20250610 이미 등록되어있음......<br>
 20250610 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250430001<br>
 입력 SLUS_ID  : 20250430002<br>
 입력 SLUS_ID  : 20250430003<br>
 입력 SLUS_ID  : 20250430004<br>
 입력 SLUS_ID  : 20250430005