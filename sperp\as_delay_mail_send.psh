#!/usr/bin/php -q
<?php 
	// 0 9 * * 1-5 php -q /home/<USER>/sperp/as_delay_mail_send.psh
	# 온라인AS접수 지연 현황 알림 (업무연락, 구글 웹훅)
	$ROOT_PATH = "/home/<USER>";
	include($ROOT_PATH . "/inc/func.php");
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/Encode.php");

	$dbconn = new DBController($db['sperp_posbank']);
	if(empty($dbconn->success)) {
		echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
	}
 
    echo "\n[".date('Y-m-d H:i:s')."]\n"; 

// ************************************************************** [온라인AS접수] 수리지연현황 **************************************************************
    // 미출고
    $SQL = 
    "   SELECT
            COALESCE(A.OUT_DATE, B.OUT_DATE, C.OUT_DATE, D.OUT_DATE) AS OUT_DATE,
            NVL(A.AVG_ASTIME1, 0) AS AVG_ASTIME1, 
            NVL(A.DELAY_CNT1, 0) AS DELAY_CNT1, 
            NVL(A.AS_CNT1, 0) AS AS_CNT1,
            NVL(B.AVG_ASTIME2, 0) AS AVG_ASTIME2, 
            NVL(B.DELAY_CNT2, 0) AS DELAY_CNT2, 
            NVL(B.AS_CNT2, 0) AS AS_CNT2,
            NVL(C.AVG_ASTIME3, 0) AS AVG_ASTIME3, 
            NVL(C.DELAY_CNT3, 0) AS DELAY_CNT3, 
            NVL(C.AS_CNT3, 0) AS AS_CNT3,
            NVL(D.AVG_ASTIME4, 0) AS AVG_ASTIME4,
            NVL(D.DELAY_CNT4, 0) AS DELAY_CNT4,
            NVL(D.AS_CNT4, 0) AS AS_CNT4,
            NVL(NVL(A.DELAY_CNT1, 0) + NVL(B.DELAY_CNT2, 0) + NVL(C.DELAY_CNT3, 0) + NVL(D.DELAY_CNT4, 0),0) AS SUM_DELAY_CNT,
            NVL(NVL(A.AS_CNT1, 0) + NVL(B.AS_CNT2, 0) + NVL(C.AS_CNT3, 0) + NVL(D.AS_CNT4, 0) ,0) AS SUM_AS_CNT,
            NVL2(A.AVG_ASTIME1,
                (CASE WHEN FLOOR(A.AVG_ASTIME1 * 3600 / 86400) > 0 THEN FLOOR(A.AVG_ASTIME1 * 3600 / 86400) || '일 'ELSE '' END )
                || FLOOR(MOD(A.AVG_ASTIME1 * 3600, 86400) / 3600) || '시 ' || FLOOR(MOD(A.AVG_ASTIME1 * 3600, 3600) / 60) || '분 ' ,'' 
            ) AS AVG_ASTIME1_STR ,
            NVL2(B.AVG_ASTIME2,
                (CASE WHEN FLOOR(B.AVG_ASTIME2 * 3600 / 86400) > 0 THEN FLOOR(B.AVG_ASTIME2 * 3600 / 86400) || '일 ' ELSE '' END)
                || FLOOR(MOD(B.AVG_ASTIME2 * 3600, 86400) / 3600) || '시 ' || FLOOR(MOD(B.AVG_ASTIME2 * 3600, 3600) / 60) || '분 '
            ,'' ) AS AVG_ASTIME2_STR ,
            NVL2(C.AVG_ASTIME3,
                (CASE WHEN FLOOR(C.AVG_ASTIME3 * 3600 / 86400) > 0 THEN FLOOR(C.AVG_ASTIME3 * 3600 / 86400) || '일 ' ELSE ''END )
                ||FLOOR(MOD(C.AVG_ASTIME3 * 3600, 86400) / 3600) || '시 ' ||FLOOR(MOD(C.AVG_ASTIME3 * 3600, 3600) / 60) || '분 '
            ,'' ) AS AVG_ASTIME3_STR ,
            NVL2(D.AVG_ASTIME4,
                (CASE WHEN FLOOR(D.AVG_ASTIME4 * 3600 / 86400) > 0 THEN FLOOR(D.AVG_ASTIME4 * 3600 / 86400) || '일 ' ELSE ''END )
                ||FLOOR(MOD(D.AVG_ASTIME4 * 3600, 86400) / 3600) || '시 ' ||FLOOR(MOD(D.AVG_ASTIME4 * 3600, 3600) / 60) || '분 '
            ,'' ) AS AVG_ASTIME4_STR 
        FROM  
        (
            SELECT 
                '미출고' AS OUT_DATE,
                ROUND(AVG((SYSDATE - A.WAYBILL_DATE) * 24), 2) AS AVG_ASTIME1,
                NVL(COUNT(CASE WHEN (SYSDATE - A.WAYBILL_DATE) * 24 > 48 THEN 1 END),0) AS DELAY_CNT1,
                NVL(COUNT(*),0) AS AS_CNT1
            FROM ASS_ACCEPT A
            LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
            LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID
            WHERE A.STATE NOT IN ('40','31','90','91','92','93','94','95')
                AND ASS.STATE NOT IN ('1', 'G', 'H', 'I')
                AND A.REP_STATE = '3'
                AND A.REG_IDATE >= TO_DATE('2025-06-09','YYYY-MM-DD')
            GROUP BY '미출고'
        ) A  -- 입고~출고   
        FULL OUTER JOIN 
        (
            SELECT 
                '미출고' AS OUT_DATE, 
                ROUND(AVG((NVL(A.SEND_ESTIMATE_DATE,SYSDATE) - A.WAYBILL_DATE) * 24), 2) AS AVG_ASTIME2,
                NVL(COUNT(CASE WHEN (NVL(A.SEND_ESTIMATE_DATE,SYSDATE) - A.WAYBILL_DATE) * 24 > 48 THEN 1 END),0) AS DELAY_CNT2,
                NVL(COUNT(*),0) AS AS_CNT2
            FROM ASS_ACCEPT A
            LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
            LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID
            LEFT JOIN AS_PAYMENT F2 ON A.RCT_CODE = F2.RCT_CODE AND A.HDATE=F2.HDATE AND A.HNO = F2.HNO AND F2.SERVICE_TYPE ='1'
            WHERE A.STATE NOT IN ('40','31','90','91','92','93','94','95')
                AND ASS.STATE NOT IN ('1', 'G', 'H', 'I')
                AND A.REP_STATE NOT IN ('0','3')
                -- AND  NVL(F2.REG_IDATE,A.REP_SELECT_DATE) IS NULL
                AND A.REG_IDATE >= TO_DATE('2025-06-09','YYYY-MM-DD')
            GROUP BY '미출고'
        ) B ON A.OUT_DATE = B.OUT_DATE     -- 입고~견적
        FULL OUTER JOIN 
        (
            SELECT 
                '미출고' AS OUT_DATE, 
                ROUND(AVG((SYSDATE - NVL(F2.REG_IDATE,A.REP_SELECT_DATE)) * 24), 2) AS AVG_ASTIME3,
                NVL(COUNT(CASE WHEN (SYSDATE - NVL(F2.REG_IDATE,A.REP_SELECT_DATE)) * 24 > 48 THEN 1 END),0) AS DELAY_CNT3,
                NVL(COUNT(*),0) AS AS_CNT3
            FROM ASS_ACCEPT A
            LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
            LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID
            LEFT JOIN AS_PAYMENT F2 ON A.RCT_CODE = F2.RCT_CODE AND A.HDATE=F2.HDATE AND A.HNO = F2.HNO AND F2.SERVICE_TYPE ='1'
            WHERE A.STATE NOT IN ('40','31','90','91','92','93','94','95')
                AND ASS.STATE NOT IN ('1', 'G', 'H', 'I')
                AND A.REP_STATE NOT IN ('0','3')
                AND  NVL(F2.REG_IDATE,A.REP_SELECT_DATE) IS NOT NULL
                AND A.REG_IDATE >= TO_DATE('2025-06-09','YYYY-MM-DD')
            GROUP BY '미출고'
        ) C ON COALESCE(A.OUT_DATE, B.OUT_DATE) = C.OUT_DATE -- 수리비결제~출고
        FULL OUTER JOIN 
        (
            SELECT 
                '미출고' AS OUT_DATE, 
                ROUND(AVG((SYSDATE - NVL(F2.REG_IDATE,A.REP_SELECT_DATE)) * 24), 2) AS AVG_ASTIME4,
                NVL(COUNT(CASE WHEN (SYSDATE - NVL(F2.REG_IDATE,A.REP_SELECT_DATE)) * 24 > 48 THEN 1 END),0) AS DELAY_CNT4,
                NVL(COUNT(*),0) AS AS_CNT4
            FROM ASS_ACCEPT A
            LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
            LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID
            LEFT JOIN AS_PAYMENT F2 ON A.RCT_CODE = F2.RCT_CODE AND A.HDATE=F2.HDATE AND A.HNO = F2.HNO AND F2.SERVICE_TYPE ='1'
            WHERE A.STATE NOT IN ('40','31','90','91','92','93','94','95')
                AND( ASS.STATE NOT IN ('1','G', 'H', 'I') OR ASS.STATE IS NULL)
                AND A.REP_STATE ='0'
                AND A.REG_IDATE >= TO_DATE('2025-06-09','YYYY-MM-DD')
            GROUP BY '미출고'
        ) D ON COALESCE(A.OUT_DATE, B.OUT_DATE,C.OUT_DATE) = D.OUT_DATE -- 수리비 미결정 (점검전)
        ORDER BY COALESCE(A.OUT_DATE, B.OUT_DATE, C.OUT_DATE, D.OUT_DATE) DESC
    ";
    $NCrow = $dbconn->query_row($SQL);

    // 출고완료 데이터
    $SQL = 
    "  
        SELECT
            'TOTAL' AS OUT_DATE,
            ROUND(AVG(A.AVG_ASTIME1), 2) AS AVG_ASTIME1,
            SUM(NVL(A.DELAY_CNT1, 0)) AS DELAY_CNT1,
            SUM(NVL(A.AS_CNT1, 0)) AS AS_CNT1,
            ROUND(AVG(B.AVG_ASTIME2), 2) AS AVG_ASTIME2,
            SUM(NVL(B.DELAY_CNT2, 0)) AS DELAY_CNT2,
            SUM(NVL(B.AS_CNT2, 0)) AS AS_CNT2,
            ROUND(AVG(C.AVG_ASTIME3), 2) AS AVG_ASTIME3,
            SUM(NVL(C.DELAY_CNT3, 0)) AS DELAY_CNT3,
            SUM(NVL(C.AS_CNT3, 0)) AS AS_CNT3,
            SUM(NVL(A.DELAY_CNT1, 0)) + SUM(NVL(B.DELAY_CNT2, 0)) + SUM(NVL(C.DELAY_CNT3, 0)) AS SUM_DELAY_CNT,
            SUM(NVL(A.AS_CNT1, 0)) + SUM(NVL(B.AS_CNT2, 0)) + SUM(NVL(C.AS_CNT3, 0)) AS SUM_AS_CNT,

            NVL2(AVG(A.AVG_ASTIME1),
                (CASE WHEN FLOOR(AVG(A.AVG_ASTIME1) * 3600 / 86400) > 0 
                    THEN FLOOR(AVG(A.AVG_ASTIME1) * 3600 / 86400) || '일 ' 
                    ELSE '' END) ||
                FLOOR(MOD(AVG(A.AVG_ASTIME1) * 3600, 86400) / 3600) || '시 ' || 
                FLOOR(MOD(AVG(A.AVG_ASTIME1) * 3600, 3600) / 60) || '분 '
            , '') AS AVG_ASTIME1_STR,

            NVL2(AVG(B.AVG_ASTIME2),
                (CASE WHEN FLOOR(AVG(B.AVG_ASTIME2) * 3600 / 86400) > 0 
                    THEN FLOOR(AVG(B.AVG_ASTIME2) * 3600 / 86400) || '일 ' 
                    ELSE '' END) ||
                FLOOR(MOD(AVG(B.AVG_ASTIME2) * 3600, 86400) / 3600) || '시 ' || 
                FLOOR(MOD(AVG(B.AVG_ASTIME2) * 3600, 3600) / 60) || '분 '
            , '') AS AVG_ASTIME2_STR,

            NVL2(AVG(C.AVG_ASTIME3),
                (CASE WHEN FLOOR(AVG(C.AVG_ASTIME3) * 3600 / 86400) > 0 
                    THEN FLOOR(AVG(C.AVG_ASTIME3) * 3600 / 86400) || '일 ' 
                    ELSE '' END) ||
                FLOOR(MOD(AVG(C.AVG_ASTIME3) * 3600, 86400) / 3600) || '시 ' || 
                FLOOR(MOD(AVG(C.AVG_ASTIME3) * 3600, 3600) / 60) || '분 '
            , '') AS AVG_ASTIME3_STR

        FROM 
        (
            SELECT 
                TO_CHAR(AO.QDATE, 'YYYY-MM-DD') AS OUT_DATE,
                ROUND(AVG((AO.QDATE - A.WAYBILL_DATE) * 24), 2) AS AVG_ASTIME1,
                NVL(COUNT(CASE WHEN (AO.QDATE - A.WAYBILL_DATE) * 24 > 48 THEN 1 END),0) AS DELAY_CNT1,
                NVL(COUNT(*),0) AS AS_CNT1
            FROM ASS_ACCEPT A
            LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
            LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID
            WHERE A.STATE = '40'
                AND ASS.STATE IN ('1', 'G', 'H', 'I')
                AND A.REP_STATE = '3'
                AND A.REG_IDATE >= TO_DATE('2025-06-09','YYYY-MM-DD')
                AND TRUNC(AO.QDATE) BETWEEN TRUNC(SYSDATE, 'IW') - 7 AND TRUNC(SYSDATE, 'IW') - 1   -- (지난주 월~일)
            GROUP BY TO_CHAR(AO.QDATE, 'YYYY-MM-DD')
        ) A      -- 입고~출고
        FULL OUTER JOIN 
        (
            SELECT 
                TO_CHAR(AO.QDATE, 'YYYY-MM-DD') AS OUT_DATE,
                ROUND(AVG((A.SEND_ESTIMATE_DATE - A.WAYBILL_DATE) * 24), 2) AS AVG_ASTIME2,
                NVL(COUNT(CASE WHEN (A.SEND_ESTIMATE_DATE - A.WAYBILL_DATE) * 24 > 48 THEN 1 END),0) AS DELAY_CNT2,
                NVL(COUNT(*),0) AS AS_CNT2
            FROM ASS_ACCEPT A
            LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
            LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID
            WHERE A.STATE = '40'
                AND ASS.STATE IN ('1', 'G', 'H', 'I')
                AND A.REP_STATE IN ('1','4')
                AND A.REG_IDATE >= TO_DATE('2025-06-09','YYYY-MM-DD')
                AND TRUNC(AO.QDATE) BETWEEN TRUNC(SYSDATE, 'IW') - 7 AND TRUNC(SYSDATE, 'IW') - 1   -- (지난주 월~일)
            GROUP BY TO_CHAR(AO.QDATE, 'YYYY-MM-DD')
        ) B ON A.OUT_DATE = B.OUT_DATE -- 입고~견적
        FULL OUTER JOIN 
        (
            SELECT 
                TO_CHAR(AO.QDATE, 'YYYY-MM-DD') AS OUT_DATE, 
                ROUND(AVG((AO.QDATE - NVL(F2.REG_IDATE,A.REP_SELECT_DATE)) * 24), 2) AS AVG_ASTIME3,
                NVL(COUNT(CASE WHEN (AO.QDATE - NVL(F2.REG_IDATE,A.REP_SELECT_DATE)) * 24 > 48 THEN 1 END),0) AS DELAY_CNT3,
                NVL(COUNT(*),0) AS AS_CNT3
            FROM ASS_ACCEPT A
            LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
            LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID
            LEFT JOIN AS_PAYMENT F2 ON A.RCT_CODE = F2.RCT_CODE AND A.HDATE=F2.HDATE AND A.HNO = F2.HNO AND F2.SERVICE_TYPE ='1'
            WHERE A.STATE = '40'
                AND ASS.STATE IN ('1', 'G', 'H', 'I')
                AND A.REP_STATE IN ('1','4')
                AND A.REG_IDATE >= TO_DATE('2025-06-09','YYYY-MM-DD')
                AND TRUNC(AO.QDATE) BETWEEN TRUNC(SYSDATE, 'IW') - 7 AND TRUNC(SYSDATE, 'IW') - 1   -- (지난주 월~일)
            GROUP BY TO_CHAR(AO.QDATE, 'YYYY-MM-DD')
        ) C ON COALESCE(A.OUT_DATE, B.OUT_DATE) = C.OUT_DATE     -- 수리비결제~출고    

        UNION ALL 
        
        SELECT
            COALESCE(A.OUT_DATE, B.OUT_DATE, C.OUT_DATE) AS OUT_DATE,
            NVL(A.AVG_ASTIME1, 0) AS AVG_ASTIME1, 
            NVL(A.DELAY_CNT1, 0) AS DELAY_CNT1, 
            NVL(A.AS_CNT1, 0) AS AS_CNT1,
            NVL(B.AVG_ASTIME2, 0) AS AVG_ASTIME2, 
            NVL(B.DELAY_CNT2, 0) AS DELAY_CNT2, 
            NVL(B.AS_CNT2, 0) AS AS_CNT2,
            NVL(C.AVG_ASTIME3, 0) AS AVG_ASTIME3, 
            NVL(C.DELAY_CNT3, 0) AS DELAY_CNT3, 
            NVL(C.AS_CNT3, 0) AS AS_CNT3,
            NVL(NVL(A.DELAY_CNT1, 0) + NVL(B.DELAY_CNT2, 0) + NVL(C.DELAY_CNT3, 0),0) AS SUM_DELAY_CNT,
            NVL(NVL(A.AS_CNT1, 0) + NVL(B.AS_CNT2, 0) + NVL(C.AS_CNT3, 0),0) AS SUM_AS_CNT ,
            NVL2(A.AVG_ASTIME1,
                (CASE  WHEN FLOOR(A.AVG_ASTIME1 * 3600 / 86400) > 0  THEN FLOOR(A.AVG_ASTIME1 * 3600 / 86400) || '일 ' ELSE '' END)
                || FLOOR(MOD(A.AVG_ASTIME1 * 3600, 86400) / 3600) || '시 ' || FLOOR(MOD(A.AVG_ASTIME1 * 3600, 3600) / 60) || '분 '
            ,'' ) AS AVG_ASTIME1_STR,
            NVL2(B.AVG_ASTIME2,
                (CASE  WHEN FLOOR(B.AVG_ASTIME2 * 3600 / 86400) > 0  THEN FLOOR(B.AVG_ASTIME2 * 3600 / 86400) || '일 ' ELSE '' END )
                || FLOOR(MOD(B.AVG_ASTIME2 * 3600, 86400) / 3600) || '시 ' || FLOOR(MOD(B.AVG_ASTIME2 * 3600, 3600) / 60) || '분 '
            ,'' ) AS AVG_ASTIME2_STR,
            NVL2(C.AVG_ASTIME3,
                (CASE  WHEN FLOOR(C.AVG_ASTIME3 * 3600 / 86400) > 0  THEN FLOOR(C.AVG_ASTIME3 * 3600 / 86400) || '일 ' ELSE '' END )
                || FLOOR(MOD(C.AVG_ASTIME3 * 3600, 86400) / 3600) || '시 ' || FLOOR(MOD(C.AVG_ASTIME3 * 3600, 3600) / 60) || '분 '
            ,'' ) AS AVG_ASTIME3_STR
        FROM 
        (
            SELECT 
                TO_CHAR(AO.QDATE, 'YYYY-MM-DD') AS OUT_DATE,
                ROUND(AVG((AO.QDATE - A.WAYBILL_DATE) * 24), 2) AS AVG_ASTIME1,
                NVL(COUNT(CASE WHEN (AO.QDATE - A.WAYBILL_DATE) * 24 > 48 THEN 1 END),0) AS DELAY_CNT1,
                NVL(COUNT(*),0) AS AS_CNT1
            FROM ASS_ACCEPT A
            LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
            LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID
            WHERE A.STATE = '40'
                AND ASS.STATE IN ('1', 'G', 'H', 'I')
                AND A.REP_STATE = '3'
                AND A.REG_IDATE >= TO_DATE('2025-06-09','YYYY-MM-DD')
                AND TRUNC(AO.QDATE) BETWEEN TRUNC(SYSDATE, 'IW') - 7 AND TRUNC(SYSDATE, 'IW') - 1   -- (지난주 월~일)
            GROUP BY TO_CHAR(AO.QDATE, 'YYYY-MM-DD')
        ) A      -- 입고~출고
        FULL OUTER JOIN 
        (
            SELECT 
                TO_CHAR(AO.QDATE, 'YYYY-MM-DD') AS OUT_DATE,
                ROUND(AVG((A.SEND_ESTIMATE_DATE - A.WAYBILL_DATE) * 24), 2) AS AVG_ASTIME2,
                NVL(COUNT(CASE WHEN (A.SEND_ESTIMATE_DATE - A.WAYBILL_DATE) * 24 > 48 THEN 1 END),0) AS DELAY_CNT2,
                NVL(COUNT(*),0) AS AS_CNT2
            FROM ASS_ACCEPT A
            LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
            LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID
            WHERE A.STATE = '40'
                AND ASS.STATE IN ('1', 'G', 'H', 'I')
                AND A.REP_STATE IN ('1','4')
                AND A.REG_IDATE >= TO_DATE('2025-06-09','YYYY-MM-DD')
                AND TRUNC(AO.QDATE) BETWEEN TRUNC(SYSDATE, 'IW') - 7 AND TRUNC(SYSDATE, 'IW') - 1   -- (지난주 월~일)
            GROUP BY TO_CHAR(AO.QDATE, 'YYYY-MM-DD')
        ) B ON A.OUT_DATE = B.OUT_DATE -- 입고~견적
        FULL OUTER JOIN 
        (
            SELECT 
                TO_CHAR(AO.QDATE, 'YYYY-MM-DD') AS OUT_DATE, 
                ROUND(AVG((AO.QDATE - NVL(F2.REG_IDATE,A.REP_SELECT_DATE)) * 24), 2) AS AVG_ASTIME3,
                NVL(COUNT(CASE WHEN (AO.QDATE - NVL(F2.REG_IDATE,A.REP_SELECT_DATE)) * 24 > 48 THEN 1 END),0) AS DELAY_CNT3,
                NVL(COUNT(*),0) AS AS_CNT3
            FROM ASS_ACCEPT A
            LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
            LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID
            LEFT JOIN AS_PAYMENT F2 ON A.RCT_CODE = F2.RCT_CODE AND A.HDATE=F2.HDATE AND A.HNO = F2.HNO AND F2.SERVICE_TYPE ='1'
            WHERE A.STATE = '40'
                AND ASS.STATE IN ('1', 'G', 'H', 'I')
                AND A.REP_STATE IN ('1','4')
                AND A.REG_IDATE >= TO_DATE('2025-06-09','YYYY-MM-DD')
                AND TRUNC(AO.QDATE) BETWEEN TRUNC(SYSDATE, 'IW') - 7 AND TRUNC(SYSDATE, 'IW') - 1   -- (지난주 월~일)
            GROUP BY TO_CHAR(AO.QDATE, 'YYYY-MM-DD')
        ) C ON COALESCE(A.OUT_DATE, B.OUT_DATE) = C.OUT_DATE     -- 수리비결제~출고

        ORDER BY 1 DESC
    ";
    $rows = $dbconn->query_rows($SQL);

    // 미출고 + 출고완료데이터
    $SQL = "
        WITH
        -- 수리1 입고~출고
        A AS (
            SELECT 
                ROUND(AVG((NVL(AO.QDATE,SYSDATE) - A.WAYBILL_DATE) * 24), 2) AS AVG_ASTIME1,
                NVL(COUNT(CASE WHEN (NVL(AO.QDATE,SYSDATE) - A.WAYBILL_DATE) * 24 > 48 THEN 1 END),0) AS DELAY_CNT1,
                NVL(COUNT(*),0) AS AS_CNT1 
            FROM ASS_ACCEPT A
            LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
            LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID	
            WHERE  A.REP_STATE = '3' AND TRUNC(A.REG_IDATE) >= TRUNC(ADD_MONTHS(SYSDATE,-1))
        ),
        -- 수리2-1 입고~견적
        B AS (
			SELECT 
				ROUND(AVG((NVL(A.SEND_ESTIMATE_DATE,NVL(A.WAYBILL_DATE,SYSDATE)) - A.WAYBILL_DATE) * 24), 2) AS AVG_ASTIME2,
				NVL(COUNT(CASE WHEN (NVL(A.SEND_ESTIMATE_DATE,NVL(A.WAYBILL_DATE,SYSDATE)) - A.WAYBILL_DATE) * 24 > 48 THEN 1 END),0) AS DELAY_CNT2,
				NVL(COUNT(*),0) AS AS_CNT2 
			FROM ASS_ACCEPT A
			LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
			LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID
			WHERE  A.REP_STATE NOT IN ('0','3')  AND TRUNC(A.REG_IDATE) >= TRUNC(ADD_MONTHS(SYSDATE,-1))
        ),
        -- 수리2-2 결제~출고
        C AS (
			SELECT  
			    ROUND(AVG((NVL(AO.QDATE,SYSDATE) - NVL(F2.REG_IDATE,A.REP_SELECT_DATE)) * 24), 2) AS AVG_ASTIME3,
			    NVL(COUNT(CASE WHEN (NVL(AO.QDATE,SYSDATE)- NVL(F2.REG_IDATE,A.REP_SELECT_DATE)) * 24 > 48 THEN 1 END),0) AS DELAY_CNT3,
			    NVL(COUNT(*),0) AS AS_CNT3 
			FROM ASS_ACCEPT A
			LEFT JOIN ASS ON A.AS_ID = ASS.AS_ID
			LEFT JOIN ASIVOUT AO ON A.AS_ID = AO.AS_ID
			LEFT JOIN AS_PAYMENT F2 ON A.RCT_CODE = F2.RCT_CODE AND A.HDATE=F2.HDATE AND A.HNO = F2.HNO AND F2.SERVICE_TYPE ='1'
			WHERE  A.REP_STATE NOT IN ('0','3') AND TRUNC(A.REG_IDATE) >= TRUNC(ADD_MONTHS(SYSDATE,-1))
        )
        -- 결과 병합
        SELECT 
            NVL(A.AVG_ASTIME1, 0) AS AVG_ASTIME1, 
            NVL(A.DELAY_CNT1, 0) AS DELAY_CNT1, 
            NVL(A.AS_CNT1, 0) AS AS_CNT1,
            NVL(B.AVG_ASTIME2, 0) AS AVG_ASTIME2, 
            NVL(B.DELAY_CNT2, 0) AS DELAY_CNT2, 
            NVL(B.AS_CNT2, 0) AS AS_CNT2,
            NVL(C.AVG_ASTIME3, 0) AS AVG_ASTIME3, 
            NVL(C.DELAY_CNT3, 0) AS DELAY_CNT3, 
            NVL(C.AS_CNT3, 0) AS AS_CNT3,
            NVL2(A.AVG_ASTIME1,
                (CASE WHEN FLOOR(A.AVG_ASTIME1 * 3600 / 86400) > 0 THEN FLOOR(A.AVG_ASTIME1 * 3600 / 86400) || '일 'ELSE '' END )
                || FLOOR(MOD(A.AVG_ASTIME1 * 3600, 86400) / 3600) || '시 ' || FLOOR(MOD(A.AVG_ASTIME1 * 3600, 3600) / 60) || '분 ' ,'' 
            ) AS AVG_ASTIME1_STR ,
            NVL2(B.AVG_ASTIME2,
                (CASE WHEN FLOOR(B.AVG_ASTIME2 * 3600 / 86400) > 0 THEN FLOOR(B.AVG_ASTIME2 * 3600 / 86400) || '일 ' ELSE '' END)
                || FLOOR(MOD(B.AVG_ASTIME2 * 3600, 86400) / 3600) || '시 ' || FLOOR(MOD(B.AVG_ASTIME2 * 3600, 3600) / 60) || '분 '
            ,'' ) AS AVG_ASTIME2_STR ,
            NVL2(C.AVG_ASTIME3,
                (CASE WHEN FLOOR(C.AVG_ASTIME3 * 3600 / 86400) > 0 THEN FLOOR(C.AVG_ASTIME3 * 3600 / 86400) || '일 ' ELSE ''END )
                ||FLOOR(MOD(C.AVG_ASTIME3 * 3600, 86400) / 3600) || '시 ' ||FLOOR(MOD(C.AVG_ASTIME3 * 3600, 3600) / 60) || '분 '
            ,'' ) AS AVG_ASTIME3_STR 
        FROM A
        CROSS JOIN B
        CROSS JOIN C
    ";
    $delayRow = $dbconn->query_row($SQL); 

    $thColor = ' background-color: #EAF0F4; ';

    // 주차 계산: 해당 월의 1일부터의 경과 일 수를 기준으로 주차 계산
    $today = date('Y-m-d');
    $baseDate = strtotime("last Monday");
    $year = date("Y", $baseDate);
    $month = date("n", $baseDate);
    $firstOfMonth = strtotime(date("Y-m-01", $baseDate));
    $weekOfMonth = ceil((date("j", $baseDate) + date("w", $firstOfMonth)) / 7);
    $weekStr = "{$year}년 {$month}월 {$weekOfMonth}주차";    // 출력 예: 2025년 6월 4주차


    $asTime1 = floatval($NCrow['AVG_ASTIME1']);
    $asColor1 = '';
    if ($asTime1 > 144) { $asColor1 = 'background-color: rgba(255, 0, 0, 0.5);';
    } elseif ($asTime1 > 120) { $asColor1 = 'background-color: rgba(255, 165, 0, 0.5);';
    } elseif ($asTime1 > 96) { $asColor1 = 'background-color: rgba(255, 255, 0, 0.5);'; }
    $asTime2 = floatval($NCrow['AVG_ASTIME2']);
    $asColor2 = '';
    if ($asTime2 > 96) { $asColor2 = 'background-color: rgba(255, 0, 0, 0.5);';
    } elseif ($asTime2 > 72) { $asColor2 = 'background-color: rgba(255, 165, 0, 0.5);';
    } elseif ($asTime2 > 48) { $asColor2 = 'background-color: rgba(255, 255, 0, 0.5);'; }
    $asTime3 = floatval($NCrow['AVG_ASTIME3']);
    $asColor3 = '';
    if ($asTime3 > 96) { $asColor3 = 'background-color: rgba(255, 0, 0, 0.5);';
    } elseif ($asTime3 > 72) { $asColor3 = 'background-color: rgba(255, 165, 0, 0.5);';
    } elseif ($asTime3 > 48) { $asColor3 = 'background-color: rgba(255, 255, 0, 0.5);'; }
    $asColor4 = '';

    $asTime1 = floatval($delayRow['AVG_ASTIME1']);
    $DRColor1 = '';
    if ($asTime1 > 144) { $DRColor1 = 'background-color: rgba(255, 0, 0, 0.5);';
    } elseif ($asTime1 > 120) { $DRColor1 = 'background-color: rgba(255, 165, 0, 0.5);';
    } elseif ($asTime1 > 96) { $DRColor1 = 'background-color: rgba(255, 255, 0, 0.5);'; }
    $asTime2 = floatval($delayRow['AVG_ASTIME2']);
    $DRColor2 = '';
    if ($asTime2 > 96) { $DRColor2 = 'background-color: rgba(255, 0, 0, 0.5);';
    } elseif ($asTime2 > 72) { $DRColor2 = 'background-color: rgba(255, 165, 0, 0.5);';
    } elseif ($asTime2 > 48) { $DRColor2 = 'background-color: rgba(255, 255, 0, 0.5);'; }
    $asTime3 = floatval($delayRow['AVG_ASTIME3']);
    $DRColor3 = '';
    if ($asTime3 > 96) { $DRColor3 = 'background-color: rgba(255, 0, 0, 0.5);';
    } elseif ($asTime3 > 72) { $DRColor3 = 'background-color: rgba(255, 165, 0, 0.5);';
    } elseif ($asTime3 > 48) { $DRColor3 = 'background-color: rgba(255, 255, 0, 0.5);'; }

    // 업무연락 내용
    $content = '
        <p><span style="font-size: 14pt; color: rgb(0, 117, 200);"><b><u>온라인A/S접수 수리시간 48시간 초과 항목 알림 (' . $today . ')</u></b></span></p>
        <br>
        <blockquote class="se2_quote9" style="margin: 0px 0px 30px; padding: 10px; border: 2px solid rgb(229, 229, 229);  color: rgb(0, 0, 0);"  border-left: 5px solid #005bac;">
            
            <p style="margin-left: 0px;"><b><span style="font-size: 10pt; color: rgb(0, 0, 0);">※ 수리시간 기준 </span></b></p>
            <p style="margin-left: 20px;">-(수리비X) <b>수리시간1</b> (96시간) : (입고확인) ~ (수리완료) &nbsp;&nbsp;&nbsp;&nbsp;  ​ <b>(<span style="color: rgb(255, 0, 0);">■■■</span>:6일↑ <span style="color: rgb(240, 173, 78);">■■■</span>:5~6일 <span style="color: rgb(247, 228, 128);">■■■</span>: 4~5일</b>)</p>
            <p style="margin-left: 20px;">-(수리비O) <b>수리시간2-1</b> (48시간) : (입고확인) ~ (견적서 전송)​ <b>(<span style="color: rgb(255, 0, 0);">■■■</span>:4일↑ <span style="color: rgb(240, 173, 78);">■■■</span>:3~4일 <span style="color: rgb(247, 228, 128);">■■■</span>: 2~3일</b>)</p>
            <p style="margin-left: 20px;">-(수리비O) <b>수리시간2-2</b> (48시간) : (수리비결제) ~ (수리완료) ​ <b>(<span style="color: rgb(255, 0, 0);">■■■</span>:4일↑ <span style="color: rgb(240, 173, 78);">■■■</span>:3~4일 <span style="color: rgb(247, 228, 128);">■■■</span>: 2~3일</b>)</p> 
 
        </blockquote>
        <p><b><span style="font-size: 12pt; color: rgb(0, 117, 200);">현재 A/S 진행 중 현황</span></b></p>
        <table border="0" cellpadding="0" cellspacing="0" style="border:1px solid #cccccc; border-left:0; border-bottom:0;" class="__se_tbl">
            <tbody>
                
                <tr>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center; "></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center; "><b>수리비 미결정</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center; "><b>수리시간1</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>수리시간2-1</p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>수리시간2-2</b></p>
                    </td>
                </tr>


                <tr>
                    <td colspan="5" style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center; ">현재 수리진행중 평균 수리시간</p>
                    </td>
                </tr>
                <tr>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>접수건수</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">' . number_format($NCrow['AS_CNT4']) . ' 건</p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">' . number_format($NCrow['AS_CNT1']) . ' 건</p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">' . number_format($NCrow['AS_CNT2']) . ' 건</p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">' . number_format($NCrow['AS_CNT3']) . ' 건</p>
                    </td>
                </tr>
                <tr>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>지연건수</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">기준X 계산불가</p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">' . number_format($NCrow['DELAY_CNT1']) . ' 건</p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">' . number_format($NCrow['DELAY_CNT2']) . ' 건</p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">' . number_format($NCrow['DELAY_CNT3']) . ' 건</p>
                    </td> 
                </tr>
                <tr>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>평균 수리시간</b></p>
                    </td> 
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$asColor4.'" class="">
                        <p style="text-align: center;"><b></b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$asColor1.'" class="">
                        <p style="text-align: center;"><b>' . $NCrow['AVG_ASTIME1_STR'] . '</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$asColor2.'" class="">
                        <p style="text-align: center;"><b>' . $NCrow['AVG_ASTIME2_STR'] . '</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$asColor3.'" class="">
                        <p style="text-align: center;"><b>' . $NCrow['AVG_ASTIME3_STR'] . '</b></p>
                    </td>
                </tr> 


                <tr>
                    <td colspan="5" style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center; ">최근 1달간 평균 수리시간 (수리진행 + 완료)</p>
                    </td>
                </tr>
                <tr>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>평균 수리시간</b></p>
                    </td> 
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; " class="">
                        <p style="text-align: center;"><b></b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$DRColor1.'" class="">
                        <p style="text-align: center;"><b>' . $delayRow['AVG_ASTIME1_STR'] . '</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$DRColor2.'" class="">
                        <p style="text-align: center;"><b>' . $delayRow['AVG_ASTIME2_STR'] . '</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 300px; height: 24px; '.$DRColor3.'" class="">
                        <p style="text-align: center;"><b>' . $delayRow['AVG_ASTIME3_STR'] . '</b></p>
                    </td>
                </tr> 

            </tbody>
        </table>
        <br> 
        
        <p><b><span style="font-size: 12pt; color: rgb(0, 117, 200);">지난주(월~일) 수리 완료 현황 </span></b></p>
        <table border="0" cellpadding="0" cellspacing="0" style="border:1px solid #cccccc; border-left:0; border-bottom:0;" class="__se_tbl">
            <tbody>
                <tr>
                    <td rowspan="2" style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 50px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>NO.</b></p>
                    </td>
                    <td rowspan="2" style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 174px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>출고완료일</b></p>
                    </td>
                    <td colspan="3" style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>수리시간1</b></p>
                    </td>
                    <td colspan="3" style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>수리시간2-1</b></p>
                    </td>
                    <td colspan="3" style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>수리시간2-2</b></p>
                    </td>
                </tr>

                <tr>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>평균수리시간</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>지연건수</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>전체건수</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>평균수리시간</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>지연건수</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>전체건수</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>평균수리시간</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>지연건수</b></p>
                    </td>
                    <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$thColor.'" class="">
                        <p style="text-align: center;"><b>전체건수</b></p>
                    </td>
                </tr>
    ';

    if (!empty($rows)) { 
        // 2. 출력
        $no = 1;
        foreach ($rows as $row) { 

            $asColor1 = '';
            $asColor2 = '';
            $asColor3 = '';
            $tdColor = '';

            // total 행 디자인    
            $isTotalRow = ($row['OUT_DATE'] == 'TOTAL');
            if($isTotalRow){
                $asColor1 = 'background-color: #EAF4F3;';
                $asColor2 = 'background-color: #EAF4F3;';
                $asColor3 = 'background-color: #EAF4F3;';
                $tdColor = 'background-color: #EAF4F3;';
            }else{
                $tdColor = 'background-color: #ffffff;';
            }
            
            $asTime1 = floatval($row['AVG_ASTIME1']);
            if ($asTime1 > 144) { $asColor1 = 'background-color: rgba(255, 0, 0, 0.5);';
            } elseif ($asTime1 > 120) { $asColor1 = 'background-color: rgba(255, 165, 0, 0.5);';
            } elseif ($asTime1 > 96) { $asColor1 = 'background-color: rgba(255, 255, 0, 0.5);'; }
            $asTime2 = floatval($row['AVG_ASTIME2']);
            if ($asTime2 > 96) { $asColor2 = 'background-color: rgba(255, 0, 0, 0.5);';
            } elseif ($asTime2 > 72) { $asColor2 = 'background-color: rgba(255, 165, 0, 0.5);';
            } elseif ($asTime2 > 48) { $asColor2 = 'background-color: rgba(255, 255, 0, 0.5);'; }
            $asTime3 = floatval($row['AVG_ASTIME3']);
            if ($asTime3 > 96) { $asColor3 = 'background-color: rgba(255, 0, 0, 0.5);';
            } elseif ($asTime3 > 72) { $asColor3 = 'background-color: rgba(255, 165, 0, 0.5);';
            } elseif ($asTime3 > 48) { $asColor3 = 'background-color: rgba(255, 255, 0, 0.5);'; }

            $content .= 
            '<tr style="' . ($isTotalRow ? 'font-weight:bold;' : '') . '"> 
                <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 50px; height: 24px; '.$tdColor.'" class="">
                    <p style="text-align: center;">' . $no++ . '</p>
                </td>
                <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 174px; height: 24px; '.$tdColor.'" class="">
                    <p style="text-align: center;">' . htmlspecialchars($row['OUT_DATE']) . '</p>
                </td>
                <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 174px; height: 24px;'.$asColor1.'" class="">
                    <p style="text-align: center;">' . htmlspecialchars($row['AVG_ASTIME1_STR']) . '</p>
                </td>
                <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$tdColor.'" class="">
                    <p style="text-align: center;">' . htmlspecialchars($row['DELAY_CNT1']) . '</p>
                </td>
                <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$tdColor.'" class="">
                    <p style="text-align: center;">' . htmlspecialchars($row['AS_CNT1']) . '</p>
                </td>
                
                <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 174px; height: 24px; '.$asColor2.'" class="">
                    <p style="text-align: center;">' . htmlspecialchars($row['AVG_ASTIME2_STR']) . '</p>
                </td>
                <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$tdColor.'" class="">
                    <p style="text-align: center;">' . htmlspecialchars($row['DELAY_CNT2']) . '</p>
                </td>
                <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$tdColor.'" class="">
                    <p style="text-align: center;">' . htmlspecialchars($row['AS_CNT2']) . '</p>
                </td>

                <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 174px; height: 24px;'.$asColor3.'" class="">
                    <p style="text-align: center;">' . htmlspecialchars($row['AVG_ASTIME3_STR']) . '</p>
                </td>
                <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$tdColor.'" class="">
                    <p style="text-align: center;">' . htmlspecialchars($row['DELAY_CNT3']) . '</p>
                </td>
                <td style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; '.$tdColor.'" class="">
                    <p style="text-align: center;">' . htmlspecialchars($row['AS_CNT3']) . '</p>
                </td>
            </tr>';
        }
    } else {
        $content .= '<tr>
                    <td  colspan="11" style="border-width: 0px 0px 1px 1px; border-bottom-style: solid; border-left-style: solid; border-bottom-color: rgb(204, 204, 204); border-left-color: rgb(204, 204, 204); border-image: initial; border-top-style: initial; border-top-color: initial; border-right-style: initial; border-right-color: initial; width: 112px; height: 24px; background-color: rgb(255, 255, 255);" class="">
                        <p style="text-align: center;">해당 항목이 없습니다.</p>
                    </td> 
                </tr>
        ';
    }

    $content .= '
            </tbody>
        </table>
        <br>
        <br>
        
        <table style="width: 100%; border-top: 2px solid #005bac; margin-top: 30px; ">
        <tr>
            <td style="padding: 16px 0; text-align: center;">
            <span style="font-size: 14px; color: #333;">
                <strong>A/S 수리 지연현황 상세 리포트</strong>는 아래 링크에서 확인하실 수 있습니다.
            </span>
            </td>
        </tr>
        <tr>
            <td style="text-align: center; padding-bottom:  20px;">
            <a href="https://erp.posbank.com/?pageCode=MTI4ODk" target="_blank"
                style="background-color: #005bac; color: #fff; padding: 10px 20px; text-decoration: none; font-weight: bold; border-radius: 6px; font-size: 14px;">
                리포트 바로가기
            </a>
            </td>
        </tr>
        </table>
    ';



    //수신자
    $arr_ST = alarm_st("E096");
    // $arr_ST = ["101141"];
    $intra_Params = [];
    $intra_Params['gu'] = "erp";
    $intra_Params['from'] = "";
    $intra_Params['to'] = $arr_ST;
    $intra_Params['title'] = "[온라인AS접수] 수리 지연 현황 알림";
    $intra_Params['content'] = $content;
    $rs3 = intra_send_curl($intra_Params);
 
    echo "수신자              : " . json_encode($intra_Params['to'], true) . "\n";
    echo "인트라넷 업무연락 rs : " . json_encode($rs3, true) . "\n";

	##### End. 2025.06.25 KSH(101141) 온라인AS접수 지연 현황 알림 (업무연락, 구글 웹훅)
	###########################################
    
	// ## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(86400, "온라인AS접수 지연 현황 알림 (업무연락, 구글 웹훅)");

	echo date("Y-m-d H:i:s")." - 끝\n";


?>
