<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Calculation_Functions</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_DIV0" title="DIV0 :: DIV0"><span class="description">DIV0</span><pre>DIV0()</pre></a></li>
<li class="method public "><a href="#method_DUMMY" title="DUMMY :: DUMMY"><span class="description">DUMMY</span><pre>DUMMY()</pre></a></li>
<li class="method public "><a href="#method_ERROR_TYPE" title="ERROR_TYPE :: ERROR_TYPE"><span class="description">ERROR_TYPE</span><pre>ERROR_TYPE()</pre></a></li>
<li class="method public "><a href="#method_IS_BLANK" title="IS_BLANK :: IS_BLANK"><span class="description">IS_BLANK</span><pre>IS_BLANK()</pre></a></li>
<li class="method public "><a href="#method_IS_ERR" title="IS_ERR :: IS_ERR"><span class="description">IS_ERR</span><pre>IS_ERR()</pre></a></li>
<li class="method public "><a href="#method_IS_ERROR" title="IS_ERROR :: IS_ERROR"><span class="description">IS_ERROR</span><pre>IS_ERROR()</pre></a></li>
<li class="method public "><a href="#method_IS_EVEN" title="IS_EVEN :: IS_EVEN"><span class="description">IS_EVEN</span><pre>IS_EVEN()</pre></a></li>
<li class="method public "><a href="#method_IS_LOGICAL" title="IS_LOGICAL :: IS_LOGICAL"><span class="description">IS_LOGICAL</span><pre>IS_LOGICAL()</pre></a></li>
<li class="method public "><a href="#method_IS_NA" title="IS_NA :: IS_NA"><span class="description">IS_NA</span><pre>IS_NA()</pre></a></li>
<li class="method public "><a href="#method_IS_NONTEXT" title="IS_NONTEXT :: IS_NONTEXT"><span class="description">IS_NONTEXT</span><pre>IS_NONTEXT()</pre></a></li>
<li class="method public "><a href="#method_IS_NUMBER" title="IS_NUMBER :: IS_NUMBER"><span class="description">IS_NUMBER</span><pre>IS_NUMBER()</pre></a></li>
<li class="method public "><a href="#method_IS_ODD" title="IS_ODD :: IS_ODD"><span class="description">IS_ODD</span><pre>IS_ODD()</pre></a></li>
<li class="method public "><a href="#method_IS_TEXT" title="IS_TEXT :: IS_TEXT"><span class="description">IS_TEXT</span><pre>IS_TEXT()</pre></a></li>
<li class="method public "><a href="#method_N" title="N :: N"><span class="description">N</span><pre>N()</pre></a></li>
<li class="method public "><a href="#method_NA" title="NA :: NA"><span class="description">NA</span><pre>NA()</pre></a></li>
<li class="method public "><a href="#method_NAME" title="NAME :: NAME"><span class="description">NAME</span><pre>NAME()</pre></a></li>
<li class="method public "><a href="#method_NULL" title="NULL :: NULL"><span class="description">NULL</span><pre>NULL()</pre></a></li>
<li class="method public "><a href="#method_NaN" title="NaN :: NaN"><span class="description">NaN</span><pre>NaN()</pre></a></li>
<li class="method public "><a href="#method_REF" title="REF :: REF"><span class="description">REF</span><pre>REF()</pre></a></li>
<li class="method public "><a href="#method_TYPE" title="TYPE :: TYPE"><span class="description">TYPE</span><pre>TYPE()</pre></a></li>
<li class="method public "><a href="#method_VALUE" title="VALUE :: VALUE"><span class="description">VALUE</span><pre>VALUE()</pre></a></li>
<li class="method public "><a href="#method_VERSION" title="VERSION :: VERSION"><span class="description">VERSION</span><pre>VERSION()</pre></a></li>
<li class="method public "><a href="#method__ifCondition" title="_ifCondition :: "><span class="description">_ifCondition()
        </span><pre>_ifCondition()</pre></a></li>
<li class="method public "><a href="#method_flattenArray" title="flattenArray :: Convert a multi-dimensional array to a simple 1-dimensional array"><span class="description">Convert a multi-dimensional array to a simple 1-dimensional array</span><pre>flattenArray()</pre></a></li>
<li class="method public "><a href="#method_flattenArrayIndexed" title="flattenArrayIndexed :: Convert a multi-dimensional array to a simple 1-dimensional array, but retain an element of indexing"><span class="description">Convert a multi-dimensional array to a simple 1-dimensional array, but retain an element of indexing</span><pre>flattenArrayIndexed()</pre></a></li>
<li class="method public "><a href="#method_flattenSingleValue" title="flattenSingleValue :: Convert an array to a single scalar value by extracting the first element"><span class="description">Convert an array to a single scalar value by extracting the first element</span><pre>flattenSingleValue()</pre></a></li>
<li class="method public "><a href="#method_getCompatibilityMode" title="getCompatibilityMode :: Return the current Compatibility Mode"><span class="description">Return the current Compatibility Mode</span><pre>getCompatibilityMode()</pre></a></li>
<li class="method public "><a href="#method_getReturnDateType" title="getReturnDateType :: Return the current Return Date Format for functions that return a date/time (Excel, PHP Serialized Numeric or PHP Object)"><span class="description">Return the current Return Date Format for functions that return a date/time (Excel, PHP Serialized Numeric or PHP Object)</span><pre>getReturnDateType()</pre></a></li>
<li class="method public "><a href="#method_isCellValue" title="isCellValue :: "><span class="description">isCellValue()
        </span><pre>isCellValue()</pre></a></li>
<li class="method public "><a href="#method_isMatrixValue" title="isMatrixValue :: "><span class="description">isMatrixValue()
        </span><pre>isMatrixValue()</pre></a></li>
<li class="method public "><a href="#method_isValue" title="isValue :: "><span class="description">isValue()
        </span><pre>isValue()</pre></a></li>
<li class="method public "><a href="#method_setCompatibilityMode" title="setCompatibilityMode :: Set the Compatibility Mode"><span class="description">Set the Compatibility Mode</span><pre>setCompatibilityMode()</pre></a></li>
<li class="method public "><a href="#method_setReturnDateType" title="setReturnDateType :: Set the Return Date Format used by functions that return a date/time (Excel, PHP Serialized Numeric or PHP Object)"><span class="description">Set the Return Date Format used by functions that return a date/time (Excel, PHP Serialized Numeric or PHP Object)</span><pre>setReturnDateType()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header protected">» Protected
                    <ul>
<li class="property protected "><a href="#property_ReturnDateType" title="$ReturnDateType :: Data Type to use when returning date values"><span class="description"></span><pre>$ReturnDateType</pre></a></li>
<li class="property protected "><a href="#property__errorCodes" title="$_errorCodes :: List of error codes"><span class="description"></span><pre>$_errorCodes</pre></a></li>
<li class="property protected "><a href="#property_compatibilityMode" title="$compatibilityMode :: Compatibility mode to use for error checking and responses"><span class="description"></span><pre>$compatibilityMode</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_COMPATIBILITY_EXCEL" title="COMPATIBILITY_EXCEL :: constants"><span class="description">constants</span><pre>COMPATIBILITY_EXCEL</pre></a></li>
<li class="constant  "><a href="#constant_COMPATIBILITY_GNUMERIC" title="COMPATIBILITY_GNUMERIC :: "><span class="description">COMPATIBILITY_GNUMERIC</span><pre>COMPATIBILITY_GNUMERIC</pre></a></li>
<li class="constant  "><a href="#constant_COMPATIBILITY_OPENOFFICE" title="COMPATIBILITY_OPENOFFICE :: "><span class="description">COMPATIBILITY_OPENOFFICE</span><pre>COMPATIBILITY_OPENOFFICE</pre></a></li>
<li class="constant  "><a href="#constant_RETURNDATE_EXCEL" title="RETURNDATE_EXCEL :: "><span class="description">RETURNDATE_EXCEL</span><pre>RETURNDATE_EXCEL</pre></a></li>
<li class="constant  "><a href="#constant_RETURNDATE_PHP_NUMERIC" title="RETURNDATE_PHP_NUMERIC :: "><span class="description">RETURNDATE_PHP_NUMERIC</span><pre>RETURNDATE_PHP_NUMERIC</pre></a></li>
<li class="constant  "><a href="#constant_RETURNDATE_PHP_OBJECT" title="RETURNDATE_PHP_OBJECT :: "><span class="description">RETURNDATE_PHP_OBJECT</span><pre>RETURNDATE_PHP_OBJECT</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Calculation_Functions"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Calculation_Functions.html">PHPExcel_Calculation_Functions</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Calculation_Functions</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Calculation.html">PHPExcel_Calculation</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_DIV0"></a><div class="element clickable method public method_DIV0" data-toggle="collapse" data-target=".method_DIV0 .collapse">
<h2>DIV0</h2>
<pre>DIV0() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Error Returns</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>#Not Yet Implemented</div>
</div></div>
</div>
<a id="method_DUMMY"></a><div class="element clickable method public method_DUMMY" data-toggle="collapse" data-target=".method_DUMMY .collapse">
<h2>DUMMY</h2>
<pre>DUMMY() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Error Returns</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>#Not Yet Implemented</div>
</div></div>
</div>
<a id="method_ERROR_TYPE"></a><div class="element clickable method public method_ERROR_TYPE" data-toggle="collapse" data-target=".method_ERROR_TYPE .collapse">
<h2>ERROR_TYPE</h2>
<pre>ERROR_TYPE(mixed $value) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>Value to check</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_IS_BLANK"></a><div class="element clickable method public method_IS_BLANK" data-toggle="collapse" data-target=".method_IS_BLANK .collapse">
<h2>IS_BLANK</h2>
<pre>IS_BLANK(mixed $value) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>Value to check</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_IS_ERR"></a><div class="element clickable method public method_IS_ERR" data-toggle="collapse" data-target=".method_IS_ERR .collapse">
<h2>IS_ERR</h2>
<pre>IS_ERR(mixed $value) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>Value to check</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_IS_ERROR"></a><div class="element clickable method public method_IS_ERROR" data-toggle="collapse" data-target=".method_IS_ERROR .collapse">
<h2>IS_ERROR</h2>
<pre>IS_ERROR(mixed $value) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>Value to check</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_IS_EVEN"></a><div class="element clickable method public method_IS_EVEN" data-toggle="collapse" data-target=".method_IS_EVEN .collapse">
<h2>IS_EVEN</h2>
<pre>IS_EVEN(mixed $value) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>Value to check</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_IS_LOGICAL"></a><div class="element clickable method public method_IS_LOGICAL" data-toggle="collapse" data-target=".method_IS_LOGICAL .collapse">
<h2>IS_LOGICAL</h2>
<pre>IS_LOGICAL(mixed $value) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>Value to check</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_IS_NA"></a><div class="element clickable method public method_IS_NA" data-toggle="collapse" data-target=".method_IS_NA .collapse">
<h2>IS_NA</h2>
<pre>IS_NA(mixed $value) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>Value to check</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_IS_NONTEXT"></a><div class="element clickable method public method_IS_NONTEXT" data-toggle="collapse" data-target=".method_IS_NONTEXT .collapse">
<h2>IS_NONTEXT</h2>
<pre>IS_NONTEXT(mixed $value) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>Value to check</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_IS_NUMBER"></a><div class="element clickable method public method_IS_NUMBER" data-toggle="collapse" data-target=".method_IS_NUMBER .collapse">
<h2>IS_NUMBER</h2>
<pre>IS_NUMBER(mixed $value) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>Value to check</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_IS_ODD"></a><div class="element clickable method public method_IS_ODD" data-toggle="collapse" data-target=".method_IS_ODD .collapse">
<h2>IS_ODD</h2>
<pre>IS_ODD(mixed $value) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>Value to check</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_IS_TEXT"></a><div class="element clickable method public method_IS_TEXT" data-toggle="collapse" data-target=".method_IS_TEXT .collapse">
<h2>IS_TEXT</h2>
<pre>IS_TEXT(mixed $value) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>Value to check</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_N"></a><div class="element clickable method public method_N" data-toggle="collapse" data-target=".method_N .collapse">
<h2>N</h2>
<pre>N(\value $value) : \number</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns a value converted to a number</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>\value</code><p>The value you want converted</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>\number</code>N converts values listed in the following table
	If value is or refers to N returns
	A number			That number
	A date				The serial number of that date
	TRUE				1
	FALSE				0
	An error value		The error value
	Anything else		0</div>
</div></div>
</div>
<a id="method_NA"></a><div class="element clickable method public method_NA" data-toggle="collapse" data-target=".method_NA .collapse">
<h2>NA</h2>
<pre>NA() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Excel Function:
    =NA()</p>

<p>Returns the error value #N/A
    #N/A is the error value that means "no value is available."</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Logical Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>#N/A!</div>
</div></div>
</div>
<a id="method_NAME"></a><div class="element clickable method public method_NAME" data-toggle="collapse" data-target=".method_NAME .collapse">
<h2>NAME</h2>
<pre>NAME() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the error value #NAME?</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Error Returns</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>#NAME?</div>
</div></div>
</div>
<a id="method_NULL"></a><div class="element clickable method public method_NULL" data-toggle="collapse" data-target=".method_NULL .collapse">
<h2>NULL</h2>
<pre>NULL() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the error value #NULL!</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Error Returns</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>#NULL!</div>
</div></div>
</div>
<a id="method_NaN"></a><div class="element clickable method public method_NaN" data-toggle="collapse" data-target=".method_NaN .collapse">
<h2>NaN</h2>
<pre>NaN() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the error value #NUM!</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Error Returns</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>#NUM!</div>
</div></div>
</div>
<a id="method_REF"></a><div class="element clickable method public method_REF" data-toggle="collapse" data-target=".method_REF .collapse">
<h2>REF</h2>
<pre>REF() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the error value #REF!</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Error Returns</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>#REF!</div>
</div></div>
</div>
<a id="method_TYPE"></a><div class="element clickable method public method_TYPE" data-toggle="collapse" data-target=".method_TYPE .collapse">
<h2>TYPE</h2>
<pre>TYPE(\value $value) : \number</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns a number that identifies the type of a value</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>\value</code><p>The value you want tested</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>\number</code>N converts values listed in the following table
	If value is or refers to N returns
	A number			1
	Text				2
	Logical Value		4
	An error value		16
	Array or Matrix		64</div>
</div></div>
</div>
<a id="method_VALUE"></a><div class="element clickable method public method_VALUE" data-toggle="collapse" data-target=".method_VALUE .collapse">
<h2>VALUE</h2>
<pre>VALUE() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the error value #VALUE!</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Error Returns</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>#VALUE!</div>
</div></div>
</div>
<a id="method_VERSION"></a><div class="element clickable method public method_VERSION" data-toggle="collapse" data-target=".method_VERSION .collapse">
<h2>VERSION</h2>
<pre>VERSION() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Version information</div>
</div></div>
</div>
<a id="method__ifCondition"></a><div class="element clickable method public method__ifCondition" data-toggle="collapse" data-target=".method__ifCondition .collapse">
<h2>_ifCondition()
        </h2>
<pre>_ifCondition($condition) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$condition</h4></div>
</div></div>
</div>
<a id="method_flattenArray"></a><div class="element clickable method public method_flattenArray" data-toggle="collapse" data-target=".method_flattenArray .collapse">
<h2>Convert a multi-dimensional array to a simple 1-dimensional array</h2>
<pre>flattenArray(array $array) : array</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$array</h4>
<code>array</code><p>Array to be flattened</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>array</code>Flattened array</div>
</div></div>
</div>
<a id="method_flattenArrayIndexed"></a><div class="element clickable method public method_flattenArrayIndexed" data-toggle="collapse" data-target=".method_flattenArrayIndexed .collapse">
<h2>Convert a multi-dimensional array to a simple 1-dimensional array, but retain an element of indexing</h2>
<pre>flattenArrayIndexed(array $array) : array</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$array</h4>
<code>array</code><p>Array to be flattened</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>array</code>Flattened array</div>
</div></div>
</div>
<a id="method_flattenSingleValue"></a><div class="element clickable method public method_flattenSingleValue" data-toggle="collapse" data-target=".method_flattenSingleValue .collapse">
<h2>Convert an array to a single scalar value by extracting the first element</h2>
<pre>flattenSingleValue(mixed $value) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code><p>Array or scalar value</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>mixed</code></div>
</div></div>
</div>
<a id="method_getCompatibilityMode"></a><div class="element clickable method public method_getCompatibilityMode" data-toggle="collapse" data-target=".method_getCompatibilityMode .collapse">
<h2>Return the current Compatibility Mode</h2>
<pre>getCompatibilityMode() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Function Configuration</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Compatibility Mode
						Possible Return values are:
							PHPExcel_Calculation_Functions::COMPATIBILITY_EXCEL			'Excel'
							PHPExcel_Calculation_Functions::COMPATIBILITY_GNUMERIC		'Gnumeric'
							PHPExcel_Calculation_Functions::COMPATIBILITY_OPENOFFICE	'OpenOfficeCalc'</div>
</div></div>
</div>
<a id="method_getReturnDateType"></a><div class="element clickable method public method_getReturnDateType" data-toggle="collapse" data-target=".method_getReturnDateType .collapse">
<h2>Return the current Return Date Format for functions that return a date/time (Excel, PHP Serialized Numeric or PHP Object)</h2>
<pre>getReturnDateType() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Function Configuration</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Return Date Format
						Possible Return values are:
							PHPExcel_Calculation_Functions::RETURNDATE_PHP_NUMERIC		'P'
							PHPExcel_Calculation_Functions::RETURNDATE_PHP_OBJECT		'O'
							PHPExcel_Calculation_Functions::RETURNDATE_EXCEL			'E'</div>
</div></div>
</div>
<a id="method_isCellValue"></a><div class="element clickable method public method_isCellValue" data-toggle="collapse" data-target=".method_isCellValue .collapse">
<h2>isCellValue()
        </h2>
<pre>isCellValue($idx) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$idx</h4></div>
</div></div>
</div>
<a id="method_isMatrixValue"></a><div class="element clickable method public method_isMatrixValue" data-toggle="collapse" data-target=".method_isMatrixValue .collapse">
<h2>isMatrixValue()
        </h2>
<pre>isMatrixValue($idx) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$idx</h4></div>
</div></div>
</div>
<a id="method_isValue"></a><div class="element clickable method public method_isValue" data-toggle="collapse" data-target=".method_isValue .collapse">
<h2>isValue()
        </h2>
<pre>isValue($idx) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$idx</h4></div>
</div></div>
</div>
<a id="method_setCompatibilityMode"></a><div class="element clickable method public method_setCompatibilityMode" data-toggle="collapse" data-target=".method_setCompatibilityMode .collapse">
<h2>Set the Compatibility Mode</h2>
<pre>setCompatibilityMode(string $compatibilityMode) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Function Configuration</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$compatibilityMode</h4>
<code>string</code><p>Compatibility Mode
                                            Permitted values are:
                                                PHPExcel_Calculation_Functions::COMPATIBILITY_EXCEL         'Excel'
                                                PHPExcel_Calculation_Functions::COMPATIBILITY_GNUMERIC      'Gnumeric'
                                                PHPExcel_Calculation_Functions::COMPATIBILITY_OPENOFFICE    'OpenOfficeCalc'</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>boolean</code>(Success or Failure)</div>
</div></div>
</div>
<a id="method_setReturnDateType"></a><div class="element clickable method public method_setReturnDateType" data-toggle="collapse" data-target=".method_setReturnDateType .collapse">
<h2>Set the Return Date Format used by functions that return a date/time (Excel, PHP Serialized Numeric or PHP Object)</h2>
<pre>setReturnDateType(string $returnDateType) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Function Configuration</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$returnDateType</h4>
<code>string</code><p>Return Date Format
                                            Permitted values are:
                                                PHPExcel_Calculation_Functions::RETURNDATE_PHP_NUMERIC      'P'
                                                PHPExcel_Calculation_Functions::RETURNDATE_PHP_OBJECT       'O'
                                                PHPExcel_Calculation_Functions::RETURNDATE_EXCEL            'E'</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>boolean</code>Success or failure</div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property_ReturnDateType"> </a><div class="element clickable property protected property_ReturnDateType" data-toggle="collapse" data-target=".property_ReturnDateType .collapse">
<h2></h2>
<pre>$ReturnDateType : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>private</td>
</tr></table>
</div></div>
</div>
<a id="property__errorCodes"> </a><div class="element clickable property protected property__errorCodes" data-toggle="collapse" data-target=".property__errorCodes .collapse">
<h2></h2>
<pre>$_errorCodes : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>private</td>
</tr></table>
</div></div>
</div>
<a id="property_compatibilityMode"> </a><div class="element clickable property protected property_compatibilityMode" data-toggle="collapse" data-target=".property_compatibilityMode .collapse">
<h2></h2>
<pre>$compatibilityMode : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>private</td>
</tr></table>
</div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_COMPATIBILITY_EXCEL"> </a><div class="element clickable constant  constant_COMPATIBILITY_EXCEL" data-toggle="collapse" data-target=".constant_COMPATIBILITY_EXCEL .collapse">
<h2>constants</h2>
<pre>COMPATIBILITY_EXCEL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COMPATIBILITY_GNUMERIC"> </a><div class="element clickable constant  constant_COMPATIBILITY_GNUMERIC" data-toggle="collapse" data-target=".constant_COMPATIBILITY_GNUMERIC .collapse">
<h2>COMPATIBILITY_GNUMERIC</h2>
<pre>COMPATIBILITY_GNUMERIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COMPATIBILITY_OPENOFFICE"> </a><div class="element clickable constant  constant_COMPATIBILITY_OPENOFFICE" data-toggle="collapse" data-target=".constant_COMPATIBILITY_OPENOFFICE .collapse">
<h2>COMPATIBILITY_OPENOFFICE</h2>
<pre>COMPATIBILITY_OPENOFFICE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_RETURNDATE_EXCEL"> </a><div class="element clickable constant  constant_RETURNDATE_EXCEL" data-toggle="collapse" data-target=".constant_RETURNDATE_EXCEL .collapse">
<h2>RETURNDATE_EXCEL</h2>
<pre>RETURNDATE_EXCEL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_RETURNDATE_PHP_NUMERIC"> </a><div class="element clickable constant  constant_RETURNDATE_PHP_NUMERIC" data-toggle="collapse" data-target=".constant_RETURNDATE_PHP_NUMERIC .collapse">
<h2>RETURNDATE_PHP_NUMERIC</h2>
<pre>RETURNDATE_PHP_NUMERIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_RETURNDATE_PHP_OBJECT"> </a><div class="element clickable constant  constant_RETURNDATE_PHP_OBJECT" data-toggle="collapse" data-target=".constant_RETURNDATE_PHP_OBJECT .collapse">
<h2>RETURNDATE_PHP_OBJECT</h2>
<pre>RETURNDATE_PHP_OBJECT </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:34Z.<br></footer></div>
</div>
</body>
</html>
