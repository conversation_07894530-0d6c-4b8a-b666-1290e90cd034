<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_Font</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_calculateColumnWidth" title="calculateColumnWidth :: Calculate an (approximate) OpenXML column width, based on font size and text contained"><span class="description">Calculate an (approximate) OpenXML column width, based on font size and text contained</span><pre>calculateColumnWidth()</pre></a></li>
<li class="method public "><a href="#method_centimeterSizeToPixels" title="centimeterSizeToPixels :: Calculate an (approximate) pixel size, based on centimeter size"><span class="description">Calculate an (approximate) pixel size, based on centimeter size</span><pre>centimeterSizeToPixels()</pre></a></li>
<li class="method public "><a href="#method_fontSizeToPixels" title="fontSizeToPixels :: Calculate an (approximate) pixel size, based on a font points size"><span class="description">Calculate an (approximate) pixel size, based on a font points size</span><pre>fontSizeToPixels()</pre></a></li>
<li class="method public "><a href="#method_getAutoSizeMethod" title="getAutoSizeMethod :: Get autoSize method"><span class="description">Get autoSize method</span><pre>getAutoSizeMethod()</pre></a></li>
<li class="method public "><a href="#method_getCharsetFromFontName" title="getCharsetFromFontName :: Returns the associated charset for the font name."><span class="description">Returns the associated charset for the font name.</span><pre>getCharsetFromFontName()</pre></a></li>
<li class="method public "><a href="#method_getDefaultColumnWidthByFont" title="getDefaultColumnWidthByFont :: Get the effective column width for columns without a column dimension or column with width -1
For example, for Calibri 11 this is 9.140625 (64 px)"><span class="description">Get the effective column width for columns without a column dimension or column with width -1
For example, for Calibri 11 this is 9.140625 (64 px)</span><pre>getDefaultColumnWidthByFont()</pre></a></li>
<li class="method public "><a href="#method_getDefaultRowHeightByFont" title="getDefaultRowHeightByFont :: Get the effective row height for rows without a row dimension or rows with height -1
For example, for Calibri 11 this is 15 points"><span class="description">Get the effective row height for rows without a row dimension or rows with height -1
For example, for Calibri 11 this is 15 points</span><pre>getDefaultRowHeightByFont()</pre></a></li>
<li class="method public "><a href="#method_getTextWidthPixelsApprox" title="getTextWidthPixelsApprox :: Get approximate width in pixels for a string of text in a certain font at a certain rotation angle"><span class="description">Get approximate width in pixels for a string of text in a certain font at a certain rotation angle</span><pre>getTextWidthPixelsApprox()</pre></a></li>
<li class="method public "><a href="#method_getTextWidthPixelsExact" title="getTextWidthPixelsExact :: Get GD text width in pixels for a string of text in a certain font at a certain rotation angle"><span class="description">Get GD text width in pixels for a string of text in a certain font at a certain rotation angle</span><pre>getTextWidthPixelsExact()</pre></a></li>
<li class="method public "><a href="#method_getTrueTypeFontFileFromFont" title="getTrueTypeFontFileFromFont :: Returns the font path given the font"><span class="description">Returns the font path given the font</span><pre>getTrueTypeFontFileFromFont()</pre></a></li>
<li class="method public "><a href="#method_getTrueTypeFontPath" title="getTrueTypeFontPath :: Get the path to the folder containing .ttf files."><span class="description">Get the path to the folder containing .ttf files.</span><pre>getTrueTypeFontPath()</pre></a></li>
<li class="method public "><a href="#method_inchSizeToPixels" title="inchSizeToPixels :: Calculate an (approximate) pixel size, based on inch size"><span class="description">Calculate an (approximate) pixel size, based on inch size</span><pre>inchSizeToPixels()</pre></a></li>
<li class="method public "><a href="#method_setAutoSizeMethod" title="setAutoSizeMethod :: Set autoSize method"><span class="description">Set autoSize method</span><pre>setAutoSizeMethod()</pre></a></li>
<li class="method public "><a href="#method_setTrueTypeFontPath" title="setTrueTypeFontPath :: Set the path to the folder containing .ttf files."><span class="description">Set the path to the folder containing .ttf files.</span><pre>setTrueTypeFontPath()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul><li class="property public "><a href="#property_defaultColumnWidths" title="$defaultColumnWidths :: How wide is a default column for a given default font and size?
Empirical data found by inspecting real Excel files and reading off the pixel width
in Microsoft Office Excel 2007."><span class="description"></span><pre>$defaultColumnWidths</pre></a></li></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__autoSizeMethods" title="$_autoSizeMethods :: "><span class="description"></span><pre>$_autoSizeMethods</pre></a></li>
<li class="property private "><a href="#property_autoSizeMethod" title="$autoSizeMethod :: AutoSize method"><span class="description"></span><pre>$autoSizeMethod</pre></a></li>
<li class="property private "><a href="#property_trueTypeFontPath" title="$trueTypeFontPath :: Path to folder containing TrueType font .ttf files"><span class="description"></span><pre>$trueTypeFontPath</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_ARIAL" title="ARIAL :: Font filenames"><span class="description">Font filenames</span><pre>ARIAL</pre></a></li>
<li class="constant  "><a href="#constant_ARIAL_BOLD" title="ARIAL_BOLD :: "><span class="description">ARIAL_BOLD</span><pre>ARIAL_BOLD</pre></a></li>
<li class="constant  "><a href="#constant_ARIAL_BOLD_ITALIC" title="ARIAL_BOLD_ITALIC :: "><span class="description">ARIAL_BOLD_ITALIC</span><pre>ARIAL_BOLD_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_ARIAL_ITALIC" title="ARIAL_ITALIC :: "><span class="description">ARIAL_ITALIC</span><pre>ARIAL_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_AUTOSIZE_METHOD_APPROX" title="AUTOSIZE_METHOD_APPROX :: "><span class="description">AUTOSIZE_METHOD_APPROX</span><pre>AUTOSIZE_METHOD_APPROX</pre></a></li>
<li class="constant  "><a href="#constant_AUTOSIZE_METHOD_EXACT" title="AUTOSIZE_METHOD_EXACT :: "><span class="description">AUTOSIZE_METHOD_EXACT</span><pre>AUTOSIZE_METHOD_EXACT</pre></a></li>
<li class="constant  "><a href="#constant_CALIBRI" title="CALIBRI :: "><span class="description">CALIBRI</span><pre>CALIBRI</pre></a></li>
<li class="constant  "><a href="#constant_CALIBRI_BOLD" title="CALIBRI_BOLD :: "><span class="description">CALIBRI_BOLD</span><pre>CALIBRI_BOLD</pre></a></li>
<li class="constant  "><a href="#constant_CALIBRI_BOLD_ITALIC" title="CALIBRI_BOLD_ITALIC :: "><span class="description">CALIBRI_BOLD_ITALIC</span><pre>CALIBRI_BOLD_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_CALIBRI_ITALIC" title="CALIBRI_ITALIC :: "><span class="description">CALIBRI_ITALIC</span><pre>CALIBRI_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_ARABIC" title="CHARSET_ANSI_ARABIC :: "><span class="description">CHARSET_ANSI_ARABIC</span><pre>CHARSET_ANSI_ARABIC</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_BALTIC" title="CHARSET_ANSI_BALTIC :: "><span class="description">CHARSET_ANSI_BALTIC</span><pre>CHARSET_ANSI_BALTIC</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_CHINESE_SIMIPLIFIED" title="CHARSET_ANSI_CHINESE_SIMIPLIFIED :: "><span class="description">CHARSET_ANSI_CHINESE_SIMIPLIFIED</span><pre>CHARSET_ANSI_CHINESE_SIMIPLIFIED</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_CHINESE_TRADITIONAL" title="CHARSET_ANSI_CHINESE_TRADITIONAL :: "><span class="description">CHARSET_ANSI_CHINESE_TRADITIONAL</span><pre>CHARSET_ANSI_CHINESE_TRADITIONAL</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_CYRILLIC" title="CHARSET_ANSI_CYRILLIC :: "><span class="description">CHARSET_ANSI_CYRILLIC</span><pre>CHARSET_ANSI_CYRILLIC</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_GREEK" title="CHARSET_ANSI_GREEK :: "><span class="description">CHARSET_ANSI_GREEK</span><pre>CHARSET_ANSI_GREEK</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_HEBREW" title="CHARSET_ANSI_HEBREW :: "><span class="description">CHARSET_ANSI_HEBREW</span><pre>CHARSET_ANSI_HEBREW</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_JAPANESE_SHIFTJIS" title="CHARSET_ANSI_JAPANESE_SHIFTJIS :: "><span class="description">CHARSET_ANSI_JAPANESE_SHIFTJIS</span><pre>CHARSET_ANSI_JAPANESE_SHIFTJIS</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_KOREAN_HANGUL" title="CHARSET_ANSI_KOREAN_HANGUL :: "><span class="description">CHARSET_ANSI_KOREAN_HANGUL</span><pre>CHARSET_ANSI_KOREAN_HANGUL</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_KOREAN_JOHAB" title="CHARSET_ANSI_KOREAN_JOHAB :: "><span class="description">CHARSET_ANSI_KOREAN_JOHAB</span><pre>CHARSET_ANSI_KOREAN_JOHAB</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_LATIN" title="CHARSET_ANSI_LATIN :: Character set codes used by BIFF5-8 in Font records"><span class="description">Character set codes used by BIFF5-8 in Font records</span><pre>CHARSET_ANSI_LATIN</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_LATIN_II" title="CHARSET_ANSI_LATIN_II :: "><span class="description">CHARSET_ANSI_LATIN_II</span><pre>CHARSET_ANSI_LATIN_II</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_THAI" title="CHARSET_ANSI_THAI :: "><span class="description">CHARSET_ANSI_THAI</span><pre>CHARSET_ANSI_THAI</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_TURKISH" title="CHARSET_ANSI_TURKISH :: "><span class="description">CHARSET_ANSI_TURKISH</span><pre>CHARSET_ANSI_TURKISH</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_ANSI_VIETNAMESE" title="CHARSET_ANSI_VIETNAMESE :: "><span class="description">CHARSET_ANSI_VIETNAMESE</span><pre>CHARSET_ANSI_VIETNAMESE</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_APPLE_ROMAN" title="CHARSET_APPLE_ROMAN :: "><span class="description">CHARSET_APPLE_ROMAN</span><pre>CHARSET_APPLE_ROMAN</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_OEM_LATIN_I" title="CHARSET_OEM_LATIN_I :: "><span class="description">CHARSET_OEM_LATIN_I</span><pre>CHARSET_OEM_LATIN_I</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_SYMBOL" title="CHARSET_SYMBOL :: "><span class="description">CHARSET_SYMBOL</span><pre>CHARSET_SYMBOL</pre></a></li>
<li class="constant  "><a href="#constant_CHARSET_SYSTEM_DEFAULT" title="CHARSET_SYSTEM_DEFAULT :: "><span class="description">CHARSET_SYSTEM_DEFAULT</span><pre>CHARSET_SYSTEM_DEFAULT</pre></a></li>
<li class="constant  "><a href="#constant_COMIC_SANS_MS" title="COMIC_SANS_MS :: "><span class="description">COMIC_SANS_MS</span><pre>COMIC_SANS_MS</pre></a></li>
<li class="constant  "><a href="#constant_COMIC_SANS_MS_BOLD" title="COMIC_SANS_MS_BOLD :: "><span class="description">COMIC_SANS_MS_BOLD</span><pre>COMIC_SANS_MS_BOLD</pre></a></li>
<li class="constant  "><a href="#constant_COURIER_NEW" title="COURIER_NEW :: "><span class="description">COURIER_NEW</span><pre>COURIER_NEW</pre></a></li>
<li class="constant  "><a href="#constant_COURIER_NEW_BOLD" title="COURIER_NEW_BOLD :: "><span class="description">COURIER_NEW_BOLD</span><pre>COURIER_NEW_BOLD</pre></a></li>
<li class="constant  "><a href="#constant_COURIER_NEW_BOLD_ITALIC" title="COURIER_NEW_BOLD_ITALIC :: "><span class="description">COURIER_NEW_BOLD_ITALIC</span><pre>COURIER_NEW_BOLD_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_COURIER_NEW_ITALIC" title="COURIER_NEW_ITALIC :: "><span class="description">COURIER_NEW_ITALIC</span><pre>COURIER_NEW_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_GEORGIA" title="GEORGIA :: "><span class="description">GEORGIA</span><pre>GEORGIA</pre></a></li>
<li class="constant  "><a href="#constant_GEORGIA_BOLD" title="GEORGIA_BOLD :: "><span class="description">GEORGIA_BOLD</span><pre>GEORGIA_BOLD</pre></a></li>
<li class="constant  "><a href="#constant_GEORGIA_BOLD_ITALIC" title="GEORGIA_BOLD_ITALIC :: "><span class="description">GEORGIA_BOLD_ITALIC</span><pre>GEORGIA_BOLD_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_GEORGIA_ITALIC" title="GEORGIA_ITALIC :: "><span class="description">GEORGIA_ITALIC</span><pre>GEORGIA_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_IMPACT" title="IMPACT :: "><span class="description">IMPACT</span><pre>IMPACT</pre></a></li>
<li class="constant  "><a href="#constant_LIBERATION_SANS" title="LIBERATION_SANS :: "><span class="description">LIBERATION_SANS</span><pre>LIBERATION_SANS</pre></a></li>
<li class="constant  "><a href="#constant_LIBERATION_SANS_BOLD" title="LIBERATION_SANS_BOLD :: "><span class="description">LIBERATION_SANS_BOLD</span><pre>LIBERATION_SANS_BOLD</pre></a></li>
<li class="constant  "><a href="#constant_LIBERATION_SANS_BOLD_ITALIC" title="LIBERATION_SANS_BOLD_ITALIC :: "><span class="description">LIBERATION_SANS_BOLD_ITALIC</span><pre>LIBERATION_SANS_BOLD_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_LIBERATION_SANS_ITALIC" title="LIBERATION_SANS_ITALIC :: "><span class="description">LIBERATION_SANS_ITALIC</span><pre>LIBERATION_SANS_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_LUCIDA_CONSOLE" title="LUCIDA_CONSOLE :: "><span class="description">LUCIDA_CONSOLE</span><pre>LUCIDA_CONSOLE</pre></a></li>
<li class="constant  "><a href="#constant_LUCIDA_SANS_UNICODE" title="LUCIDA_SANS_UNICODE :: "><span class="description">LUCIDA_SANS_UNICODE</span><pre>LUCIDA_SANS_UNICODE</pre></a></li>
<li class="constant  "><a href="#constant_MICROSOFT_SANS_SERIF" title="MICROSOFT_SANS_SERIF :: "><span class="description">MICROSOFT_SANS_SERIF</span><pre>MICROSOFT_SANS_SERIF</pre></a></li>
<li class="constant  "><a href="#constant_PALATINO_LINOTYPE" title="PALATINO_LINOTYPE :: "><span class="description">PALATINO_LINOTYPE</span><pre>PALATINO_LINOTYPE</pre></a></li>
<li class="constant  "><a href="#constant_PALATINO_LINOTYPE_BOLD" title="PALATINO_LINOTYPE_BOLD :: "><span class="description">PALATINO_LINOTYPE_BOLD</span><pre>PALATINO_LINOTYPE_BOLD</pre></a></li>
<li class="constant  "><a href="#constant_PALATINO_LINOTYPE_BOLD_ITALIC" title="PALATINO_LINOTYPE_BOLD_ITALIC :: "><span class="description">PALATINO_LINOTYPE_BOLD_ITALIC</span><pre>PALATINO_LINOTYPE_BOLD_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_PALATINO_LINOTYPE_ITALIC" title="PALATINO_LINOTYPE_ITALIC :: "><span class="description">PALATINO_LINOTYPE_ITALIC</span><pre>PALATINO_LINOTYPE_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_SYMBOL" title="SYMBOL :: "><span class="description">SYMBOL</span><pre>SYMBOL</pre></a></li>
<li class="constant  "><a href="#constant_TAHOMA" title="TAHOMA :: "><span class="description">TAHOMA</span><pre>TAHOMA</pre></a></li>
<li class="constant  "><a href="#constant_TAHOMA_BOLD" title="TAHOMA_BOLD :: "><span class="description">TAHOMA_BOLD</span><pre>TAHOMA_BOLD</pre></a></li>
<li class="constant  "><a href="#constant_TIMES_NEW_ROMAN" title="TIMES_NEW_ROMAN :: "><span class="description">TIMES_NEW_ROMAN</span><pre>TIMES_NEW_ROMAN</pre></a></li>
<li class="constant  "><a href="#constant_TIMES_NEW_ROMAN_BOLD" title="TIMES_NEW_ROMAN_BOLD :: "><span class="description">TIMES_NEW_ROMAN_BOLD</span><pre>TIMES_NEW_ROMAN_BOLD</pre></a></li>
<li class="constant  "><a href="#constant_TIMES_NEW_ROMAN_BOLD_ITALIC" title="TIMES_NEW_ROMAN_BOLD_ITALIC :: "><span class="description">TIMES_NEW_ROMAN_BOLD_ITALIC</span><pre>TIMES_NEW_ROMAN_BOLD_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_TIMES_NEW_ROMAN_ITALIC" title="TIMES_NEW_ROMAN_ITALIC :: "><span class="description">TIMES_NEW_ROMAN_ITALIC</span><pre>TIMES_NEW_ROMAN_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_TREBUCHET_MS" title="TREBUCHET_MS :: "><span class="description">TREBUCHET_MS</span><pre>TREBUCHET_MS</pre></a></li>
<li class="constant  "><a href="#constant_TREBUCHET_MS_BOLD" title="TREBUCHET_MS_BOLD :: "><span class="description">TREBUCHET_MS_BOLD</span><pre>TREBUCHET_MS_BOLD</pre></a></li>
<li class="constant  "><a href="#constant_TREBUCHET_MS_BOLD_ITALIC" title="TREBUCHET_MS_BOLD_ITALIC :: "><span class="description">TREBUCHET_MS_BOLD_ITALIC</span><pre>TREBUCHET_MS_BOLD_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_TREBUCHET_MS_ITALIC" title="TREBUCHET_MS_ITALIC :: "><span class="description">TREBUCHET_MS_ITALIC</span><pre>TREBUCHET_MS_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_VERDANA" title="VERDANA :: "><span class="description">VERDANA</span><pre>VERDANA</pre></a></li>
<li class="constant  "><a href="#constant_VERDANA_BOLD" title="VERDANA_BOLD :: "><span class="description">VERDANA_BOLD</span><pre>VERDANA_BOLD</pre></a></li>
<li class="constant  "><a href="#constant_VERDANA_BOLD_ITALIC" title="VERDANA_BOLD_ITALIC :: "><span class="description">VERDANA_BOLD_ITALIC</span><pre>VERDANA_BOLD_ITALIC</pre></a></li>
<li class="constant  "><a href="#constant_VERDANA_ITALIC" title="VERDANA_ITALIC :: "><span class="description">VERDANA_ITALIC</span><pre>VERDANA_ITALIC</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_Font"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_Font.html">PHPExcel_Shared_Font</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Shared_Font</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Shared.html">PHPExcel_Shared</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_calculateColumnWidth"></a><div class="element clickable method public method_calculateColumnWidth" data-toggle="collapse" data-target=".method_calculateColumnWidth .collapse">
<h2>Calculate an (approximate) OpenXML column width, based on font size and text contained</h2>
<pre>calculateColumnWidth(\PHPExcel_Style_Font $font, <a href="../classes/PHPExcel_RichText.html">\PHPExcel_RichText</a> | string $cellText, integer $rotation, \PHPExcel_Style_Font $defaultFont) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$font</h4>
<code><a href="../classes/PHPExcel_Style_Font.html">\PHPExcel_Style_Font</a></code><p>Font object</p></div>
<div class="subelement argument">
<h4>$cellText</h4>
<code><a href="../classes/PHPExcel_RichText.html">\PHPExcel_RichText</a></code><code>string</code><p>Text to calculate width</p></div>
<div class="subelement argument">
<h4>$rotation</h4>
<code>integer</code><p>Rotation angle</p></div>
<div class="subelement argument">
<h4>$defaultFont</h4>
<code><a href="../classes/PHPExcel_Style_Font.html">\PHPExcel_Style_Font</a></code><code>NULL</code><p>Font object</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>Column width</div>
</div></div>
</div>
<a id="method_centimeterSizeToPixels"></a><div class="element clickable method public method_centimeterSizeToPixels" data-toggle="collapse" data-target=".method_centimeterSizeToPixels .collapse">
<h2>Calculate an (approximate) pixel size, based on centimeter size</h2>
<pre>centimeterSizeToPixels(int $sizeInCm) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$sizeInCm</h4>
<code>int</code><p>Font size (in centimeters)</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Size (in pixels)</div>
</div></div>
</div>
<a id="method_fontSizeToPixels"></a><div class="element clickable method public method_fontSizeToPixels" data-toggle="collapse" data-target=".method_fontSizeToPixels .collapse">
<h2>Calculate an (approximate) pixel size, based on a font points size</h2>
<pre>fontSizeToPixels(int $fontSizeInPoints) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$fontSizeInPoints</h4>
<code>int</code><p>Font size (in points)</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Font size (in pixels)</div>
</div></div>
</div>
<a id="method_getAutoSizeMethod"></a><div class="element clickable method public method_getAutoSizeMethod" data-toggle="collapse" data-target=".method_getAutoSizeMethod .collapse">
<h2>Get autoSize method</h2>
<pre>getAutoSizeMethod() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getCharsetFromFontName"></a><div class="element clickable method public method_getCharsetFromFontName" data-toggle="collapse" data-target=".method_getCharsetFromFontName .collapse">
<h2>Returns the associated charset for the font name.</h2>
<pre>getCharsetFromFontName(string $name) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$name</h4>
<code>string</code><p>Font name</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Character set code</div>
</div></div>
</div>
<a id="method_getDefaultColumnWidthByFont"></a><div class="element clickable method public method_getDefaultColumnWidthByFont" data-toggle="collapse" data-target=".method_getDefaultColumnWidthByFont .collapse">
<h2>Get the effective column width for columns without a column dimension or column with width -1
For example, for Calibri 11 this is 9.140625 (64 px)</h2>
<pre>getDefaultColumnWidthByFont(\PHPExcel_Style_Font $font, boolean $pPixels) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$font</h4>
<code><a href="../classes/PHPExcel_Style_Font.html">\PHPExcel_Style_Font</a></code><p>The workbooks default font</p></div>
<div class="subelement argument">
<h4>$pPixels</h4>
<code>boolean</code><p>true = return column width in pixels, false = return in OOXML units</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Column width</div>
</div></div>
</div>
<a id="method_getDefaultRowHeightByFont"></a><div class="element clickable method public method_getDefaultRowHeightByFont" data-toggle="collapse" data-target=".method_getDefaultRowHeightByFont .collapse">
<h2>Get the effective row height for rows without a row dimension or rows with height -1
For example, for Calibri 11 this is 15 points</h2>
<pre>getDefaultRowHeightByFont(\PHPExcel_Style_Font $font) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$font</h4>
<code><a href="../classes/PHPExcel_Style_Font.html">\PHPExcel_Style_Font</a></code><p>The workbooks default font</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>Row height in points</div>
</div></div>
</div>
<a id="method_getTextWidthPixelsApprox"></a><div class="element clickable method public method_getTextWidthPixelsApprox" data-toggle="collapse" data-target=".method_getTextWidthPixelsApprox .collapse">
<h2>Get approximate width in pixels for a string of text in a certain font at a certain rotation angle</h2>
<pre>getTextWidthPixelsApprox(string $columnText, \PHPExcel_Style_Font $font, int $rotation) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$columnText</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$font</h4>
<code><a href="../classes/PHPExcel_Style_Font.html">\PHPExcel_Style_Font</a></code>
</div>
<div class="subelement argument">
<h4>$rotation</h4>
<code>int</code>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Text width in pixels (no padding added)</div>
</div></div>
</div>
<a id="method_getTextWidthPixelsExact"></a><div class="element clickable method public method_getTextWidthPixelsExact" data-toggle="collapse" data-target=".method_getTextWidthPixelsExact .collapse">
<h2>Get GD text width in pixels for a string of text in a certain font at a certain rotation angle</h2>
<pre>getTextWidthPixelsExact(string $text, \PHPExcel_Style_Font $font, int $rotation) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$text</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$font</h4>
<code><a href="../classes/PHPExcel_Style_Font.html">\PHPExcel_Style_Font</a></code>
</div>
<div class="subelement argument">
<h4>$rotation</h4>
<code>int</code>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_getTrueTypeFontFileFromFont"></a><div class="element clickable method public method_getTrueTypeFontFileFromFont" data-toggle="collapse" data-target=".method_getTrueTypeFontFileFromFont .collapse">
<h2>Returns the font path given the font</h2>
<pre>getTrueTypeFontFileFromFont(<a href="../classes/PHPExcel_Style_Font.html">\PHPExcel_Style_Font</a> $font) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$font</h4>
<code><a href="../classes/PHPExcel_Style_Font.html">\PHPExcel_Style_Font</a></code>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Path to TrueType font file</div>
</div></div>
</div>
<a id="method_getTrueTypeFontPath"></a><div class="element clickable method public method_getTrueTypeFontPath" data-toggle="collapse" data-target=".method_getTrueTypeFontPath .collapse">
<h2>Get the path to the folder containing .ttf files.</h2>
<pre>getTrueTypeFontPath() : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_inchSizeToPixels"></a><div class="element clickable method public method_inchSizeToPixels" data-toggle="collapse" data-target=".method_inchSizeToPixels .collapse">
<h2>Calculate an (approximate) pixel size, based on inch size</h2>
<pre>inchSizeToPixels(int $sizeInInch) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$sizeInInch</h4>
<code>int</code><p>Font size (in inch)</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Size (in pixels)</div>
</div></div>
</div>
<a id="method_setAutoSizeMethod"></a><div class="element clickable method public method_setAutoSizeMethod" data-toggle="collapse" data-target=".method_setAutoSizeMethod .collapse">
<h2>Set autoSize method</h2>
<pre>setAutoSizeMethod(string $pValue) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>boolean</code>Success or failure</div>
</div></div>
</div>
<a id="method_setTrueTypeFontPath"></a><div class="element clickable method public method_setTrueTypeFontPath" data-toggle="collapse" data-target=".method_setTrueTypeFontPath .collapse">
<h2>Set the path to the folder containing .ttf files.</h2>
<pre>setTrueTypeFontPath(string $pValue) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>There should be a trailing slash.
Typical locations on variout some platforms:</p>

<ul>
    <li>C:/Windows/Fonts/</li>
    <li>/usr/share/fonts/truetype/</li>
    <li>~/.fonts/</li>
</ul></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property_defaultColumnWidths"> </a><div class="element clickable property public property_defaultColumnWidths" data-toggle="collapse" data-target=".property_defaultColumnWidths .collapse">
<h2></h2>
<pre>$defaultColumnWidths : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__autoSizeMethods"> </a><div class="element clickable property private property__autoSizeMethods" data-toggle="collapse" data-target=".property__autoSizeMethods .collapse">
<h2></h2>
<pre>$_autoSizeMethods </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_autoSizeMethod"> </a><div class="element clickable property private property_autoSizeMethod" data-toggle="collapse" data-target=".property_autoSizeMethod .collapse">
<h2></h2>
<pre>$autoSizeMethod : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_trueTypeFontPath"> </a><div class="element clickable property private property_trueTypeFontPath" data-toggle="collapse" data-target=".property_trueTypeFontPath .collapse">
<h2></h2>
<pre>$trueTypeFontPath : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_ARIAL"> </a><div class="element clickable constant  constant_ARIAL" data-toggle="collapse" data-target=".constant_ARIAL .collapse">
<h2>Font filenames</h2>
<pre>ARIAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_ARIAL_BOLD"> </a><div class="element clickable constant  constant_ARIAL_BOLD" data-toggle="collapse" data-target=".constant_ARIAL_BOLD .collapse">
<h2>ARIAL_BOLD</h2>
<pre>ARIAL_BOLD </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_ARIAL_BOLD_ITALIC"> </a><div class="element clickable constant  constant_ARIAL_BOLD_ITALIC" data-toggle="collapse" data-target=".constant_ARIAL_BOLD_ITALIC .collapse">
<h2>ARIAL_BOLD_ITALIC</h2>
<pre>ARIAL_BOLD_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_ARIAL_ITALIC"> </a><div class="element clickable constant  constant_ARIAL_ITALIC" data-toggle="collapse" data-target=".constant_ARIAL_ITALIC .collapse">
<h2>ARIAL_ITALIC</h2>
<pre>ARIAL_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_AUTOSIZE_METHOD_APPROX"> </a><div class="element clickable constant  constant_AUTOSIZE_METHOD_APPROX" data-toggle="collapse" data-target=".constant_AUTOSIZE_METHOD_APPROX .collapse">
<h2>AUTOSIZE_METHOD_APPROX</h2>
<pre>AUTOSIZE_METHOD_APPROX </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_AUTOSIZE_METHOD_EXACT"> </a><div class="element clickable constant  constant_AUTOSIZE_METHOD_EXACT" data-toggle="collapse" data-target=".constant_AUTOSIZE_METHOD_EXACT .collapse">
<h2>AUTOSIZE_METHOD_EXACT</h2>
<pre>AUTOSIZE_METHOD_EXACT </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CALIBRI"> </a><div class="element clickable constant  constant_CALIBRI" data-toggle="collapse" data-target=".constant_CALIBRI .collapse">
<h2>CALIBRI</h2>
<pre>CALIBRI </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CALIBRI_BOLD"> </a><div class="element clickable constant  constant_CALIBRI_BOLD" data-toggle="collapse" data-target=".constant_CALIBRI_BOLD .collapse">
<h2>CALIBRI_BOLD</h2>
<pre>CALIBRI_BOLD </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CALIBRI_BOLD_ITALIC"> </a><div class="element clickable constant  constant_CALIBRI_BOLD_ITALIC" data-toggle="collapse" data-target=".constant_CALIBRI_BOLD_ITALIC .collapse">
<h2>CALIBRI_BOLD_ITALIC</h2>
<pre>CALIBRI_BOLD_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CALIBRI_ITALIC"> </a><div class="element clickable constant  constant_CALIBRI_ITALIC" data-toggle="collapse" data-target=".constant_CALIBRI_ITALIC .collapse">
<h2>CALIBRI_ITALIC</h2>
<pre>CALIBRI_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_ARABIC"> </a><div class="element clickable constant  constant_CHARSET_ANSI_ARABIC" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_ARABIC .collapse">
<h2>CHARSET_ANSI_ARABIC</h2>
<pre>CHARSET_ANSI_ARABIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_BALTIC"> </a><div class="element clickable constant  constant_CHARSET_ANSI_BALTIC" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_BALTIC .collapse">
<h2>CHARSET_ANSI_BALTIC</h2>
<pre>CHARSET_ANSI_BALTIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_CHINESE_SIMIPLIFIED"> </a><div class="element clickable constant  constant_CHARSET_ANSI_CHINESE_SIMIPLIFIED" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_CHINESE_SIMIPLIFIED .collapse">
<h2>CHARSET_ANSI_CHINESE_SIMIPLIFIED</h2>
<pre>CHARSET_ANSI_CHINESE_SIMIPLIFIED </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_CHINESE_TRADITIONAL"> </a><div class="element clickable constant  constant_CHARSET_ANSI_CHINESE_TRADITIONAL" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_CHINESE_TRADITIONAL .collapse">
<h2>CHARSET_ANSI_CHINESE_TRADITIONAL</h2>
<pre>CHARSET_ANSI_CHINESE_TRADITIONAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_CYRILLIC"> </a><div class="element clickable constant  constant_CHARSET_ANSI_CYRILLIC" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_CYRILLIC .collapse">
<h2>CHARSET_ANSI_CYRILLIC</h2>
<pre>CHARSET_ANSI_CYRILLIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_GREEK"> </a><div class="element clickable constant  constant_CHARSET_ANSI_GREEK" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_GREEK .collapse">
<h2>CHARSET_ANSI_GREEK</h2>
<pre>CHARSET_ANSI_GREEK </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_HEBREW"> </a><div class="element clickable constant  constant_CHARSET_ANSI_HEBREW" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_HEBREW .collapse">
<h2>CHARSET_ANSI_HEBREW</h2>
<pre>CHARSET_ANSI_HEBREW </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_JAPANESE_SHIFTJIS"> </a><div class="element clickable constant  constant_CHARSET_ANSI_JAPANESE_SHIFTJIS" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_JAPANESE_SHIFTJIS .collapse">
<h2>CHARSET_ANSI_JAPANESE_SHIFTJIS</h2>
<pre>CHARSET_ANSI_JAPANESE_SHIFTJIS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_KOREAN_HANGUL"> </a><div class="element clickable constant  constant_CHARSET_ANSI_KOREAN_HANGUL" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_KOREAN_HANGUL .collapse">
<h2>CHARSET_ANSI_KOREAN_HANGUL</h2>
<pre>CHARSET_ANSI_KOREAN_HANGUL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_KOREAN_JOHAB"> </a><div class="element clickable constant  constant_CHARSET_ANSI_KOREAN_JOHAB" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_KOREAN_JOHAB .collapse">
<h2>CHARSET_ANSI_KOREAN_JOHAB</h2>
<pre>CHARSET_ANSI_KOREAN_JOHAB </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_LATIN"> </a><div class="element clickable constant  constant_CHARSET_ANSI_LATIN" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_LATIN .collapse">
<h2>Character set codes used by BIFF5-8 in Font records</h2>
<pre>CHARSET_ANSI_LATIN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_LATIN_II"> </a><div class="element clickable constant  constant_CHARSET_ANSI_LATIN_II" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_LATIN_II .collapse">
<h2>CHARSET_ANSI_LATIN_II</h2>
<pre>CHARSET_ANSI_LATIN_II </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_THAI"> </a><div class="element clickable constant  constant_CHARSET_ANSI_THAI" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_THAI .collapse">
<h2>CHARSET_ANSI_THAI</h2>
<pre>CHARSET_ANSI_THAI </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_TURKISH"> </a><div class="element clickable constant  constant_CHARSET_ANSI_TURKISH" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_TURKISH .collapse">
<h2>CHARSET_ANSI_TURKISH</h2>
<pre>CHARSET_ANSI_TURKISH </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_ANSI_VIETNAMESE"> </a><div class="element clickable constant  constant_CHARSET_ANSI_VIETNAMESE" data-toggle="collapse" data-target=".constant_CHARSET_ANSI_VIETNAMESE .collapse">
<h2>CHARSET_ANSI_VIETNAMESE</h2>
<pre>CHARSET_ANSI_VIETNAMESE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_APPLE_ROMAN"> </a><div class="element clickable constant  constant_CHARSET_APPLE_ROMAN" data-toggle="collapse" data-target=".constant_CHARSET_APPLE_ROMAN .collapse">
<h2>CHARSET_APPLE_ROMAN</h2>
<pre>CHARSET_APPLE_ROMAN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_OEM_LATIN_I"> </a><div class="element clickable constant  constant_CHARSET_OEM_LATIN_I" data-toggle="collapse" data-target=".constant_CHARSET_OEM_LATIN_I .collapse">
<h2>CHARSET_OEM_LATIN_I</h2>
<pre>CHARSET_OEM_LATIN_I </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_SYMBOL"> </a><div class="element clickable constant  constant_CHARSET_SYMBOL" data-toggle="collapse" data-target=".constant_CHARSET_SYMBOL .collapse">
<h2>CHARSET_SYMBOL</h2>
<pre>CHARSET_SYMBOL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CHARSET_SYSTEM_DEFAULT"> </a><div class="element clickable constant  constant_CHARSET_SYSTEM_DEFAULT" data-toggle="collapse" data-target=".constant_CHARSET_SYSTEM_DEFAULT .collapse">
<h2>CHARSET_SYSTEM_DEFAULT</h2>
<pre>CHARSET_SYSTEM_DEFAULT </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COMIC_SANS_MS"> </a><div class="element clickable constant  constant_COMIC_SANS_MS" data-toggle="collapse" data-target=".constant_COMIC_SANS_MS .collapse">
<h2>COMIC_SANS_MS</h2>
<pre>COMIC_SANS_MS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COMIC_SANS_MS_BOLD"> </a><div class="element clickable constant  constant_COMIC_SANS_MS_BOLD" data-toggle="collapse" data-target=".constant_COMIC_SANS_MS_BOLD .collapse">
<h2>COMIC_SANS_MS_BOLD</h2>
<pre>COMIC_SANS_MS_BOLD </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COURIER_NEW"> </a><div class="element clickable constant  constant_COURIER_NEW" data-toggle="collapse" data-target=".constant_COURIER_NEW .collapse">
<h2>COURIER_NEW</h2>
<pre>COURIER_NEW </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COURIER_NEW_BOLD"> </a><div class="element clickable constant  constant_COURIER_NEW_BOLD" data-toggle="collapse" data-target=".constant_COURIER_NEW_BOLD .collapse">
<h2>COURIER_NEW_BOLD</h2>
<pre>COURIER_NEW_BOLD </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COURIER_NEW_BOLD_ITALIC"> </a><div class="element clickable constant  constant_COURIER_NEW_BOLD_ITALIC" data-toggle="collapse" data-target=".constant_COURIER_NEW_BOLD_ITALIC .collapse">
<h2>COURIER_NEW_BOLD_ITALIC</h2>
<pre>COURIER_NEW_BOLD_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COURIER_NEW_ITALIC"> </a><div class="element clickable constant  constant_COURIER_NEW_ITALIC" data-toggle="collapse" data-target=".constant_COURIER_NEW_ITALIC .collapse">
<h2>COURIER_NEW_ITALIC</h2>
<pre>COURIER_NEW_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_GEORGIA"> </a><div class="element clickable constant  constant_GEORGIA" data-toggle="collapse" data-target=".constant_GEORGIA .collapse">
<h2>GEORGIA</h2>
<pre>GEORGIA </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_GEORGIA_BOLD"> </a><div class="element clickable constant  constant_GEORGIA_BOLD" data-toggle="collapse" data-target=".constant_GEORGIA_BOLD .collapse">
<h2>GEORGIA_BOLD</h2>
<pre>GEORGIA_BOLD </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_GEORGIA_BOLD_ITALIC"> </a><div class="element clickable constant  constant_GEORGIA_BOLD_ITALIC" data-toggle="collapse" data-target=".constant_GEORGIA_BOLD_ITALIC .collapse">
<h2>GEORGIA_BOLD_ITALIC</h2>
<pre>GEORGIA_BOLD_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_GEORGIA_ITALIC"> </a><div class="element clickable constant  constant_GEORGIA_ITALIC" data-toggle="collapse" data-target=".constant_GEORGIA_ITALIC .collapse">
<h2>GEORGIA_ITALIC</h2>
<pre>GEORGIA_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_IMPACT"> </a><div class="element clickable constant  constant_IMPACT" data-toggle="collapse" data-target=".constant_IMPACT .collapse">
<h2>IMPACT</h2>
<pre>IMPACT </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_LIBERATION_SANS"> </a><div class="element clickable constant  constant_LIBERATION_SANS" data-toggle="collapse" data-target=".constant_LIBERATION_SANS .collapse">
<h2>LIBERATION_SANS</h2>
<pre>LIBERATION_SANS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_LIBERATION_SANS_BOLD"> </a><div class="element clickable constant  constant_LIBERATION_SANS_BOLD" data-toggle="collapse" data-target=".constant_LIBERATION_SANS_BOLD .collapse">
<h2>LIBERATION_SANS_BOLD</h2>
<pre>LIBERATION_SANS_BOLD </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_LIBERATION_SANS_BOLD_ITALIC"> </a><div class="element clickable constant  constant_LIBERATION_SANS_BOLD_ITALIC" data-toggle="collapse" data-target=".constant_LIBERATION_SANS_BOLD_ITALIC .collapse">
<h2>LIBERATION_SANS_BOLD_ITALIC</h2>
<pre>LIBERATION_SANS_BOLD_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_LIBERATION_SANS_ITALIC"> </a><div class="element clickable constant  constant_LIBERATION_SANS_ITALIC" data-toggle="collapse" data-target=".constant_LIBERATION_SANS_ITALIC .collapse">
<h2>LIBERATION_SANS_ITALIC</h2>
<pre>LIBERATION_SANS_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_LUCIDA_CONSOLE"> </a><div class="element clickable constant  constant_LUCIDA_CONSOLE" data-toggle="collapse" data-target=".constant_LUCIDA_CONSOLE .collapse">
<h2>LUCIDA_CONSOLE</h2>
<pre>LUCIDA_CONSOLE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_LUCIDA_SANS_UNICODE"> </a><div class="element clickable constant  constant_LUCIDA_SANS_UNICODE" data-toggle="collapse" data-target=".constant_LUCIDA_SANS_UNICODE .collapse">
<h2>LUCIDA_SANS_UNICODE</h2>
<pre>LUCIDA_SANS_UNICODE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_MICROSOFT_SANS_SERIF"> </a><div class="element clickable constant  constant_MICROSOFT_SANS_SERIF" data-toggle="collapse" data-target=".constant_MICROSOFT_SANS_SERIF .collapse">
<h2>MICROSOFT_SANS_SERIF</h2>
<pre>MICROSOFT_SANS_SERIF </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_PALATINO_LINOTYPE"> </a><div class="element clickable constant  constant_PALATINO_LINOTYPE" data-toggle="collapse" data-target=".constant_PALATINO_LINOTYPE .collapse">
<h2>PALATINO_LINOTYPE</h2>
<pre>PALATINO_LINOTYPE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_PALATINO_LINOTYPE_BOLD"> </a><div class="element clickable constant  constant_PALATINO_LINOTYPE_BOLD" data-toggle="collapse" data-target=".constant_PALATINO_LINOTYPE_BOLD .collapse">
<h2>PALATINO_LINOTYPE_BOLD</h2>
<pre>PALATINO_LINOTYPE_BOLD </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_PALATINO_LINOTYPE_BOLD_ITALIC"> </a><div class="element clickable constant  constant_PALATINO_LINOTYPE_BOLD_ITALIC" data-toggle="collapse" data-target=".constant_PALATINO_LINOTYPE_BOLD_ITALIC .collapse">
<h2>PALATINO_LINOTYPE_BOLD_ITALIC</h2>
<pre>PALATINO_LINOTYPE_BOLD_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_PALATINO_LINOTYPE_ITALIC"> </a><div class="element clickable constant  constant_PALATINO_LINOTYPE_ITALIC" data-toggle="collapse" data-target=".constant_PALATINO_LINOTYPE_ITALIC .collapse">
<h2>PALATINO_LINOTYPE_ITALIC</h2>
<pre>PALATINO_LINOTYPE_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_SYMBOL"> </a><div class="element clickable constant  constant_SYMBOL" data-toggle="collapse" data-target=".constant_SYMBOL .collapse">
<h2>SYMBOL</h2>
<pre>SYMBOL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TAHOMA"> </a><div class="element clickable constant  constant_TAHOMA" data-toggle="collapse" data-target=".constant_TAHOMA .collapse">
<h2>TAHOMA</h2>
<pre>TAHOMA </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TAHOMA_BOLD"> </a><div class="element clickable constant  constant_TAHOMA_BOLD" data-toggle="collapse" data-target=".constant_TAHOMA_BOLD .collapse">
<h2>TAHOMA_BOLD</h2>
<pre>TAHOMA_BOLD </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TIMES_NEW_ROMAN"> </a><div class="element clickable constant  constant_TIMES_NEW_ROMAN" data-toggle="collapse" data-target=".constant_TIMES_NEW_ROMAN .collapse">
<h2>TIMES_NEW_ROMAN</h2>
<pre>TIMES_NEW_ROMAN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TIMES_NEW_ROMAN_BOLD"> </a><div class="element clickable constant  constant_TIMES_NEW_ROMAN_BOLD" data-toggle="collapse" data-target=".constant_TIMES_NEW_ROMAN_BOLD .collapse">
<h2>TIMES_NEW_ROMAN_BOLD</h2>
<pre>TIMES_NEW_ROMAN_BOLD </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TIMES_NEW_ROMAN_BOLD_ITALIC"> </a><div class="element clickable constant  constant_TIMES_NEW_ROMAN_BOLD_ITALIC" data-toggle="collapse" data-target=".constant_TIMES_NEW_ROMAN_BOLD_ITALIC .collapse">
<h2>TIMES_NEW_ROMAN_BOLD_ITALIC</h2>
<pre>TIMES_NEW_ROMAN_BOLD_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TIMES_NEW_ROMAN_ITALIC"> </a><div class="element clickable constant  constant_TIMES_NEW_ROMAN_ITALIC" data-toggle="collapse" data-target=".constant_TIMES_NEW_ROMAN_ITALIC .collapse">
<h2>TIMES_NEW_ROMAN_ITALIC</h2>
<pre>TIMES_NEW_ROMAN_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TREBUCHET_MS"> </a><div class="element clickable constant  constant_TREBUCHET_MS" data-toggle="collapse" data-target=".constant_TREBUCHET_MS .collapse">
<h2>TREBUCHET_MS</h2>
<pre>TREBUCHET_MS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TREBUCHET_MS_BOLD"> </a><div class="element clickable constant  constant_TREBUCHET_MS_BOLD" data-toggle="collapse" data-target=".constant_TREBUCHET_MS_BOLD .collapse">
<h2>TREBUCHET_MS_BOLD</h2>
<pre>TREBUCHET_MS_BOLD </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TREBUCHET_MS_BOLD_ITALIC"> </a><div class="element clickable constant  constant_TREBUCHET_MS_BOLD_ITALIC" data-toggle="collapse" data-target=".constant_TREBUCHET_MS_BOLD_ITALIC .collapse">
<h2>TREBUCHET_MS_BOLD_ITALIC</h2>
<pre>TREBUCHET_MS_BOLD_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TREBUCHET_MS_ITALIC"> </a><div class="element clickable constant  constant_TREBUCHET_MS_ITALIC" data-toggle="collapse" data-target=".constant_TREBUCHET_MS_ITALIC .collapse">
<h2>TREBUCHET_MS_ITALIC</h2>
<pre>TREBUCHET_MS_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_VERDANA"> </a><div class="element clickable constant  constant_VERDANA" data-toggle="collapse" data-target=".constant_VERDANA .collapse">
<h2>VERDANA</h2>
<pre>VERDANA </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_VERDANA_BOLD"> </a><div class="element clickable constant  constant_VERDANA_BOLD" data-toggle="collapse" data-target=".constant_VERDANA_BOLD .collapse">
<h2>VERDANA_BOLD</h2>
<pre>VERDANA_BOLD </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_VERDANA_BOLD_ITALIC"> </a><div class="element clickable constant  constant_VERDANA_BOLD_ITALIC" data-toggle="collapse" data-target=".constant_VERDANA_BOLD_ITALIC .collapse">
<h2>VERDANA_BOLD_ITALIC</h2>
<pre>VERDANA_BOLD_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_VERDANA_ITALIC"> </a><div class="element clickable constant  constant_VERDANA_ITALIC" data-toggle="collapse" data-target=".constant_VERDANA_ITALIC .collapse">
<h2>VERDANA_ITALIC</h2>
<pre>VERDANA_ITALIC </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:36Z.<br></footer></div>
</div>
</body>
</html>
