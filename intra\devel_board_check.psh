#!/usr/local/bin/php -q
<?
//devel_board_check.psh
# 피드백 업무연락 중 확인 안한 건 알림
# 매일 9시 실행
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

$dbconn = new DBController($db['posbank_intra']);
if(empty($dbconn->success)) {
	echo "[" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패입니다.";
}

$dbconn2 = new DBController($db['chrome_push']);
if(empty($dbconn2->success)) {
	echo "[" . $db['chrome_push']['host'] . "] 데이터베이스 연결 실패입니다.";
}
/**********************************************************/

$SQL = "SELECT count(*) FROM HOLIDAY_DATA WHERE HDATE=CURDATE()";
$holiday_chk = $dbconn->query_one($SQL);
if(!$holiday_chk){
	// 토,일요일
	if(in_array(date('w'),array("0","6"))) $holiday_chk = '1';
}

if($holiday_chk){
	echo "휴무일\n";
}else{
	echo date("Y-m-d H:i:s")." - 전산개발요청 알림\n";

	// 금일 휴가자
	$SQL = "SELECT
					group_concat(DISTINCT A.PRS_NUM)
				FROM APPV_HOLI A
					LEFT JOIN PRS_MASTER B ON A.PRS_NUM=B.PRS_NUM
				WHERE
					A.GU2='-'
					AND CURDATE() BETWEEN A.HOLI_SDATE AND A.HOLI_EDATE
					AND A.VIEW_YN<>'N'";
	$prs_num = $dbconn->query_one($SQL);
	$arr_prs_num = [];
	if($prs_num) $arr_prs_num = explode(",", $prs_num);

	// 검수대기 처리자에게 전송
	$SQL = "SELECT concat(HDATE,'_',HNO) HID,TITLE,IPRSNUM,CONTENT 
				FROM devel_board 
				WHERE STATE='5' ";
	if($arr_prs_num){
		$SQL .= " and IPRSNUM not in ('".implode("','",$arr_prs_num)."') ";
	}
	//$SQL .= "AND HDATE='20230407' and HNO='0001' ";
	$arrRow = $dbconn->query_rows($SQL);
	if($arrRow){
		foreach($arrRow as $key => $row) {
			//$content = strip_tags($row['CONTENT']);
			//$content = str_replace("&nbsp;", " ", $content);
			//$content = htmlspecialchars_decode($content);

			$content = html_tags($row['CONTENT']);
			$content = mb_substr($content,0,500,'utf-8');

			$MESSAGE = [];
			$MESSAGE[] = ["검수대기중인 전산개발요청건이 있습니다."];
			$MESSAGE[] = ["<b>요청번호 : </b><font color=#555555>".$row['HID']."</font>"];
			$MESSAGE[] = ["<b>제목 : </b><font color=#555555>".$row['TITLE']."</font>"];
			$MESSAGE[] = ["<b>내용 : </b><font color=#555555>".$content."</font>"];

			$BUTTONS = [];
			$BUTTONS[0]['name'] = "확인하러가기";
			$BUTTONS[0]['link'] = "https:\/\/i.posbank.com\/login.html?pageType=devel_board&pageCode=".$row['HID'];
//$row['IPRSNUM']="********";
			$query = "insert into goolge_webhooks (PROGRAM, GU, ID, TITLE, PREVIEW, MSG_TYPE, MESSAGE, BUTTONS, STATE, IDATE, UDATE) values (";
			$query .= "'intranet', 'devel_board', '[\"".$row['IPRSNUM']."\"]' ";
			$query .= ",'전산개발요청' ";
			$query .= ",'검수대기중인 전산개발요청건이 있습니다.' ";
			$query .= ",'1' ";
			$query .= ",'". json_encode($MESSAGE, JSON_UNESCAPED_UNICODE) . "' ";
			$query .= ",'". json_encode($BUTTONS, JSON_UNESCAPED_UNICODE) . "' ";
			$query .= ",'0' ";
			$query .= ",now() ";
			$query .= ",now() ";
			$query .= ") ";
			$rs = $dbconn2->query($query);
			//echo $query."\n\n";
			echo "검수대기(".$row['HID'].") - " . json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
		}
	}


	// 결재대기자에게 전송
	$SQL2 = "select 
					concat(A.HDATE,'_',A.HNO) HID
					,A.TITLE,A.CONTENT,B.PRSNUM
					,F_PRS_NAME(A.IPRSNUM) IPRSNAME
				from devel_board A
					inner join devel_board_appv B ON A.HDATE=B.HDATE AND A.HNO=B.HNO
				where 
					A.STATE not IN ('5','6','7','8','9') 
					and B.GU IN ('G','C') 
					and B.STATE='1' ";
	if($arr_prs_num){
		$SQL2 .= " and B.PRSNUM not in ('".implode("','",$arr_prs_num)."') ";
	}
	//$SQL2 .= " and B.PRSNUM='********' ";
	//$SQL2 .= " and A.HDATE='20230418' and A.HNO='0002' ";
	$arrRow2 = $dbconn->query_rows($SQL2);
	if($arrRow2){
		foreach($arrRow2 as $key => $row) {
//$row['PRSNUM']="********";
			//$content = strip_tags($row['CONTENT']);
			//$content = str_replace("&nbsp;", " ", $content);
			//$content = str_replace("'", "’", $content);
			//$content = htmlspecialchars_decode($content);
			$content = html_tags($row['CONTENT']);
			$content = mb_substr($content,0,500,'utf-8');

			$MESSAGE = [];
			$MESSAGE[] = ["결재대기중인 전산개발요청건이 있습니다."];
			$MESSAGE[] = ["<b>요청번호 : </b><font color=#555555>".$row['HID']."</font>"];
			$MESSAGE[] = ["<b>요청자 : </b><font color=#555555>".$row['IPRSNAME']."</font>"];
			$MESSAGE[] = ["<b>제목 : </b><font color=#555555>".$row['TITLE']."</font>"];
			$MESSAGE[] = ["<b>내용 : </b><font color=#555555>".$content."</font>"];

			$BUTTONS = [];
			$BUTTONS[0]['name'] = "결재하러가기";
			$BUTTONS[0]['link'] = "https:\/\/i.posbank.com\/login.html?pageType=devel_board&pageCode=".$row['HID'];

			$query = "insert into goolge_webhooks (PROGRAM, GU, ID, TITLE, PREVIEW, MSG_TYPE, MESSAGE, BUTTONS, STATE, IDATE, UDATE) values (";
			$query .= "'intranet', 'devel_board', '[\"".$row['PRSNUM']."\"]' ";
			$query .= ",'전산개발요청' ";
			$query .= ",'결재대기중인 전산개발요청건이 있습니다.' ";
			$query .= ",'1' ";
			$query .= ",'". json_encode($MESSAGE, JSON_UNESCAPED_UNICODE) . "' ";
			$query .= ",'". json_encode($BUTTONS, JSON_UNESCAPED_UNICODE) . "' ";
			$query .= ",'0' ";
			$query .= ",now() ";
			$query .= ",now() ";
			$query .= ") ";

			$rs = $dbconn2->query($query);
			//echo $query."\n";
			echo "결재대기(".$row['HID'].") - " . json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
		}
	}



}


## 스케즐 처리 상황 intra DB에 저장
crontab_execution(86400, "전산개발요청 알림");

echo date("Y-m-d H:i:s")." - 끝\n";
?>
