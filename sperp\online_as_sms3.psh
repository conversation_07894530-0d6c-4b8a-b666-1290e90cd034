#!/usr/bin/php -q
<?php
	// 0 9 * * * php -q /home/<USER>/sperp/online_as_sms3.psh
	# 온라인 AS 접수 카카오톡 알림톡(SMS) 발송 비즈뿌리오
	$ROOT_PATH = "/home/<USER>";
	include($ROOT_PATH . "/inc/func.php");
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/Encode.php");


	$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
	if(empty($dbconn_sperp_posbank->success)) {
		echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패\n";
	}

	$dbconn_m2g = new DBController($db['m2g']);
	if(empty($dbconn_m2g->success)) {
		echo "dbconn error [" . $db['m2g']['host'] . "] 데이터베이스 연결 실패\n";
	}

    echo "\n카카오톡 알림톡 [".date('Y-m-d H:i:s')."]\n";
    echo "[온라인코드] rs 전송시간 전송타입 전송타입명\n";

    //1. 미입고 1일전 SMS
	// $SQL = "
    //     SELECT 
    //         A.RCT_CODE||A.HDATE||A.HNO ONLINE_CODE ,A.RCT_CODE ,A.HDATE ,A.HNO ,A.SNO ,A.AS_ID 
    //         ,(CASE 
    //             WHEN A.INSP_STATE = '1' AND A.STATE IN ('10','15') THEN P1.PAY_DATE 
    //             WHEN A.INSP_STATE IN ('2','3','4') AND A.STATE IN ('10','15') THEN A.REG_IDATE 
    //             ELSE null 
    //         END ) AS RECEIVE_DEADLINE
    //         ,A.STATE ,A.WAYBILL_STATE  ,A.TEL2
    //         ,PR.PR_NAME ,B.BAS_NAME 
    //     FROM ASS_ACCEPT A
    //     LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE 
    //     LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE 
    //     LEFT JOIN AS_PAYMENT P1 ON A.RCT_CODE = P1.RCT_CODE AND A.HDATE =P1.HDATE AND A.HNO =P1.HNO AND P1.SERVICE_TYPE ='0'
    //     WHERE A.STATE IN ('10','15') AND A.WAYBILL_STATE != '1'
    //     ORDER BY A.HDATE , A.HNO 
	// ";
    $SQL = "
    SELECT * FROM (
        SELECT 
            A.RCT_CODE||A.HDATE||A.HNO AS ONLINE_CODE,
            A.RCT_CODE, A.HDATE, A.HNO, A.SNO, A.AS_ID,
            CASE 
                WHEN A.INSP_STATE = '1' AND A.STATE IN ('10','15') THEN P1.PAY_DATE 
                WHEN A.INSP_STATE IN ('2','3','4') AND A.STATE IN ('10','15') THEN A.REG_IDATE 
                ELSE NULL 
            END AS RECEIVE_DEADLINE,
            A.STATE, A.WAYBILL_STATE, A.TEL2,
            PR.PR_NAME, B.BAS_NAME,
            ROW_NUMBER() OVER (PARTITION BY A.RCT_CODE||A.HDATE||A.HNO ORDER BY A.HDATE, A.HNO) AS RN
        FROM ASS_ACCEPT A
        LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE 
        LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE 
        LEFT JOIN AS_PAYMENT P1 
            ON A.RCT_CODE = P1.RCT_CODE AND A.HDATE = P1.HDATE AND A.HNO = P1.HNO 
            AND P1.SERVICE_TYPE = '0'
        WHERE A.STATE IN ('10','15') 
        AND A.WAYBILL_STATE != '1'
    ) 
    WHERE RN = 1
    ORDER BY HDATE, HNO";

    $rows = $dbconn_sperp_posbank->query_rows($SQL);
    foreach($rows as $row){
        $DEADLINE = getASBusinessDate($row['RECEIVE_DEADLINE'], 1);
        if ($DEADLINE === date('Y-m-d')) {
            $rs = AS_BIZ_KAKAO_RECEPTION_ROWS($row['ONLINE_CODE'],'1');
            echo "[".$row['ONLINE_CODE']."]"." ".$rs['state']." ". date('Y-m-d H:i:s')." 1  미입고1일전\n";
        }
    }
    

	//4-1. 미결제 7일차 /4-2. 미결제 14일차/4-3. 미결제 20일차
    // $SQL ="
    //     SELECT 
    //         A.RCT_CODE||A.HDATE||A.HNO ONLINE_CODE ,A.RCT_CODE ,A.HDATE ,A.HNO ,A.SNO ,A.AS_ID 
    //         ,A.STATE ,A.WAYBILL_STATE  ,A.TEL2
    //         ,PR.PR_NAME ,B.BAS_NAME 
    //         ,A.SEND_ESTIMATE_DATE
    //     FROM ASS_ACCEPT A
    //     LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE 
    //     LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE 
    //     LEFT JOIN AS_PAYMENT F2 ON A.RCT_CODE = F2.RCT_CODE AND A.HDATE=F2.HDATE AND A.HNO = F2.HNO AND F2.SERVICE_TYPE ='1'
    //     WHERE A.STATE ='25'
    //     ORDER BY RCT_CODE DESC, HDATE DESC, HNO DESC
    // ";  

    // 수리비 미결제 알림
    $SQL = "
        SELECT 
            A.RCT_CODE||A.HDATE||A.HNO ONLINE_CODE ,A.RCT_CODE ,A.HDATE ,A.HNO ,A.SNO ,A.AS_ID 
            ,A.STATE ,A.WAYBILL_STATE  ,A.TEL2
            ,PR.PR_NAME ,B.BAS_NAME 
            ,A.SEND_ESTIMATE_DATE
            ,(CASE WHEN BAS4.BAS_NAME IS NOT NULL AND BAS4.BAS_OP5 IS NOT NULL THEN BAS4.BAS_OP5 ELSE BAS3.BAS_OP5 END) AS BAS_OP5
        FROM ASS_ACCEPT A
        LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE 
        LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE 
        LEFT JOIN BAS BAS3 ON BAS3.BAS_CODE ='A302'
        LEFT JOIN BAS BAS4 ON BAS4.BAS_CODE LIKE 'A5%' AND A.CT_CODE= BAS4.BAS_NAME 
        LEFT JOIN AS_PAYMENT F2 ON A.RCT_CODE = F2.RCT_CODE AND A.HDATE=F2.HDATE AND A.HNO = F2.HNO AND F2.SERVICE_TYPE ='1'
        WHERE A.STATE ='25'
        ORDER BY RCT_CODE DESC, HDATE DESC, HNO DESC
    ";
    $rows = $dbconn_sperp_posbank->query_rows($SQL);
    foreach($rows as $row){
        $DEADLINE5 = getBusinessDate($row['SEND_ESTIMATE_DATE'], $row['BAS_OP5']);
        if ($DEADLINE5 === date('Y-m-d')) {
            $rs = AS_BIZ_KAKAO_RECEPTION_ROWS($row['ONLINE_CODE'],'4');
            echo "[".$row['ONLINE_CODE']."]"." ".$rs['state']." ". date('Y-m-d H:i:s')." 4 미결제1일전\n";
        }  
    }

	##### End. 2025.05.26 KSH(101141) 신규 스케줄링 비즈뿌리오 알림톡
	###########################################


	// ## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(86400, "온라인 AS 접수 카카오톡 알림톡3(SMS) 발송");

	echo date("Y-m-d H:i:s")." - 끝\n";
?>