<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Calculation_Database</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_DAVERAGE" title="DAVERAGE :: DAVERAGE"><span class="description">DAVERAGE</span><pre>DAVERAGE()</pre></a></li>
<li class="method public "><a href="#method_DCOUNT" title="DCOUNT :: DCOUNT"><span class="description">DCOUNT</span><pre>DCOUNT()</pre></a></li>
<li class="method public "><a href="#method_DCOUNTA" title="DCOUNTA :: DCOUNTA"><span class="description">DCOUNTA</span><pre>DCOUNTA()</pre></a></li>
<li class="method public "><a href="#method_DGET" title="DGET :: DGET"><span class="description">DGET</span><pre>DGET()</pre></a></li>
<li class="method public "><a href="#method_DMAX" title="DMAX :: DMAX"><span class="description">DMAX</span><pre>DMAX()</pre></a></li>
<li class="method public "><a href="#method_DMIN" title="DMIN :: DMIN"><span class="description">DMIN</span><pre>DMIN()</pre></a></li>
<li class="method public "><a href="#method_DPRODUCT" title="DPRODUCT :: DPRODUCT"><span class="description">DPRODUCT</span><pre>DPRODUCT()</pre></a></li>
<li class="method public "><a href="#method_DSTDEV" title="DSTDEV :: DSTDEV"><span class="description">DSTDEV</span><pre>DSTDEV()</pre></a></li>
<li class="method public "><a href="#method_DSTDEVP" title="DSTDEVP :: DSTDEVP"><span class="description">DSTDEVP</span><pre>DSTDEVP()</pre></a></li>
<li class="method public "><a href="#method_DSUM" title="DSUM :: DSUM"><span class="description">DSUM</span><pre>DSUM()</pre></a></li>
<li class="method public "><a href="#method_DVAR" title="DVAR :: DVAR"><span class="description">DVAR</span><pre>DVAR()</pre></a></li>
<li class="method public "><a href="#method_DVARP" title="DVARP :: DVARP"><span class="description">DVARP</span><pre>DVARP()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="method private "><a href="#method___fieldExtract" title="__fieldExtract :: __fieldExtract"><span class="description">__fieldExtract</span><pre>__fieldExtract()</pre></a></li>
<li class="method private "><a href="#method___filter" title="__filter :: __filter"><span class="description">__filter</span><pre>__filter()</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Calculation_Database"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Calculation_Database.html">PHPExcel_Calculation_Database</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Calculation_Database</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Calculation.html">PHPExcel_Calculation</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_DAVERAGE"></a><div class="element clickable method public method_DAVERAGE" data-toggle="collapse" data-target=".method_DAVERAGE .collapse">
<h2>DAVERAGE</h2>
<pre>DAVERAGE(mixed[] $database, string | integer $field, mixed[] $criteria) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Averages the values in a column of a list or database that match conditions you specify.</p>

<p>Excel Function:
    DAVERAGE(database,field,criteria)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Database Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>string</code><code>integer</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_DCOUNT"></a><div class="element clickable method public method_DCOUNT" data-toggle="collapse" data-target=".method_DCOUNT .collapse">
<h2>DCOUNT</h2>
<pre>DCOUNT(mixed[] $database, string | integer $field, mixed[] $criteria) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Counts the cells that contain numbers in a column of a list or database that match conditions
that you specify.</p>

<p>Excel Function:
    DCOUNT(database,[field],criteria)</p>

<p>Excel Function:
    DAVERAGE(database,field,criteria)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Database Functions</td>
</tr>
<tr>
<th>TODO</th>
<td>The field argument is optional. If field is omitted, DCOUNT counts all records in the
		database that match the criteria.</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>string</code><code>integer</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>integer</code></div>
</div></div>
</div>
<a id="method_DCOUNTA"></a><div class="element clickable method public method_DCOUNTA" data-toggle="collapse" data-target=".method_DCOUNTA .collapse">
<h2>DCOUNTA</h2>
<pre>DCOUNTA(mixed[] $database, string | integer $field, mixed[] $criteria) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Counts the nonblank cells in a column of a list or database that match conditions that you specify.</p>

<p>Excel Function:
    DCOUNTA(database,[field],criteria)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Database Functions</td>
</tr>
<tr>
<th>TODO</th>
<td>The field argument is optional. If field is omitted, DCOUNTA counts all records in the
		database that match the criteria.</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>string</code><code>integer</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>integer</code></div>
</div></div>
</div>
<a id="method_DGET"></a><div class="element clickable method public method_DGET" data-toggle="collapse" data-target=".method_DGET .collapse">
<h2>DGET</h2>
<pre>DGET(mixed[] $database, string | integer $field, mixed[] $criteria) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Extracts a single value from a column of a list or database that matches conditions that you
specify.</p>

<p>Excel Function:
    DGET(database,field,criteria)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Database Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>string</code><code>integer</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>mixed</code></div>
</div></div>
</div>
<a id="method_DMAX"></a><div class="element clickable method public method_DMAX" data-toggle="collapse" data-target=".method_DMAX .collapse">
<h2>DMAX</h2>
<pre>DMAX(mixed[] $database, string | integer $field, mixed[] $criteria) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the largest number in a column of a list or database that matches conditions you that
specify.</p>

<p>Excel Function:
    DMAX(database,field,criteria)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Database Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>string</code><code>integer</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_DMIN"></a><div class="element clickable method public method_DMIN" data-toggle="collapse" data-target=".method_DMIN .collapse">
<h2>DMIN</h2>
<pre>DMIN(mixed[] $database, string | integer $field, mixed[] $criteria) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the smallest number in a column of a list or database that matches conditions you that
specify.</p>

<p>Excel Function:
    DMIN(database,field,criteria)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Database Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>string</code><code>integer</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_DPRODUCT"></a><div class="element clickable method public method_DPRODUCT" data-toggle="collapse" data-target=".method_DPRODUCT .collapse">
<h2>DPRODUCT</h2>
<pre>DPRODUCT(mixed[] $database, string | integer $field, mixed[] $criteria) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Multiplies the values in a column of a list or database that match conditions that you specify.</p>

<p>Excel Function:
    DPRODUCT(database,field,criteria)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Database Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>string</code><code>integer</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_DSTDEV"></a><div class="element clickable method public method_DSTDEV" data-toggle="collapse" data-target=".method_DSTDEV .collapse">
<h2>DSTDEV</h2>
<pre>DSTDEV(mixed[] $database, string | integer $field, mixed[] $criteria) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Estimates the standard deviation of a population based on a sample by using the numbers in a
column of a list or database that match conditions that you specify.</p>

<p>Excel Function:
    DSTDEV(database,field,criteria)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Database Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>string</code><code>integer</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_DSTDEVP"></a><div class="element clickable method public method_DSTDEVP" data-toggle="collapse" data-target=".method_DSTDEVP .collapse">
<h2>DSTDEVP</h2>
<pre>DSTDEVP(mixed[] $database, string | integer $field, mixed[] $criteria) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Calculates the standard deviation of a population based on the entire population by using the
numbers in a column of a list or database that match conditions that you specify.</p>

<p>Excel Function:
    DSTDEVP(database,field,criteria)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Database Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>string</code><code>integer</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_DSUM"></a><div class="element clickable method public method_DSUM" data-toggle="collapse" data-target=".method_DSUM .collapse">
<h2>DSUM</h2>
<pre>DSUM(mixed[] $database, string | integer $field, mixed[] $criteria) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Adds the numbers in a column of a list or database that match conditions that you specify.</p>

<p>Excel Function:
    DSUM(database,field,criteria)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Database Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>string</code><code>integer</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_DVAR"></a><div class="element clickable method public method_DVAR" data-toggle="collapse" data-target=".method_DVAR .collapse">
<h2>DVAR</h2>
<pre>DVAR(mixed[] $database, string | integer $field, mixed[] $criteria) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Estimates the variance of a population based on a sample by using the numbers in a column
of a list or database that match conditions that you specify.</p>

<p>Excel Function:
    DVAR(database,field,criteria)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Database Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>string</code><code>integer</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method_DVARP"></a><div class="element clickable method public method_DVARP" data-toggle="collapse" data-target=".method_DVARP .collapse">
<h2>DVARP</h2>
<pre>DVARP(mixed[] $database, string | integer $field, mixed[] $criteria) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Calculates the variance of a population based on the entire population by using the numbers
in a column of a list or database that match conditions that you specify.</p>

<p>Excel Function:
    DVARP(database,field,criteria)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Database Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>string</code><code>integer</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>float</code></div>
</div></div>
</div>
<a id="method___fieldExtract"></a><div class="element clickable method private method___fieldExtract" data-toggle="collapse" data-target=".method___fieldExtract .collapse">
<h2>__fieldExtract</h2>
<pre>__fieldExtract(mixed[] $database, mixed $field) : string | NULL</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Extracts the column ID to use for the data field.</p></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>private</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$field</h4>
<code>mixed</code><p>Indicates which column is used in the function. Enter the
                                    column label enclosed between double quotation marks, such as
                                    "Age" or "Yield," or a number (without quotation marks) that
                                    represents the position of the column within the list: 1 for
                                    the first column, 2 for the second column, and so on.</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code><code>NULL</code>
</div>
</div></div>
</div>
<a id="method___filter"></a><div class="element clickable method private method___filter" data-toggle="collapse" data-target=".method___filter .collapse">
<h2>__filter</h2>
<pre>__filter(mixed[] $database, mixed[] $criteria) : array</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Parses the selection criteria, extracts the database rows that match those criteria, and
returns that subset of rows.</p></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>private</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$database</h4>
<code>mixed[]</code><p>The range of cells that makes up the list or database.
									A database is a list of related data in which rows of related
									information are records, and columns of data are fields. The
									first row of the list contains labels for each column.</p></div>
<div class="subelement argument">
<h4>$criteria</h4>
<code>mixed[]</code><p>The range of cells that contains the conditions you specify.
									You can use any range for the criteria argument, as long as it
									includes at least one column label and at least one cell below
									the column label in which you specify a condition for the
									column.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>array</code>of mixed</div>
</div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:33Z.<br></footer></div>
</div>
</body>
</html>
