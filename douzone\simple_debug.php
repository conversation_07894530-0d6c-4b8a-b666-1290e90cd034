#!/usr/local/bin/php -q
<?php
/**
 * 간단한 API 응답 상태 체크
 */

include('api_config.php');


$config = getAmaranth10ApiConfig('API_gcmsAmaranth39740', '/apiproxy/api16S08');
$url = $config['api_url'] . $config['api_endpoint'];


$timestamp = time();
$transaction_id = uniqid('', true); // Java와 동일한 방식
// Java 서명 방식: authToken + transactionId + timestamp + url
$message = $config['access_token'] . $transaction_id . $timestamp . $config['api_endpoint'];
$signature = createHmacSha256($message, $config['hash_key']);

$post_data = array(
    'searchText' => '1000',
    'useYn' => '0'
);

$json_data = json_encode($post_data, JSON_UNESCAPED_UNICODE);

echo "URL: " . $url . "\n";
echo "요청 데이터: " . $json_data . "\n";
echo "서명 메시지: " . $message . "\n";
echo "Authorization: Bearer " . $config['access_token'] . "\n";
echo "wehago-sign: " . $signature . "\n";
echo "transaction-id: " . $transaction_id . "\n";
echo "timestamp: " . $timestamp . "\n";
echo "callerName: " . $config['caller_name'] . "\n";
echo "groupSeq: " . $config['group_seq'] . "\n\n";

$ch = curl_init();
curl_setopt_array($ch, array(
    CURLOPT_URL => $url,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => $json_data,
    CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json',
        'Content-Encoding: utf-8',
        'Authorization: Bearer ' . $config['access_token'],
        'wehago-sign: ' . $signature,
        'transaction-id: ' . $transaction_id,
        'timestamp: ' . $timestamp,
        'callerName: ' . $config['caller_name'],
        'groupSeq: ' . $config['group_seq']
    ),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_CONNECTTIMEOUT => 10,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false
));

echo "cURL 실행 중...\n";
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curl_error = curl_error($ch);
$curl_info = curl_getinfo($ch);

echo "=== 응답 정보 ===\n";
echo "HTTP 코드: " . $http_code . "\n";
echo "cURL 에러: " . ($curl_error ?: '없음') . "\n";
echo "응답 크기: " . strlen($response) . " bytes\n";
echo "연결 시간: " . $curl_info['connect_time'] . "초\n";
echo "전체 시간: " . $curl_info['total_time'] . "초\n";
echo "Content-Type: " . ($curl_info['content_type'] ?: 'N/A') . "\n";

if ($response === false) {
    echo "응답 없음 (false)\n";
} elseif ($response === '') {
    echo "빈 응답 (empty string)\n";
} else {
    echo "응답 내용:\n";
    echo "'" . $response . "'\n";
    
    if (strlen($response) > 0) {
        echo "\n16진수 덤프:\n";
        echo bin2hex($response) . "\n";
    }
}

curl_close($ch);

echo "\n=== 완료 ===\n";
?>