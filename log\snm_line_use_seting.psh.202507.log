<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250701 | sEdate : 20250812<br>
<br>
 MAX_DAY  : 20250811<br>
 DIFF_DAY  : 42<br>
 20250701 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250811 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 이미 등록되어있음......<br>
 20250811 이미 등록되어있음......<br>
 20250811 이미 등록되어있음......<br>
 20250811 이미 등록되어있음......<br>
 20250811 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250701001<br>
 입력 SLUS_ID  : 20250701002<br>
 입력 SLUS_ID  : 20250701003<br>
 입력 SLUS_ID  : 20250701004<br>
 입력 SLUS_ID  : 20250701005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250702 | sEdate : 20250813<br>
<br>
 MAX_DAY  : 20250812<br>
 DIFF_DAY  : 42<br>
 20250702 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250812 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 이미 등록되어있음......<br>
 20250812 이미 등록되어있음......<br>
 20250812 이미 등록되어있음......<br>
 20250812 이미 등록되어있음......<br>
 20250812 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250702001<br>
 입력 SLUS_ID  : 20250702002<br>
 입력 SLUS_ID  : 20250702003<br>
 입력 SLUS_ID  : 20250702004<br>
 입력 SLUS_ID  : 20250702005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250703 | sEdate : 20250814<br>
<br>
 MAX_DAY  : 20250813<br>
 DIFF_DAY  : 42<br>
 20250703 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250813 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 이미 등록되어있음......<br>
 20250813 이미 등록되어있음......<br>
 20250813 이미 등록되어있음......<br>
 20250813 이미 등록되어있음......<br>
 20250813 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250703001<br>
 입력 SLUS_ID  : 20250703002<br>
 입력 SLUS_ID  : 20250703003<br>
 입력 SLUS_ID  : 20250703004<br>
 입력 SLUS_ID  : 20250703005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250704 | sEdate : 20250815<br>
<br>
 MAX_DAY  : 20250814<br>
 DIFF_DAY  : 42<br>
 20250704 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 광복절 휴무일인 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250705 | sEdate : 20250816<br>
<br>
 MAX_DAY  : 20250814<br>
 DIFF_DAY  : 42<br>
 20250705 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 광복절 휴무일인 경우 패스.....<br>
 20250816 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250706 | sEdate : 20250817<br>
<br>
 MAX_DAY  : 20250814<br>
 DIFF_DAY  : 42<br>
 20250706 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 광복절 휴무일인 경우 패스.....<br>
 20250816 주말일 경우 패스.....<br>
 20250817 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250707 | sEdate : 20250818<br>
<br>
 MAX_DAY  : 20250814<br>
 DIFF_DAY  : 42<br>
 20250707 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250814 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 20250814 이미 등록되어있음......<br>
 광복절 휴무일인 경우 패스.....<br>
 20250816 주말일 경우 패스.....<br>
 20250817 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250707001<br>
 입력 SLUS_ID  : 20250707002<br>
 입력 SLUS_ID  : 20250707003<br>
 입력 SLUS_ID  : 20250707004<br>
 입력 SLUS_ID  : 20250707005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250708 | sEdate : 20250819<br>
<br>
 MAX_DAY  : 20250818<br>
 DIFF_DAY  : 42<br>
 20250708 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250818 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 이미 등록되어있음......<br>
 20250818 이미 등록되어있음......<br>
 20250818 이미 등록되어있음......<br>
 20250818 이미 등록되어있음......<br>
 20250818 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250708001<br>
 입력 SLUS_ID  : 20250708002<br>
 입력 SLUS_ID  : 20250708003<br>
 입력 SLUS_ID  : 20250708004<br>
 입력 SLUS_ID  : 20250708005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250709 | sEdate : 20250820<br>
<br>
 MAX_DAY  : 20250819<br>
 DIFF_DAY  : 42<br>
 20250709 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250819 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 이미 등록되어있음......<br>
 20250819 이미 등록되어있음......<br>
 20250819 이미 등록되어있음......<br>
 20250819 이미 등록되어있음......<br>
 20250819 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250709001<br>
 입력 SLUS_ID  : 20250709002<br>
 입력 SLUS_ID  : 20250709003<br>
 입력 SLUS_ID  : 20250709004<br>
 입력 SLUS_ID  : 20250709005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250710 | sEdate : 20250821<br>
<br>
 MAX_DAY  : 20250820<br>
 DIFF_DAY  : 42<br>
 20250710 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250820 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 이미 등록되어있음......<br>
 20250820 이미 등록되어있음......<br>
 20250820 이미 등록되어있음......<br>
 20250820 이미 등록되어있음......<br>
 20250820 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250710001<br>
 입력 SLUS_ID  : 20250710002<br>
 입력 SLUS_ID  : 20250710003<br>
 입력 SLUS_ID  : 20250710004<br>
 입력 SLUS_ID  : 20250710005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250711 | sEdate : 20250822<br>
<br>
 MAX_DAY  : 20250821<br>
 DIFF_DAY  : 42<br>
 20250711 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250821 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 이미 등록되어있음......<br>
 20250821 이미 등록되어있음......<br>
 20250821 이미 등록되어있음......<br>
 20250821 이미 등록되어있음......<br>
 20250821 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250711001<br>
 입력 SLUS_ID  : 20250711002<br>
 입력 SLUS_ID  : 20250711003<br>
 입력 SLUS_ID  : 20250711004<br>
 입력 SLUS_ID  : 20250711005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250712 | sEdate : 20250823<br>
<br>
 MAX_DAY  : 20250822<br>
 DIFF_DAY  : 42<br>
 20250712 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 이미 등록되어있음......<br>
 20250822 이미 등록되어있음......<br>
 20250822 이미 등록되어있음......<br>
 20250822 이미 등록되어있음......<br>
 20250822 이미 등록되어있음......<br>
 20250823 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250713 | sEdate : 20250824<br>
<br>
 MAX_DAY  : 20250822<br>
 DIFF_DAY  : 42<br>
 20250713 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 이미 등록되어있음......<br>
 20250822 이미 등록되어있음......<br>
 20250822 이미 등록되어있음......<br>
 20250822 이미 등록되어있음......<br>
 20250822 이미 등록되어있음......<br>
 20250823 주말일 경우 패스.....<br>
 20250824 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250714 | sEdate : 20250825<br>
<br>
 MAX_DAY  : 20250822<br>
 DIFF_DAY  : 42<br>
 20250714 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250822 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 이미 등록되어있음......<br>
 20250822 이미 등록되어있음......<br>
 20250822 이미 등록되어있음......<br>
 20250822 이미 등록되어있음......<br>
 20250822 이미 등록되어있음......<br>
 20250823 주말일 경우 패스.....<br>
 20250824 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250714001<br>
 입력 SLUS_ID  : 20250714002<br>
 입력 SLUS_ID  : 20250714003<br>
 입력 SLUS_ID  : 20250714004<br>
 입력 SLUS_ID  : 20250714005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250715 | sEdate : 20250826<br>
<br>
 MAX_DAY  : 20250825<br>
 DIFF_DAY  : 42<br>
 20250715 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250825 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 이미 등록되어있음......<br>
 20250825 이미 등록되어있음......<br>
 20250825 이미 등록되어있음......<br>
 20250825 이미 등록되어있음......<br>
 20250825 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250715001<br>
 입력 SLUS_ID  : 20250715002<br>
 입력 SLUS_ID  : 20250715003<br>
 입력 SLUS_ID  : 20250715004<br>
 입력 SLUS_ID  : 20250715005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250716 | sEdate : 20250827<br>
<br>
 MAX_DAY  : 20250826<br>
 DIFF_DAY  : 42<br>
 20250716 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250826 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 이미 등록되어있음......<br>
 20250826 이미 등록되어있음......<br>
 20250826 이미 등록되어있음......<br>
 20250826 이미 등록되어있음......<br>
 20250826 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250716001<br>
 입력 SLUS_ID  : 20250716002<br>
 입력 SLUS_ID  : 20250716003<br>
 입력 SLUS_ID  : 20250716004<br>
 입력 SLUS_ID  : 20250716005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250717 | sEdate : 20250828<br>
<br>
 MAX_DAY  : 20250827<br>
 DIFF_DAY  : 42<br>
 20250717 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250827 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 이미 등록되어있음......<br>
 20250827 이미 등록되어있음......<br>
 20250827 이미 등록되어있음......<br>
 20250827 이미 등록되어있음......<br>
 20250827 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250717001<br>
 입력 SLUS_ID  : 20250717002<br>
 입력 SLUS_ID  : 20250717003<br>
 입력 SLUS_ID  : 20250717004<br>
 입력 SLUS_ID  : 20250717005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250718 | sEdate : 20250829<br>
<br>
 MAX_DAY  : 20250828<br>
 DIFF_DAY  : 42<br>
 20250718 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250828 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 이미 등록되어있음......<br>
 20250828 이미 등록되어있음......<br>
 20250828 이미 등록되어있음......<br>
 20250828 이미 등록되어있음......<br>
 20250828 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250718001<br>
 입력 SLUS_ID  : 20250718002<br>
 입력 SLUS_ID  : 20250718003<br>
 입력 SLUS_ID  : 20250718004<br>
 입력 SLUS_ID  : 20250718005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250719 | sEdate : 20250830<br>
<br>
 MAX_DAY  : 20250829<br>
 DIFF_DAY  : 42<br>
 20250719 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 이미 등록되어있음......<br>
 20250829 이미 등록되어있음......<br>
 20250829 이미 등록되어있음......<br>
 20250829 이미 등록되어있음......<br>
 20250829 이미 등록되어있음......<br>
 20250830 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250720 | sEdate : 20250831<br>
<br>
 MAX_DAY  : 20250829<br>
 DIFF_DAY  : 42<br>
 20250720 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 이미 등록되어있음......<br>
 20250829 이미 등록되어있음......<br>
 20250829 이미 등록되어있음......<br>
 20250829 이미 등록되어있음......<br>
 20250829 이미 등록되어있음......<br>
 20250830 주말일 경우 패스.....<br>
 20250831 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250721 | sEdate : 20250901<br>
<br>
 MAX_DAY  : 20250829<br>
 DIFF_DAY  : 42<br>
 20250721 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250829 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 이미 등록되어있음......<br>
 20250829 이미 등록되어있음......<br>
 20250829 이미 등록되어있음......<br>
 20250829 이미 등록되어있음......<br>
 20250829 이미 등록되어있음......<br>
 20250830 주말일 경우 패스.....<br>
 20250831 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250721001<br>
 입력 SLUS_ID  : 20250721002<br>
 입력 SLUS_ID  : 20250721003<br>
 입력 SLUS_ID  : 20250721004<br>
 입력 SLUS_ID  : 20250721005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250722 | sEdate : 20250902<br>
<br>
 MAX_DAY  : 20250901<br>
 DIFF_DAY  : 42<br>
 20250722 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250901 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 이미 등록되어있음......<br>
 20250901 이미 등록되어있음......<br>
 20250901 이미 등록되어있음......<br>
 20250901 이미 등록되어있음......<br>
 20250901 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250722001<br>
 입력 SLUS_ID  : 20250722002<br>
 입력 SLUS_ID  : 20250722003<br>
 입력 SLUS_ID  : 20250722004<br>
 입력 SLUS_ID  : 20250722005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250723 | sEdate : 20250903<br>
<br>
 MAX_DAY  : 20250902<br>
 DIFF_DAY  : 42<br>
 20250723 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250902 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 이미 등록되어있음......<br>
 20250902 이미 등록되어있음......<br>
 20250902 이미 등록되어있음......<br>
 20250902 이미 등록되어있음......<br>
 20250902 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250723001<br>
 입력 SLUS_ID  : 20250723002<br>
 입력 SLUS_ID  : 20250723003<br>
 입력 SLUS_ID  : 20250723004<br>
 입력 SLUS_ID  : 20250723005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250724 | sEdate : 20250904<br>
<br>
 MAX_DAY  : 20250903<br>
 DIFF_DAY  : 42<br>
 20250724 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250903 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 이미 등록되어있음......<br>
 20250903 이미 등록되어있음......<br>
 20250903 이미 등록되어있음......<br>
 20250903 이미 등록되어있음......<br>
 20250903 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250724001<br>
 입력 SLUS_ID  : 20250724002<br>
 입력 SLUS_ID  : 20250724003<br>
 입력 SLUS_ID  : 20250724004<br>
 입력 SLUS_ID  : 20250724005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250725 | sEdate : 20250905<br>
<br>
 MAX_DAY  : 20250904<br>
 DIFF_DAY  : 42<br>
 20250725 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250904 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 이미 등록되어있음......<br>
 20250904 이미 등록되어있음......<br>
 20250904 이미 등록되어있음......<br>
 20250904 이미 등록되어있음......<br>
 20250904 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250725001<br>
 입력 SLUS_ID  : 20250725002<br>
 입력 SLUS_ID  : 20250725003<br>
 입력 SLUS_ID  : 20250725004<br>
 입력 SLUS_ID  : 20250725005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250726 | sEdate : 20250906<br>
<br>
 MAX_DAY  : 20250905<br>
 DIFF_DAY  : 42<br>
 20250726 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 이미 등록되어있음......<br>
 20250905 이미 등록되어있음......<br>
 20250905 이미 등록되어있음......<br>
 20250905 이미 등록되어있음......<br>
 20250905 이미 등록되어있음......<br>
 20250906 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250727 | sEdate : 20250907<br>
<br>
 MAX_DAY  : 20250905<br>
 DIFF_DAY  : 42<br>
 20250727 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 이미 등록되어있음......<br>
 20250905 이미 등록되어있음......<br>
 20250905 이미 등록되어있음......<br>
 20250905 이미 등록되어있음......<br>
 20250905 이미 등록되어있음......<br>
 20250906 주말일 경우 패스.....<br>
 20250907 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250728 | sEdate : 20250908<br>
<br>
 MAX_DAY  : 20250905<br>
 DIFF_DAY  : 42<br>
 20250728 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250905 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 이미 등록되어있음......<br>
 20250905 이미 등록되어있음......<br>
 20250905 이미 등록되어있음......<br>
 20250905 이미 등록되어있음......<br>
 20250905 이미 등록되어있음......<br>
 20250906 주말일 경우 패스.....<br>
 20250907 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250728001<br>
 입력 SLUS_ID  : 20250728002<br>
 입력 SLUS_ID  : 20250728003<br>
 입력 SLUS_ID  : 20250728004<br>
 입력 SLUS_ID  : 20250728005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250729 | sEdate : 20250909<br>
<br>
 MAX_DAY  : 20250908<br>
 DIFF_DAY  : 42<br>
 20250729 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250908 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 이미 등록되어있음......<br>
 20250908 이미 등록되어있음......<br>
 20250908 이미 등록되어있음......<br>
 20250908 이미 등록되어있음......<br>
 20250908 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250729001<br>
 입력 SLUS_ID  : 20250729002<br>
 입력 SLUS_ID  : 20250729003<br>
 입력 SLUS_ID  : 20250729004<br>
 입력 SLUS_ID  : 20250729005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250730 | sEdate : 20250910<br>
<br>
 MAX_DAY  : 20250909<br>
 DIFF_DAY  : 42<br>
 20250730 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250909 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 이미 등록되어있음......<br>
 20250909 이미 등록되어있음......<br>
 20250909 이미 등록되어있음......<br>
 20250909 이미 등록되어있음......<br>
 20250909 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250730001<br>
 입력 SLUS_ID  : 20250730002<br>
 입력 SLUS_ID  : 20250730003<br>
 입력 SLUS_ID  : 20250730004<br>
 입력 SLUS_ID  : 20250730005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250731 | sEdate : 20250911<br>
<br>
 MAX_DAY  : 20250910<br>
 DIFF_DAY  : 42<br>
 20250731 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250910 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 이미 등록되어있음......<br>
 20250910 이미 등록되어있음......<br>
 20250910 이미 등록되어있음......<br>
 20250910 이미 등록되어있음......<br>
 20250910 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250731001<br>
 입력 SLUS_ID  : 20250731002<br>
 입력 SLUS_ID  : 20250731003<br>
 입력 SLUS_ID  : 20250731004<br>
 입력 SLUS_ID  : 20250731005