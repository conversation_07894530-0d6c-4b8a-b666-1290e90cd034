<?php
// 동적 경로 설정 - Windows와 Linux 모두 지원
if (!defined('ROOT_PATH')) {
    if (isset($_SERVER['DOCUMENT_ROOT']) && !empty($_SERVER['DOCUMENT_ROOT'])) {
        $ROOT_PATH = $_SERVER['DOCUMENT_ROOT'];
    } else {
        $ROOT_PATH = dirname(__DIR__);
    }
} else {
    $ROOT_PATH = ROOT_PATH;
}

require_once $ROOT_PATH.'/inc/db_config.php';

class DBController
{
	public function __construct($db)
	{
		$this->version = phpversion();
		$this->db = $db;
		$this->connect_db();
	}

	function __destruct()
	{
		$this->db_close();
	}

	// DB 접속
	function connect_db()
	{
		$phpversion = $this->version;

		$db = $this->db;
		$resource = [];
		switch($db['dbdriver']){
			case "mysql":
				if($phpversion > 7){
					//$conn = @new mysqli($db['host'], $db['userid'], $db['password-'], $db['database']);
					$conn = @mysqli_connect($db['host'], $db['userid'], $db['password'], $db['database']);
					if($conn && $db['charset']) $conn->set_charset($db['charset']);
				}else{
					$conn = mysql_connect($db['host'], $db['userid'], $db['password']);
					$mysql = mysql_select_db($db['database'], $conn);
					if($db['charset']) mysql_query('set names ' . $db['charset'], $conn);
				}
				break;
			case "oracle":
				$charset = '';
				if($db['charset']=='euckr') $charset = 'KO16MSWIN949';
				if($db['charset']=='utf8') $charset = 'AL32UTF8';
				if($phpversion > 7){
					$conn = @oci_connect($db['userid'], $db['password'], $db['host'], $charset);
				}else{
					$conn = @OCILogon($db['userid'], $db['password'], $db['host'], $charset);
				}
				break;
			case "mssql":
				if($phpversion > 7){
					$conn = sqlsrv_connect($db['host'], array("database"=>$db['database'],"uid"=>$db['userid'],"pwd"=>$db['password']));
				}else{
					$conn = mssql_connect($db['host'], $db['userid'], $db['password']);
					$mysql = mssql_select_db($db['database'], $conn);
					if($db['charset']) mssql_query('set names ' . $db['charset'], $conn);
				}
				break;
		}
		if($conn){
			$this->success = true;
		}
		$resource['resource'] = $conn;
		$resource['db'] = $db;
		$this->dbconn = $conn;
//success
		//return $conn;
	}

	// DB 종료
	function db_close()
	{
		$db = $this->db;
		$conn = $this->dbconn;
		$phpversion = $this->version;
		if($conn){
			switch($db['dbdriver']){
				case "mysql":
					if($phpversion > 7){
						$conn->close();
					}else{
						mysql_close($conn);
					}
					break;
				case "oracle":
					if($phpversion > 7){
						oci_close($conn);
					}else{
						OCILogOff($conn);
					}
					break;
				case "mssql":
					if($phpversion > 7){
						sqlsrv_close($conn);
					}else{
						mssql_close($conn);
					}
					break;
			}
		}
		unset($this->dbconn);
	}

	// $arr = db->iud_query($SQL);
	function iud_query($arr_sql,$transaction=false) // INSERT,UPDATE,DELETE 처리시 사용
	{
		$db = $this->db;
		$conn = $this->dbconn;
		$phpversion = $this->version;

		if($arr_sql){
			if(is_array($arr_sql)) $arr_sql2 = $arr_sql;
			else $arr_sql2 = array($arr_sql);

			$sw = 0;
			$cnt = 0;
			$ErrorMsg = "";
			if($transaction==true) $this->begin();

			foreach($arr_sql2 as $key => $query) {
				$Rs = $this->query($query,$transaction);
				switch($db['dbdriver']){
					case "mysql":
						if($Error=$conn->error){
							$sw++;
							$Error2['code'] = mysqli_errno($conn);
							$Error2['message'] = $Error;
						}else{
							$cnt += $conn->affected_rows;
						}
						break;
					case "oracle":
						if ($Error = oci_error($Rs)) {
							$sw++;
							$Error2 = $Error;
						}else{
							$cnt += oci_num_rows($Rs);
						}
						break;
					case "mssql":
						if($Error=sqlsrv_errors()){
							$sw++;
							$Error2['code'] = $Error['code'];
							$Error2['message'] = $Error['message'];
						}else{
							$cnt += sqlsrv_rows_affected($Rs);
						}
						break;
				}
				if($sw) {
					$ErrorMsg .= "[".addslashes(trim($Error2['code']))."] ".addslashes(trim($Error2['message']))."\\n";
					break;
				}
			}
			if($transaction==true) {
				if($sw) $this->rollback();
				else $this->commit();
			}
		}
		$row['state'] = ($sw)?false:true ; // 성공유무(성공:true,실패:false)
		$row['count'] = $cnt; // 처리된 갯수
		$row['error'] = $ErrorMsg; // 에러메시지

		return $row;
	}

	// 쿼리실행
	function query($SQL,$transaction=false)
	{
		$db = $this->db;
		$conn = $this->dbconn;
		$phpversion = $this->version;
		$ErrorMsg = "";
		if($conn){
			switch($db['dbdriver']){
				case "mysql":
					$Rs = $conn->query($SQL);
					if(!$Rs) $ErrorMsg = mysqli_errno($conn) . " : " . mysqli_error($conn);
					break;
				case "oracle":
					$Rs = oci_parse($conn,$SQL);
					if($transaction==true){
						@oci_execute($Rs, OCI_DEFAULT);
					}else{
						@oci_execute($Rs);
					}
					if ($Error = oci_error($Rs)) {
						$ErrorMsg = "[".addslashes(trim($Error['code']))."] ".addslashes(trim($Error['message']));
					}
					break;
				case "mssql":
					$Rs = sqlsrv_query($conn,$SQL);
					if($Error=sqlsrv_errors()) $ErrorMsg = "[".addslashes(trim($Error['code']))."] ".addslashes(trim($Error['message']));
					break;
			}
//			if($ErrorMsg && $_SERVER['REMOTE_ADDR']=="210.220.189.63"){
//				echo"<div style='text-align:left;width:100%;font-size:11px;line-height:100%;'><span style='color:#ff0000'>$ErrorMsg</span><pre style='padding-left:10px;'>Error Query : $SQL</pre><div>";
//			}
			if($ErrorMsg){
				echo $ErrorMsg ."\n";
				echo"Error Query : ".$SQL."\n";

				if($ErrorMsg){
					$slack = [];
					$slack['title'] = "Query Error";
					$slack['type'] = "mrkdwn";
					$slack['message'] .= "`Error`  ```".$ErrorMsg."```\n";
					$slack['message'] .= "`Query`  ```".$SQL."```\n";
					$slack_rs = SLACK_MESSAGE($slack);
				}
			}
		}
		return $Rs;
	}

	// 쿼리값을 배열로 돌려줌
	/*
	$arrRow = query_rows($SQL);
	if($arrRow){
		foreach($arrRow as $key => $row) {
			echo"$row[A] / $row[B]";
		}
	}
	keyType : name,num
	*/
	function query_rows($SQL,$keyType="",$transaction=false) // ROW값이 1줄이상일때
	{
		//$resource = $conn['resource'];
		//$db = $conn['db'];
		//$phpversion = phpversion();
		$db = $this->db;
		$conn = $this->dbconn;
		$phpversion = $this->version;
		$n = 0;
		$rows = [];
		$Rs = $this->query($SQL,$transaction);
		if($Rs){
			switch($db['dbdriver']){
				case "mysql":
					if($phpversion > 7){
						if($keyType=="num"){
							while ($row = $Rs->fetch_row()) {
								$rows[$n++] = $row;
							}
						}else{
							while ($row = $Rs->fetch_assoc()) {
								$rows[$n++] = $row;
							}
						}
						$Rs->close();
					}else{
						if($keyType=="num"){
							while ($row = mysql_fetch_row ($Rs)) {
								$rows[$n++] = $row;
							}
						}else{
							while ($row = mysql_fetch_assoc ($Rs)) {
								$rows[$n++] = $row;
							}
						}
						mysql_free_result ($Rs);
					}
					break;
				case "oracle":
					if($phpversion > 7){
						if($keyType=="num"){
							while($row = @oci_fetch_row($Rs)) {
								$rows[$n++] = $row;
							}
						}else{
							while($row = @oci_fetch_assoc($Rs)) {
								$rows[$n++] = $row;
							}
						}
						oci_free_statement($Rs);
					}else{
						if($keyType=="num"){
							while(@OCIFetchinto($Rs,$row,OCI_RETURN_NULLS)){
								$rows[$n++] = $row;
							}
						}else{
							while(@OCIFetchinto($Rs,$row,OCI_ASSOC+OCI_RETURN_NULLS)){
								$rows[$n++] = $row;
							}
						}

						OCIFreeStatement($Rs);
					}
					break;
				case "mssql":
					if($phpversion > 7){
						if($keyType=="num"){
							while ($row = sqlsrv_fetch_array($Rs, SQLSRV_FETCH_NUMERIC)) {
								$rows[$n++] = $row;
							}
						}else{
							while ($row = sqlsrv_fetch_array($Rs, SQLSRV_FETCH_ASSOC)) {
								$rows[$n++] = $row;
							}
						}
						sqlsrv_free_stmt($Rs);
					}else{
						if($keyType=="num"){
							while ($row = mssql_fetch_row ($Rs)) {
								$rows[$n++] = $row;
							}
						}else{
							while ($row = mssql_fetch_assoc ($Rs)) {
								$rows[$n++] = $row;
							}
						}
						mssql_free_result ($Rs);
					}
					break;
			}
		}
		return $rows;
	}

	// $row = db->query_row($SQL);
	function query_row($SQL,$keyType="",$transaction=false) // ROW값이 1줄만 출력
	{
		$rows = $this->query_rows($SQL,$keyType,$transaction);
		$row = [];
		if($rows) $row = $rows[0];
		return $row;
	}

	// $row = db->query_one($SQL);
	function query_one($SQL,$transaction=false) // 값이 1개 일때
	{
		$row = $this->query_row($SQL,'num',$transaction);
		$val = "";
		if($row) $val = $row[0];
		return $val;
	}

	####### [ 쿼리 키값으로 배열 만듬 ] #######
	function query_array($sql,$key,$value,$transaction=false){
		$rows = $this->query_rows($sql,"",$transaction);
		if($rows){
			foreach($rows as $row) {
				$arr[$row[$key]] = $row[$value];
			}
		}
		return $arr;
	}

//	// 트렌젝션 쿼리실행
//	function query_transaction($SQL){
//		$db = $this->db;
//		$conn = $this->dbconn;
//		$phpversion = $this->version;
//		$ErrorMsg = "";
//		if($conn){
//			switch($db['dbdriver']){
//				case "mysql":
//					if($phpversion > 7){
//						$Rs = $conn->query($SQL);
//						if(!$Rs) $ErrorMsg = mysqli_errno($conn) . " : " . mysqli_error($conn);
//					}else{
//						$Rs = mysql_query($SQL,$conn);
//						if(!$Rs) $ErrorMsg = mysql_errno($conn) . " : " . mysql_error($conn);
//					}
//					break;
//				case "oracle":
//					if($phpversion > 7){
//						$Rs = oci_parse($conn,$SQL);
//						//@oci_execute($Rs, OCI_NO_AUTO_COMMIT);
//						@oci_execute($Rs, OCI_DEFAULT);
//						if ($Error = oci_error($Rs)) {
//							$ErrorMsg = "[".addslashes(trim($Error['code']))."] ".addslashes(trim($Error['message']));
//						}
//					}else{
//						$Rs = OCIParse($conn,$SQL);
//						@OCIExecute($Rs,OCI_DEFAULT);
//						$Error=OCIError($Rs);
//						if($Error = OCIError($Rs)){
//							$ErrorMsg = "[".addslashes(trim($Error['code']))."] ".addslashes(trim($Error['message']));
//						}
//					}
//					break;
//			}
//			if($ErrorMsg && $_SERVER['REMOTE_ADDR']=="**************"){
//				echo"<div style='text-align:left;width:100%;font-size:11px;line-height:100%;'><span style='color:#ff0000'>$ErrorMsg</span><pre style='padding-left:10px;'>Error Query : $SQL</pre><div>";
//			}
//		}
//		$row[0] = $sw; // 성공유무(성공0,실패1)
//		$row[1] = $cnt; // 처리된 갯수
//		$row[2] = $ErrorMsg; // 에러메시지
//		return $row;
//	}

	// $id = db->insert_id();
	function insert_id()
	{
		$db = $this->db;
		$conn = $this->dbconn;
		$phpversion = $this->version;
		$id = "";
		if($conn){
			switch($db['dbdriver']){
				case "mysql":
					$id = $conn->insert_id;
					break;
			}
		}
		return $id;
	}

	####### [ BEGIN ] #######
	function begin()
	{
		$db = $this->db;
		$conn = $this->dbconn;
		$phpversion = $this->version;
		if($conn){
			switch($db['dbdriver']){
				case "mysql":
					if($phpversion > 7){
						//$conn->begin_transaction(MYSQLI_TRANS_START_READ_ONLY);
						//$conn->begin_transaction(MYSQLI_TRANS_START_READ_WRITE);
						$conn->begin_transaction(MYSQLI_TRANS_START_WITH_CONSISTENT_SNAPSHOT);
					}else{
						$Rs = mysql_query("begin");
					}
					break;
				case "mssql":
					if($phpversion > 7){
						sqlsrv_begin_transaction($conn);
					}else{
						$Rs = mssql_query("begin");
					}
					break;
			}
		}
		return;
	}


	function rollback()
	{
		$db = $this->db;
		$conn = $this->dbconn;
		$phpversion = $this->version;
		if($conn){
			switch($db['dbdriver']){
				case "mysql":
					if($phpversion > 7){
						//$conn->begin_transaction(MYSQLI_TRANS_START_READ_ONLY);
						//$conn->begin_transaction(MYSQLI_TRANS_START_READ_WRITE);
						$conn->begin_transaction(MYSQLI_TRANS_START_WITH_CONSISTENT_SNAPSHOT);
					}else{
						mysql_query("rollback");
					}
					break;
				case "oracle":
					if($phpversion > 7){
						oci_rollback($conn);
					}else{
						ocirollback($conn);
					}
					break;
				case "mssql":
					if($phpversion > 7){
						sqlsrv_rollback($conn);
					}else{
						mssql_query("rollback");
					}
					break;
			}
		}
		return;
	}

	function commit()
	{
		$db = $this->db;
		$conn = $this->dbconn;
		$phpversion = $this->version;
		if($conn){
			switch($db['dbdriver']){
				case "mysql":
					if($phpversion > 7){
						//$conn->begin_transaction(MYSQLI_TRANS_START_READ_ONLY);
						//$conn->begin_transaction(MYSQLI_TRANS_START_READ_WRITE);
						$conn->begin_transaction(MYSQLI_TRANS_START_WITH_CONSISTENT_SNAPSHOT);
					}else{
						mysql_query("commit");
					}
					break;
				case "oracle":
					if($phpversion > 7){
						oci_commit($conn);
					}else{
						ocicommit($conn);
					}
					break;
				case "mssql":
					if($phpversion > 7){
						sqlsrv_commit($conn);
					}else{
						mssql_query("commit");
					}
					break;
			}
		}
		return;
	}















/*
	private function connectDB($db=[]) {
		print_r($db);
//        $dbconn = mysqli_connect($this->host, $this->userid, $this->password, $this->database);
//        mysqli_set_charset($dbconn, "utf8"); // DB설정이 잘못되어 euc-kr 로 되어 있으면 문제가 됨
//        if (mysqli_connect_errno()) {
//           printf("Connect failed: %s\n", mysqli_connect_error());
//           exit();
//        } else {
//          return $dbconn;
//        }
	}

	//DB-UID데이터
	function getUidData($table,$uid){
		return $this->getDbData($table,'uid='.(int)$uid,'*');
	}

	// DB Query Cutom 함수
	function getDbData($table,$where,$column) {
		$result = mysqli_query($this->db,'select '.$column.' from '.$table.($where?' where '.$this->getSqlFilter($where):''));
		$row = mysqli_fetch_array($result);
		return $row;
	}

	// DB Query result 함수
	function getDbresult($table,$where,$column) {
		$result = mysqli_query($this->db,'select '.$column.' from '.$table.($where?' where '.$this->getSqlFilter($where):''));
		return $result;
	}

	//DB데이터 ARRAY -> 테이블에 출력할 데이터 배열
	function getDbArray($table,$where,$flddata,$orderby,$rowsPage,$curPage){
		$sql = 'select '.$flddata.' from '.$table.($where?' where '.$this->getSqlFilter($where):'').($orderby?' order by '.$orderby:'').($rowsPage?' limit '.(($curPage-1)*$rowsPage).', '.$rowsPage:'');
		if($result = mysqli_query($this->db,$sql)){
			return $result;
		}
	}

	//DB데이터 레코드 총 개수
	function getDbRows($table,$where){
		$sql = 'select count(*) from '.$table.($where?' where '.$this->getSqlFilter($where):'');
		if($result = mysqli_query($this->db,$sql)){
			$rows = mysqli_fetch_row($result);
			return $rows[0] ? $rows[0] : 0;
		}
	}

	//DB삽입
	function getDbInsert($table,$key,$val){
		mysqli_query($this->db,"insert into ".$table." (".$key.")values(".$val.")");
	}

	//DB업데이트
	function getDbUpdate($table,$set,$where){
		mysqli_query('set names utf8');
		mysqli_query('set sql_mode=\'\'');
		mysqli_query($this->db,"update ".$table." set ".$set.($where?' where '.$this->getSqlFilter($where):''));
	}

	//DB삭제
	function getDbDelete($table,$where)    {
		mysqli_query($this->db,"delete from ".$table.($where?' where '.$this->getSqlFilter($where):''));
	}

	//SQL필터링
	function getSqlFilter($sql){
		return $sql;
	}
*/


}//end dbClass

?>
