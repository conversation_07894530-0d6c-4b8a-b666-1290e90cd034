#!/usr/local/bin/php -q
<?
// 0 9 * * * php -q /home/<USER>/sperp/notice_not_reply.psh
# 변경점 알림 승인기한 내 응답 없을때
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
$temp_folder = $ROOT_PATH."/temp";

$arr_db = [];
$arr_query = [];
//$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
$dbconn_sperp_posbank = new DBController($db['sperp_test']);
if(empty($dbconn_sperp_posbank->success)) {
	echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패 \n";
}
/**********************************************************/


echo date("Y-m-d H:i:s")." - 시작 \n";

	$SQL = "select 
					A.GU2,A.IDX,A.TITLE,A.AE_DATE,A.FILE_CODE,A.CHANGE_MANT_LINK
					,B.CT_CODE,B.STATE,B.RDATE,B.RMEMO,B.ADATE
				from 
					NOTICE A
					left join NOTICE_VIEW B on A.IDX=B.IDX
				where 
					A.GU='A' and A.STATE='0'
					and A.APPROVAL_YN='Y' and B.STATE in ('1','2')";
	$arrRow = $dbconn_sperp_posbank->query_rows($SQL);
	
	if($arrRow){
		foreach($arrRow as $key => $row) {


//			$mail = [];
//			$file_no = 0;
//
//			$excel_file = excel_save($row['IDX']);
//			$mail['file'][$file_no]['name'] = "POSBANK_ECN.xlsx";
//			$mail['file'][$file_no]['type'] = "";
//			$mail['file'][$file_no]['tmp_name'] = $temp_folder . "/" . $excel_file;
//			$mail['file'][$file_no]['size'] = filesize($temp_folder . "/" . $excel_file);
//			$file_no++;

			$SQL = "select CT_CODE,CS_NAME,CS_MAIL 
						from CT_ST 
						where CT_CODE='".$row['CT_CODE']."' and CS_END='0' and CS_MAIL is not null ";
			$rows = $dbconn_sperp_posbank->query_rows($SQL);
			if($rows){
//				if($row['FILE_CODE']){
//					$files = $FileFunction->_LINK_FILE($row['FILE_CODE']);
//					if($files){
//						foreach($files as $file2) {
//							$mail['file'][$file_no]['name'] = $file2['OFNAME'];
//							$mail['file'][$file_no]['tmp_name'] = $file2['file_src'];
//							$mail['file'][$file_no]['size'] = $file2['FSIZE'];
//							$mail['file'][$file_no]['type'] = $file2['FTYPE'];
//							$file_no++;
//						}
//					}
//				}

				// 단축URL 생성
				$dbconn_sperp_master = new DBController(_db['sperp_master']);
				if(!empty($dbconn_sperp_master->success)) {
					$query = "insert into short_url (URL, CID, PAGE_CODE, HID, IDATE, UDATE) values ";
					$query .= "('https://scm.posbank.com', '".$_SESSION['CID']."', 'MTE3OTQ=', '".$row['CHANGE_MANT_LINK']."', now(), now()) ";
					$rs = $dbconn_sperp_master->query($query);
					$short_url_idx = $dbconn_sperp_master->insert_id();
					$short_url_idx2 = $BasicFunction->aes128_encode($short_url_idx);
				}


				$mail['cc'] = ""; // 참조
				$mail['bcc'] = ""; // 숨은참조
				$mail['from'] = $_POST['FROM_MAIL1'][$GU2]; // 보내는사람 메일
				$mail['fromname'] = $_POST['FROM_MAIL2'][$GU2]; // 보내는사람명
				$mail['subject'] = $_POST['TITLE'][$GU2]; // 제목
				if(empty($mail['subject'])) $mail['subject'] = ($GU2=='KOR')?"[포스뱅크] 변경점 공지 (ECN)":"[POSBANK] ECN Notice";

				$mail['body'] = "<div style='font-size:12px;'>";
				$mail['body'] .= nl2br($_POST['CONTENT'][$GU2]); // 내용
				$mail['body'] .= "</div>";
				if($_POST['APPROVAL_YN'][$GU2]=='Y'){
					$mail['body'] .= "<br><br><a href='https://scm.posbank.com/?__".$short_url_idx2."'>[";
					$mail['body'] .= ($GU2=='KOR')?"승인하러 가기":"go to approve";
					$mail['body'] .= "]</a>";
				}
				$mail['body'] .= "<br><br><a href='https://www.posbank.com'><img src='https://".$_SERVER['HTTP_HOST']."/images/posbank_banner.jpg' style='width:1300px;height:325px;'></a>";

				foreach($rows as $row) {
					unset($mail['to']);
					unset($mail['toname']);
					$mail['to'] = $row['CS_MAIL']; // 받는사람 메일
					$mail['toname'] = $row['CS_NAME']; // 받는사람명
					$mail_rs = $BasicFunction->mailsend($mail); // 메일발송
					if($mail_rs=='YES') $mail_cnt['Y']++;
					if($mail_rs=='NO') $mail_cnt['N']++;
				}
			}

		}
	}
print_r($arrRow);
exit;
foreach($arr_db as $cms_id => $cms_conn) {

	$SQL = "select max(EA22_INX) from CMS_RESULT_LOG where CMS_ID='".$cms_id."'";
	$EA22_INX = $dbconn_sperp_posbank->query_one($SQL);

	$SQL = "select ea22_inx,mem_inx,ext_inx,file_name,bank_code,amount,div_amount,result,result_code,result_msg,send_stat,in_time 
				from file_ea22 
				where ea22_inx>'".$EA22_INX."' 
				order by ea22_inx";
	$arrRow = $cms_conn->query_rows($SQL);
	if($arrRow){
		foreach($arrRow as $key => $row) {
			$arr_query[] = "insert into CMS_RESULT_LOG 
										(CMS_ID,EA22_INX,MEM_INX,EXT_INX,FILE_NAME,BANK_CODE,AMOUNT,DIV_AMOUNT,RESULT,RESULT_CODE,RESULT_MSG,SEND_STAT,IN_TIME)
									values 
										('".$cms_id."','".$row['ea22_inx']."','".$row['mem_inx']."','".$row['ext_inx']."','".$row['file_name']."','".$row['bank_code']."','".$row['amount']."','".$row['div_amount']."','".$row['result']."','".$row['result_code']."','".$row['result_msg']."','".$row['send_stat']."','".$row['in_time']."')
			";
		}
	}
}
if($arr_query){
	$rs = $dbconn_sperp_posbank->iud_query($arr_query,true);
	if($rs['state']){
		$msg = number_format($rs['count']) . "건 등록";
	}else{
		$msg = "전산장애!! " . $rs['error'];
	}
}else{
	$msg = "없음";
}
echo date("Y-m-d H:i:s") . " - " . $msg . "\n";


## 스케즐 처리 상황 monitor DB에 저장 
//crontab_execution(86400, "CMS처리결과 ERP에 연동");

echo "================================================== \n";

	function excel_save($idx) 
	{
		global $BasicFunction,$FileFunction,$ROOT_PATH,$dbconn_sperp_posbank,$temp_folder;

		$arr_SCOPE = [];
		$arr_SCOPE['KOR'] = ['EP01'=>'사내 변경','EP02'=>'사외 변경'];
		$arr_SCOPE['ENG'] = ['EP01'=>'Internal (In-house) change','EP02'=>'External (Customer) change'];
		$arr_TARGET = ['EQ01'=>'Man','EQ02'=>'Machine','EQ03'=>'Method','EQ04'=>'Material','EQ99'=>'Etc'];

		$SQL = "select 
						IDX,GU,TITLE,CONTENT,IDATE,STATE,FILE_CODE,VIEW_CNT,VIEW_TYPE,CHANGE_MANT_LINK
,						REG_STCODE,REG_DATE,REG_ISTCODE,REG_IDATE,FROM_MAIL,GU2
						,ETC1,ETC2,ETC3,ETC4,ETC5,ETC6,ETC7 
						,TO_CHAR(TO_DATE(AE_DATE,'YYYY-MM-DD'),'YYYY-MM-DD') AE_DATE2
					from NOTICE 
					where IDX='".$idx."' ";
		$notice_row = $dbconn_sperp_posbank->query_row($SQL);

		$RCT_CODE = substr($notice_row['CHANGE_MANT_LINK'],0,4);
		$HDATE = substr($notice_row['CHANGE_MANT_LINK'],4,8);
		$HNO = substr($notice_row['CHANGE_MANT_LINK'],12,4);

		$SQL = "SELECT 
						RCT_CODE||HDATE||HNO HID
						,RCT_CODE,HDATE,HNO,STATE,DEPARTMENT_BAS,SCOPE_BAS,TARGET_BAS
						,F_BAS(DEPARTMENT_BAS,'NAME') DEPARTMENT_BAS_NM
						,ECR_MEMO1,ECR_MEMO2,ECR_MEMO3,ECR_MEMO4
						,FILECODE1,FILECODE2,FILECODE3
						,PCN_MEMO1,PCN_MEMO2,PCN_MEMO2_GU,PCN_MEMO3,PCN_MEMO3_GU,PCN_MEMO4
						,PCN_MEMO1_FILE,PCN_MEMO2_FILE,PCN_MEMO3_FILE
						,APPROVER_CODE,REG_STCODE,REG_DATE,REG_ISTCODE,REG_IDATE
						,STCODE,STCODE_B,STCODE_C,STCODE_D
						,F_STNAME(STCODE) STNAME
						,F_STNAME(STCODE_B) STNAME_B
						,F_STNAME(STCODE_C) STNAME_C
						,F_STNAME(STCODE_D) STNAME_D
						,TO_CHAR(REG_IDATE,'YYYY.MM.DD') REG_IDATE2
						,TO_CHAR(REG_DATE_B,'YYYY.MM.DD') REG_DATE_B2
						,TO_CHAR(REG_DATE_C,'YYYY.MM.DD') REG_DATE_C2
						,TO_CHAR(REG_DATE_D,'YYYY.MM.DD') REG_DATE_D2
						,IOS_MEMO,PCCB_MEMO
						,NOTICE_GU,MODEL
					FROM CHANGE_MANT
					WHERE RCT_CODE='".$RCT_CODE."' AND HDATE='".$HDATE."' AND HNO='".$HNO."'"; // echo nl2br($SQL);
		$cm_row = $dbconn_sperp_posbank->query_row($SQL);


		//PHPExcel
		require_once($ROOT_PATH."/lib/PHPExcel/Classes/PHPExcel.php");

		$objPHPExcel = new PHPExcel();
		$sheet = $objPHPExcel->getActiveSheet();

		$sheet->getDefaultStyle()->getFont()->setName('맑은 고딕')->setSize(8);
		$sheet->getDefaultStyle()->getNumberFormat()->setFormatCode( PHPExcel_Style_NumberFormat::FORMAT_TEXT );
		//$sheet->getDefaultStyle()->getAlignment()->setWrapText(true); // 줄바꿈 허용
		$objPHPExcel->setActiveSheetIndex(0);


		// 제목
		$Excel_Title = 'ECN';
		$sheet->setTitle($Excel_Title);
		//$sheet->setCellValue('A1',$Excel_Title);
		$sheet->getStyle('A1')->getFont()->setSize(14)->setBold(true);
		$sheet->getRowDimension(1)->setRowHeight(30); // 셀 높이
		$sheet->getStyle('A1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER); // 가운데 정렬

		//글자 굵게 
		$sheet->getStyle('B2')->getFont()->setBold(true);
		$sheet->getStyle('B6:B30')->getFont()->setBold(true);
		$sheet->getStyle('D6:D7')->getFont()->setBold(true);
		$sheet->getStyle('C13')->getFont()->setBold(true);
		$sheet->getStyle('C16')->getFont()->setBold(true);
		//글자 사이즈 
		$sheet->getStyle('B2')->getFont()->setSize(16);

		// 열 넓이 조절
		$sheet->getColumnDimension('A')->setWidth(2);
		$sheet->getColumnDimension('B')->setWidth(14);
		$sheet->getColumnDimension('C')->setWidth(45);
		$sheet->getColumnDimension('D')->setWidth(14);
		$sheet->getColumnDimension('E')->setWidth(15);
		$sheet->getColumnDimension('F')->setWidth(15);
		$sheet->getColumnDimension('G')->setWidth(15);

		//행 높이 조절
		$sheet->getRowDimension(1)->setRowHeight(10);
		$sheet->getRowDimension(2)->setRowHeight(12);
		$sheet->getRowDimension(3)->setRowHeight(30);
		$sheet->getRowDimension(4)->setRowHeight(12);
		$sheet->getRowDimension(5)->setRowHeight(5);
		$sheet->getRowDimension(6)->setRowHeight(20);
		$sheet->getRowDimension(7)->setRowHeight(20);
		$sheet->getRowDimension(8)->setRowHeight(20);
		$sheet->getRowDimension(9)->setRowHeight(20);
		$sheet->getRowDimension(10)->setRowHeight(5);
		$sheet->getRowDimension(11)->setRowHeight(20);
		$sheet->getRowDimension(12)->setRowHeight(50);
		$sheet->getRowDimension(13)->setRowHeight(20);
		$sheet->getRowDimension(14)->setRowHeight(160);
		$sheet->getRowDimension(15)->setRowHeight(50);
		$sheet->getRowDimension(16)->setRowHeight(20);
		$sheet->getRowDimension(17)->setRowHeight(160);
		$sheet->getRowDimension(18)->setRowHeight(50);
		$sheet->getRowDimension(19)->setRowHeight(20);
		$sheet->getRowDimension(20)->setRowHeight(50);
		$sheet->getRowDimension(21)->setRowHeight(50);
		$sheet->getRowDimension(22)->setRowHeight(50);
		$sheet->getRowDimension(23)->setRowHeight(20);
		$sheet->getRowDimension(24)->setRowHeight(20);
		$sheet->getRowDimension(25)->setRowHeight(20);
		$sheet->getRowDimension(26)->setRowHeight(20);
		$sheet->getRowDimension(27)->setRowHeight(20);


		// 셀병합
		$sheet->mergeCells('B1:G1');
		$sheet->mergeCells('B2:D4');
		$sheet->mergeCells('B5:G5');
		$sheet->mergeCells('E6:G6');
		$sheet->mergeCells('E7:G7');
		$sheet->mergeCells('C8:G8');
		$sheet->mergeCells('C9:G9');
		$sheet->mergeCells('B10:G10');
		$sheet->mergeCells('C11:G11');
		$sheet->mergeCells('C12:G12');
		$sheet->mergeCells('B13:B18');
		$sheet->mergeCells('C13:G13');
		$sheet->mergeCells('C14:G14');
		$sheet->mergeCells('C15:G15');
		$sheet->mergeCells('C16:G16');
		$sheet->mergeCells('C17:G17');
		$sheet->mergeCells('C18:G18');
		$sheet->mergeCells('C19:G19');
		$sheet->mergeCells('C20:G20');
		$sheet->mergeCells('C21:G21');
		$sheet->mergeCells('C22:G22');
		$sheet->mergeCells('C23:G23');
		$sheet->mergeCells('C24:G24');
		$sheet->mergeCells('C25:G25');
		$sheet->mergeCells('C26:G26');
		$sheet->mergeCells('C27:G27');

		// 줄바꿈 허용
		$sheet->getStyle('A2:H50')->getAlignment()->setWrapText(true);

		//가로정렬 
		// 중앙 정렬
		$sheet->getStyle('B2:G4')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getStyle('B6:B27')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getStyle('D6:D7')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getStyle('B6:G7')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getStyle('C13')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getStyle('C16')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		// 오른쪽 정렬
		//$sheet->getStyle('A2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_RIGHT); 
		// 왼쪽 정렬
		//$sheet->getStyle('A2')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_LEFT);

		//세로정렬 
		$sheet->getStyle('A1:G30')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
		$sheet->getStyle('C12')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
		$sheet->getStyle('C15')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
		$sheet->getStyle('C18')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);
		$sheet->getStyle('C20:C22')->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_TOP);

		//배경색 
		//$sheet->getStyle('B2:G4')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setARGB("FFD9D9D9");

		// 문자형
		//$sheet->getDefaultStyle()->getNumberFormat()->setFormatCode( PHPExcel_Style_NumberFormat::FORMAT_TEXT );


		$sheet->setCellValue("B2", ($notice_row['GU2']=="KOR")?"변경점 공지 (ECN)":"ECN");

		$sheet->setCellValue("E2", ($notice_row['GU2']=="KOR")?"작성":"Written by");
		$sheet->setCellValue("E3", $cm_row['STNAME']);
		$sheet->setCellValue("E4", $cm_row['REG_IDATE2']);
		
		$sheet->setCellValue("F2", ($notice_row['GU2']=="KOR")?"검토":"Reviewed by");
		$sheet->setCellValue("F3", $cm_row['STNAME_B']);
		$sheet->setCellValue("F4", $cm_row['REG_DATE_B2']);

		$sheet->setCellValue("G2", ($notice_row['GU2']=="KOR")?"승인":"Approved by");
		$sheet->setCellValue("G3", $cm_row['STNAME_D']);
		$sheet->setCellValue("G4", $cm_row['REG_DATE_D2']);

		$sheet->setCellValue("B6", ($notice_row['GU2']=="KOR")?"문서번호":"No.");
		//$sheet->setCellValue("C6", $notice_row['CHANGE_MANT_LINK']);
		$sheet->setCellValueExplicit("C6", $notice_row['CHANGE_MANT_LINK'],PHPExcel_Cell_DataType::TYPE_STRING);

		$sheet->setCellValue("D6", ($notice_row['GU2']=="KOR")?"작성일":"Issue Date");
		$sheet->setCellValue("E6", date('Y.m.d'));

		$sheet->setCellValue("B7", ($notice_row['GU2']=="KOR")?"작성 부서":"Department");
		$sheet->setCellValue("C7", $cm_row['DEPARTMENT_BAS_NM']);

		$sheet->setCellValue("D7", ($notice_row['GU2']=="KOR")?"작성자":"Person");
		$sheet->setCellValue("E7", $cm_row['STNAME']);

		$sheet->setCellValue("B8", ($notice_row['GU2']=="KOR")?"변경범위":"Scope");
//		$sheet->setCellValue("C8", check_mark2($arr_SCOPE[$notice_row['GU2']],$cm_row['SCOPE_BAS']));

		$sheet->setCellValue("B9", ($notice_row['GU2']=="KOR")?"변경사항":"Changes");
//		$sheet->setCellValue("C9", check_mark2($arr_TARGET,$cm_row['TARGET_BAS']));

		$sheet->setCellValue("B11", ($notice_row['GU2']=="KOR")?"제품모델":"Product Model");
		$sheet->setCellValue("C11", $cm_row['MODEL']);

		$sheet->setCellValue("B12", ($notice_row['GU2']=="KOR")?"변경사유":"REASON");
		$sheet->setCellValue("C12", $notice_row['ETC1']);

		$sheet->setCellValue("B13", ($notice_row['GU2']=="KOR")?"변경내용":"CHANGE");
		$sheet->setCellValue("C13", ($notice_row['GU2']=="KOR")?"변경전":"BEFORE");
//		$files = $FileFunction->_LINK_FILE($cm_row['FILECODE2']);
//		if($files){
//			foreach($files as $img_idx => $img) {
//				if(in_array($img['file_ext'],array("gif","png","jpeg","jpg"))){
//					$x = ($img_idx==0)?5:50;
//					$cell = ($img_idx==0)?"C14":"D14";
//					$imgSize = $FileFunction->ImgSize($img['file_src'],300,200); // 이미지 크기 구하기
//					$objDrawing = new PHPExcel_Worksheet_Drawing();
//					$objDrawing->setPath($img['file_src']);
//					$objDrawing->setResizeProportional(true);
//					$objDrawing->setWidth($imgSize[0]);
//					$objDrawing->setOffsetX($x);
//					$objDrawing->setOffsetY(5);
//					$objDrawing->setCoordinates($cell);
//					$objDrawing->setWorksheet($sheet);
//				}
//			}
//		}
		$sheet->setCellValue("C15", $notice_row['ETC2']);

		$sheet->setCellValue("C16", ($notice_row['GU2']=="KOR")?"변경후":"AFTER");
//		$files = $FileFunction->_LINK_FILE($cm_row['FILECODE3']);
//		if($files){
//			foreach($files as $img_idx => $img) {
//				if(in_array($img['file_ext'],array("gif","png","jpeg","jpg"))){
//					$x = ($img_idx==0)?5:50;
//					$cell = ($img_idx==0)?"C17":"D17";
//					$imgSize = $FileFunction->ImgSize($img['file_src'],300,200); // 이미지 크기 구하기
//					$objDrawing = new PHPExcel_Worksheet_Drawing();
//					$objDrawing->setPath($img['file_src']);
//					$objDrawing->setResizeProportional(true);
//					$objDrawing->setWidth($imgSize[0]);
//					$objDrawing->setOffsetX($x);
//					$objDrawing->setOffsetY(5);
//					$objDrawing->setCoordinates($cell);
//					$objDrawing->setWorksheet($sheet);
//				}
//			}
//		}
		$sheet->setCellValue("C18", $notice_row['ETC3']);

		$sheet->setCellValue("B19", ($notice_row['GU2']=="KOR")?"승인 완료 기한":"Due date for ECN Approval");
		$sheet->setCellValue("C19", $notice_row['AE_DATE2']);

		$sheet->setCellValue("B20", ($notice_row['GU2']=="KOR")?"고객사 질의":"Q/A");
		$sheet->setCellValue("C20", $notice_row['ETC5']);

		$sheet->setCellValue("B21", ($notice_row['GU2']=="KOR")?"변경 적용 시점":"New component implementation timeline");
		$sheet->setCellValue("C21", $notice_row['ETC6']);

		$sheet->setCellValue("B22", ($notice_row['GU2']=="KOR")?"기타":"REMARK");
		$sheet->setCellValue("C22", $notice_row['ETC7']);

		$sheet->setCellValue("B23", ($notice_row['GU2']=="KOR")?"유첨-1":"Attachment-1");
		$sheet->setCellValue("B24", ($notice_row['GU2']=="KOR")?"유첨-2":"Attachment-2");
		$sheet->setCellValue("B25", ($notice_row['GU2']=="KOR")?"유첨-3":"Attachment-3");
		$sheet->setCellValue("B26", ($notice_row['GU2']=="KOR")?"유첨-4":"Attachment-4");
		$sheet->setCellValue("B27", ($notice_row['GU2']=="KOR")?"유첨-5":"Attachment-5");
//		$files = $FileFunction->_LINK_FILE($notice_row['FILE_CODE']);
//		if($files){
//			foreach($files as $file_idx => $file) {
//				$sheet->setCellValue("C".(23+$file_idx), $file['OFNAME']."    ".$file['file_size_name']);
//			}
//		}


		// 테두리
		// 셀 전체(윤곽선 + 안쪽)
		$sheet->getStyle('B2:G27')->getBorders()->getAllBorders()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);

		// 외곽
		$sheet->getStyle('B2:G27')->getBorders()->getOutline()->setBorderStyle(PHPExcel_Style_Border::BORDER_MEDIUM);

		//배경색 
		$sheet->getStyle('E2:G2')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setARGB("FFEEEEEE");
		$sheet->getStyle('B6:B9')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setARGB("FFEEEEEE");
		$sheet->getStyle('C13')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setARGB("FFEEEEEE");
		$sheet->getStyle('C16')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setARGB("FFEEEEEE");
		$sheet->getStyle('D6:D7')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setARGB("FFEEEEEE");
		$sheet->getStyle('B11:B27')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID)->getStartColor()->setARGB("FFEEEEEE");


		$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
		$temp_file = randomFileName($temp_folder,"xlsx");
		$objWriter->save($temp_folder."/".$temp_file); 
		return $temp_file ;
	}

	####### [ 연결된 파일정보 ] #######
	function _LINK_FILE($code)
	{
		global $file_root_folder;
		$row = "";
		if($code){
			$SQL = "SELECT 
							FCODE,FNAME,OFNAME,FSIZE,FTYPE,CNT,LINK_FCODE
							,replace(FOLDER,'/file/','/files/') FOLDER
							,REG_STCODE,REG_DATE,REG_ISTCODE,REG_IDATE
							,to_char(REG_DATE,'YYYY-MM-DD') REG_DATE2
							,to_char(REG_IDATE,'YYYY-MM-DD') REG_IDATE2
							,F_STNAME(REG_ISTCODE) REG_ISTNAME
						FROM FILES 
						WHERE FCODE='".$code."' OR LINK_FCODE='".$code."'"; //echo $SQL;
			$arrRow = $this->dbconn->query_rows($SQL);
			if($arrRow){
				foreach($arrRow as $key => $row) { 
					$arrRow[$key]['file_ext'] = $this->file_extension($row['OFNAME']); // 파일 확장자
					$arr_nm = explode(".", $row['OFNAME']);
					$arrRow[$key]['file_name'] = $arr_nm[0];
					$arrRow[$key]['file_src'] = $file_root_folder . $row['FOLDER'] . "/" . $row['FNAME'];

					$FILESIZE = $row["FSIZE"]/1024;
					if($FILESIZE<1) $FILESIZE = 1;
					$fileSizsNm = number_format($FILESIZE)."KB";
					if($FILESIZE>=1024) $fileSizsNm = number_format($FILESIZE/1024,1)."MB";
					$arrRow[$key]['file_size_name'] = $fileSizsNm;
					$arrRow[$key]['file_icon'] = "/images/file/" . $this->File_icon($arrRow[$key]['file_ext']);
					if(in_array($arrRow[$key]['file_ext'],array("gif","png","jpeg","jpg"))){
						$image_size = getimagesize($arrRow[$key]['file_src']);
						$arrRow[$key]['image_size']['w'] = $image_size[0];
						$arrRow[$key]['image_size']['h'] = $image_size[1];
					}
				}
			}
		}
		return $arrRow;
	}
?>
