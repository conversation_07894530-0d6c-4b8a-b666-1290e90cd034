<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_JAMA_QRDecomposition</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___construct" title="__construct :: QR Decomposition computed by Householder reflections."><span class="description">QR Decomposition computed by Householder reflections.</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_getH" title="getH :: Return the Householder vectors"><span class="description">Return the Householder vectors</span><pre>getH()</pre></a></li>
<li class="method public "><a href="#method_getQ" title="getQ :: Generate and return the (economy-sized) orthogonal factor"><span class="description">Generate and return the (economy-sized) orthogonal factor</span><pre>getQ()</pre></a></li>
<li class="method public "><a href="#method_getR" title="getR :: Return the upper triangular factor"><span class="description">Return the upper triangular factor</span><pre>getR()</pre></a></li>
<li class="method public "><a href="#method_isFullRank" title="isFullRank :: Is the matrix full rank?"><span class="description">Is the matrix full rank?</span><pre>isFullRank()</pre></a></li>
<li class="method public "><a href="#method_solve" title="solve :: Least squares solution of A*X = B"><span class="description">Least squares solution of A*X = B</span><pre>solve()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property_QR" title="$QR :: Array for internal storage of decomposition."><span class="description"></span><pre>$QR</pre></a></li>
<li class="property private "><a href="#property_Rdiag" title="$Rdiag :: Array for internal storage of diagonal of R."><span class="description"></span><pre>$Rdiag</pre></a></li>
<li class="property private "><a href="#property_m" title="$m :: Row dimension."><span class="description"></span><pre>$m</pre></a></li>
<li class="property private "><a href="#property_n" title="$n :: Column dimension."><span class="description"></span><pre>$n</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul><li class="constant  "><a href="#constant_MatrixRankException" title="MatrixRankException :: "><span class="description">MatrixRankException</span><pre>MatrixRankException</pre></a></li></ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_JAMA_QRDecomposition"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_JAMA_QRDecomposition.html">PHPExcel_Shared_JAMA_QRDecomposition</a>
</li>
</ul>
<div class="element class">
<p class="short_description"></p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>package</th>
<td><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.%0D%0AThe%20QR%20decompostion%20always%20exists,%20even%20if%20the%20matrix%20does%20not%20have%0D%0Afull%20rank,%20so%20the%20constructor%20will%20never%20fail.%20%20The%20primary%20use%20of%20the%0D%0AQR%20decomposition%20is%20in%20the%20least%20squares%20solution%20of%20nonsquare%20systems%0D%0Aof%20simultaneous%20linear%20equations.%20%20This%20will%20fail%20if%20isFullRank()%0D%0Areturns%20false..html">JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R.
The QR decompostion always exists, even if the matrix does not have
full rank, so the constructor will never fail.  The primary use of the
QR decomposition is in the least squares solution of nonsquare systems
of simultaneous linear equations.  This will fail if isFullRank()
returns false.</a></td>
</tr>
<tr>
<th>author</th>
<td><a href="">Paul Meagher</a></td>
</tr>
<tr>
<th>license</th>
<td><a href="">PHP v3.0</a></td>
</tr>
<tr>
<th>version</th>
<td>1.1</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>QR Decomposition computed by Householder reflections.</h2>
<pre>__construct(\matrix $A) : \Structure</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$A</h4>
<code>\matrix</code><p>Rectangular matrix</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>\Structure</code>to access R and the Householder vectors and compute Q.</div>
</div></div>
</div>
<a id="method_getH"></a><div class="element clickable method public method_getH" data-toggle="collapse" data-target=".method_getH .collapse">
<h2>Return the Householder vectors</h2>
<pre>getH() : \Matrix</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>\Matrix</code>Lower trapezoidal matrix whose columns define the reflections</div>
</div></div>
</div>
<a id="method_getQ"></a><div class="element clickable method public method_getQ" data-toggle="collapse" data-target=".method_getQ .collapse">
<h2>Generate and return the (economy-sized) orthogonal factor</h2>
<pre>getQ() : \Matrix</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>\Matrix</code>orthogonal factor</div>
</div></div>
</div>
<a id="method_getR"></a><div class="element clickable method public method_getR" data-toggle="collapse" data-target=".method_getR .collapse">
<h2>Return the upper triangular factor</h2>
<pre>getR() : \Matrix</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>\Matrix</code>upper triangular factor</div>
</div></div>
</div>
<a id="method_isFullRank"></a><div class="element clickable method public method_isFullRank" data-toggle="collapse" data-target=".method_isFullRank .collapse">
<h2>Is the matrix full rank?</h2>
<pre>isFullRank() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>boolean</code>true if R, and hence A, has full rank, else false.</div>
</div></div>
</div>
<a id="method_solve"></a><div class="element clickable method public method_solve" data-toggle="collapse" data-target=".method_solve .collapse">
<h2>Least squares solution of A*X = B</h2>
<pre>solve(\Matrix $B) : \Matrix</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$B</h4>
<code>\Matrix</code><p>A Matrix with as many rows as A and any number of columns.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>\Matrix</code>Matrix that minimizes the two norm of Q*R*X-B.</div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property_QR"> </a><div class="element clickable property private property_QR" data-toggle="collapse" data-target=".property_QR .collapse">
<h2></h2>
<pre>$QR : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_Rdiag"> </a><div class="element clickable property private property_Rdiag" data-toggle="collapse" data-target=".property_Rdiag .collapse">
<h2></h2>
<pre>$Rdiag : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_m"> </a><div class="element clickable property private property_m" data-toggle="collapse" data-target=".property_m .collapse">
<h2></h2>
<pre>$m : integer</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_n"> </a><div class="element clickable property private property_n" data-toggle="collapse" data-target=".property_n .collapse">
<h2></h2>
<pre>$n : integer</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_MatrixRankException"> </a><div class="element clickable constant  constant_MatrixRankException" data-toggle="collapse" data-target=".constant_MatrixRankException .collapse">
<h2>MatrixRankException</h2>
<pre>MatrixRankException </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:36Z.<br></footer></div>
</div>
</body>
</html>
