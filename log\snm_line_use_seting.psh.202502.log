<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250201 | sEdate : 20250315<br>
<br>
 MAX_DAY  : 20250314<br>
 DIFF_DAY  : 42<br>
 20250201 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250202 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250203 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250204 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250205 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250206 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250207 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250208 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250209 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250210 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250211 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250212 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250213 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 이미 등록되어있음......<br>
 20250314 이미 등록되어있음......<br>
 20250314 이미 등록되어있음......<br>
 20250314 이미 등록되어있음......<br>
 20250314 이미 등록되어있음......<br>
 20250315 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250202 | sEdate : 20250316<br>
<br>
 MAX_DAY  : 20250314<br>
 DIFF_DAY  : 42<br>
 20250202 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250203 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250204 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250205 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250206 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250207 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250208 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250209 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250210 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250211 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250212 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250213 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 이미 등록되어있음......<br>
 20250314 이미 등록되어있음......<br>
 20250314 이미 등록되어있음......<br>
 20250314 이미 등록되어있음......<br>
 20250314 이미 등록되어있음......<br>
 20250315 주말일 경우 패스.....<br>
 20250316 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250203 | sEdate : 20250317<br>
<br>
 MAX_DAY  : 20250314<br>
 DIFF_DAY  : 42<br>
 20250203 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250204 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250205 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250206 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250207 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250208 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250209 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250210 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250211 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250212 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250213 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250314 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 이미 등록되어있음......<br>
 20250314 이미 등록되어있음......<br>
 20250314 이미 등록되어있음......<br>
 20250314 이미 등록되어있음......<br>
 20250314 이미 등록되어있음......<br>
 20250315 주말일 경우 패스.....<br>
 20250316 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250203001<br>
 입력 SLUS_ID  : 20250203002<br>
 입력 SLUS_ID  : 20250203003<br>
 입력 SLUS_ID  : 20250203004<br>
 입력 SLUS_ID  : 20250203005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250204 | sEdate : 20250318<br>
<br>
 MAX_DAY  : 20250317<br>
 DIFF_DAY  : 42<br>
 20250204 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250205 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250206 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250207 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250208 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250209 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250210 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250211 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250212 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250213 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250317 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 이미 등록되어있음......<br>
 20250317 이미 등록되어있음......<br>
 20250317 이미 등록되어있음......<br>
 20250317 이미 등록되어있음......<br>
 20250317 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250204001<br>
 입력 SLUS_ID  : 20250204002<br>
 입력 SLUS_ID  : 20250204003<br>
 입력 SLUS_ID  : 20250204004<br>
 입력 SLUS_ID  : 20250204005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250205 | sEdate : 20250319<br>
<br>
 MAX_DAY  : 20250318<br>
 DIFF_DAY  : 42<br>
 20250205 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250206 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250207 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250208 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250209 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250210 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250211 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250212 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250213 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250318 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 이미 등록되어있음......<br>
 20250318 이미 등록되어있음......<br>
 20250318 이미 등록되어있음......<br>
 20250318 이미 등록되어있음......<br>
 20250318 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250205001<br>
 입력 SLUS_ID  : 20250205002<br>
 입력 SLUS_ID  : 20250205003<br>
 입력 SLUS_ID  : 20250205004<br>
 입력 SLUS_ID  : 20250205005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250206 | sEdate : 20250320<br>
<br>
 MAX_DAY  : 20250319<br>
 DIFF_DAY  : 42<br>
 20250206 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250207 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250208 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250209 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250210 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250211 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250212 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250213 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250319 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 이미 등록되어있음......<br>
 20250319 이미 등록되어있음......<br>
 20250319 이미 등록되어있음......<br>
 20250319 이미 등록되어있음......<br>
 20250319 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250206001<br>
 입력 SLUS_ID  : 20250206002<br>
 입력 SLUS_ID  : 20250206003<br>
 입력 SLUS_ID  : 20250206004<br>
 입력 SLUS_ID  : 20250206005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250207 | sEdate : 20250321<br>
<br>
 MAX_DAY  : 20250320<br>
 DIFF_DAY  : 42<br>
 20250207 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250208 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250209 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250210 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250211 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250212 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250213 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250320 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 이미 등록되어있음......<br>
 20250320 이미 등록되어있음......<br>
 20250320 이미 등록되어있음......<br>
 20250320 이미 등록되어있음......<br>
 20250320 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250207001<br>
 입력 SLUS_ID  : 20250207002<br>
 입력 SLUS_ID  : 20250207003<br>
 입력 SLUS_ID  : 20250207004<br>
 입력 SLUS_ID  : 20250207005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250208 | sEdate : 20250322<br>
<br>
 MAX_DAY  : 20250321<br>
 DIFF_DAY  : 42<br>
 20250208 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250209 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250210 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250211 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250212 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250213 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 이미 등록되어있음......<br>
 20250321 이미 등록되어있음......<br>
 20250321 이미 등록되어있음......<br>
 20250321 이미 등록되어있음......<br>
 20250321 이미 등록되어있음......<br>
 20250322 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250209 | sEdate : 20250323<br>
<br>
 MAX_DAY  : 20250321<br>
 DIFF_DAY  : 42<br>
 20250209 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250210 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250211 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250212 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250213 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 이미 등록되어있음......<br>
 20250321 이미 등록되어있음......<br>
 20250321 이미 등록되어있음......<br>
 20250321 이미 등록되어있음......<br>
 20250321 이미 등록되어있음......<br>
 20250322 주말일 경우 패스.....<br>
 20250323 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250210 | sEdate : 20250324<br>
<br>
 MAX_DAY  : 20250321<br>
 DIFF_DAY  : 42<br>
 20250210 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250211 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250212 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250213 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250321 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 이미 등록되어있음......<br>
 20250321 이미 등록되어있음......<br>
 20250321 이미 등록되어있음......<br>
 20250321 이미 등록되어있음......<br>
 20250321 이미 등록되어있음......<br>
 20250322 주말일 경우 패스.....<br>
 20250323 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250210001<br>
 입력 SLUS_ID  : 20250210002<br>
 입력 SLUS_ID  : 20250210003<br>
 입력 SLUS_ID  : 20250210004<br>
 입력 SLUS_ID  : 20250210005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250211 | sEdate : 20250325<br>
<br>
 MAX_DAY  : 20250324<br>
 DIFF_DAY  : 42<br>
 20250211 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250212 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250213 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250324 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 이미 등록되어있음......<br>
 20250324 이미 등록되어있음......<br>
 20250324 이미 등록되어있음......<br>
 20250324 이미 등록되어있음......<br>
 20250324 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250211001<br>
 입력 SLUS_ID  : 20250211002<br>
 입력 SLUS_ID  : 20250211003<br>
 입력 SLUS_ID  : 20250211004<br>
 입력 SLUS_ID  : 20250211005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250212 | sEdate : 20250326<br>
<br>
 MAX_DAY  : 20250325<br>
 DIFF_DAY  : 42<br>
 20250212 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250213 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250325 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 이미 등록되어있음......<br>
 20250325 이미 등록되어있음......<br>
 20250325 이미 등록되어있음......<br>
 20250325 이미 등록되어있음......<br>
 20250325 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250212001<br>
 입력 SLUS_ID  : 20250212002<br>
 입력 SLUS_ID  : 20250212003<br>
 입력 SLUS_ID  : 20250212004<br>
 입력 SLUS_ID  : 20250212005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250213 | sEdate : 20250327<br>
<br>
 MAX_DAY  : 20250326<br>
 DIFF_DAY  : 42<br>
 20250213 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250214 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250326 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 이미 등록되어있음......<br>
 20250326 이미 등록되어있음......<br>
 20250326 이미 등록되어있음......<br>
 20250326 이미 등록되어있음......<br>
 20250326 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250213001<br>
 입력 SLUS_ID  : 20250213002<br>
 입력 SLUS_ID  : 20250213003<br>
 입력 SLUS_ID  : 20250213004<br>
 입력 SLUS_ID  : 20250213005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250214 | sEdate : 20250328<br>
<br>
 MAX_DAY  : 20250327<br>
 DIFF_DAY  : 42<br>
 20250214 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250215 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250327 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 이미 등록되어있음......<br>
 20250327 이미 등록되어있음......<br>
 20250327 이미 등록되어있음......<br>
 20250327 이미 등록되어있음......<br>
 20250327 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250214001<br>
 입력 SLUS_ID  : 20250214002<br>
 입력 SLUS_ID  : 20250214003<br>
 입력 SLUS_ID  : 20250214004<br>
 입력 SLUS_ID  : 20250214005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250215 | sEdate : 20250329<br>
<br>
 MAX_DAY  : 20250328<br>
 DIFF_DAY  : 42<br>
 20250215 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250216 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 이미 등록되어있음......<br>
 20250328 이미 등록되어있음......<br>
 20250328 이미 등록되어있음......<br>
 20250328 이미 등록되어있음......<br>
 20250328 이미 등록되어있음......<br>
 20250329 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250216 | sEdate : 20250330<br>
<br>
 MAX_DAY  : 20250328<br>
 DIFF_DAY  : 42<br>
 20250216 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250217 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 이미 등록되어있음......<br>
 20250328 이미 등록되어있음......<br>
 20250328 이미 등록되어있음......<br>
 20250328 이미 등록되어있음......<br>
 20250328 이미 등록되어있음......<br>
 20250329 주말일 경우 패스.....<br>
 20250330 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250217 | sEdate : 20250331<br>
<br>
 MAX_DAY  : 20250328<br>
 DIFF_DAY  : 42<br>
 20250217 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250218 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250328 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 이미 등록되어있음......<br>
 20250328 이미 등록되어있음......<br>
 20250328 이미 등록되어있음......<br>
 20250328 이미 등록되어있음......<br>
 20250328 이미 등록되어있음......<br>
 20250329 주말일 경우 패스.....<br>
 20250330 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250217001<br>
 입력 SLUS_ID  : 20250217002<br>
 입력 SLUS_ID  : 20250217003<br>
 입력 SLUS_ID  : 20250217004<br>
 입력 SLUS_ID  : 20250217005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250218 | sEdate : 20250401<br>
<br>
 MAX_DAY  : 20250331<br>
 DIFF_DAY  : 42<br>
 20250218 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250219 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250331 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 이미 등록되어있음......<br>
 20250331 이미 등록되어있음......<br>
 20250331 이미 등록되어있음......<br>
 20250331 이미 등록되어있음......<br>
 20250331 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250218001<br>
 입력 SLUS_ID  : 20250218002<br>
 입력 SLUS_ID  : 20250218003<br>
 입력 SLUS_ID  : 20250218004<br>
 입력 SLUS_ID  : 20250218005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250219 | sEdate : 20250402<br>
<br>
 MAX_DAY  : 20250401<br>
 DIFF_DAY  : 42<br>
 20250219 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250220 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250401 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 이미 등록되어있음......<br>
 20250401 이미 등록되어있음......<br>
 20250401 이미 등록되어있음......<br>
 20250401 이미 등록되어있음......<br>
 20250401 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250219001<br>
 입력 SLUS_ID  : 20250219002<br>
 입력 SLUS_ID  : 20250219003<br>
 입력 SLUS_ID  : 20250219004<br>
 입력 SLUS_ID  : 20250219005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250220 | sEdate : 20250403<br>
<br>
 MAX_DAY  : 20250402<br>
 DIFF_DAY  : 42<br>
 20250220 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250221 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250402 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 이미 등록되어있음......<br>
 20250402 이미 등록되어있음......<br>
 20250402 이미 등록되어있음......<br>
 20250402 이미 등록되어있음......<br>
 20250402 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250220001<br>
 입력 SLUS_ID  : 20250220002<br>
 입력 SLUS_ID  : 20250220003<br>
 입력 SLUS_ID  : 20250220004<br>
 입력 SLUS_ID  : 20250220005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250221 | sEdate : 20250404<br>
<br>
 MAX_DAY  : 20250403<br>
 DIFF_DAY  : 42<br>
 20250221 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250222 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250403 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 이미 등록되어있음......<br>
 20250403 이미 등록되어있음......<br>
 20250403 이미 등록되어있음......<br>
 20250403 이미 등록되어있음......<br>
 20250403 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250221001<br>
 입력 SLUS_ID  : 20250221002<br>
 입력 SLUS_ID  : 20250221003<br>
 입력 SLUS_ID  : 20250221004<br>
 입력 SLUS_ID  : 20250221005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250222 | sEdate : 20250405<br>
<br>
 MAX_DAY  : 20250404<br>
 DIFF_DAY  : 42<br>
 20250222 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250223 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 이미 등록되어있음......<br>
 20250404 이미 등록되어있음......<br>
 20250404 이미 등록되어있음......<br>
 20250404 이미 등록되어있음......<br>
 20250404 이미 등록되어있음......<br>
 20250405 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250223 | sEdate : 20250406<br>
<br>
 MAX_DAY  : 20250404<br>
 DIFF_DAY  : 42<br>
 20250223 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250224 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 이미 등록되어있음......<br>
 20250404 이미 등록되어있음......<br>
 20250404 이미 등록되어있음......<br>
 20250404 이미 등록되어있음......<br>
 20250404 이미 등록되어있음......<br>
 20250405 주말일 경우 패스.....<br>
 20250406 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250224 | sEdate : 20250407<br>
<br>
 MAX_DAY  : 20250404<br>
 DIFF_DAY  : 42<br>
 20250224 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250225 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250404 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 이미 등록되어있음......<br>
 20250404 이미 등록되어있음......<br>
 20250404 이미 등록되어있음......<br>
 20250404 이미 등록되어있음......<br>
 20250404 이미 등록되어있음......<br>
 20250405 주말일 경우 패스.....<br>
 20250406 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250224001<br>
 입력 SLUS_ID  : 20250224002<br>
 입력 SLUS_ID  : 20250224003<br>
 입력 SLUS_ID  : 20250224004<br>
 입력 SLUS_ID  : 20250224005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250225 | sEdate : 20250408<br>
<br>
 MAX_DAY  : 20250407<br>
 DIFF_DAY  : 42<br>
 20250225 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250226 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250407 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 이미 등록되어있음......<br>
 20250407 이미 등록되어있음......<br>
 20250407 이미 등록되어있음......<br>
 20250407 이미 등록되어있음......<br>
 20250407 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250225001<br>
 입력 SLUS_ID  : 20250225002<br>
 입력 SLUS_ID  : 20250225003<br>
 입력 SLUS_ID  : 20250225004<br>
 입력 SLUS_ID  : 20250225005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250226 | sEdate : 20250409<br>
<br>
 MAX_DAY  : 20250408<br>
 DIFF_DAY  : 42<br>
 20250226 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250227 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250408 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 이미 등록되어있음......<br>
 20250408 이미 등록되어있음......<br>
 20250408 이미 등록되어있음......<br>
 20250408 이미 등록되어있음......<br>
 20250408 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250226001<br>
 입력 SLUS_ID  : 20250226002<br>
 입력 SLUS_ID  : 20250226003<br>
 입력 SLUS_ID  : 20250226004<br>
 입력 SLUS_ID  : 20250226005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250227 | sEdate : 20250410<br>
<br>
 MAX_DAY  : 20250409<br>
 DIFF_DAY  : 42<br>
 20250227 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250228 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250409 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 이미 등록되어있음......<br>
 20250409 이미 등록되어있음......<br>
 20250409 이미 등록되어있음......<br>
 20250409 이미 등록되어있음......<br>
 20250409 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250227001<br>
 입력 SLUS_ID  : 20250227002<br>
 입력 SLUS_ID  : 20250227003<br>
 입력 SLUS_ID  : 20250227004<br>
 입력 SLUS_ID  : 20250227005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250228 | sEdate : 20250411<br>
<br>
 MAX_DAY  : 20250410<br>
 DIFF_DAY  : 42<br>
 20250228 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250301 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250302 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250303 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250304 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250305 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250306 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250307 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250308 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250309 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250310 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250311 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250312 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250313 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250314 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250315 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250316 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250317 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250318 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250319 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250320 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250321 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250322 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250323 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250324 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250325 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250326 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250327 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250328 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250329 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250330 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250331 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250401 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250402 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250403 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250404 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250405 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250406 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250407 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250408 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250409 < 20250410 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250410 이미 등록되어있음......<br>
 20250410 이미 등록되어있음......<br>
 20250410 이미 등록되어있음......<br>
 20250410 이미 등록되어있음......<br>
 20250410 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250228001<br>
 입력 SLUS_ID  : 20250228002<br>
 입력 SLUS_ID  : 20250228003<br>
 입력 SLUS_ID  : 20250228004<br>
 입력 SLUS_ID  : 20250228005