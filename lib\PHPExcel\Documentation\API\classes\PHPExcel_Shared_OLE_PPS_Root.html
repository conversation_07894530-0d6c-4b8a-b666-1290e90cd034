<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_OLE_PPS_Root</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public inherited"><a href="#method__DataLen" title="_DataLen :: Returns the amount of data saved for this PPS"><span class="description">Returns the amount of data saved for this PPS</span><pre>_DataLen()</pre></a></li>
<li class="method public "><a href="#method___construct" title="__construct :: The constructor"><span class="description">The constructor</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method__calcSize" title="_calcSize :: Calculate some numbers"><span class="description">Calculate some numbers</span><pre>_calcSize()</pre></a></li>
<li class="method public inherited"><a href="#method__getPpsWk" title="_getPpsWk :: Returns a string with the PPS's WK (What is a WK?)"><span class="description">Returns a string with the PPS's WK (What is a WK?)</span><pre>_getPpsWk()</pre></a></li>
<li class="method public "><a href="#method__makeSmallData" title="_makeSmallData :: get small data (PPS's with data smaller than PHPExcel_Shared_OLE::OLE_DATA_SIZE_SMALL)"><span class="description">get small data (PPS's with data smaller than PHPExcel_Shared_OLE::OLE_DATA_SIZE_SMALL)</span><pre>_makeSmallData()</pre></a></li>
<li class="method public "><a href="#method__saveBbd" title="_saveBbd :: Saving Big Block Depot"><span class="description">Saving Big Block Depot</span><pre>_saveBbd()</pre></a></li>
<li class="method public "><a href="#method__saveBigData" title="_saveBigData :: Saving big data (PPS's with data bigger than PHPExcel_Shared_OLE::OLE_DATA_SIZE_SMALL)"><span class="description">Saving big data (PPS's with data bigger than PHPExcel_Shared_OLE::OLE_DATA_SIZE_SMALL)</span><pre>_saveBigData()</pre></a></li>
<li class="method public "><a href="#method__saveHeader" title="_saveHeader :: Save OLE header"><span class="description">Save OLE header</span><pre>_saveHeader()</pre></a></li>
<li class="method public "><a href="#method__savePps" title="_savePps :: Saves all the PPS's WKs"><span class="description">Saves all the PPS's WKs</span><pre>_savePps()</pre></a></li>
<li class="method public inherited"><a href="#method__savePpsSetPnt" title="_savePpsSetPnt :: Updates index and pointers to previous, next and children PPS's for this
PPS."><span class="description">Updates index and pointers to previous, next and children PPS's for this
PPS.</span><pre>_savePpsSetPnt()</pre></a></li>
<li class="method public "><a href="#method_save" title="save :: Method for saving the whole OLE container (including files)."><span class="description">Method for saving the whole OLE container (including files).</span><pre>save()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul><li class="method private "><a href="#method__adjust2" title="_adjust2 :: Helper function for caculating a magic value for block sizes"><span class="description">Helper function for caculating a magic value for block sizes</span><pre>_adjust2()</pre></a></li></ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul>
<li class="property public inherited"><a href="#property_DirPps" title="$DirPps :: The index of it's first child if this is a Dir or Root PPS"><span class="description"></span><pre>$DirPps</pre></a></li>
<li class="property public inherited"><a href="#property_Name" title="$Name :: The PPS name (in Unicode)"><span class="description"></span><pre>$Name</pre></a></li>
<li class="property public inherited"><a href="#property_NextPps" title="$NextPps :: The index of the next PPS"><span class="description"></span><pre>$NextPps</pre></a></li>
<li class="property public inherited"><a href="#property_No" title="$No :: The PPS index"><span class="description"></span><pre>$No</pre></a></li>
<li class="property public inherited"><a href="#property_PrevPps" title="$PrevPps :: The index of the previous PPS"><span class="description"></span><pre>$PrevPps</pre></a></li>
<li class="property public inherited"><a href="#property_Size" title="$Size :: The size of the PPS's data (in bytes)"><span class="description"></span><pre>$Size</pre></a></li>
<li class="property public inherited"><a href="#property_Time1st" title="$Time1st :: A timestamp"><span class="description"></span><pre>$Time1st</pre></a></li>
<li class="property public inherited"><a href="#property_Time2nd" title="$Time2nd :: A timestamp"><span class="description"></span><pre>$Time2nd</pre></a></li>
<li class="property public inherited"><a href="#property_Type" title="$Type :: The PPS type."><span class="description"></span><pre>$Type</pre></a></li>
<li class="property public inherited"><a href="#property__StartBlock" title="$_StartBlock :: Starting block (small or big) for this PPS's data  inside the container"><span class="description"></span><pre>$_StartBlock</pre></a></li>
<li class="property public inherited"><a href="#property__data" title="$_data :: The PPS's data (only used if it's not using a temporary file)"><span class="description"></span><pre>$_data</pre></a></li>
<li class="property public inherited"><a href="#property_children" title="$children :: Array of child PPS's (only used by Root and Dir PPS's)"><span class="description"></span><pre>$children</pre></a></li>
<li class="property public inherited"><a href="#property_ole" title="$ole :: Pointer to OLE container"><span class="description"></span><pre>$ole</pre></a></li>
</ul>
</li>
<li class="nav-header protected">» Protected
                    <ul><li class="property protected "><a href="#property__tmp_dir" title="$_tmp_dir :: Directory for temporary files"><span class="description"></span><pre>$_tmp_dir</pre></a></li></ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_OLE_PPS_Root"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_OLE_PPS_Root.html">PHPExcel_Shared_OLE_PPS_Root</a>
</li>
</ul>
<div class="element class">
<p class="short_description">Class for creating Root PPS's for OLE containers</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>author</th>
<td><a href="mailto:<EMAIL>">Xavier Noguer</a></td>
</tr>
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Shared.OLE.html">PHPExcel_Shared_OLE</a></td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method__DataLen"></a><div class="element clickable method public method__DataLen" data-toggle="collapse" data-target=".method__DataLen .collapse">
<h2>Returns the amount of data saved for this PPS</h2>
<pre>_DataLen() : integer</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::_DataLen()</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>The amount of data (in bytes)</div>
</div></div>
</div>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>The constructor</h2>
<pre>__construct(integer $time_1st, integer $time_2nd, $raChild) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$time_1st</h4>
<code>integer</code><p>A timestamp</p></div>
<div class="subelement argument">
<h4>$time_2nd</h4>
<code>integer</code><p>A timestamp</p></div>
<div class="subelement argument"><h4>$raChild</h4></div>
</div></div>
</div>
<a id="method__calcSize"></a><div class="element clickable method public method__calcSize" data-toggle="collapse" data-target=".method__calcSize .collapse">
<h2>Calculate some numbers</h2>
<pre>_calcSize(array $raList) : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$raList</h4>
<code>array</code><p>Reference to an array of PPS's</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>array</code>The array of numbers</div>
</div></div>
</div>
<a id="method__getPpsWk"></a><div class="element clickable method public method__getPpsWk" data-toggle="collapse" data-target=".method__getPpsWk .collapse">
<h2>Returns a string with the PPS's WK (What is a WK?)</h2>
<pre>_getPpsWk() : string</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::_getPpsWk()</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>The binary string</div>
</div></div>
</div>
<a id="method__makeSmallData"></a><div class="element clickable method public method__makeSmallData" data-toggle="collapse" data-target=".method__makeSmallData .collapse">
<h2>get small data (PPS's with data smaller than PHPExcel_Shared_OLE::OLE_DATA_SIZE_SMALL)</h2>
<pre>_makeSmallData(array $raList) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$raList</h4>
<code>array</code><p>&$raList Reference to array of PPS's</p>
</div>
</div></div>
</div>
<a id="method__saveBbd"></a><div class="element clickable method public method__saveBbd" data-toggle="collapse" data-target=".method__saveBbd .collapse">
<h2>Saving Big Block Depot</h2>
<pre>_saveBbd(integer $iSbdSize, integer $iBsize, integer $iPpsCnt) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$iSbdSize</h4>
<code>integer</code>
</div>
<div class="subelement argument">
<h4>$iBsize</h4>
<code>integer</code>
</div>
<div class="subelement argument">
<h4>$iPpsCnt</h4>
<code>integer</code>
</div>
</div></div>
</div>
<a id="method__saveBigData"></a><div class="element clickable method public method__saveBigData" data-toggle="collapse" data-target=".method__saveBigData .collapse">
<h2>Saving big data (PPS's with data bigger than PHPExcel_Shared_OLE::OLE_DATA_SIZE_SMALL)</h2>
<pre>_saveBigData(integer $iStBlk, array $raList) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$iStBlk</h4>
<code>integer</code>
</div>
<div class="subelement argument">
<h4>$raList</h4>
<code>array</code><p>&$raList Reference to array of PPS's</p>
</div>
</div></div>
</div>
<a id="method__saveHeader"></a><div class="element clickable method public method__saveHeader" data-toggle="collapse" data-target=".method__saveHeader .collapse">
<h2>Save OLE header</h2>
<pre>_saveHeader(integer $iSBDcnt, integer $iBBcnt, integer $iPPScnt) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$iSBDcnt</h4>
<code>integer</code>
</div>
<div class="subelement argument">
<h4>$iBBcnt</h4>
<code>integer</code>
</div>
<div class="subelement argument">
<h4>$iPPScnt</h4>
<code>integer</code>
</div>
</div></div>
</div>
<a id="method__savePps"></a><div class="element clickable method public method__savePps" data-toggle="collapse" data-target=".method__savePps .collapse">
<h2>Saves all the PPS's WKs</h2>
<pre>_savePps(array $raList) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$raList</h4>
<code>array</code><p>Reference to an array with all PPS's</p>
</div>
</div></div>
</div>
<a id="method__savePpsSetPnt"></a><div class="element clickable method public method__savePpsSetPnt" data-toggle="collapse" data-target=".method__savePpsSetPnt .collapse">
<h2>Updates index and pointers to previous, next and children PPS's for this
PPS.</h2>
<pre>_savePpsSetPnt(array $raList, $to_save, $depth) : integer</pre>
<div class="labels">
<span class="label">Inherited</span><span class="label">Static</span>
</div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>I don't think it'll work with Dir PPS's.</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::_savePpsSetPnt()</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$raList</h4>
<code>array</code><p>&$raList Reference to the array of PPS's for the whole OLE
                         container</p>
</div>
<div class="subelement argument"><h4>$to_save</h4></div>
<div class="subelement argument"><h4>$depth</h4></div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>The index for this PPS</div>
</div></div>
</div>
<a id="method_save"></a><div class="element clickable method public method_save" data-toggle="collapse" data-target=".method_save .collapse">
<h2>Method for saving the whole OLE container (including files).</h2>
<pre>save(string | resource $filename) : mixed</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>In fact, if called with an empty argument (or '-'), it saves to a
temporary file and then outputs it's contents to stdout.
If a resource pointer to a stream created by fopen() is passed
it will be used, but you have to close such stream by yourself.</p></div>
<table class="table table-bordered"><tr>
<th>access</th>
<td>public</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$filename</h4>
<code>string</code><code>resource</code><p>The name of the file or stream where to save the OLE container.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>true on success</div>
</div></div>
</div>
<a id="method__adjust2"></a><div class="element clickable method private method__adjust2" data-toggle="collapse" data-target=".method__adjust2 .collapse">
<h2>Helper function for caculating a magic value for block sizes</h2>
<pre>_adjust2(integer $i2) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>see</th>
<td><a href="">\save()</a></td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$i2</h4>
<code>integer</code><p>The argument</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>integer</code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property_DirPps"> </a><div class="element clickable property public property_DirPps" data-toggle="collapse" data-target=".property_DirPps .collapse">
<h2></h2>
<pre>$DirPps : integer</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$DirPps</td>
</tr></table>
</div></div>
</div>
<a id="property_Name"> </a><div class="element clickable property public property_Name" data-toggle="collapse" data-target=".property_Name .collapse">
<h2></h2>
<pre>$Name : string</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$Name</td>
</tr></table>
</div></div>
</div>
<a id="property_NextPps"> </a><div class="element clickable property public property_NextPps" data-toggle="collapse" data-target=".property_NextPps .collapse">
<h2></h2>
<pre>$NextPps : integer</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$NextPps</td>
</tr></table>
</div></div>
</div>
<a id="property_No"> </a><div class="element clickable property public property_No" data-toggle="collapse" data-target=".property_No .collapse">
<h2></h2>
<pre>$No : integer</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$No</td>
</tr></table>
</div></div>
</div>
<a id="property_PrevPps"> </a><div class="element clickable property public property_PrevPps" data-toggle="collapse" data-target=".property_PrevPps .collapse">
<h2></h2>
<pre>$PrevPps : integer</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$PrevPps</td>
</tr></table>
</div></div>
</div>
<a id="property_Size"> </a><div class="element clickable property public property_Size" data-toggle="collapse" data-target=".property_Size .collapse">
<h2></h2>
<pre>$Size : integer</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$Size</td>
</tr></table>
</div></div>
</div>
<a id="property_Time1st"> </a><div class="element clickable property public property_Time1st" data-toggle="collapse" data-target=".property_Time1st .collapse">
<h2></h2>
<pre>$Time1st : integer</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$Time1st</td>
</tr></table>
</div></div>
</div>
<a id="property_Time2nd"> </a><div class="element clickable property public property_Time2nd" data-toggle="collapse" data-target=".property_Time2nd .collapse">
<h2></h2>
<pre>$Time2nd : integer</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$Time2nd</td>
</tr></table>
</div></div>
</div>
<a id="property_Type"> </a><div class="element clickable property public property_Type" data-toggle="collapse" data-target=".property_Type .collapse">
<h2></h2>
<pre>$Type : integer</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Dir, Root or File</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$Type</td>
</tr></table>
</div></div>
</div>
<a id="property__StartBlock"> </a><div class="element clickable property public property__StartBlock" data-toggle="collapse" data-target=".property__StartBlock .collapse">
<h2></h2>
<pre>$_StartBlock : integer</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$_StartBlock</td>
</tr></table>
</div></div>
</div>
<a id="property__data"> </a><div class="element clickable property public property__data" data-toggle="collapse" data-target=".property__data .collapse">
<h2></h2>
<pre>$_data : string</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$_data</td>
</tr></table>
</div></div>
</div>
<a id="property_children"> </a><div class="element clickable property public property_children" data-toggle="collapse" data-target=".property_children .collapse">
<h2></h2>
<pre>$children : array</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$children</td>
</tr></table>
</div></div>
</div>
<a id="property_ole"> </a><div class="element clickable property public property_ole" data-toggle="collapse" data-target=".property_ole .collapse">
<h2></h2>
<pre>$ole : \OLE</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Shared_OLE_PPS::$$ole</td>
</tr></table>
</div></div>
</div>
<a id="property__tmp_dir"> </a><div class="element clickable property protected property__tmp_dir" data-toggle="collapse" data-target=".property__tmp_dir .collapse">
<h2></h2>
<pre>$_tmp_dir : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:37Z.<br></footer></div>
</div>
</body>
</html>
