#!/usr/local/bin/php -q
<?php
// 0 9 * * * php -q /home/<USER>/sperp/faulty_issue.psh
# 불량이슈 처리기한 알림
# 불량이슈건별로 보내던 것을 사원기준으로 보내는걸로 변경 2023-04-20
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/inc/Encode.php");

$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
if(empty($dbconn_sperp_posbank->success)) {
	echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
}

$dbconn_posbank_intra = new DBController($db['posbank_intra']);
if(empty($dbconn_posbank_intra->success)) {
	echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패";
}
/**********************************************************/

if(in_array(date('w'),array("0","6"))){
	echo date("Y-m-d") . " - 휴무일\n";
	## 스케즐 처리 상황 intra DB에 저장 
	crontab_execution(86400, "ERP 불량이슈 처리기한 알람");
	exit;
}

// 불량이슈 상태
$arr_FAULTY_ISSUE_STATE = [];
$arr_FAULTY_ISSUE_STATE['D'] = "등록"; //정의
$arr_FAULTY_ISSUE_STATE['M'] = "접수"; //측정
$arr_FAULTY_ISSUE_STATE['A'] = "진단"; //분석
$arr_FAULTY_ISSUE_STATE['I'] = "처리"; //개선
$arr_FAULTY_ISSUE_STATE['C'] = "승인"; //관리

$arr_FAULTY_ISSUE_STATE['A'] = "진단/처리"; //분석


$SQL = "SELECT NAME FROM HOLIDAY_DATA WHERE to_char(HDATE,'YYYYMMDD')='".date('Ymd')."'";
$HOLIDAY_NM = $dbconn_sperp_posbank->query_one($SQL);
if($HOLIDAY_NM){
	echo date("Y-m-d") . " - 휴무일(".$HOLIDAY_NM.")\n";
	## 스케즐 처리 상황 intra DB에 저장 
	crontab_execution(86400, "ERP 불량이슈 처리기한 알람");
	exit;
}


$SQL = "
SELECT 
	H.RCT_CODE||H.HDATE||H.HNO HID
	,H.HDATE,H.HCODE,H.STATE,H.U_COMPANY,H.SYMPTOM,H.STCODE
	,TO_CHAR(TO_DATE(H.HDATE,'YYYYMMDD'),'YYYY-MM-DD') HDATE2
	,(SELECT DBMS_LOB.SUBSTR(WM_CONCAT(STCODE)) FROM FAULTY_ISSUE_ASSING A WHERE H.RCT_CODE=A.RCT_CODE AND H.HDATE=A.HDATE AND H.HNO=A.HNO AND A.STATE='A') ASSING_ST
	-- ,(SELECT WM_CONCAT(B.CONTENTS) FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='M') D_CONTENTS
	,(SELECT CONTENTS FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='M' AND B.DNO=
		(SELECT max(B.DNO) FROM FAULTY_ISSUE_PROGRESS B WHERE H.RCT_CODE=B.RCT_CODE AND H.HDATE=B.HDATE AND H.HNO=B.HNO AND B.STATE='M')
	) D_CONTENTS
	--  , TO_CHAR(SYSDATE+1,'YYYYMMDD'),  TO_CHAR(F_CALCULATE_BUSINESS_DAYS(TO_DATE(H.HDATE,'YYYYMMDD'),DDAY_A),'YYYYMMDD')
	, H.REACT, H.OCC_AREA
FROM 
	FAULTY_ISSUE H 
		LEFT JOIN FAULTY_ISSUE_PROGRESS D ON (H.RCT_CODE=D.RCT_CODE AND H.HDATE=D.HDATE AND H.HNO=D.HNO AND D.STATE='A')
WHERE H.STATE IN ('M','A','I') AND H.HDATE >= '********' AND TO_CHAR(SYSDATE+1,'YYYYMMDD') >= TO_CHAR(F_CALCULATE_BUSINESS_DAYS(TO_DATE(H.HDATE,'YYYYMMDD'),DDAY_A),'YYYYMMDD') AND D.RCT_CODE IS NULL
ORDER BY HID DESC
";
$arrRow = $dbconn_sperp_posbank->query_rows($SQL);

// $SQL2 = "SELECT BAS_OP4 FROM BAS WHERE BAS_CODE='E009'";
// $BAS_E009 = $dbconn_sperp_posbank->query_one($SQL2);


$arr_FAULTY_ISSUE_STATE = [];
$arr_FAULTY_ISSUE_STATE['D'] = "등록";//정의
$arr_FAULTY_ISSUE_STATE['M'] = "접수";//측정
$arr_FAULTY_ISSUE_STATE['A'] = "진단";//분석
$arr_FAULTY_ISSUE_STATE['I'] = "처리";//개선
$arr_FAULTY_ISSUE_STATE['C'] = "승인";//관리

$arr_FAULTY_ISSUE_STATE['A'] = "진단/처리"; //분석

$arr_HID = [];
$arr_ST_HID = [];
if($arrRow){
	foreach($arrRow as $key => $row) { 
		$arr_HID[$row['HID']] = $row;
		if($row['ASSING_ST']){
			$arr_ST = explode(",", $row['ASSING_ST']);
			if($arr_ST){
				foreach($arr_ST as $stcode) { 
					$arr_ST_HID[$stcode][] = $row['HID'];
				}
			}
		}
	}
}

$arr_FAULTY_ISSUE_REACT = arr_FAULTY_ISSUE_REACT(); // 대응분야
$arr_FAULTY_OCC_AREA = arr_FAULTY_OCC_AREA(); // 발생장소


if($arr_ST_HID){
	foreach($arr_ST_HID as $stcode => $arr_data) { 




$REACT = $row2["REACT"];
$REACT_NAME = $arr_FAULTY_ISSUE_REACT[$REACT];
// 발생장소

$OCC_AREA = $row2["OCC_AREA"];
$OCC_AREA_NAME = $arr_FAULTY_OCC_AREA[$OCC_AREA];



		echo "[".$stcode."]\n";

		$title = "[품질이슈] " . sizeof($arr_data) . "건 처리기한이 지났거나 임박하였습니다.";
		$content = "<div style=\"font-size:12px;\">";
		$content .= "<div><b>[품질이슈]</b></div>";
		if($arr_data){
			foreach($arr_data as $HID) { 
				$row2 = $arr_HID[$HID];
				$row2['SYMPTOM'] = html_tags($row2['SYMPTOM']);
				$row2['SYMPTOM'] = mb_substr($row2['SYMPTOM'],0,100,'utf-8');


				$content .= "<div style=\"padding:5px;margin:10px 0;border:1px solid #dddddd;\">";
				$content .= "<div><b>[이슈번호]</b> ".$HID."</div>";
				$content .= "<div><b>[등록일]</b> ".$row2['HDATE2']."</div>";
				$content .= "<div><b>[상태]</b> ".$arr_FAULTY_ISSUE_STATE[$row2['STATE']]."</div>";
				$content .= "<div>대응분야 : ".$REACT_NAME."</div>";
				$content .= "<div>발생장소 : ".$OCC_AREA_NAME."</div>";
				$content .= "<div><b>[업체명]</b> ".$row2['U_COMPANY']."</div>";
				$content .= "<div><b>[증상]</b> ".nl2br($row2['SYMPTOM'])."...</div>";
				$content .= "<div><a href=\"https://www.spqms.co.kr/?pageCode=MTE0MzM=&searKey=H.RCT_CODE||H.HDATE||H.HNO&searKeyword=".$HID."\" target=\"_blank\">[불량이슈 보러가기]</a></div>";
				$content .= "</div>";
				$content .= "<div>";
			}
		}

//if($stcode=="100999"){
//$stcode="100695";

		//인트라넷 업무연락 보내는 함수(ERP 사원코드)
		$rs = intra_send_erp('',[$stcode],$title,$content);
		echo date("Y-m-d H:i:s")." 업무연락 발송(NEW) - ".$title . "(".$stcode.") - ";
		echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";

		// 구글워크스테이션 웹훅 보내기
		$goolgework_Params = [];
		$goolgework_Params['PROGRAM'] = "sperp";
		$goolgework_Params['GU'] = "qms";
		$goolgework_Params['ID'] = [$stcode];
		$goolgework_Params['PREVIEW'] = $title;
		$goolgework_Params['TITLE'] = "품질이슈";
		$goolgework_Params['MESSAGE'][] = ["처리 기한이 지난 품질이슈가 " . sizeof($arr_data) . "건 있습니다."];

		$goolgework_Params['BUTTONS'][0]['name'] = "확인하러가기";
		$goolgework_Params['BUTTONS'][0]['link'] = "https://www.spqms.co.kr/?pageCode=MTE0MzM=";

		$rs = goolgework_send($goolgework_Params);

		echo date("Y-m-d H:i:s")." 구글챗 발송(NEW) - ".$title . "(".$stcode.") - ";
		echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
//}
	}
}

## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(86400, "ERP 불량이슈 처리기한 알람");

echo date("Y-m-d H:i:s")." - 끝\n";




####### [ 불량이슈 대응분야 ] #######
// 2024.02.20. 현주가
function arr_FAULTY_ISSUE_REACT() {
	$arr["1"] = "이상동작분석";
	$arr["2"] = "기능개선검토";
	$arr["3"] = "펌웨어업데이트(터치패널튜닝포함)";
	$arr["4"] = "기타";

	return $arr;
} // End. function arr_FAULTY_ISSUE_REACT() 

####### [ 불량이슈 발생장소 ] #######
// 2024.02.20. 현주가
// 발생장소 Occuring AREA
function arr_FAULTY_OCC_AREA() {
	$arr["1"] = "고객사";
	$arr["2"] = "필드";
	$arr["3"] = "연구소";

	return $arr;
} // End. function arr_FAULTY_OCC_AREA() {

?>
