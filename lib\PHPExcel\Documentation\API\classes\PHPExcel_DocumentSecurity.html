<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_DocumentSecurity</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___clone" title="__clone :: Implement PHP __clone to create a deep clone, not just a shallow copy."><span class="description">Implement PHP __clone to create a deep clone, not just a shallow copy.</span><pre>__clone()</pre></a></li>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_DocumentSecurity"><span class="description">Create a new PHPExcel_DocumentSecurity</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_getLockRevision" title="getLockRevision :: Get LockRevision"><span class="description">Get LockRevision</span><pre>getLockRevision()</pre></a></li>
<li class="method public "><a href="#method_getLockStructure" title="getLockStructure :: Get LockStructure"><span class="description">Get LockStructure</span><pre>getLockStructure()</pre></a></li>
<li class="method public "><a href="#method_getLockWindows" title="getLockWindows :: Get LockWindows"><span class="description">Get LockWindows</span><pre>getLockWindows()</pre></a></li>
<li class="method public "><a href="#method_getRevisionsPassword" title="getRevisionsPassword :: Get RevisionsPassword (hashed)"><span class="description">Get RevisionsPassword (hashed)</span><pre>getRevisionsPassword()</pre></a></li>
<li class="method public "><a href="#method_getWorkbookPassword" title="getWorkbookPassword :: Get WorkbookPassword (hashed)"><span class="description">Get WorkbookPassword (hashed)</span><pre>getWorkbookPassword()</pre></a></li>
<li class="method public "><a href="#method_isSecurityEnabled" title="isSecurityEnabled :: Is some sort of dcument security enabled?"><span class="description">Is some sort of dcument security enabled?</span><pre>isSecurityEnabled()</pre></a></li>
<li class="method public "><a href="#method_setLockRevision" title="setLockRevision :: Set LockRevision"><span class="description">Set LockRevision</span><pre>setLockRevision()</pre></a></li>
<li class="method public "><a href="#method_setLockStructure" title="setLockStructure :: Set LockStructure"><span class="description">Set LockStructure</span><pre>setLockStructure()</pre></a></li>
<li class="method public "><a href="#method_setLockWindows" title="setLockWindows :: Set LockWindows"><span class="description">Set LockWindows</span><pre>setLockWindows()</pre></a></li>
<li class="method public "><a href="#method_setRevisionsPassword" title="setRevisionsPassword :: Set RevisionsPassword"><span class="description">Set RevisionsPassword</span><pre>setRevisionsPassword()</pre></a></li>
<li class="method public "><a href="#method_setWorkbookPassword" title="setWorkbookPassword :: Set WorkbookPassword"><span class="description">Set WorkbookPassword</span><pre>setWorkbookPassword()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__lockRevision" title="$_lockRevision :: LockRevision"><span class="description"></span><pre>$_lockRevision</pre></a></li>
<li class="property private "><a href="#property__lockStructure" title="$_lockStructure :: LockStructure"><span class="description"></span><pre>$_lockStructure</pre></a></li>
<li class="property private "><a href="#property__lockWindows" title="$_lockWindows :: LockWindows"><span class="description"></span><pre>$_lockWindows</pre></a></li>
<li class="property private "><a href="#property__revisionsPassword" title="$_revisionsPassword :: RevisionsPassword"><span class="description"></span><pre>$_revisionsPassword</pre></a></li>
<li class="property private "><a href="#property__workbookPassword" title="$_workbookPassword :: WorkbookPassword"><span class="description"></span><pre>$_workbookPassword</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_DocumentSecurity"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_DocumentSecurity.html">PHPExcel_DocumentSecurity</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_DocumentSecurity</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.html">PHPExcel</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___clone"></a><div class="element clickable method public method___clone" data-toggle="collapse" data-target=".method___clone .collapse">
<h2>Implement PHP __clone to create a deep clone, not just a shallow copy.</h2>
<pre>__clone() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_DocumentSecurity</h2>
<pre>__construct() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_getLockRevision"></a><div class="element clickable method public method_getLockRevision" data-toggle="collapse" data-target=".method_getLockRevision .collapse">
<h2>Get LockRevision</h2>
<pre>getLockRevision() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getLockStructure"></a><div class="element clickable method public method_getLockStructure" data-toggle="collapse" data-target=".method_getLockStructure .collapse">
<h2>Get LockStructure</h2>
<pre>getLockStructure() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getLockWindows"></a><div class="element clickable method public method_getLockWindows" data-toggle="collapse" data-target=".method_getLockWindows .collapse">
<h2>Get LockWindows</h2>
<pre>getLockWindows() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getRevisionsPassword"></a><div class="element clickable method public method_getRevisionsPassword" data-toggle="collapse" data-target=".method_getRevisionsPassword .collapse">
<h2>Get RevisionsPassword (hashed)</h2>
<pre>getRevisionsPassword() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getWorkbookPassword"></a><div class="element clickable method public method_getWorkbookPassword" data-toggle="collapse" data-target=".method_getWorkbookPassword .collapse">
<h2>Get WorkbookPassword (hashed)</h2>
<pre>getWorkbookPassword() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_isSecurityEnabled"></a><div class="element clickable method public method_isSecurityEnabled" data-toggle="collapse" data-target=".method_isSecurityEnabled .collapse">
<h2>Is some sort of dcument security enabled?</h2>
<pre>isSecurityEnabled() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_setLockRevision"></a><div class="element clickable method public method_setLockRevision" data-toggle="collapse" data-target=".method_setLockRevision .collapse">
<h2>Set LockRevision</h2>
<pre>setLockRevision(boolean $pValue) : <a href="../classes/PHPExcel_DocumentSecurity.html">\PHPExcel_DocumentSecurity</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentSecurity.html">\PHPExcel_DocumentSecurity</a></code></div>
</div></div>
</div>
<a id="method_setLockStructure"></a><div class="element clickable method public method_setLockStructure" data-toggle="collapse" data-target=".method_setLockStructure .collapse">
<h2>Set LockStructure</h2>
<pre>setLockStructure(boolean $pValue) : <a href="../classes/PHPExcel_DocumentSecurity.html">\PHPExcel_DocumentSecurity</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentSecurity.html">\PHPExcel_DocumentSecurity</a></code></div>
</div></div>
</div>
<a id="method_setLockWindows"></a><div class="element clickable method public method_setLockWindows" data-toggle="collapse" data-target=".method_setLockWindows .collapse">
<h2>Set LockWindows</h2>
<pre>setLockWindows(boolean $pValue) : <a href="../classes/PHPExcel_DocumentSecurity.html">\PHPExcel_DocumentSecurity</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentSecurity.html">\PHPExcel_DocumentSecurity</a></code></div>
</div></div>
</div>
<a id="method_setRevisionsPassword"></a><div class="element clickable method public method_setRevisionsPassword" data-toggle="collapse" data-target=".method_setRevisionsPassword .collapse">
<h2>Set RevisionsPassword</h2>
<pre>setRevisionsPassword(string $pValue, boolean $pAlreadyHashed) : <a href="../classes/PHPExcel_DocumentSecurity.html">\PHPExcel_DocumentSecurity</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$pAlreadyHashed</h4>
<code>boolean</code><p>If the password has already been hashed, set this to true</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentSecurity.html">\PHPExcel_DocumentSecurity</a></code></div>
</div></div>
</div>
<a id="method_setWorkbookPassword"></a><div class="element clickable method public method_setWorkbookPassword" data-toggle="collapse" data-target=".method_setWorkbookPassword .collapse">
<h2>Set WorkbookPassword</h2>
<pre>setWorkbookPassword(string $pValue, boolean $pAlreadyHashed) : <a href="../classes/PHPExcel_DocumentSecurity.html">\PHPExcel_DocumentSecurity</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$pAlreadyHashed</h4>
<code>boolean</code><p>If the password has already been hashed, set this to true</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_DocumentSecurity.html">\PHPExcel_DocumentSecurity</a></code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__lockRevision"> </a><div class="element clickable property private property__lockRevision" data-toggle="collapse" data-target=".property__lockRevision .collapse">
<h2></h2>
<pre>$_lockRevision : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__lockStructure"> </a><div class="element clickable property private property__lockStructure" data-toggle="collapse" data-target=".property__lockStructure .collapse">
<h2></h2>
<pre>$_lockStructure : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__lockWindows"> </a><div class="element clickable property private property__lockWindows" data-toggle="collapse" data-target=".property__lockWindows .collapse">
<h2></h2>
<pre>$_lockWindows : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__revisionsPassword"> </a><div class="element clickable property private property__revisionsPassword" data-toggle="collapse" data-target=".property__revisionsPassword .collapse">
<h2></h2>
<pre>$_revisionsPassword : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__workbookPassword"> </a><div class="element clickable property private property__workbookPassword" data-toggle="collapse" data-target=".property__workbookPassword .collapse">
<h2></h2>
<pre>$_workbookPassword : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:35Z.<br></footer></div>
</div>
</body>
</html>
