<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_Excel5</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list"><li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_getDistanceX" title="getDistanceX :: Get the horizontal distance in pixels between two anchors
The distanceX is found as sum of all the spanning columns widths minus correction for the two offsets"><span class="description">Get the horizontal distance in pixels between two anchors
The distanceX is found as sum of all the spanning columns widths minus correction for the two offsets</span><pre>getDistanceX()</pre></a></li>
<li class="method public "><a href="#method_getDistanceY" title="getDistanceY :: Get the vertical distance in pixels between two anchors
The distanceY is found as sum of all the spanning rows minus two offsets"><span class="description">Get the vertical distance in pixels between two anchors
The distanceY is found as sum of all the spanning rows minus two offsets</span><pre>getDistanceY()</pre></a></li>
<li class="method public "><a href="#method_oneAnchor2twoAnchor" title="oneAnchor2twoAnchor :: Convert 1-cell anchor coordinates to 2-cell anchor coordinates
This function is ported from PEAR Spreadsheet_Writer_Excel with small modifications"><span class="description">Convert 1-cell anchor coordinates to 2-cell anchor coordinates
This function is ported from PEAR Spreadsheet_Writer_Excel with small modifications</span><pre>oneAnchor2twoAnchor()</pre></a></li>
<li class="method public "><a href="#method_sizeCol" title="sizeCol :: Get the width of a column in pixels."><span class="description">Get the width of a column in pixels.</span><pre>sizeCol()</pre></a></li>
<li class="method public "><a href="#method_sizeRow" title="sizeRow :: Convert the height of a cell from user's units to pixels."><span class="description">Convert the height of a cell from user's units to pixels.</span><pre>sizeRow()</pre></a></li>
</ul>
</li></ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_Excel5"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_Excel5.html">PHPExcel_Shared_Excel5</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Shared_Excel5</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Shared.html">PHPExcel_Shared</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_getDistanceX"></a><div class="element clickable method public method_getDistanceX" data-toggle="collapse" data-target=".method_getDistanceX .collapse">
<h2>Get the horizontal distance in pixels between two anchors
The distanceX is found as sum of all the spanning columns widths minus correction for the two offsets</h2>
<pre>getDistanceX(\PHPExcel_Worksheet $sheet, string $startColumn, integer $startOffsetX, string $endColumn, integer $endOffsetX) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$sheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code>
</div>
<div class="subelement argument">
<h4>$startColumn</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$startOffsetX</h4>
<code>integer</code><p>Offset within start cell measured in 1/1024 of the cell width</p>
</div>
<div class="subelement argument">
<h4>$endColumn</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$endOffsetX</h4>
<code>integer</code><p>Offset within end cell measured in 1/1024 of the cell width</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>Horizontal measured in pixels</div>
</div></div>
</div>
<a id="method_getDistanceY"></a><div class="element clickable method public method_getDistanceY" data-toggle="collapse" data-target=".method_getDistanceY .collapse">
<h2>Get the vertical distance in pixels between two anchors
The distanceY is found as sum of all the spanning rows minus two offsets</h2>
<pre>getDistanceY(\PHPExcel_Worksheet $sheet, integer $startRow, integer $startOffsetY, integer $endRow, integer $endOffsetY) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$sheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code>
</div>
<div class="subelement argument">
<h4>$startRow</h4>
<code>integer</code><p>(1-based)</p>
</div>
<div class="subelement argument">
<h4>$startOffsetY</h4>
<code>integer</code><p>Offset within start cell measured in 1/256 of the cell height</p>
</div>
<div class="subelement argument">
<h4>$endRow</h4>
<code>integer</code><p>(1-based)</p>
</div>
<div class="subelement argument">
<h4>$endOffsetY</h4>
<code>integer</code><p>Offset within end cell measured in 1/256 of the cell height</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>Vertical distance measured in pixels</div>
</div></div>
</div>
<a id="method_oneAnchor2twoAnchor"></a><div class="element clickable method public method_oneAnchor2twoAnchor" data-toggle="collapse" data-target=".method_oneAnchor2twoAnchor .collapse">
<h2>Convert 1-cell anchor coordinates to 2-cell anchor coordinates
This function is ported from PEAR Spreadsheet_Writer_Excel with small modifications</h2>
<pre>oneAnchor2twoAnchor(<a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a> $sheet, string $coordinates, integer $offsetX, integer $offsetY, integer $width, integer $height) : array</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Calculate the vertices that define the position of the image as required by
the OBJ record.</p>

<pre><code> +------------+------------+
 |   A    |   B  |
</code></pre>

<p>+-----+------------+------------+
  |  |(x1,y1)    |          |
  |  1  |(A1)._______|______      |
  |  |  |             |  |
  |  |  |             |  |
  +-----+----|  BITMAP  |-----+
  |  |  |             |  |
  |  2  |   |______________.     |
  |  |          |       (B2)|
  |  |          |    (x2,y2)|
  +---- +------------+------------+</p>

<p>Example of a bitmap that covers some of the area from cell A1 to cell B2.</p>

<p>Based on the width and height of the bitmap we need to calculate 8 vars:
 $col_start, $row_start, $col_end, $row_end, $x1, $y1, $x2, $y2.
The width and height of the cells are also variable and have to be taken into
account.
The values of $col_start and $row_start are passed in from the calling
function. The values of $col_end and $row_end are calculated by subtracting
the width and height of the bitmap from the width and height of the
underlying cells.
The vertices are expressed as a percentage of the underlying cell width as
follows (rhs values are in pixels):</p>

<p>x1 = X / W *1024
   y1 = Y / H *256
   x2 = (X-1) / W *1024
   y2 = (Y-1) / H *256</p>

<p>Where:  X is distance from the left side of the underlying cell
           Y is distance from the top of the underlying cell
           W is the width of the cell
           H is the height of the cell</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$sheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code>
</div>
<div class="subelement argument">
<h4>$coordinates</h4>
<code>string</code><p>E.g. 'A1'</p>
</div>
<div class="subelement argument">
<h4>$offsetX</h4>
<code>integer</code><p>Horizontal offset in pixels</p></div>
<div class="subelement argument">
<h4>$offsetY</h4>
<code>integer</code><p>Vertical offset in pixels</p></div>
<div class="subelement argument">
<h4>$width</h4>
<code>integer</code><p>Width in pixels</p></div>
<div class="subelement argument">
<h4>$height</h4>
<code>integer</code><p>Height in pixels</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>array</code></div>
</div></div>
</div>
<a id="method_sizeCol"></a><div class="element clickable method public method_sizeCol" data-toggle="collapse" data-target=".method_sizeCol .collapse">
<h2>Get the width of a column in pixels.</h2>
<pre>sizeCol(<a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a> $sheet, string $col) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>We use the relationship y = ceil(7x) where
x is the width in intrinsic Excel units (measuring width in number of normal characters)
This holds for Arial 10</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$sheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><p>The sheet</p></div>
<div class="subelement argument">
<h4>$col</h4>
<code>string</code><p>The column</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>The width in pixels</div>
</div></div>
</div>
<a id="method_sizeRow"></a><div class="element clickable method public method_sizeRow" data-toggle="collapse" data-target=".method_sizeRow .collapse">
<h2>Convert the height of a cell from user's units to pixels.</h2>
<pre>sizeRow(<a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a> $sheet, integer $row) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>By interpolation
the relationship is: y = 4/3x. If the height hasn't been set by the user we
use the default value. If the row is hidden we use a value of zero.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$sheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><p>The sheet</p></div>
<div class="subelement argument">
<h4>$row</h4>
<code>integer</code><p>The row index (1-based)</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>The width in pixels</div>
</div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:36Z.<br></footer></div>
</div>
</body>
</html>
