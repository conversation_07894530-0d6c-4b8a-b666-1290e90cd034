#!/usr/local/bin/php -q
<?
# 피드백 업무연락 중 확인 안한 건 알림
# 매일 9시 실행
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

$dbconn = new DBController($db['posbank_intra']);
if(empty($dbconn->success)) {
	echo "[" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패입니다.";
}

$dbconn2 = new DBController($db['chrome_push']);
if(empty($dbconn2->success)) {
	echo "[" . $db['chrome_push']['host'] . "] 데이터베이스 연결 실패입니다.";
}
/**********************************************************/

echo date("Y-m-d H:i:s")." - 업무연락 알림\n";

$SQL = "SELECT count(*) FROM HOLIDAY_DATA WHERE HDATE=CURDATE()";
$holiday_chk = $dbconn->query_one($SQL);
if(!$holiday_chk){
	// 토,일요일
	if(in_array(date('w'),array("0","6"))) $holiday_chk = '1';
}
if($holiday_chk){
	echo "휴무일\n";
}else{
	$Checkday = date("Y-m-d-00-00", strtotime ("-7 day"));

	// 금일 휴가자
	$SQL = "SELECT
					group_concat(DISTINCT A.PRS_NUM)
				FROM APPV_HOLI A
					LEFT JOIN PRS_MASTER B ON A.PRS_NUM=B.PRS_NUM
				WHERE
					A.GU2='-'
					AND CURDATE() BETWEEN A.HOLI_SDATE AND A.HOLI_EDATE
					AND A.VIEW_YN<>'N'";
	$prs_num = $dbconn->query_one($SQL);


	$SQL = "SELECT 
					B.PRS_NUM
					,group_concat(DISTINCT concat(A.REG_NO,'|;',A.TITLE) SEPARATOR '\n') ITEM
					,count(DISTINCT A.REG_NO) CNT
				FROM trans A
					LEFT JOIN TRANS_TARGET B ON A.REG_NO=B.REG_NO
					LEFT JOIN TRANS_OPEN C ON A.REG_NO=C.REG_NO AND B.PRS_NUM=C.PRS_NUM
					LEFT JOIN TRANS_HIDE D ON A.REG_NO=D.REG_NO AND B.PRS_NUM=D.PRS_NUM
				WHERE 
					A.IN_DT >= '".$Checkday."' 
					AND A.FEEDBACK='Y' 
					AND C.SNUM IS null 
					AND D.REG_NO IS null ";
	if($prs_num){
		$arr_prs_num = explode(",", $prs_num);
		$SQL .= " and B.PRS_NUM not in ('".implode("','",$arr_prs_num)."') ";
	}
	//$SQL .= " and B.PRS_NUM in ('********','**********') ";
	$SQL .= " GROUP BY B.PRS_NUM ";


/*
	$SQL = "SELECT 
					B.PRS_NUM
					,group_concat(DISTINCT concat(A.REG_NO,'|;',A.TITLE) SEPARATOR '\n') ITEM
					,count(DISTINCT A.REG_NO) CNT
				FROM trans A
					LEFT JOIN TRANS_TARGET B ON A.REG_NO=B.REG_NO
					LEFT JOIN TRANS_OPEN C ON A.REG_NO=C.REG_NO AND B.PRS_NUM=C.PRS_NUM
				WHERE A.REG_NO in ('1988587','1988598') and B.PRS_NUM in ('********','**********')
				GROUP BY B.PRS_NUM ";
*/


	$arrRow = $dbconn->query_rows($SQL);
	if($arrRow){
		foreach($arrRow as $key => $row) {
			$arr_ITEM = explode("\n",$row['ITEM']);
			
			$MESSAGE = [];
			$MESSAGE[] = ["미확인 중요 업무연락이 ".$row['CNT']."건 있습니다."];
			if($arr_ITEM){
				foreach($arr_ITEM as $key2 => $ITEM) {
					$arr_ITEM2 = explode("|;",$ITEM);
					$MESSAGE[] = ["<b>문서번호 : </b><font color=#555555>".$arr_ITEM2[0]."</font> <a href=https://i.posbank.com/login.html?pageType=trans&pageCode=".$arr_ITEM2[0].">[확인]</a>"];
					$MESSAGE[] = ["<b>제목 : </b><font color=#555555>".$arr_ITEM2[1]."</font>"];
					$MESSAGE[] = [""];
				}
			}
			$query = "insert into goolge_webhooks (PROGRAM, GU, ID, TITLE, PREVIEW, MSG_TYPE, MESSAGE, BUTTONS, STATE, IDATE, UDATE) values (";
			$query .= "'intranet', 'trans', '[\"".$row['PRS_NUM']."\"]' ";
			$query .= ",'업무연락' ";
			$query .= ",'최근1주 미확인 중요 업무연락이 ".$row['CNT']."건 있습니다.' ";
			$query .= ",'1' ";
			$query .= ",'". json_encode($MESSAGE, JSON_UNESCAPED_UNICODE) . "' ";
			$query .= ",'' ";
			$query .= ",'0' ";
			$query .= ",now() ";
			$query .= ",now() ";
			$query .= ") ";
			$rs = $dbconn2->query($query);
			echo $row['PRS_NUM'] . " - " . json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
		}
	}
}



## 스케즐 처리 상황 intra DB에 저장
crontab_execution(86400, "인트라넷 업무연락 미확인 알림");

echo date("Y-m-d H:i:s")." - 끝\n";
?>
