<?php

	## 통신
	//$arr_head[0] = "content-type:application/json";
	//$arr_head[1] = "authorization:KakaoAK 1bb37078af20539637d5b09caa54eba8";
	//$arr_body['aa'] = "aa";
	//$arr_body['bb'] = "bb";
	//make_curl($url, "POST", $arr_head, $arr_body);
	function make_curl($url, $type="POST", $arr_head=null, $arr_body=null)
	{
		$request_timeout = 30; // 1 second timeout
		$request = curl_init();
		curl_setopt($request, CURLOPT_URL, $url);
		curl_setopt($request, CURLOPT_CUSTOMREQUEST, $type);
		curl_setopt($request, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($request, CURLOPT_FOLLOWLOCATION, true);
		curl_setopt($request, CURLOPT_TIMEOUT, $request_timeout);
		curl_setopt($request, CURLOPT_CONNECTTIMEOUT, $request_timeout);
		curl_setopt($request, CURLOPT_HEADER, 0);//헤더를 포함한다.
		if (!empty($arr_body)) {
			curl_setopt($request, CURLOPT_POST, true);
			curl_setopt($request, CURLOPT_POSTFIELDS, json_encode($arr_body));
		} else {
			curl_setopt($request, CURLOPT_POST, 0);
		}
		if (!empty($arr_head)) {
			curl_setopt($request, CURLOPT_HTTPHEADER, $arr_head);
		}
		$return['result'] = curl_exec($request);
		$return['result'] = json_decode($return['result'], JSON_UNESCAPED_UNICODE);
		$return['error_code'] = curl_errno($request);
		curl_close($request);
		return $return;
	}

	## 통신
	function make_curl_get($url, $head_info, $post_body)
	{
		$request_timeout = 30; // 1 second timeout
		$url = $url.'?'.http_build_query($post_body, '', '&');
		$request = curl_init();
		curl_setopt($request, CURLOPT_URL, $url);
		curl_setopt($request, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($request, CURLOPT_CONNECTTIMEOUT, 10);
		curl_setopt($request, CURLOPT_SSL_VERIFYPEER, false);
		//curl_setopt($request, CURLOPT_HEADER, true);//헤더를 포함한다.
		if (!empty($head_info)) {
			curl_setopt($request, CURLOPT_HTTPHEADER, $head_info);
		}
		$result = curl_exec($request);
		$return['result'] = json_decode($result, JSON_UNESCAPED_UNICODE);
		$return['error_code'] = curl_errno($request);
		curl_close($request);
		return $return;
	}

	// 문자열에서 숫자만 추출(소숫점 포함)
	function num_replace($str) {
		$str = trim($str);
		$int = preg_replace("/[^0-9.]/", "", $str);  // 숫자만 추출
		return $int;
	}

	// 문자열에서 숫자만 추출
	function num_replace2($str) {
		$str = trim($str);
		$int = preg_replace("/[^0-9]/", "", $str);  // 숫자만 추출
		return $int;
	}

	// 배열 중복제거 key 유지 안함
	function array_unique2($arr)
	{
		$return_array = [];
		$arr = array_unique ($arr);
		if($arr){
			foreach($arr as $key => $value) {
				$return_array[] = $value;
			}
		}
		return $return_array;
	}

	## 스케즐 처리 상황 monitor DB에 저장
	function crontab_execution($CYCLE=86400, $CE_MEMO="")
	{
		global $db;

		$dbconn_monitor = new DBController($db['monitor']);
		if(empty($dbconn_monitor->success)) {
			echo "[" . $db['monitor']['host'] . "] 데이터베이스 연결 실패";
		}else{
			$CE_NAME = $_SERVER['SCRIPT_NAME'];
			//$CE_IP = $_SERVER['HOSTNAME'];
			//$CE_IP = "***************";
			$CE_IP = "***********";


			$query = "INSERT INTO CRONTAB_EXECUTION (CE_NAME, CE_IP, CYCLE, CE_MEMO, IDATE, UDATE) VALUES
							('".$CE_NAME."','".$CE_IP."','".$CYCLE."','".$CE_MEMO."', NOW(), NOW())
							ON DUPLICATE KEY UPDATE
							UDATE = NOW()";
			$arr = $dbconn_monitor->iud_query($query);
		}
	}

	// 카카오톡 보내기
	// KakaoTalk_send(array('msgType'=>'1008','dispatchPhone'=>'1588-6335','receptionPhone'=>'01045032846','temCode'=>'','imgUrl'=>'','imgLink'=>'','toMsg'=>'내용'));
	function KakaoTalk_send($arr)
	{
		global $ROOT_PATH;
		$return_array = [];
		if(empty($arr['msgType'])) $arr['msgType'] = "1008"; // 1008:알림톡,1009:친구톡
		if(empty($arr['dispatchPhone'])) $arr['dispatchPhone'] = "1588-6335";
		if(empty($arr['SMS_ID'])) $arr['SMS_ID'] = "intranet";
		if(empty($arr['temCode'])) $arr['temCode'] = "";
		if(empty($arr['imgUrl'])) $arr['imgUrl'] = "";
		if(empty($arr['imgLink'])) $arr['imgLink'] = "";
		if(empty($arr['etc1'])) $arr['etc1'] = "";
		if(empty($arr['etc2'])) $arr['etc2'] = "";
		if(empty($arr['etc3'])) $arr['etc3'] = "";
		if(empty($arr['subject'])) $arr['subject'] = "";


		//include_once("/home/<USER>/inc/pb_encode.php");
		include_once($ROOT_PATH."/inc/pb_encode.php");

		# SMS 체크값용 체크키 생성
		$PBEncode = new PBEncode();
		$checkKey = $PBEncode->Phone($arr['dispatchPhone'],$arr['receptionPhone']);

		$setParams['msgType'] = $arr['msgType']; // 1008:알림톡,1009:친구톡
		$setParams['userId'] = $arr['SMS_ID']; // 아이디
		$setParams['sendType'] = "0"; // 전송구분(0:즉시전송)
		$setParams['dispatchPhone'] = $arr['dispatchPhone']; // 발신번호
		$setParams['returnUrl'] = ""; // 리턴될 페이지 (절대경로로 입력)
		//$setParams['MsgView'] = "N";
		$setParams['temCode'] = $arr['temCode']; // 알림톡 전용
		$setParams['imgUrl'] = $arr['imgUrl']; // 친구톡 전용
		$setParams['imgLink'] = $arr['imgLink']; // 친구톡 전용
		$setParams['etc1'] = $arr['etc1']; // 친구톡확장 SMS대체 문구
		$setParams['etc2'] = $arr['etc2']; // 친구톡확장
		$setParams['etc3'] = $arr['etc3']; // 친구톡확장
		$setParams['subject'] = $arr['subject']; // 제목
		//$setParams['returnMsgType'] = "3";
		//$setParams['charset'] = "UTF-8";

		$arr['toMsg'] = trim($arr['toMsg']);
		$msg_len = 2000;
		if($arr['msgType']=="1009") $msg_len = 1000;
		if($arr['imgUrl']) $msg_len = 400;
		$arr['toMsg'] = mb_substr($arr['toMsg'], 0, $msg_len, "EUC-KR");
		if(mb_strlen($arr['toMsg'], "EUC-KR") >= ($msg_len-3)){
			$arr['toMsg'] = mb_substr($arr['toMsg'], 0, $msg_len-3, "EUC-KR") . "...";
		}
		$setParams['checkKey'] = $checkKey; // 체크키

		$hp3 = "";
		$arr_hp = explode(";", $arr['receptionPhone']);
		$cnt = 0;
		if($arr_hp){
			foreach($arr_hp as $key => $value) {
				if($value){
					$arr_hp2 = explode("/", $value);
					if(empty($arr_hp2[1])) $arr_hp2[1] = "";
					$hp2 = preg_replace("/[^0-9]/", "", trim($arr_hp2[0]));  // 숫자만
					if($hp2){
						$hp3 .= $hp2;
						if($arr_hp2[1]) $hp3 .= "/" . $arr_hp2[1] . ";";
						$hp3 .= ";";
						$cnt++;
					}
				}
			}
		}

		//$setParams['Cnt'] = $cnt; // 메시지 수
		if($hp3){
			$setParams['receptionPhone'] = $hp3; // 전화번호/이름;전화번호/이름;
			$setParams['toMessage'] = trim($arr['toMsg']); // 메시지

			$url = "https://sms.posbank.co.kr/api/kakao_remit.php";
			$arr_head[0] = "Content-Type:application/json";
			$arr_head[1] = "Authorization:PosbankTK vNIcMghTc14SURtAIOnZAQmfmAdJkLdSmw8maZp6yA2dXjD86sk7n/lqEqp4qs1Bnc+BQTbe60slodyCx4p2Dw==";
			$arr_body = $setParams;

			$return_array = make_curl($url, "POST", $arr_head, $arr_body);
		}
		unset($setParams);
		return $return_array;
	}

	//문자 보내는 함수
	// $hp => 전화번호/이름;전화번호/이름;
	function sms_send($hp,$message,$callback="1588-6335",$SMS_ID="") {
		$ROOT_PATH = "/home/<USER>";
		include_once($ROOT_PATH . "/inc/pb_encode.php");

		if(!$SMS_ID) $SMS_ID = "intranet";
		$setParams['userId'] = $SMS_ID; // 아이디
		$setParams['sendType'] = "0"; // 전송구분(0:즉시전송)
		$setParams['dispatchPhone'] = $callback; // 발신번호

		# SMS 체크값용 체크키 생성
		$PBEncode = new PBEncode();
		$checkKey = $PBEncode->Phone($setParams['dispatchPhone'],$hp);

		$hp3 = "";
		$arr_hp = explode(";", $hp);
		$cnt = 0;
		if($arr_hp){
			foreach($arr_hp as $key => $value) {
				if($value){
					$value = $value."/";
					$arr_hp2 = explode("/", $value);
					//echo json_encode($arr_hp2, JSON_UNESCAPED_UNICODE)."\n\n";
					$hp2 = preg_replace("/[^0-9]/", "", trim($arr_hp2[0]));  // 숫자만
					if($hp2){
						$hp3 .= $hp2 . "/";
						if($arr_hp2[1]) $hp3 .= $arr_hp2[1];
						$hp3 .= ";";
						$cnt++;
					}
				}
			}
		}
		if($hp3){
			$message = mb_substr($message, 0, 90, 'euc-kr');
			$setParams['receptionPhone'] = $hp3; // 전화번호/이름;전화번호/이름;
			$setParams['message'] = trim($message); // 메시지
			$setParams['checkKey'] = $checkKey; // 체크키
			//$setParams['RemoteAddr'] = $_SERVER['REMOTE_ADDR']; // 처리자 IP

			$url = "https://api.posbank.co.kr/sms/sms_send";
			$arr_head = [];
			$arr_head[0] = "Content-Type:application/json";
			$arr_head[1] = "Authorization:PosbankTK MjM3Yj+llM#jU5Zm\$E4ND/YzgxNzFlMDYyOTc5MmYwYTE3ZDI2Ng==";
			$arr_body = $setParams;

			$return_array = make_curl($url, "POST", $arr_head, $arr_body);
		}
		unset($setParams);
		return $return_array;
	}

	// 카카오워크 보내기
	//	$kakaowork_Params = [];
	//	$kakaowork_Params['bot_gu'] = "trans"; // 봇구분(trans:업무연락,appv:결재문서,qms:QMS,system:시스템) 값 없을시 기본 봇
	//	$kakaowork_Params['to'] = $tmpCode;
	//	//$kakaowork_Params['email'] = $arr['email'];
	//	$kakaowork_Params['text'] = "결재문서가 등록되었습니다";
	//	$kakaowork_Params['header'] = "결재문서";
	//	$kakaowork_Params['header_color'] = "blue";
	//	$kakaowork_Params['msg'][0] = ["문서분류",$DOC_CLASS_NM];
	//	$kakaowork_Params['msg'][1] = ["제목",$_POST['p_TITLE']];
	//	$kakaowork_Params['msg'][2] = ["기안자",$pm_row['PRS_NM']];
	//	$kakaowork_Params['msg'][3] = ["일시",date('Y년 m월 d일 H시 i분')];
	//	$kakaowork_Params['button'] = ["결재하러가기",$_SERVER['REQUEST_SCHEME']."://".$_SERVER['HTTP_HOST'] . "/login.html?pageType=appv_doc&pageCode=".$p_ADC];
	function kakaowork_send($arr) 
	{
		$url = "https://api.posbank.com/kakaowork/messages/send";
		$arr_head[0] = "content-type:application/json";
		$arr_head[1] = "Authorization:KakaoworkTK MjND/YzgDYt5Fc5TEDI2NgTcmEyuvQvZtMtv14MmE4M#jU5Zm9IapFlM3Yj+lMCku7==";
		$arr_body = $arr;
		$arr_body['program'] = "intranet";

		$result_array = make_curl($url, "POST", $arr_head, $arr_body);
		return $result_array['result'];
	}

	// 구글워크스테이션 웹훅 보내기
	function googlework_send($arr) 
	{
		$url = "https://api.posbank.com/goolgewebhooks/messages/send";
		$arr_head[0] = "content-type:application/json";
		$arr_head[1] = "Authorization:GoolgeworkTK vQvZtM#jU5pFlM3Yj+lMMtvYzgDYt5F14ZD/c9IaMjNE4Cku5TEDI2NgTm7cmEyuMm==";
		$arr_body = $arr;
    	$arr_body['program'] = $arr['program'] ?? "sperp";
		$arr_body['spaces_gu'] = "POSBANK";

		$result_array = make_curl($url, "POST", $arr_head, $arr_body);
		return $result_array['result'];
	}


	//인트라넷 업무연락 보내는 함수
	function intra_send_curl($data) {
		$url = "https://api.posbank.com/intra/trans/send";
		$arr_head[0] = "content-type:application/json";
		$arr_head[1] = "Authorization:intraTK vQpFlM3Yj+lIMcYt5Fc54M#jU5Zm9jMCkumED/YzgDyu2NgTIaNTEDvZtMtv14MmE7==";
		$arr_body['program'] = "intranet";
		$arr_body['gu'] = $data['gu'];
		$arr_body['from'] = $data['from'];
		$arr_body['to'] = $data['to'];
		$arr_body['title'] = $data['title'];
		$arr_body['content'] = $data['content'];
		$result_array = make_curl($url, "POST", $arr_head, $arr_body);

		return $result_array['result'];
	}

	//인트라넷 업무연락 보내는 함수(ERP 사원코드)
	function intra_send_erp($stCode,$arr_stCode,$title,$content) {
		global $db, $dbconn_posbank_intra;

		if(!$dbconn_posbank_intra) $dbconn_posbank_intra = new DBController($db['posbank_intra']);

		$SQL = "SELECT PRS_NUM FROM PRS_MASTER WHERE FLAG='1' AND ERP_CODE='".$stCode."'";
		$prsNum = $dbconn_posbank_intra->query_one($SQL);

		$arr_prsNum = [];
		$SQL = "SELECT PRS_NUM FROM PRS_MASTER WHERE FLAG='1' AND ERP_CODE IN ('".implode("','",$arr_stCode)."')";
		$arrRow = $dbconn_posbank_intra->query_rows($SQL);
		if($arrRow){
			foreach($arrRow as $key => $row) {
				$arr_prsNum[] = $row['PRS_NUM'];
			}
		}

		$rs = intra_send($prsNum,$arr_prsNum,$title,$content);

		return $rs;
	}

	//인트라넷 업무연락 보내는 함수
	function intra_send($prsNum,$arr_prsNum,$title,$content) {
		global $db, $dbconn_posbank_intra;

		if(!$dbconn_posbank_intra) $dbconn_posbank_intra = new DBController($db['posbank_intra']);

		$regdate = strftime("%Y-%m-%d-%H-%M");
		$hdate = date("Ymd");
		if(!empty($prsNum)){
			$SQL = "SELECT PRS_NUM,PRS_NM,PART_CODE,PART_NM FROM PRS_MASTER WHERE FLAG='1' AND PRS_NUM='".$prsNum."'";
			$prs_row = $dbconn_posbank_intra->query_row($SQL);
		}else{
			$prs_row = [];
			$prs_row['PRS_NUM'] = "";
			$prs_row['PRS_NM'] = "";
			$prs_row['PART_CODE'] = "";
			$prs_row['PART_NM'] = "";
		}

		$arr_st = [];
		$SQL = "SELECT PRS_NUM,PRS_NM,PART_CODE,PART_NM,HP FROM PRS_MASTER WHERE FLAG='1' AND PRS_NUM IN ('".implode("','",$arr_prsNum)."')";
		$arrRow = $dbconn_posbank_intra->query_rows($SQL);
		if($arrRow){

			$query = "INSERT INTO TRANS (`PRS_NUM`, `PRS_NM`, `PART_CODE`, `PART_NM`, `DIV`, `TITLE`, `HDATE`, `IN_DT`, `FILE`, `CONTENT`, `OPEN_YN`, `wr_content_File`, `PB_LINK`)
							VALUES('".$prs_row['PRS_NUM']."', '".$prs_row['PRS_NM']."', '".$prs_row['PART_CODE']."', '".$prs_row['PART_NM']."', '1', '".$title."', '".$hdate."', '".$regdate."', '', '".$content."', '2', '', 'posbank')";
			$rs = $dbconn_posbank_intra->query($query);
			$last_seq = $dbconn_posbank_intra->insert_id();

			foreach($arrRow as $key => $row) {
				// 분류함 자동 분류
				$kdCode = "";
				$SQL2 = "select KD_CODE,TITLE_KEYWORD from TRANS_KIND_SET where PRS_NUM='".$row['PRS_NUM']."' and ifnull(TITLE_KEYWORD,'')<>'' order by KD_CODE desc";
				$rows = $dbconn_posbank_intra->query_rows($SQL2);
				if($rows){
					foreach($rows as $row2) {
						if($row2['TITLE_KEYWORD']){
							$arr_KEYWORD = explode("|", $row2['TITLE_KEYWORD']);
							if($arr_KEYWORD){
								foreach($arr_KEYWORD as $keyword) {
									if($keyword){
										if(stripos($title, $keyword) !== false) $kdCode = $row2['KD_CODE'];
									}
								}
							}
						}
					}
				}

				// 받는사람
				$query = "INSERT INTO TRANS_TARGET (`REG_NO`, `PRS_NUM`, `PRS_NM`, `KD_CODE`)
								VALUES ('".$last_seq."', '".$row['PRS_NUM']."', '".$row['PRS_NM']."', '".$kdCode."')";
				$rs = $dbconn_posbank_intra->query($query);

				// 알람 메시지 발송
				$AM_memo = "업무연락이 등록되었습니다.<br>";
				$AM_memo .= "문서번호 : " . $last_seq . "<br>";
				$AM_memo .= "문서제목 : " . $title . "<br>";
				$AM_memo .= "보낸사람 : " . $prs_row['PRS_NM'];
				$AM_link = "/a_new/?page_idx=320&pageCode=".$last_seq."";
				$query = "insert into ALARM_MESSAGE (PRS_NUM, MEMO, LINK, GU, GU_CODE, IDATE) values ('".$row['PRS_NUM']."','".$AM_memo."','".$AM_link."','TRANS','".$last_seq."',now());";
				$rs2 = $dbconn_posbank_intra->query($query);

				$arr_st[] = $row['PRS_NUM'];
			}

			// 구글워크스테이션 웹훅 보내기
			$goolgework_Params = [];
			$goolgework_Params['PROGRAM'] = "intranet";
			$goolgework_Params['GU'] = "trans";
			$goolgework_Params['ID'] = $arr_st;
			$goolgework_Params['PREVIEW'] = $prs_row['PRS_NM']."님에게서 업무연락이 등록되었습니다. \n" . $title;
			$goolgework_Params['TITLE'] = "업무연락";
			$goolgework_Params['MESSAGE'][] = ["<b>제목 : </b><font color='#555555'>".$title."</font>"];
			$goolgework_Params['MESSAGE'][] = ["<b>보낸사람 : </b><font color='#555555'>".$prs_row['PRS_NM']."</font>"];
			$goolgework_Params['MESSAGE'][] = ["<b>문서번호 : </b><font color='#555555'>".$last_seq."</font>"];
			$goolgework_Params['MESSAGE'][] = ["<b>일시 : </b><font color='#555555'>".date('Y년 m월 d일 H시 i분')."</font>"];
			$goolgework_content = html_tags($content);
			$goolgework_content = KoreanStr_cut($goolgework_content, 2000, "...");
			$goolgework_Params['MESSAGE'][] = ["<b>간략내용 : </b><font color='#555555'>".$goolgework_content."</font>"];
			$goolgework_Params['LINK_NM'] = "확인하러가기";
			$goolgework_Params['LINK'] = "https://i.posbank.com/login.html?pageType=trans&pageCode=".$last_seq;
			$rs3 = goolgework_send($goolgework_Params);
		}
		return $rs;
	}

	## 메일 변환
	function mail_convert($data=[]) {
		$arr3 = [];
		$arr = explode(",", $data);
		if($arr){
			foreach($arr as $key => $data2) {
				$arr2 = explode("|", $data2);
				$arr3[$key]['mail']=$arr2[0];
				$arr3[$key]['name']=(!empty($arr2[1]))?$arr2[1]:"";

				//$arr3[$key] = ['mail'=>$arr2[0], 'name'=>$arr2[1]];
			}
		}
		return $arr3;
	}

	## 사원코드로 등록된 웹훅URL 가져오기.
	function goolge_webhooks_member_chk($data=[]) {
		global $db, $dbconn_posbank_intra;

		if(!$dbconn_posbank_intra) $dbconn_posbank_intra = new DBController($db['posbank_intra']);

		$arr = [];
		$join = "";

		## 업무구분
		if(in_array($data['GU'],array("trans","appv_G","appv_C","appv_R"))){ // 업무연락,결재문서 일때 - 알림발송 허용한 사원만 보낸다
			switch($data['GU']){
				case "trans": // 업무연락
					$BAS_CODE = "KK01";
					break;
				//case "appv": // 결재문서
				//	$BAS_CODE = "KK02";
				//	break;
				case "appv_G": // 결재문서(결재자)
					$BAS_CODE = "KK02";
					break;
				case "appv_C": // 결재문서(합의자)
					$BAS_CODE = "KK03";
					break;
				case "appv_R": // 결재문서(참조자)
					$BAS_CODE = "KK04";
					break;
			}
			$join = "inner join BAS_PRSNUM B on A.PRS_NUM=B.PRS_NUM AND B.BAS_CODE='".$BAS_CODE."' AND B.BAS_OP1='Y' ";
		}
		$where = "";
		switch($data['PROGRAM']){
			case "intranet":
				$where = "and A.PRS_NUM in ('".implode("','",$data['ID'])."') ";
				break;
			case "sperp":
			case "spqms":
				$where = "and A.ERP_CODE in ('".implode("','",$data['ID'])."') and A.ERP_CODE<>'' ";
				break;
			default:
				$where = "and A.PRS_NUM in ('".implode("','",$data['ID'])."') ";
				break;
		}
		$SQL = "select A.PRS_NM, A.EMAIL, A.GOOLGEWEBHOOKS_URL ";
		$SQL .= "from PRS_MASTER A ";
		$SQL .= $join;
		$SQL .= "where A.FLAG='1' ";
		$SQL .= $where;

		$rows = $dbconn_posbank_intra->query_rows($SQL);

		return $rows;
	}

	## 구글워크스테이션 웹훅 보내기
	function goolgework_send($data=[]) {
		global $db, $dbconn_chrome_push;

		if(!$dbconn_chrome_push) $dbconn_chrome_push = new DBController($db['chrome_push']);

		// 버턴 여러개 생성 추가 2023-04-20 jjs
		// 기존 방식도 배열에 합쳐서 저장
		$buttons = [];
		if(empty($data['LINK'])) $data['LINK'] = "";
		if(empty($data['LINK_NM'])) $data['LINK_NM'] = "";
		if(!empty($data['BUTTONS'])) $buttons = $data['BUTTONS'];
		if(!empty($data['LINK_NM'])){
			$buttons = [];
			$buttons[0]['name'] = $data['LINK_NM'];
			$buttons[0]['link'] = $data['LINK'];
			if(!empty($data['BUTTONS'])) $buttons = array_merge($buttons, $data['BUTTONS']); //배열 합치기
		}


		$data['TITLE'] = addslashes($data['TITLE']);
		$data['PREVIEW'] = addslashes($data['PREVIEW']);
		$data['ID'] = json_encode($data['ID'], JSON_UNESCAPED_UNICODE);
		$data['MESSAGE'] = json_encode($data['MESSAGE'], JSON_UNESCAPED_UNICODE);
		$data['MESSAGE'] = addslashes($data['MESSAGE']);

		$json_buttons = json_encode($buttons,JSON_UNESCAPED_UNICODE);
		$json_buttons = addslashes($json_buttons);

		$query = "insert into goolge_webhooks (PROGRAM, GU, ID, TITLE, PREVIEW, MESSAGE, LINK, LINK_NM, BUTTONS, STATE, IDATE, UDATE) values ";
		$query .= "('".$data['PROGRAM']."', '".$data['GU']."', '".$data['ID']."', '".$data['TITLE']."', '".$data['PREVIEW']."', '".$data['MESSAGE']."', '".$data['LINK']."', '".$data['LINK_NM']."', '".$json_buttons."', '0', now(), now()) ";

		$rs = $dbconn_chrome_push->query($query);


		return $rs;
	}





	// html 태그제거
	function html_tags($html) {
		$str = strip_tags($html); // html,php 태그 제거
		$str = str_replace("&nbsp;", " ", $str);
		$str = str_replace("&lt;", "＜", $str);
		$str = str_replace("&gt;​", "＞", $str);
		$str = str_replace("&#8203;"," ",$str);
		$str = str_replace("\r\n"," ",$str);
		$str = str_replace("\"","＂",$str);
		$str = str_replace("\'","’",$str);
		$str = str_replace("'","’",$str);
		$str = str_replace("  "," ",$str);
		$str = str_replace("  "," ",$str);
		$str = str_replace("  "," ",$str);
		$str = str_replace("  "," ",$str);
		return $str;
	}

	####### [ slack 메시지 보내기 ] #######
	function SLACK_MESSAGE($arr)
	{
		if(empty($arr['title'])) $arr['title'] = "Message Bot";
		$url = "*********************************************************************************";
		$arr_body = [];
		$arr_body['channel'] = "#schedule";
		$arr_body['username'] = $arr['title'];
		if(!empty($arr['icon_emoji'])) $arr_body['icon_emoji'] = $arr['icon_emoji'];
		if(!empty($arr['icon_url'])) $arr_body['icon_url'] = $arr['icon_url'];
		$arr_body['text'] = "`Date`  ".date('Y-m-d H:i:s')."\n";
		$arr_body['text'] .= "`Page`  ".$_SERVER['PHP_SELF']."\n";
		$arr_body['text'] .= $arr['message'];
		//array_walk($arr_body, "change_iconv");

		$request = curl_init();
		curl_setopt($request, CURLOPT_URL, $url);
		curl_setopt($request, CURLOPT_CUSTOMREQUEST, 'POST');
		curl_setopt($request, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($request, CURLOPT_FOLLOWLOCATION, true);
		curl_setopt($request, CURLOPT_TIMEOUT, 10);
		curl_setopt($request, CURLOPT_CONNECTTIMEOUT, 10);
		curl_setopt($request, CURLOPT_POST, true);
		curl_setopt($request, CURLOPT_POSTFIELDS, json_encode($arr_body));
		$rs = curl_exec($request);

		return $rs;
	}

	####### [ 배열을 체크표시로 출력] #######
	function check_mark2($arr,$val=null) {
		if(is_array($arr)){
			foreach($arr as $key => $value) {
				//$html .= ($key==$val)?"☑ ":"☐ ";
				$html .= ($key==$val)?"A ":"B ";
				$html .= $value . "          ";
			}
		}
		return $html;
	}

	####### [ 임의 파일명 구하기 ] #######
	function randomFileName($folder,$extension=null) 
	{
		$mctime			= microtime();
		$imsiFileName	= explode(" ",$mctime);
		$filename			= $imsiFileName[1].$imsiFileName[0];
		$filename			= str_replace(".","",$filename);

		if($extension)$changefn = $filename . "." . $extension;
		else $changefn = $filename;
		for( $i=0; file_exists($folder."/".$changefn); $i++ ){ // 같은 이름이 있을시
			if($extension)$changefn = $filename . "_" . $i . "." . $extension;
			else $changefn = $filename . "_" . $i;
		}

		return $changefn;
	}

	####### [ 한글 문자열 자르기 ] #######
	function KoreanStr_cut($str, $len, $tail=""){
		$msg = (strlen($str) > $len) ? mb_strcut($str, 0, $len, 'UTF-8').$tail : $str; 
		return $msg; 
	}

	####### [ 배열을 쿼리 Case문으로 변환 ] #######
	function Case_Convert($nm,$arr)
	{
		$str = "CASE $nm ";
		if($arr){
			foreach($arr as $key => $value) {
				$str .= "WHEN '$key' THEN '$value' ";
			}
		}
		$str .= "ELSE '' END ";
		return $str;
	}

	
 
	#########################################################
	########## [schedule/sperp/online_as_sms.psh] ###########
	####### [온라인 AS 접수 카카오톡 알림톡(SMS) 발송] ####### 20250226 KSH(101141) 추가
	// AS - 접근 불가 상태  
	function arrAsInvalidState($type = 1) {//$type 1: arr , 2 : str
        $invalidStates = ['31', '40', '90', '91', '92', '93', '94', '95'];
        return ($type == 1) 
            ? $invalidStates 
            : implode(',', array_map(function($v) { return "'$v'"; }, $invalidStates));
    }
    
    // AS - 입고대기전 상태
    function arrAsWaybillState($type = 1) { //$type 1: arr , 2 : str
        $waybillStates = ['01', '05', '10', '15'];
        return ($type == 1) 
            ? $waybillStates 
            : implode(',', array_map(function($v) { return "'$v'"; }, $waybillStates));
    }

    // AS - 공휴일 정보
	function arrHOLIDAY($where=null,$order=null,$dateF='YYYY-MM-DD') {
		global $dbconn_sperp_posbank;
		if($order) $order = $order." ,";
		if($where) $where = " AND ".$where;
		$SQL = "SELECT TO_CHAR(HDATE,'".$dateF."') AS HDATE FROM HOLIDAY_DATA
		WHERE HDATE IS NOT NULL ".$where."
		ORDER BY ".$order." HDATE";
		$arrROW = $dbconn_sperp_posbank->query_rows($SQL);
		return $arrROW;
	}
	// AS - 공휴일 계산
	function isBusinessDay($date, $arrHolidays) {
		$weekDay = date('N', strtotime($date)); // 1 (월) ~ 7 (일)
		if ($weekDay >= 6) return false; // 주말 제외
		foreach ($arrHolidays as $holiday) {
			if ($holiday['HDATE'] == $date) return false; // 공휴일 제외
		}
		return true;
	}
	function getBusinessDate($startDate, $days, $arrHolidays=null) {
		if(empty($arrHolidays)){$arrHolidays = arrHOLIDAY();}
		$currentDate = $startDate;
		$businessDays = 0;
		
		while ($businessDays < $days-1) {
			$currentDate = date('Y-m-d', strtotime($currentDate . ' +1 day'));
			if (isBusinessDay($currentDate, $arrHolidays)) {
				$businessDays++;
			}
		}
		return $currentDate;
	}
	// AS - 온라인AS접수 영업일 마감기한 계산
	function getASBusinessDate($startDate, $type) { 	// yyyymmdd입력 (마감일 TYPE입력 - 1:입고 2:점검료결제 3:수리선택 4:수리비결제 5 미결제 )
		global $dbconn_sperp_posbank;
		$F_startDate = date('Ymd', strtotime($startDate));
        if($type ==5){
			$BAS_DATE = $dbconn_sperp_posbank->query_one("SELECT BAS_OP1 FROM BAS WHERE BAS_CODE LIKE 'A302'");
        }else{
            $BAS_DATE = $dbconn_sperp_posbank->query_one("SELECT BAS_OP".($type+1)." FROM BAS WHERE BAS_CODE LIKE 'A302'");
        }
		$cal_date = getBusinessDate($F_startDate,$BAS_DATE);
		return $cal_date;
	}

    // AS - 온라인AS접수 접수건별 카카오톡 알림톡 전송
	function AS_KAKAO_SMS_BY_RECEPTION($ONLINE_CODE,$SMS_TYPE='1'){ // 접수건별 SMS
		global $dbconn_sperp_posbank, $dbconn_m2g;
		$INSP_FEE = '10,000';
		$PB_ACCOUNT_NAME = "(주)포스뱅크";
		$PB_ACCOUNT_NUM = "[기업은행] 478-015878-01-037";
		// 온라인AS접수번호
		$RCT_CODE = substr($ONLINE_CODE,0,4);
		$HDATE = substr($ONLINE_CODE,4,8);
		$HNO = substr($ONLINE_CODE,12,4);
		// 접수내용
		$SQL = "
			SELECT 
				A.RCT_CODE ,A.HDATE ,A.HNO ,A.SNO
				,A.STATE ,TO_CHAR(A.REG_IDATE, 'YYYY') || '년 ' || TO_CHAR(A.REG_IDATE, 'MM') || '월 ' ||TO_CHAR(A.REG_IDATE, 'DD') || '일' AS IDATE
				,A.CT_CODE ,A.CUSTNAME ,A.CT_NO ,A.CT_MAN ,A.TEL2  ,A.PASSWORD ,A.REG_IDATE AREG_IDATE ,A.SEND_ESTIMATE_DATE 
				,PR.PR_NAME ,B.BAS_NAME 
				,C1.BANK_NAME C1_BANK_NAME
				,C2.BANK_NAME C2_BANK_NAME ,C2.REG_IDATE C2REG_IDATE
			FROM ASS_ACCEPT A
			LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE
			LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE
			LEFT JOIN AS_PAYMENT C1 ON A.RCT_CODE = C1.RCT_CODE AND A.HDATE = C1.HDATE AND A.HNO = C1.HNO AND C1.SERVICE_TYPE ='0'
			LEFT JOIN AS_PAYMENT C2 ON A.RCT_CODE = C2.RCT_CODE AND A.HDATE = C2.HDATE AND A.HNO = C2.HNO AND C2.SERVICE_TYPE ='1'
			WHERE A.RCT_CODE = '".$RCT_CODE."' AND A.HDATE = '".$HDATE."' AND A.HNO = '".$HNO."'
			ORDER BY A.SNO
		";
		$ASS_ROWS = $dbconn_sperp_posbank->query_rows($SQL);
		$ASS_ROW = $ASS_ROWS[0];
		// 사업자 / 개인
		if($ASS_ROW['CT_CODE']=='********'){  $CT = false;        $PERSONAL = true; $M2G_MODE ='ONLINE_AS_PERSONAL';}
		else{ $CT = true;        $PERSONAL = false;  $M2G_MODE = 'ONLINE_AS_CT2';}
		
		// INFO_MSG 제품명
		$INFO_MSG = '';
		// AS 접수 제품 개수 
		$SQL = "
			SELECT COUNT(*)
			FROM ASS_ACCEPT A
			LEFT JOIN ASSERIAL B ON A.AS_SERIAL = B.AS_SERIAL
			LEFT JOIN PR C ON B.PR_CODE = C.PR_CODE
			WHERE A.RCT_CODE = '".$RCT_CODE."' AND A.HDATE = '".$HDATE."' AND A.HNO = '".$HNO."' 
		";
		$CNT_ROW = $dbconn_sperp_posbank->query_one($SQL); 
		$PR_NAMES = array_filter(array_column($ASS_ROWS, 'PR_NAME')); // 제품명이 있는 항목만 추출
		$REP_PR_NAME = reset($PR_NAMES); // 첫 번째 제품명
		if(empty($REP_PR_NAME)){$BAS_NAMES = array_filter(array_column($ASS_ROWS, 'BAS_NAME')); $REP_PR_NAME = reset($BAS_NAMES);} // 첫번째 제품분류명
		if ($CNT_ROW == '1') { $INFO_MSG .= $REP_PR_NAME ? "{$REP_PR_NAME} 제품 건 " : " 제품 1건 ";} 
		else { $INFO_MSG .= $REP_PR_NAME ? "{$REP_PR_NAME} 제품 외 " . ($CNT_ROW - 1) . " 건 " : "제품 $CNT_ROW 건 ";}

		// 수리비 
		$SQL = "SELECT PT_AMT FROM AS_PAYMENT WHERE RCT_CODE = '".$RCT_CODE."' AND HDATE = '".$HDATE."' AND HNO = '".$HNO."'  AND SERVICE_TYPE ='1'";
		$TOTAL_REPAIR_FEE = $dbconn_sperp_posbank->query_one($SQL); 

		// 마감기한 TYPE1 :입고 / TYPE2 :점검료 / TYPE3 :수리선택 / TYPE4 :수리비
		$DEADLINE1 = getASBusinessDate($ASS_ROW['AREG_IDATE'],1);
		$DEADLINE2 = getASBusinessDate($ASS_ROW['AREG_IDATE'],2); // 접수일 기준 ? 입고이후 기준?
		$DEADLINE3 = getASBusinessDate($ASS_ROW['SEND_ESTIMATE_DATE'],3);
		$DEADLINE4 = getASBusinessDate($ASS_ROW['C2REG_IDATE'],4);
		
		// [SMS 내용]
		$TITLE = '';    $INFO_MSG2 = '';    $NOTE_MSG = ""; $FAIL_SMS_MSG = "[포스뱅크]\n";
		switch($SMS_TYPE){
			case '1': // 입고마감(자동)
				$TITLE = '제품 입고마감 1일 전'; 
				$INFO_MSG2 = '도착하지 않았습니다. 기한내 미입고시 해당 AS접수는 자동 취소처리 됩니다.';
				$NOTE_MSG = "\n■ 입고기한 : {$DEADLINE1}\n";
				$FAIL_SMS_MSG .= "A/S접수 입고마감 1일전 안내";
				break;   
			case '3': //선택마감(자동)
				$TITLE = '수리 선택마감 1일 전'; 
				$INFO_MSG2 = '수리 진행/거절을 선택하지 않았습니다. 기한 내 미선택시 해당 AS접수는 자동 반송(착불)처리 됩니다.';
				$NOTE_MSG = "\n■ 수리 진행/거절 선택 기한 : {$DEADLINE3}\n\n■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
				$FAIL_SMS_MSG .= "A/S접수 수리선택마감 1일전 안내";
				break;   
			case '4_1': // 결제마감 - 수리비 (자동) 무통장입금버전 
				$TITLE = '수리비 미결제 7일 경과'; 
				$INFO_MSG2 = '수리비 결제가 확인되지 않았습니다. 결제기한 내 결제해 주세요. 미결제 시, 해당 AS접수는 자동 반송(착불) 처리됩니다.';
				$NOTE_MSG .= "\n■ 입금금액 : {$TOTAL_REPAIR_FEE}\n";
				$NOTE_MSG .= "■ 입금계좌 : {$PB_ACCOUNT_NUM}\n";
				$NOTE_MSG .= "■ 예금주 : {$PB_ACCOUNT_NAME}\n";
				$NOTE_MSG .= "■ 수리비 결제기한 : {$DEADLINE4}\n";
				$NOTE_MSG .= "■ 입금자명 : {$ASS_ROW['C2_BANK_NAME']}\n\n■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
				$FAIL_SMS_MSG .= "A/S접수 수리비 미입금 마감전 안내";
				break;   
            case '4_2': // 결제마감 - 수리비 (자동) 무통장입금버전 
                $TITLE = '수리비 미결제 14일 경과'; 
                $INFO_MSG2 = '수리비 결제가 확인되지 않았습니다. 결제기한 내 결제해 주세요. 미결제 시, 해당 AS접수는 자동 반송(착불) 처리됩니다.';
                $NOTE_MSG .= "\n■ 입금금액 : {$TOTAL_REPAIR_FEE}\n";
                $NOTE_MSG .= "■ 입금계좌 : {$PB_ACCOUNT_NUM}\n";
                $NOTE_MSG .= "■ 예금주 : {$PB_ACCOUNT_NAME}\n";
                $NOTE_MSG .= "■ 수리비 결제기한 : {$DEADLINE4}\n";
                $NOTE_MSG .= "■ 입금자명 : {$ASS_ROW['C2_BANK_NAME']}\n\n■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
                $FAIL_SMS_MSG .= "A/S접수 수리비 미입금 마감전 안내";
                break;   
			case '4_3': // 결제마감 - 수리비 (자동) 무통장입금버전 
				$TITLE = '수리비 결제마감 1일 전'; 
				$INFO_MSG2 = '수리비 결제가 확인되지 않았습니다. 결제기한 내 결제해 주세요. 미결제 시, 해당 AS접수는 자동 반송(착불) 처리됩니다.';
				$NOTE_MSG .= "\n■ 입금금액 : {$TOTAL_REPAIR_FEE}\n";
				$NOTE_MSG .= "■ 입금계좌 : {$PB_ACCOUNT_NUM}\n";
				$NOTE_MSG .= "■ 예금주 : {$PB_ACCOUNT_NAME}\n";
				// $NOTE_MSG .= "■ 수리비 결제기한 : {$DEADLINE4}\n";
				$NOTE_MSG .= "■ 입금자명 : {$ASS_ROW['C2_BANK_NAME']}\n\n■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
				$FAIL_SMS_MSG .= "A/S접수 수리비 미입금 마감전 안내";
				break;   
			default: return;
		}
		$FAIL_SMS_MSG .= "(https://as.spqms.co.kr/)";
		// 공통메시지
		$NOTE_MSG .= "\n■ 주의: 유의 사항 미숙지로 인한 불이익은 책임지지 않습니다.\n\n▶ 문의: 고객센터 1544-3223\n[본 메시지는 발신 전용으로 회신 되지 않습니다.]\n";

		// [SMS]
		$s_content ="[포스뱅크 A/S접수 {$TITLE} 안내]\n";
		$s_content .="\n";
		if($CT){ // 거래처
			$s_content .="{$ASS_ROW['CT_MAN']}({$ASS_ROW['CUSTNAME']}) 고객님, \n";
		}else if($PERSONAL){ // 개인
			$s_content .="{$ASS_ROW['CT_MAN']}고객님, \n";
		}
		$s_content .="포스뱅크 A/S 접수 {$INFO_MSG}{$INFO_MSG2}";
		$s_content .="\n";
		$s_content .="■ 접수일 : {$ASS_ROW['IDATE']}\n";
		if($CT){ // 거래처
			$s_content .="■ 상호명 : {$ASS_ROW['CUSTNAME']}\n";
			$s_content .="■ 사업자 번호 : {$ASS_ROW['CT_NO']}\n";
			$s_content .="■ 담당자 성명 : {$ASS_ROW['CT_MAN']}\n";
		}else if($PERSONAL){ // 개인
			$s_content .="■ 이름:  {$ASS_ROW['CT_MAN']}\n";
			$s_content .="■ 전화번호 : {$ASS_ROW['TEL2']}\n";
		}
		$s_content .="{$NOTE_MSG}\n";
		$s_content .="※ 본 메시지는 고객님의 A/S 접수 유의사항 동의에 의해 발송되었습니다.※\n";
		
		$SQL = 
		" INSERT INTO M2G_DATA (
			CREATE_DTM, FROM_PHONE_NUMBER, TO_PHONE_NUMBER, SUBJECT, 
			CONTENTS, 
			SEND_REQUEST_DTM, STATUS, MESSAGE_TYPE_CODE, FROM_KAKAO_CHANNEL, TEMPLATE_ID,
			ATTACHMENT_JSON, 
			SYS_USE, COM_ID, NEXT_METHOD_ON_FAIL, NEXT_METHOD_SUBJECT, NEXT_METHOD_CONTENTS
		) VALUES (
			DATE_FORMAT(now(), '%Y%m%d%H%i%S'), '********', '".$ASS_ROW['TEL2']."', NULL, 
			'{$s_content}',
			'', '0', 'at','@posbank알리미','{$M2G_MODE}',
			'{\"button\":[{\"url_mobile\":\"https://as.spqms.co.kr/\",\"url_pc\":\"https://as.spqms.co.kr/\",\"name\":\"A/S 접수내역 조회하기\",\"type\":\"WL\"}]}',
			'1', 'POSBANK', 'sms', 
			'posbank 알림톡','{$FAIL_SMS_MSG}'
		)";
		$rs = $dbconn_m2g->iud_query($SQL);
		return $rs;
	}

    
    // AS - 온라인AS접수 제품건별 카카오톡 알림톡 전송
    function AS_KAKAO_SMS_BY_PRODUCT($AS_ID,$SMS_TYPE){
		global $dbconn_sperp_posbank,$dbconn_m2g;
		$INSP_FEE = '10,000';
		$PB_ACCOUNT_NAME = "(주)포스뱅크";
		$PB_ACCOUNT_NUM = "[기업은행] 478-015878-01-037";
	
		// 접수내용
		$SQL = "
			SELECT 
				A.RCT_CODE ,A.HDATE ,A.HNO ,A.SNO
				,A.STATE ,TO_CHAR(A.REG_IDATE, 'YYYY') || '년 ' || TO_CHAR(A.REG_IDATE, 'MM') || '월 ' ||TO_CHAR(A.REG_IDATE, 'DD') || '일' AS IDATE
				,A.CT_CODE ,A.CUSTNAME ,A.CT_NO ,A.CT_MAN ,A.TEL2  ,A.PASSWORD ,A.REG_IDATE AREG_IDATE ,A.SEND_ESTIMATE_DATE 
				,PR.PR_NAME ,B.BAS_NAME 
				,C1.BANK_NAME C1_BANK_NAME
				,C2.BANK_NAME C2_BANK_NAME ,C2.REG_IDATE C2REG_IDATE
			FROM ASS_ACCEPT A
			LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE
			LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE
			LEFT JOIN AS_PAYMENT C1 ON A.RCT_CODE = C1.RCT_CODE AND A.HDATE = C1.HDATE AND A.HNO = C1.HNO AND C1.SERVICE_TYPE ='0'
			LEFT JOIN AS_PAYMENT C2 ON A.RCT_CODE = C2.RCT_CODE AND A.HDATE = C2.HDATE AND A.HNO = C2.HNO AND C2.SERVICE_TYPE ='1'
			WHERE A.AS_ID = '{$AS_ID}'
			ORDER BY A.SNO
		";
		$ASS_ROW = $dbconn_sperp_posbank->query_row($SQL);
		// 사업자 / 개인
		if($ASS_ROW['CT_CODE']=='********'){  $CT = false;        $PERSONAL = true; $M2G_MODE ='ONLINE_AS_PERSONAL';}
		else{ $CT = true;        $PERSONAL = false;  $M2G_MODE = 'ONLINE_AS_CT2';}
		
		// INFO_MSG 제품명
		if($ASS_ROW['PR_NAME']){ $PR_NAME = $ASS_ROW['PR_NAME'];}
		else{$PR_NAME = $ASS_ROW['BAS_NAME'];}
		$INFO_MSG =  $PR_NAME." 건 ";
	 
		// [SMS 내용]
		// SMS 타입 TITLE 제목메시지 / INFO_MSG2 안내메시지 / NOTE_MSG 정보+주의 메시지 / 실패시 SMS 메시지
		$TITLE = '';    $INFO_MSG2 = '';    $NOTE_MSG = ""; $FAIL_SMS_MSG = "[포스뱅크]\n";
		switch($SMS_TYPE){
			case '1': // 수리완료
				$TITLE = '수리 완료'; 
				$INFO_MSG2 = '수리가 완료되었습니다. 곧 발송 될 예정입니다. 운송장번호는 접수내역조회에서 확인하실 수 있습니다.';
				$FAIL_SMS_MSG .= "A/S접수 수리 완료 안내";
				break;    
			default: return;
		}
		$FAIL_SMS_MSG .= "(https://as.spqms.co.kr/)";
		// 공통메시지
		$NOTE_MSG .= "\n■ 주의: 유의 사항 미숙지로 인한 불이익은 책임지지 않습니다.\n\n▶ 문의: 고객센터 1544-3223\n[본 메시지는 발신 전용으로 회신 되지 않습니다.]\n";
	
		// [SMS]
		$s_content ="[포스뱅크 A/S 접수 {$TITLE} 안내]\n";
		$s_content .="\n";
		if($CT){ // 거래처
			$s_content .="{$ASS_ROW['CT_MAN']}({$ASS_ROW['CUSTNAME']}) 고객님, \n";
		}else if($PERSONAL){ // 개인
			$s_content .="{$ASS_ROW['CT_MAN']}고객님, \n";
		}
		$s_content .="포스뱅크 A/S 접수 {$INFO_MSG}{$INFO_MSG2}";
		$s_content .="\n";
		$s_content .="■ 접수일 : {$ASS_ROW['IDATE']}\n";
		if($CT){ // 거래처
			$s_content .="■ 상호명 : {$ASS_ROW['CUSTNAME']}\n";
			$s_content .="■ 사업자 번호 : {$ASS_ROW['CT_NO']}\n";
			$s_content .="■ 담당자 성명 : {$ASS_ROW['CT_MAN']}\n";
		}else if($PERSONAL){ // 개인
			$s_content .="■ 이름:  {$ASS_ROW['CT_MAN']}\n";
			$s_content .="■ 전화번호 : {$ASS_ROW['TEL2']}\n";
		}
		$s_content .="{$NOTE_MSG}\n";
		$s_content .="※ 본 메시지는 고객님의 A/S 접수 유의사항 동의에 의해 발송되었습니다.※\n";
		
		// [M2G SMS SQL]
		$SQL = 
		" INSERT INTO M2G_DATA (
			CREATE_DTM, FROM_PHONE_NUMBER, TO_PHONE_NUMBER, SUBJECT, 
			CONTENTS, 
			SEND_REQUEST_DTM, STATUS, MESSAGE_TYPE_CODE, FROM_KAKAO_CHANNEL, TEMPLATE_ID,
			ATTACHMENT_JSON, 
			SYS_USE, COM_ID, NEXT_METHOD_ON_FAIL, NEXT_METHOD_SUBJECT, NEXT_METHOD_CONTENTS
		) VALUES (
			DATE_FORMAT(now(), '%Y%m%d%H%i%S'), '********', '".$ASS_ROW['TEL2']."', NULL, 
			'{$s_content}',
			'', '0', 'at','@posbank알리미','{$M2G_MODE}',
			'{\"button\":[{\"url_mobile\":\"https://as.spqms.co.kr/\",\"url_pc\":\"https://as.spqms.co.kr/\",\"name\":\"A/S 접수내역 조회하기\",\"type\":\"WL\"}]}',
			'1', 'POSBANK', 'sms', 
			'posbank 알림톡','{$FAIL_SMS_MSG}'
		)";
		$rs = $dbconn_m2g->iud_query($SQL);
		return $rs;
	
	} 


	
    // 온라인AS접수 / 카카오톡 알림톡 전송 / 비즈뿌리오 / 자동 미입고, 수리비 미결제
	function AS_BIZ_KAKAO_RECEPTION($ONLINE_CODE,$SMS_TYPE){ 
		global $dbconn_sperp_posbank,$dbconn_m2g;
		$DAUO_KEY = "1a0d03051f71da3842c050e648dfea13a5869431"; // 비즈뿌리오 카카오톡 알림톡 키

        $arr_SQL = [];
        $N=0;
        // 랜덤키
        $dateTime = date('ymdHis');   
        $randomNumber = str_pad(rand(0, 999), 3, '0', STR_PAD_LEFT); 
        $CMID = $dateTime . $randomNumber;
        $CONTENT_BTN = "{\"button\":[{\"url_mobile\":\"https://as.spqms.co.kr/\",\"url_pc\":\"https://as.spqms.co.kr/\",\"name\":\"A/S 접수내역 조회하기\",\"type\":\"WL\"}]}";
		// 접수내용
		$SQL = "
			SELECT 
				A.RCT_CODE ,A.HDATE ,A.HNO ,A.SNO ,A.AS_ID
				,A.STATE ,TO_CHAR(A.REG_IDATE, 'YYYY') || '년 ' || TO_CHAR(A.REG_IDATE, 'MM') || '월 ' ||TO_CHAR(A.REG_IDATE, 'DD') || '일' AS IDATE
				,A.CT_CODE ,A.CT_NAME ,A.CUSTNAME ,A.CT_NO ,A.CT_MAN ,A.TEL2  ,A.PASSWORD ,A.SEND_ESTIMATE_DATE 
                ,(CASE 
                    WHEN A.INSP_STATE = '1' AND A.STATE IN ('10','15') THEN TO_CHAR(P1.PAY_DATE,'YYYY/MM/DD') 
                    WHEN A.INSP_STATE IN ('2','3','4') AND A.STATE IN ('10','15') THEN TO_CHAR(A.REG_IDATE,'YYYY/MM/DD') 
                    ELSE '' 
                END ) AS RECEIVE_DEADLINE
				,PR.PR_NAME ,B.BAS_NAME 
			FROM ASS_ACCEPT A
			LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE
			LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE
            LEFT JOIN AS_PAYMENT P1 ON A.RCT_CODE = P1.RCT_CODE AND A.HDATE =P1.HDATE AND A.HNO =P1.HNO AND P1.SERVICE_TYPE ='0'
			WHERE A.RCT_CODE||A.HDATE||A.HNO = '{$ONLINE_CODE}'
		";
		$row = $dbconn_sperp_posbank->query_row($SQL);
		// 사업자 / 개인
        if($row['CT_CODE']!='********'){     // 사업자
			$isCT = true; 
            $isPersonal = false; 
            $M2G_MODE='ONLINE_AS_CT2';  
            $NAME = $row['CT_NAME'].'('.$row['CT_MAN'].') ';  
			$INFO3 =
                "■ 상호명 : ".$row['CT_NAME']."\n".
                "■ 사업자 번호 : ".$row['CT_NO']."\n".
                "■ 담당자 성명 : ".$row['CT_MAN']."\n";
		} else if($row['CT_CODE']=='********'){   // 개인
			$isCT = false;  
            $isPersonal = true;
            $M2G_MODE='ONLINE_AS_PERSONAL';
            $NAME = $row['CT_MAN'].' ';      
            $INFO3 = 
                "■ 이름:  ".$row['CT_MAN']."\n".
                "■ 전화번호 : ".$row['TEL2']."\n";
		}
        $INFO1 = " ".$row['PR_NAME']." 제품 ";
        $INFO4 ="■ 접수번호 : ".$row['AS_ID']."\n";
        
		// SMS 내용
		switch($SMS_TYPE){
        	case '1':   // 입고마감
                $DEADLINE1 =getASBusinessDate($row['RECEIVE_DEADLINE'],1);
				$TITLE = '입고 마감 1일 전'; 
				$INFO2 = " 입고되지 않았습니다. 기한 내 미입고 시, 해당 A/S 접수는 취소 처리 됩니다.";
				$NOTE1 = "\n■ 입고 기한 : {$DEADLINE1}\n";
				$SMS_CONTENT = "A/S접수 입고마감 1일전 안내";
				break;    
        	case '2':   // 수리비 결제마감 7일경과
        		$DEADLINE3 = getASBusinessDate($row['SEND_ESTIMATE_DATE'],3);
				$TITLE = '수리비 미결제 7일 경과'; 
				$INFO2 = "수리비가 결제되지 않았습니다. 결제 기한 내에 결제해 주세요. 미결제 시, 해당 A/S 접수는 자동 반송(착불)처리 됩니다.";
				$NOTE1 = "\n■ 결제 기한 : {$DEADLINE3}\n".
                         "■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
				$SMS_CONTENT = "A/S접수 수리비 미결제 7일경과 안내";
				break;    
        	case '3':   // 수리비 결제마감 7일경과
        		$DEADLINE3 = getASBusinessDate($row['SEND_ESTIMATE_DATE'],3);
				$TITLE = '수리비 미결제 14일 경과'; 
				$INFO2 = "수리비가 결제되지 않았습니다. 결제 기한 내에 결제해 주세요. 미결제 시, 해당 A/S 접수는 자동 반송(착불)처리 됩니다.";
				$NOTE1 = "\n■ 결제 기한 : {$DEADLINE3}\n".
                         "■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
				$SMS_CONTENT = "A/S접수 수리비 미결제 14일경과 안내";
				break;    
        	case '4':   // 수리비 결제마감 1일전
        		$DEADLINE3 = getASBusinessDate($row['SEND_ESTIMATE_DATE'],3);
				$TITLE = '수리비 결제 마감 1일 전'; 
				$INFO2 = "수리비가 결제되지 않았습니다. 결제 기한 내에 결제해 주세요. 미결제 시, 해당 A/S 접수는 자동 반송(착불)처리 됩니다.";
				$NOTE1 = "\n■ 결제 기한 : {$DEADLINE3}\n".
                         "■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
				$SMS_CONTENT = "A/S접수 수리비 결제마감 1일 전 안내";
				break;    
			case '5':   // 수리완료
				$TITLE = '수리 완료'; 
				$INFO2 = "수리가 완료되었습니다. 발송 될 예정입니다. \n운송장번호는 접수내역조회에서 확인하실 수 있습니다.";
				$NOTE1=''; $NOTE2='';
				$SMS_CONTENT = "A/S접수 수리완료 안내";
				break;    
			default: return;
		}	
        $SMS_CONTENT .= "(https://as.spqms.co.kr/)";
		$NOTE2 = "\n▶ 문의: 고객센터 1544-3223\n\n- 주의: 유의사항 미숙지로 인한 불이익은 책임지지 않습니다.\n- 발신 전용으로 회신되지 않습니다.\n";

		// SMS
        $CONTENT = 
            "[포스뱅크 A/S접수 {$TITLE} 안내]\n\n".
            "{$NAME}고객님, ".
            "포스뱅크 A/S 접수{$INFO1}{$INFO2}\n\n".
            "■ 접수일 : ".$row['IDATE']."\n".
            $INFO3.$INFO4.
            $NOTE1.$NOTE2.
            "※ 본 메시지는 고객님의 A/S 접수 유의사항 동의에 의해 발송되었습니다.※";

        // BIZ_ATTACHMENTS
        $arr_SQL[$N] = "INSERT INTO m2g.BIZ_ATTACHMENTS (MSG_KEY, SEQ, TYPE, CONTENTS)VALUES ('{$CMID}', '1', 'JSON', '{$CONTENT_BTN}')";
        $N++;
        // BIZ_MSG
        $arr_SQL[$N] = 
        "INSERT INTO m2g.BIZ_MSG (
            COM_ID, SYS_USE, CMID, MSG_TYPE,  REQUEST_TIME, SEND_TIME, DEST_PHONE, SEND_PHONE, MSG_BODY, TEMPLATE_CODE, SENDER_KEY, NATION_CODE, RE_TYPE, RE_BODY, ATTACHED_FILE
        )VALUES(
            'POSBANK_AS', '1', '{$CMID}', '6', NOW(), NOW(), '".str_replace('-','',$row['TEL2'])."', '********',  '{$CONTENT}', '{$M2G_MODE}', '{$DAUO_KEY}', 82 , 'SMS', '{$SMS_CONTENT}' ,'{$CMID}'
        )";
        $N++;
	 
        $rs = $dbconn_m2g->iud_query($arr_SQL);
		return $rs;
	}


    // 온라인AS접수 / 카카오톡 알림톡 전송 / 비즈뿌리오 / 자동 미입고, 수리비 미결제
	function AS_BIZ_KAKAO_RECEPTION_ROWS($ONLINE_CODE,$SMS_TYPE){ 
		global $dbconn_sperp_posbank,$dbconn_m2g;
		$DAUO_KEY = "1a0d03051f71da3842c050e648dfea13a5869431"; // 비즈뿌리오 카카오톡 알림톡 키

        $arr_SQL = [];
        $arr_SQL2 = [];
        $N=0;
        $N2=0;
        // 랜덤키
        $dateTime = date('ymdHis');   
        $randomNumber = str_pad(rand(0, 999), 3, '0', STR_PAD_LEFT); 
        $CMID = $dateTime . $randomNumber;
        $CONTENT_BTN = "{\"button\":[{\"url_mobile\":\"https://as.spqms.co.kr/\",\"url_pc\":\"https://as.spqms.co.kr/\",\"name\":\"A/S 접수내역 조회하기\",\"type\":\"WL\"}]}";
		// 접수내용
		$SQL = "
			SELECT 
				A.RCT_CODE ,A.HDATE ,A.HNO ,A.SNO ,A.AS_ID
				,A.STATE ,TO_CHAR(A.REG_IDATE, 'YYYY') || '년 ' || TO_CHAR(A.REG_IDATE, 'MM') || '월 ' ||TO_CHAR(A.REG_IDATE, 'DD') || '일' AS IDATE
				,A.CT_CODE ,A.CT_NAME ,A.CUSTNAME ,A.CT_NO ,A.CT_MAN ,A.TEL2  ,A.PASSWORD ,A.SEND_ESTIMATE_DATE 
				,(CASE 
					WHEN A.INSP_STATE = '1' AND A.STATE IN ('10','15') THEN TO_CHAR(P1.PAY_DATE,'YYYY/MM/DD') 
					WHEN A.INSP_STATE IN ('2','3','4') AND A.STATE IN ('10','15') THEN TO_CHAR(A.REG_IDATE,'YYYY/MM/DD') 
					ELSE '' 
				END ) AS RECEIVE_DEADLINE
				,PR.PR_NAME ,B.BAS_NAME 
				,(CASE WHEN BAS4.BAS_NAME IS NOT NULL AND BAS4.BAS_OP5 IS NOT NULL THEN BAS4.BAS_OP5 ELSE BAS3.BAS_OP5 END) AS BAS_OP5
			FROM ASS_ACCEPT A
			LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE
			LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE
			LEFT JOIN BAS BAS3 ON BAS3.BAS_CODE ='A302'
			LEFT JOIN BAS BAS4 ON BAS4.BAS_CODE LIKE 'A5%' AND A.CT_CODE= BAS4.BAS_NAME 
			LEFT JOIN AS_PAYMENT P1 ON A.RCT_CODE = P1.RCT_CODE AND A.HDATE =P1.HDATE AND A.HNO =P1.HNO AND P1.SERVICE_TYPE ='0'
			WHERE A.RCT_CODE || A.HDATE||A.HNO = '{$ONLINE_CODE}'
		";
		$rows = $dbconn_sperp_posbank->query_rows($SQL);
		$row = $rows[0];
		// 사업자 / 개인
        if($row['CT_CODE']!='********'){     // 사업자
			$isCT = true; 
            $isPersonal = false; 
            $M2G_MODE='ONLINE_AS_CT2';  
            $NAME = $row['CT_NAME'].'('.$row['CT_MAN'].') ';  
			$INFO3 =
                "■ 상호명 : ".$row['CT_NAME']."\n".
                "■ 사업자 번호 : ".$row['CT_NO']."\n".
                "■ 담당자 성명 : ".$row['CT_MAN']."\n";
		} else if($row['CT_CODE']=='********'){   // 개인
			$isCT = false;  
            $isPersonal = true;
            $M2G_MODE='ONLINE_AS_PERSONAL';
            $NAME = $row['CT_MAN'].' ';      
            $INFO3 = 
                "■ 이름:  ".$row['CT_MAN']."\n".
                "■ 전화번호 : ".$row['TEL2']."\n";
		}
        $INFO1 = " ".$row['PR_NAME']." 제품 ";
        $INFO4 ="■ 접수번호 : ".$row['AS_ID'];
		// foreach($rows as $r){$INFO4 .="   - ".$r['AS_ID']."\n";}
        
        
		// SMS 내용
		switch($SMS_TYPE){
        	case '1':   // 입고마감
                $DEADLINE1 =getASBusinessDate($row['RECEIVE_DEADLINE'],1);
				$TITLE = '입고 마감 1일 전'; 
				$INFO2 = " 입고되지 않았습니다. 기한 내 미입고 시, 해당 A/S 접수는 취소 처리 됩니다.";
				$NOTE1 = "\n■ 입고 기한 : {$DEADLINE1}\n";
				$SMS_CONTENT = "A/S접수 입고마감 1일전 안내";
				break;    
        	case '2':   // 수리비 결제마감 7일경과
        		$DEADLINE3 = getASBusinessDate($row['SEND_ESTIMATE_DATE'],4);
				$TITLE = '수리비 미결제 7일 경과'; 
				$INFO2 = "수리비가 결제되지 않았습니다. 결제 기한 내에 결제해 주세요. 미결제 시, 해당 A/S 접수는 자동 반송(착불)처리 됩니다.";
				$NOTE1 = "\n■ 결제 기한 : {$DEADLINE3}\n".
                         "■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
				$SMS_CONTENT = "A/S접수 수리비 미결제 7일경과 안내";
				break;    
        	case '3':   // 수리비 결제마감 7일경과
        		$DEADLINE3 = getASBusinessDate($row['SEND_ESTIMATE_DATE'],4);
				$TITLE = '수리비 미결제 14일 경과'; 
				$INFO2 = "수리비가 결제되지 않았습니다. 결제 기한 내에 결제해 주세요. 미결제 시, 해당 A/S 접수는 자동 반송(착불)처리 됩니다.";
				$NOTE1 = "\n■ 결제 기한 : {$DEADLINE3}\n".
                         "■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
				$SMS_CONTENT = "A/S접수 수리비 미결제 14일경과 안내";
				break;    
        	case '4':   // 수리비 결제마감 1일전
        		$DEADLINE3 = getBusinessDate($row['SEND_ESTIMATE_DATE'], $row['BAS_OP5']);;
				$TITLE = '수리비 결제 마감(내일 반송 예정)'; 
				$INFO2 = "수리비가 결제되지 않았습니다. 결제 마감일 이후에는 반송(착불) 처리되오니, 오늘 중 결제를 완료해 주세요";
				$NOTE1 = "\n■ 결제 기한 : {$DEADLINE3}\n".
                         "■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
				$SMS_CONTENT = "A/S접수 수리비 결제마감 1일 전 안내";
				break;    
			case '5':   // 수리완료
				$TITLE = '수리 완료'; 
				$INFO2 = "수리가 완료되었습니다. 발송 될 예정입니다. \n운송장번호는 접수내역조회에서 확인하실 수 있습니다.";
				$NOTE1=''; $NOTE2='';
				$SMS_CONTENT = "A/S접수 수리완료 안내";
				break;    
			default: return;
		}	
        $SMS_CONTENT .= "(https://as.spqms.co.kr/)";
		$NOTE2 = "\n▶ 문의: 고객센터 1544-3223\n\n- 주의: 유의사항 미숙지로 인한 불이익은 책임지지 않습니다.\n- 발신 전용으로 회신되지 않습니다.\n";

		// SMS
        $CONTENT = 
            "[포스뱅크 A/S접수 {$TITLE} 안내]\n\n".
            "{$NAME}고객님, ".
            "포스뱅크 A/S 접수{$INFO1}{$INFO2}\n\n".
            "■ 접수일 : ".$row['IDATE']."\n".
            $INFO3.$INFO4.
            $NOTE1.$NOTE2.
            "※ 본 메시지는 고객님의 A/S 접수 유의사항 동의에 의해 발송되었습니다.※";

        // BIZ_ATTACHMENTS
        $arr_SQL[$N] = "INSERT INTO m2g.BIZ_ATTACHMENTS (MSG_KEY, SEQ, TYPE, CONTENTS)VALUES ('{$CMID}', '1', 'JSON', '{$CONTENT_BTN}')";
        $N++;
        // BIZ_MSG
        $arr_SQL[$N] = 
        "INSERT INTO m2g.BIZ_MSG (
            COM_ID, SYS_USE, CMID, MSG_TYPE,  REQUEST_TIME, SEND_TIME, DEST_PHONE, SEND_PHONE, MSG_BODY, TEMPLATE_CODE, SENDER_KEY, NATION_CODE, RE_TYPE, RE_BODY, ATTACHED_FILE
        )VALUES(
            'POSBANK_AS', '1', '{$CMID}', '6', NOW(), NOW(), '".str_replace('-','',$row['TEL2'])."', '********',  '{$CONTENT}', '{$M2G_MODE}', '{$DAUO_KEY}', 82 , 'SMS', '{$SMS_CONTENT}' ,'{$CMID}'
        )";
        $N++;
	 
		// AS_BIZ_LOG
		$arr_SQL2[$N2] = 
		"INSERT INTO AS_BIZ_LOG ( RCT_CODE ,HDATE ,HNO ,SNO ,MESSAGE ,REG_DATE ,REG_IDATE ,SMS ,CMID ,DEST_PHONE ,SEND_PHONE 
		)VALUES( '".$row['RCT_CODE']."' ,'".$row['HDATE']."' ,'".$row['HNO']."' ,F_ASBIZLOG_SNO ('".$row['RCT_CODE']."' ,'".$row['HDATE']."' ,'".$row['HNO']."')   ,'{$CONTENT}' ,SYSDATE ,SYSDATE ,'{$SMS_CONTENT}' ,'{$CMID}' ,'".str_replace('-','',$row['TEL2'])."' ,'********'
		)";
		$N2++;
        $rs = $dbconn_m2g->iud_query($arr_SQL);
        $rs2 = $dbconn_sperp_posbank->iud_query($arr_SQL2);
		return $rs;
	}

		
    // 온라인AS접수 / 카카오톡 알림톡 전송 / 비즈뿌리오 / 자동 미입고, 수리비 미결제
	function AS_BIZ_KAKAO_RECEPTION_ASID($AS_ID,$SMS_TYPE){ 
		global $dbconn_sperp_posbank,$dbconn_m2g;
		$DAUO_KEY = "1a0d03051f71da3842c050e648dfea13a5869431"; // 비즈뿌리오 카카오톡 알림톡 키

        $arr_SQL = [];
        $arr_SQL2 = [];
        $N=0;
        $N2=0;
        // 랜덤키
        $dateTime = date('ymdHis');   
        $randomNumber = str_pad(rand(0, 999), 3, '0', STR_PAD_LEFT); 
        $CMID = $dateTime . $randomNumber;
        $CONTENT_BTN = "{\"button\":[{\"url_mobile\":\"https://as.spqms.co.kr/\",\"url_pc\":\"https://as.spqms.co.kr/\",\"name\":\"A/S 접수내역 조회하기\",\"type\":\"WL\"}]}";
		// 접수내용
		$SQL = "
			SELECT 
				A.RCT_CODE ,A.HDATE ,A.HNO ,A.SNO ,A.AS_ID
				,A.STATE ,TO_CHAR(A.REG_IDATE, 'YYYY') || '년 ' || TO_CHAR(A.REG_IDATE, 'MM') || '월 ' ||TO_CHAR(A.REG_IDATE, 'DD') || '일' AS IDATE
				,A.CT_CODE ,A.CT_NAME ,A.CUSTNAME ,A.CT_NO ,A.CT_MAN ,A.TEL2  ,A.PASSWORD ,A.SEND_ESTIMATE_DATE 
                ,(CASE 
                    WHEN A.INSP_STATE = '1' AND A.STATE IN ('10','15') THEN TO_CHAR(P1.PAY_DATE,'YYYY/MM/DD') 
                    WHEN A.INSP_STATE IN ('2','3','4') AND A.STATE IN ('10','15') THEN TO_CHAR(A.REG_IDATE,'YYYY/MM/DD') 
                    ELSE '' 
                END ) AS RECEIVE_DEADLINE
				,PR.PR_NAME ,B.BAS_NAME 
			FROM ASS_ACCEPT A
			LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE
			LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE
            LEFT JOIN AS_PAYMENT P1 ON A.RCT_CODE = P1.RCT_CODE AND A.HDATE =P1.HDATE AND A.HNO =P1.HNO AND P1.SERVICE_TYPE ='0'
			WHERE A.AS_ID = '{$AS_ID}'
		";
		$row = $dbconn_sperp_posbank->query_row($SQL);
		// 사업자 / 개인
        if($row['CT_CODE']!='********'){     // 사업자
			$isCT = true; 
            $isPersonal = false; 
            $M2G_MODE='ONLINE_AS_CT2';  
            $NAME = $row['CT_NAME'].'('.$row['CT_MAN'].') ';  
			$INFO3 =
                "■ 상호명 : ".$row['CT_NAME']."\n".
                "■ 사업자 번호 : ".$row['CT_NO']."\n".
                "■ 담당자 성명 : ".$row['CT_MAN']."\n";
		} else if($row['CT_CODE']=='********'){   // 개인
			$isCT = false;  
            $isPersonal = true;
            $M2G_MODE='ONLINE_AS_PERSONAL';
            $NAME = $row['CT_MAN'].' ';      
            $INFO3 = 
                "■ 이름:  ".$row['CT_MAN']."\n".
                "■ 전화번호 : ".$row['TEL2']."\n";
		}
        $INFO1 = " ".$row['PR_NAME']." 제품 ";
        $INFO4 ="■ 접수번호 : ".$row['AS_ID']."\n";
        
		// SMS 내용
		switch($SMS_TYPE){
        	case '1':   // 입고마감
                $DEADLINE1 =getASBusinessDate($row['RECEIVE_DEADLINE'],1);
				$TITLE = '입고 마감 1일 전'; 
				$INFO2 = " 입고되지 않았습니다. 기한 내 미입고 시, 해당 A/S 접수는 취소 처리 됩니다.";
				$NOTE1 = "\n■ 입고 기한 : {$DEADLINE1}\n";
				$SMS_CONTENT = "A/S접수 입고마감 1일전 안내";
				break;    
        	case '2':   // 수리비 결제마감 7일경과
        		$DEADLINE3 = getASBusinessDate($row['SEND_ESTIMATE_DATE'],3);
				$TITLE = '수리비 미결제 7일 경과'; 
				$INFO2 = "수리비가 결제되지 않았습니다. 결제 기한 내에 결제해 주세요. 미결제 시, 해당 A/S 접수는 자동 반송(착불)처리 됩니다.";
				$NOTE1 = "\n■ 결제 기한 : {$DEADLINE3}\n".
                         "■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
				$SMS_CONTENT = "A/S접수 수리비 미결제 7일경과 안내";
				break;    
        	case '3':   // 수리비 결제마감 7일경과
        		$DEADLINE3 = getASBusinessDate($row['SEND_ESTIMATE_DATE'],3);
				$TITLE = '수리비 미결제 14일 경과'; 
				$INFO2 = "수리비가 결제되지 않았습니다. 결제 기한 내에 결제해 주세요. 미결제 시, 해당 A/S 접수는 자동 반송(착불)처리 됩니다.";
				$NOTE1 = "\n■ 결제 기한 : {$DEADLINE3}\n".
                         "■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
				$SMS_CONTENT = "A/S접수 수리비 미결제 14일경과 안내";
				break;    
        	case '4':   // 수리비 결제마감 1일전
        		$DEADLINE3 = getASBusinessDate($row['SEND_ESTIMATE_DATE'],3);
				$TITLE = '수리비 결제 마감 1일 전'; 
				$INFO2 = "수리비가 결제되지 않았습니다. 결제 기한 내에 결제해 주세요. 미결제 시, 해당 A/S 접수는 자동 반송(착불)처리 됩니다.";
				$NOTE1 = "\n■ 결제 기한 : {$DEADLINE3}\n".
                         "■ 반송 택배는 착불(기본운임 4,000원) 발송됩니다.\n- 상자의 부피, 중량에 따라 금액은 변동될 수 있습니다.\n";
				$SMS_CONTENT = "A/S접수 수리비 결제마감 1일 전 안내";
				break;
			case '5':   // 수리완료
				$TITLE = '수리 완료'; 
				$INFO2 = "수리가 완료되었습니다. 발송 될 예정입니다. \n운송장번호는 접수내역조회에서 확인하실 수 있습니다.";
				$NOTE1=''; $NOTE2='';
				$SMS_CONTENT = "A/S접수 수리완료 안내";
				break;    
			default: return;
		}	
        $SMS_CONTENT .= "(https://as.spqms.co.kr/)";
		$NOTE2 = "\n▶ 문의: 고객센터 1544-3223\n\n- 주의: 유의사항 미숙지로 인한 불이익은 책임지지 않습니다.\n- 발신 전용으로 회신되지 않습니다.\n";

		// SMS
        $CONTENT = 
            "[포스뱅크 A/S접수 {$TITLE} 안내]\n\n".
            "{$NAME}고객님, ".
            "포스뱅크 A/S 접수{$INFO1}{$INFO2}\n\n".
            "■ 접수일 : ".$row['IDATE']."\n".
            $INFO3.$INFO4.
            $NOTE1.$NOTE2.
            "※ 본 메시지는 고객님의 A/S 접수 유의사항 동의에 의해 발송되었습니다.※";

        // BIZ_ATTACHMENTS
        $arr_SQL[$N] = "INSERT INTO m2g.BIZ_ATTACHMENTS (MSG_KEY, SEQ, TYPE, CONTENTS)VALUES ('{$CMID}', '1', 'JSON', '{$CONTENT_BTN}')";
        $N++;
        // BIZ_MSG
        $arr_SQL[$N] = 
        "INSERT INTO m2g.BIZ_MSG (
            COM_ID, SYS_USE, CMID, MSG_TYPE,  REQUEST_TIME, SEND_TIME, DEST_PHONE, SEND_PHONE, MSG_BODY, TEMPLATE_CODE, SENDER_KEY, NATION_CODE, RE_TYPE, RE_BODY, ATTACHED_FILE
        )VALUES(
            'POSBANK_AS', '1', '{$CMID}', '6', NOW(), NOW(), '".str_replace('-','',$row['TEL2'])."', '********',  '{$CONTENT}', '{$M2G_MODE}', '{$DAUO_KEY}', 82 , 'SMS', '{$SMS_CONTENT}' ,'{$CMID}'
        )";
        $N++;
	 
		// AS_BIZ_LOG
		$arr_SQL2[$N2] = 
		"INSERT INTO AS_BIZ_LOG ( RCT_CODE ,HDATE ,HNO ,SNO ,MESSAGE ,REG_DATE ,REG_IDATE ,SMS ,CMID ,DEST_PHONE ,SEND_PHONE 
		)VALUES( '".$row['RCT_CODE']."' ,'".$row['HDATE']."' ,'".$row['HNO']."' ,F_ASBIZLOG_SNO ('".$row['RCT_CODE']."' ,'".$row['HDATE']."' ,'".$row['HNO']."')   ,'{$CONTENT}' ,SYSDATE ,SYSDATE ,'{$SMS_CONTENT}' ,'{$CMID}' ,'".str_replace('-','',$row['TEL2'])."' ,'********'
		)";
		$N2++;
        $rs = $dbconn_m2g->iud_query($arr_SQL);
        $rs2 = $dbconn_sperp_posbank->iud_query($arr_SQL2);
		return $rs;
	}
	#########################################################	 


	####### [ 알림/권한 설정한 사원 가져오기 ] #######
	function alarm_st($code="",$st=[])
	{		
		global $db;

		$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
		if(empty($dbconn_sperp_posbank->success)) {
			echo "[" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
		}else{ 
			$arr_st = [];
			$SQL = "select BAS_OP4 from BAS where BAS_CODE='".$code."' ";
			$OP4 = $dbconn_sperp_posbank->query_one($SQL);
			if($OP4) $arr_st = explode(",", $OP4);
			if($st) $arr_st = array_merge($arr_st, $st); 
			$arr_st = array_unique ($arr_st);
			return $arr_st;
		}
	}