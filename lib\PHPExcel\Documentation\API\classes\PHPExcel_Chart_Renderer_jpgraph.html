<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Chart_Renderer_jpgraph</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_Chart_Renderer_jpgraph"><span class="description">Create a new PHPExcel_Chart_Renderer_jpgraph</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_render" title="render :: "><span class="description">render()
        </span><pre>render()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="method private "><a href="#method__formatDataSetLabels" title="_formatDataSetLabels :: "><span class="description">_formatDataSetLabels()
        </span><pre>_formatDataSetLabels()</pre></a></li>
<li class="method private "><a href="#method__formatPointMarker" title="_formatPointMarker :: "><span class="description">_formatPointMarker()
        </span><pre>_formatPointMarker()</pre></a></li>
<li class="method private "><a href="#method__getCaption" title="_getCaption :: "><span class="description">_getCaption()
        </span><pre>_getCaption()</pre></a></li>
<li class="method private "><a href="#method__percentageAdjustValues" title="_percentageAdjustValues :: "><span class="description">_percentageAdjustValues()
        </span><pre>_percentageAdjustValues()</pre></a></li>
<li class="method private "><a href="#method__percentageSumCalculation" title="_percentageSumCalculation :: "><span class="description">_percentageSumCalculation()
        </span><pre>_percentageSumCalculation()</pre></a></li>
<li class="method private "><a href="#method__renderAreaChart" title="_renderAreaChart :: "><span class="description">_renderAreaChart()
        </span><pre>_renderAreaChart()</pre></a></li>
<li class="method private "><a href="#method__renderBarChart" title="_renderBarChart :: "><span class="description">_renderBarChart()
        </span><pre>_renderBarChart()</pre></a></li>
<li class="method private "><a href="#method__renderBubbleChart" title="_renderBubbleChart :: "><span class="description">_renderBubbleChart()
        </span><pre>_renderBubbleChart()</pre></a></li>
<li class="method private "><a href="#method__renderCartesianPlotArea" title="_renderCartesianPlotArea :: "><span class="description">_renderCartesianPlotArea()
        </span><pre>_renderCartesianPlotArea()</pre></a></li>
<li class="method private "><a href="#method__renderCombinationChart" title="_renderCombinationChart :: "><span class="description">_renderCombinationChart()
        </span><pre>_renderCombinationChart()</pre></a></li>
<li class="method private "><a href="#method__renderContourChart" title="_renderContourChart :: "><span class="description">_renderContourChart()
        </span><pre>_renderContourChart()</pre></a></li>
<li class="method private "><a href="#method__renderLegend" title="_renderLegend :: "><span class="description">_renderLegend()
        </span><pre>_renderLegend()</pre></a></li>
<li class="method private "><a href="#method__renderLineChart" title="_renderLineChart :: "><span class="description">_renderLineChart()
        </span><pre>_renderLineChart()</pre></a></li>
<li class="method private "><a href="#method__renderPieChart" title="_renderPieChart :: "><span class="description">_renderPieChart()
        </span><pre>_renderPieChart()</pre></a></li>
<li class="method private "><a href="#method__renderPiePlotArea" title="_renderPiePlotArea :: "><span class="description">_renderPiePlotArea()
        </span><pre>_renderPiePlotArea()</pre></a></li>
<li class="method private "><a href="#method__renderPlotBar" title="_renderPlotBar :: "><span class="description">_renderPlotBar()
        </span><pre>_renderPlotBar()</pre></a></li>
<li class="method private "><a href="#method__renderPlotContour" title="_renderPlotContour :: "><span class="description">_renderPlotContour()
        </span><pre>_renderPlotContour()</pre></a></li>
<li class="method private "><a href="#method__renderPlotLine" title="_renderPlotLine :: "><span class="description">_renderPlotLine()
        </span><pre>_renderPlotLine()</pre></a></li>
<li class="method private "><a href="#method__renderPlotRadar" title="_renderPlotRadar :: "><span class="description">_renderPlotRadar()
        </span><pre>_renderPlotRadar()</pre></a></li>
<li class="method private "><a href="#method__renderPlotScatter" title="_renderPlotScatter :: "><span class="description">_renderPlotScatter()
        </span><pre>_renderPlotScatter()</pre></a></li>
<li class="method private "><a href="#method__renderPlotStock" title="_renderPlotStock :: "><span class="description">_renderPlotStock()
        </span><pre>_renderPlotStock()</pre></a></li>
<li class="method private "><a href="#method__renderRadarChart" title="_renderRadarChart :: "><span class="description">_renderRadarChart()
        </span><pre>_renderRadarChart()</pre></a></li>
<li class="method private "><a href="#method__renderRadarPlotArea" title="_renderRadarPlotArea :: "><span class="description">_renderRadarPlotArea()
        </span><pre>_renderRadarPlotArea()</pre></a></li>
<li class="method private "><a href="#method__renderScatterChart" title="_renderScatterChart :: "><span class="description">_renderScatterChart()
        </span><pre>_renderScatterChart()</pre></a></li>
<li class="method private "><a href="#method__renderStockChart" title="_renderStockChart :: "><span class="description">_renderStockChart()
        </span><pre>_renderStockChart()</pre></a></li>
<li class="method private "><a href="#method__renderTitle" title="_renderTitle :: "><span class="description">_renderTitle()
        </span><pre>_renderTitle()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__chart" title="$_chart :: "><span class="description"></span><pre>$_chart</pre></a></li>
<li class="property private "><a href="#property__colourSet" title="$_colourSet :: "><span class="description"></span><pre>$_colourSet</pre></a></li>
<li class="property private "><a href="#property__graph" title="$_graph :: "><span class="description"></span><pre>$_graph</pre></a></li>
<li class="property private "><a href="#property__height" title="$_height :: "><span class="description"></span><pre>$_height</pre></a></li>
<li class="property private "><a href="#property__markSet" title="$_markSet :: "><span class="description"></span><pre>$_markSet</pre></a></li>
<li class="property private "><a href="#property__plotColour" title="$_plotColour :: "><span class="description"></span><pre>$_plotColour</pre></a></li>
<li class="property private "><a href="#property__plotMark" title="$_plotMark :: "><span class="description"></span><pre>$_plotMark</pre></a></li>
<li class="property private "><a href="#property__width" title="$_width :: "><span class="description"></span><pre>$_width</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Chart_Renderer_jpgraph"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Chart_Renderer_jpgraph.html">PHPExcel_Chart_Renderer_jpgraph</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Chart_Renderer_jpgraph</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Chart.Renderer.html">PHPExcel_Chart_Renderer</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_Chart_Renderer_jpgraph</h2>
<pre>__construct(\PHPExcel_Chart $chart) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$chart</h4></div>
</div></div>
</div>
<a id="method_render"></a><div class="element clickable method public method_render" data-toggle="collapse" data-target=".method_render .collapse">
<h2>render()
        </h2>
<pre>render($outputDestination) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$outputDestination</h4></div>
</div></div>
</div>
<a id="method__formatDataSetLabels"></a><div class="element clickable method private method__formatDataSetLabels" data-toggle="collapse" data-target=".method__formatDataSetLabels .collapse">
<h2>_formatDataSetLabels()
        </h2>
<pre>_formatDataSetLabels($groupID, $datasetLabels, $labelCount, $rotation) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupID</h4></div>
<div class="subelement argument"><h4>$datasetLabels</h4></div>
<div class="subelement argument"><h4>$labelCount</h4></div>
<div class="subelement argument"><h4>$rotation</h4></div>
</div></div>
</div>
<a id="method__formatPointMarker"></a><div class="element clickable method private method__formatPointMarker" data-toggle="collapse" data-target=".method__formatPointMarker .collapse">
<h2>_formatPointMarker()
        </h2>
<pre>_formatPointMarker($seriesPlot, $markerID) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$seriesPlot</h4></div>
<div class="subelement argument"><h4>$markerID</h4></div>
</div></div>
</div>
<a id="method__getCaption"></a><div class="element clickable method private method__getCaption" data-toggle="collapse" data-target=".method__getCaption .collapse">
<h2>_getCaption()
        </h2>
<pre>_getCaption($captionElement) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$captionElement</h4></div>
</div></div>
</div>
<a id="method__percentageAdjustValues"></a><div class="element clickable method private method__percentageAdjustValues" data-toggle="collapse" data-target=".method__percentageAdjustValues .collapse">
<h2>_percentageAdjustValues()
        </h2>
<pre>_percentageAdjustValues($dataValues, $sumValues) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$dataValues</h4></div>
<div class="subelement argument"><h4>$sumValues</h4></div>
</div></div>
</div>
<a id="method__percentageSumCalculation"></a><div class="element clickable method private method__percentageSumCalculation" data-toggle="collapse" data-target=".method__percentageSumCalculation .collapse">
<h2>_percentageSumCalculation()
        </h2>
<pre>_percentageSumCalculation($groupID, $seriesCount) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupID</h4></div>
<div class="subelement argument"><h4>$seriesCount</h4></div>
</div></div>
</div>
<a id="method__renderAreaChart"></a><div class="element clickable method private method__renderAreaChart" data-toggle="collapse" data-target=".method__renderAreaChart .collapse">
<h2>_renderAreaChart()
        </h2>
<pre>_renderAreaChart($groupCount, $dimensions) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupCount</h4></div>
<div class="subelement argument"><h4>$dimensions</h4></div>
</div></div>
</div>
<a id="method__renderBarChart"></a><div class="element clickable method private method__renderBarChart" data-toggle="collapse" data-target=".method__renderBarChart .collapse">
<h2>_renderBarChart()
        </h2>
<pre>_renderBarChart($groupCount, $dimensions) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupCount</h4></div>
<div class="subelement argument"><h4>$dimensions</h4></div>
</div></div>
</div>
<a id="method__renderBubbleChart"></a><div class="element clickable method private method__renderBubbleChart" data-toggle="collapse" data-target=".method__renderBubbleChart .collapse">
<h2>_renderBubbleChart()
        </h2>
<pre>_renderBubbleChart($groupCount) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupCount</h4></div>
</div></div>
</div>
<a id="method__renderCartesianPlotArea"></a><div class="element clickable method private method__renderCartesianPlotArea" data-toggle="collapse" data-target=".method__renderCartesianPlotArea .collapse">
<h2>_renderCartesianPlotArea()
        </h2>
<pre>_renderCartesianPlotArea($type) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$type</h4></div>
</div></div>
</div>
<a id="method__renderCombinationChart"></a><div class="element clickable method private method__renderCombinationChart" data-toggle="collapse" data-target=".method__renderCombinationChart .collapse">
<h2>_renderCombinationChart()
        </h2>
<pre>_renderCombinationChart($groupCount, $dimensions, $outputDestination) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupCount</h4></div>
<div class="subelement argument"><h4>$dimensions</h4></div>
<div class="subelement argument"><h4>$outputDestination</h4></div>
</div></div>
</div>
<a id="method__renderContourChart"></a><div class="element clickable method private method__renderContourChart" data-toggle="collapse" data-target=".method__renderContourChart .collapse">
<h2>_renderContourChart()
        </h2>
<pre>_renderContourChart($groupCount, $dimensions) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupCount</h4></div>
<div class="subelement argument"><h4>$dimensions</h4></div>
</div></div>
</div>
<a id="method__renderLegend"></a><div class="element clickable method private method__renderLegend" data-toggle="collapse" data-target=".method__renderLegend .collapse">
<h2>_renderLegend()
        </h2>
<pre>_renderLegend() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__renderLineChart"></a><div class="element clickable method private method__renderLineChart" data-toggle="collapse" data-target=".method__renderLineChart .collapse">
<h2>_renderLineChart()
        </h2>
<pre>_renderLineChart($groupCount, $dimensions) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupCount</h4></div>
<div class="subelement argument"><h4>$dimensions</h4></div>
</div></div>
</div>
<a id="method__renderPieChart"></a><div class="element clickable method private method__renderPieChart" data-toggle="collapse" data-target=".method__renderPieChart .collapse">
<h2>_renderPieChart()
        </h2>
<pre>_renderPieChart($groupCount, $dimensions, $doughnut, $multiplePlots) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupCount</h4></div>
<div class="subelement argument"><h4>$dimensions</h4></div>
<div class="subelement argument"><h4>$doughnut</h4></div>
<div class="subelement argument"><h4>$multiplePlots</h4></div>
</div></div>
</div>
<a id="method__renderPiePlotArea"></a><div class="element clickable method private method__renderPiePlotArea" data-toggle="collapse" data-target=".method__renderPiePlotArea .collapse">
<h2>_renderPiePlotArea()
        </h2>
<pre>_renderPiePlotArea($doughnut) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$doughnut</h4></div>
</div></div>
</div>
<a id="method__renderPlotBar"></a><div class="element clickable method private method__renderPlotBar" data-toggle="collapse" data-target=".method__renderPlotBar .collapse">
<h2>_renderPlotBar()
        </h2>
<pre>_renderPlotBar($groupID, $dimensions) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupID</h4></div>
<div class="subelement argument"><h4>$dimensions</h4></div>
</div></div>
</div>
<a id="method__renderPlotContour"></a><div class="element clickable method private method__renderPlotContour" data-toggle="collapse" data-target=".method__renderPlotContour .collapse">
<h2>_renderPlotContour()
        </h2>
<pre>_renderPlotContour($groupID) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupID</h4></div>
</div></div>
</div>
<a id="method__renderPlotLine"></a><div class="element clickable method private method__renderPlotLine" data-toggle="collapse" data-target=".method__renderPlotLine .collapse">
<h2>_renderPlotLine()
        </h2>
<pre>_renderPlotLine($groupID, $filled, $combination, $dimensions) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupID</h4></div>
<div class="subelement argument"><h4>$filled</h4></div>
<div class="subelement argument"><h4>$combination</h4></div>
<div class="subelement argument"><h4>$dimensions</h4></div>
</div></div>
</div>
<a id="method__renderPlotRadar"></a><div class="element clickable method private method__renderPlotRadar" data-toggle="collapse" data-target=".method__renderPlotRadar .collapse">
<h2>_renderPlotRadar()
        </h2>
<pre>_renderPlotRadar($groupID) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupID</h4></div>
</div></div>
</div>
<a id="method__renderPlotScatter"></a><div class="element clickable method private method__renderPlotScatter" data-toggle="collapse" data-target=".method__renderPlotScatter .collapse">
<h2>_renderPlotScatter()
        </h2>
<pre>_renderPlotScatter($groupID, $bubble) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupID</h4></div>
<div class="subelement argument"><h4>$bubble</h4></div>
</div></div>
</div>
<a id="method__renderPlotStock"></a><div class="element clickable method private method__renderPlotStock" data-toggle="collapse" data-target=".method__renderPlotStock .collapse">
<h2>_renderPlotStock()
        </h2>
<pre>_renderPlotStock($groupID) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupID</h4></div>
</div></div>
</div>
<a id="method__renderRadarChart"></a><div class="element clickable method private method__renderRadarChart" data-toggle="collapse" data-target=".method__renderRadarChart .collapse">
<h2>_renderRadarChart()
        </h2>
<pre>_renderRadarChart($groupCount) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupCount</h4></div>
</div></div>
</div>
<a id="method__renderRadarPlotArea"></a><div class="element clickable method private method__renderRadarPlotArea" data-toggle="collapse" data-target=".method__renderRadarPlotArea .collapse">
<h2>_renderRadarPlotArea()
        </h2>
<pre>_renderRadarPlotArea() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__renderScatterChart"></a><div class="element clickable method private method__renderScatterChart" data-toggle="collapse" data-target=".method__renderScatterChart .collapse">
<h2>_renderScatterChart()
        </h2>
<pre>_renderScatterChart($groupCount) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupCount</h4></div>
</div></div>
</div>
<a id="method__renderStockChart"></a><div class="element clickable method private method__renderStockChart" data-toggle="collapse" data-target=".method__renderStockChart .collapse">
<h2>_renderStockChart()
        </h2>
<pre>_renderStockChart($groupCount) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$groupCount</h4></div>
</div></div>
</div>
<a id="method__renderTitle"></a><div class="element clickable method private method__renderTitle" data-toggle="collapse" data-target=".method__renderTitle .collapse">
<h2>_renderTitle()
        </h2>
<pre>_renderTitle() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__chart"> </a><div class="element clickable property private property__chart" data-toggle="collapse" data-target=".property__chart .collapse">
<h2></h2>
<pre>$_chart </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__colourSet"> </a><div class="element clickable property private property__colourSet" data-toggle="collapse" data-target=".property__colourSet .collapse">
<h2></h2>
<pre>$_colourSet </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__graph"> </a><div class="element clickable property private property__graph" data-toggle="collapse" data-target=".property__graph .collapse">
<h2></h2>
<pre>$_graph </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__height"> </a><div class="element clickable property private property__height" data-toggle="collapse" data-target=".property__height .collapse">
<h2></h2>
<pre>$_height </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__markSet"> </a><div class="element clickable property private property__markSet" data-toggle="collapse" data-target=".property__markSet .collapse">
<h2></h2>
<pre>$_markSet </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__plotColour"> </a><div class="element clickable property private property__plotColour" data-toggle="collapse" data-target=".property__plotColour .collapse">
<h2></h2>
<pre>$_plotColour </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__plotMark"> </a><div class="element clickable property private property__plotMark" data-toggle="collapse" data-target=".property__plotMark .collapse">
<h2></h2>
<pre>$_plotMark </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__width"> </a><div class="element clickable property private property__width" data-toggle="collapse" data-target=".property__width .collapse">
<h2></h2>
<pre>$_width </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:34Z.<br></footer></div>
</div>
</body>
</html>
