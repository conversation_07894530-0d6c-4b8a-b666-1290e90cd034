<?php
# SMS 체크값용 체크키 생성
# $PBEncode = new PBEncode(); 
# echo $PBEncode->Encode('12345678');
# echo $PBEncode->Phone('15886335','01000000000/이름;01011111111/이름');
class PBEncode {

	public function Phone($dispatchPhone,$receptionPhone) {
		$encode = "";
		$arr = explode(";", trim($receptionPhone));
		if($arr){
			foreach($arr as $key => $value) {
				$value2 = trim($value);
				$arr2 = explode("/", $value2);
				if($encode) $encode .= ";";
				$encode .= $this->Encode($dispatchPhone.$arr2[0]);
				flush();
			}
		}
		return $encode;
	}

	public function Encode($str) {
		$key1  = array('A','z','G','h','5','E','7','s','T','2');
		$key2  = array('3','j','8','K','4','L','w','t','X','9');
		$str = trim($str);
		$str = preg_replace("/[^0-9]/", "", $str);  // 숫자만 추출
		if(empty($str)) return;
		//$len = strlen($str);
		$len = 28;
		$n1 = 0;
		$n2 = 0;
		$s_sum = 0;
		$str_arr1 = [];
		$str_arr2 = [];
		$val1 = "";
		$val2 = "";
		for($n = 0; $n < $len; $n++){
			$val = substr($str,$n,1);
			if(empty($val)) $val = "0";
			if($n %2 == 0){ // 홀수
				$str_arr1[$n1++] = $val;
			}else{ // 짝수
				$str_arr2[$n2++] = $val;
			}
			$s_sum += (int)$val;
		}
		krsort ($str_arr1); // 역정렬
		reset ($str_arr1);
		if($str_arr1){
			foreach($str_arr1 as $key => $value) {
				$val1 .= $key1[$value];
			}
		}
		krsort ($str_arr2); // 역정렬
		reset ($str_arr2);
		if($str_arr2){
			foreach($str_arr2 as $key => $value) {
				$val2 .= $key2[$value];
			}
		}
		$str2 = $val1 . $val2;
		$len = strlen($str2);
		$n_val1 = "";
		$n_val2 = "";
		$n_val3 = "";
		for($n = 0; $n < $len; $n++){
			$val = substr($str2,$n,1);
			switch($n %3){
				case 0:
					$n_val1 .= $val;
					break;
				case 1:
					$n_val2 .= $val;
					break;
				case 2:
					$n_val3 .= $val;
					break;
			}
		}
		$check_bit = $s_sum % 10;

		$encode = $n_val1 . $n_val2 . $n_val3 .  $check_bit;

		return $encode;
	}
}
?>