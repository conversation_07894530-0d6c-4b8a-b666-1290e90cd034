<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Calculation_DateTime</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_DATE" title="DATE :: DATE"><span class="description">DATE</span><pre>DATE()</pre></a></li>
<li class="method public "><a href="#method_DATEDIF" title="DATEDIF :: DATEDIF"><span class="description">DATEDIF</span><pre>DATEDIF()</pre></a></li>
<li class="method public "><a href="#method_DATENOW" title="DATENOW :: DATENOW"><span class="description">DATENOW</span><pre>DATENOW()</pre></a></li>
<li class="method public "><a href="#method_DATETIMENOW" title="DATETIMENOW :: DATETIMENOW"><span class="description">DATETIMENOW</span><pre>DATETIMENOW()</pre></a></li>
<li class="method public "><a href="#method_DATEVALUE" title="DATEVALUE :: DATEVALUE"><span class="description">DATEVALUE</span><pre>DATEVALUE()</pre></a></li>
<li class="method public "><a href="#method_DAYOFMONTH" title="DAYOFMONTH :: DAYOFMONTH"><span class="description">DAYOFMONTH</span><pre>DAYOFMONTH()</pre></a></li>
<li class="method public "><a href="#method_DAYOFWEEK" title="DAYOFWEEK :: DAYOFWEEK"><span class="description">DAYOFWEEK</span><pre>DAYOFWEEK()</pre></a></li>
<li class="method public "><a href="#method_DAYS360" title="DAYS360 :: DAYS360"><span class="description">DAYS360</span><pre>DAYS360()</pre></a></li>
<li class="method public "><a href="#method_EDATE" title="EDATE :: EDATE"><span class="description">EDATE</span><pre>EDATE()</pre></a></li>
<li class="method public "><a href="#method_EOMONTH" title="EOMONTH :: EOMONTH"><span class="description">EOMONTH</span><pre>EOMONTH()</pre></a></li>
<li class="method public "><a href="#method_HOUROFDAY" title="HOUROFDAY :: HOUROFDAY"><span class="description">HOUROFDAY</span><pre>HOUROFDAY()</pre></a></li>
<li class="method public "><a href="#method_MINUTEOFHOUR" title="MINUTEOFHOUR :: MINUTEOFHOUR"><span class="description">MINUTEOFHOUR</span><pre>MINUTEOFHOUR()</pre></a></li>
<li class="method public "><a href="#method_MONTHOFYEAR" title="MONTHOFYEAR :: MONTHOFYEAR"><span class="description">MONTHOFYEAR</span><pre>MONTHOFYEAR()</pre></a></li>
<li class="method public "><a href="#method_NETWORKDAYS" title="NETWORKDAYS :: NETWORKDAYS"><span class="description">NETWORKDAYS</span><pre>NETWORKDAYS()</pre></a></li>
<li class="method public "><a href="#method_SECONDOFMINUTE" title="SECONDOFMINUTE :: SECONDOFMINUTE"><span class="description">SECONDOFMINUTE</span><pre>SECONDOFMINUTE()</pre></a></li>
<li class="method public "><a href="#method_TIME" title="TIME :: TIME"><span class="description">TIME</span><pre>TIME()</pre></a></li>
<li class="method public "><a href="#method_TIMEVALUE" title="TIMEVALUE :: TIMEVALUE"><span class="description">TIMEVALUE</span><pre>TIMEVALUE()</pre></a></li>
<li class="method public "><a href="#method_WEEKOFYEAR" title="WEEKOFYEAR :: WEEKOFYEAR"><span class="description">WEEKOFYEAR</span><pre>WEEKOFYEAR()</pre></a></li>
<li class="method public "><a href="#method_WORKDAY" title="WORKDAY :: WORKDAY"><span class="description">WORKDAY</span><pre>WORKDAY()</pre></a></li>
<li class="method public "><a href="#method_YEAR" title="YEAR :: YEAR"><span class="description">YEAR</span><pre>YEAR()</pre></a></li>
<li class="method public "><a href="#method_YEARFRAC" title="YEARFRAC :: YEARFRAC"><span class="description">YEARFRAC</span><pre>YEARFRAC()</pre></a></li>
<li class="method public "><a href="#method__getDateValue" title="_getDateValue :: _getDateValue"><span class="description">_getDateValue</span><pre>_getDateValue()</pre></a></li>
<li class="method public "><a href="#method__isLeapYear" title="_isLeapYear :: Identify if a year is a leap year or not"><span class="description">Identify if a year is a leap year or not</span><pre>_isLeapYear()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="method private "><a href="#method__adjustDateByMonths" title="_adjustDateByMonths :: "><span class="description">_adjustDateByMonths()
        </span><pre>_adjustDateByMonths()</pre></a></li>
<li class="method private "><a href="#method__dateDiff360" title="_dateDiff360 :: Return the number of days between two dates based on a 360 day calendar"><span class="description">Return the number of days between two dates based on a 360 day calendar</span><pre>_dateDiff360()</pre></a></li>
<li class="method private "><a href="#method__getTimeValue" title="_getTimeValue :: _getTimeValue"><span class="description">_getTimeValue</span><pre>_getTimeValue()</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Calculation_DateTime"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Calculation_DateTime.html">PHPExcel_Calculation_DateTime</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Calculation_DateTime</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Calculation.html">PHPExcel_Calculation</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_DATE"></a><div class="element clickable method public method_DATE" data-toggle="collapse" data-target=".method_DATE .collapse">
<h2>DATE</h2>
<pre>DATE(integer $year, integer $month, integer $day) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>The DATE function returns a value that represents a particular date.</p>

<p>NOTE: When used in a Cell Formula, MS Excel changes the cell format so that it matches the date
format of your regional settings. PHPExcel does not change cell formatting in this way.</p>

<p>Excel Function:
    DATE(year,month,day)</p>

<p>PHPExcel is a lot more forgiving than MS Excel when passing non numeric values to this function.
A Month name or abbreviation (English only at this point) such as 'January' or 'Jan' will still be accepted,
    as will a day value with a suffix (e.g. '21st' rather than simply 21); again only English language.</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Date/Time Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$year</h4>
<code>integer</code><p>The value of the year argument can include one to four digits.
                            Excel interprets the year argument according to the configured
                            date system: 1900 or 1904.
                            If year is between 0 (zero) and 1899 (inclusive), Excel adds that
                            value to 1900 to calculate the year. For example, DATE(108,1,2)
                            returns January 2, 2008 (1900+108).
                            If year is between 1900 and 9999 (inclusive), Excel uses that
                            value as the year. For example, DATE(2008,1,2) returns January 2,
                            2008.
                            If year is less than 0 or is 10000 or greater, Excel returns the
                            #NUM! error value.</p>
</div>
<div class="subelement argument">
<h4>$month</h4>
<code>integer</code><p>A positive or negative integer representing the month of the year
                            from 1 to 12 (January to December).
                            If month is greater than 12, month adds that number of months to
                            the first month in the year specified. For example, DATE(2008,14,2)
                            returns the serial number representing February 2, 2009.
                            If month is less than 1, month subtracts the magnitude of that
                            number of months, plus 1, from the first month in the year
                            specified. For example, DATE(2008,-3,2) returns the serial number
                            representing September 2, 2007.</p>
</div>
<div class="subelement argument">
<h4>$day</h4>
<code>integer</code><p>A positive or negative integer representing the day of the month
                            from 1 to 31.
                            If day is greater than the number of days in the month specified,
                            day adds that number of days to the first day in the month. For
                            example, DATE(2008,1,35) returns the serial number representing
                            February 4, 2008.
                            If day is less than 1, day subtracts the magnitude that number of
                            days, plus one, from the first day of the month specified. For
                            example, DATE(2008,1,-15) returns the serial number representing
                            December 16, 2007.</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Excel date/time serial value, PHP date/time serial value or PHP date/time object,
					depending on the value of the ReturnDateType flag</div>
</div></div>
</div>
<a id="method_DATEDIF"></a><div class="element clickable method public method_DATEDIF" data-toggle="collapse" data-target=".method_DATEDIF .collapse">
<h2>DATEDIF</h2>
<pre>DATEDIF(mixed $startDate, mixed $endDate, string $unit) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$startDate</h4>
<code>mixed</code><p>Excel date serial value, PHP date/time stamp, PHP DateTime object
                                or a standard date string</p>
</div>
<div class="subelement argument">
<h4>$endDate</h4>
<code>mixed</code><p>Excel date serial value, PHP date/time stamp, PHP DateTime object
                                or a standard date string</p>
</div>
<div class="subelement argument">
<h4>$unit</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>Interval between the dates</div>
</div></div>
</div>
<a id="method_DATENOW"></a><div class="element clickable method public method_DATENOW" data-toggle="collapse" data-target=".method_DATENOW .collapse">
<h2>DATENOW</h2>
<pre>DATENOW() : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the current date.
The NOW function is useful when you need to display the current date and time on a worksheet or
calculate a value based on the current date and time, and have that value updated each time you
open the worksheet.</p>

<p>NOTE: When used in a Cell Formula, MS Excel changes the cell format so that it matches the date
and time format of your regional settings. PHPExcel does not change cell formatting in this way.</p>

<p>Excel Function:
    TODAY()</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Date/Time Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Excel date/time serial value, PHP date/time serial value or PHP date/time object,
					depending on the value of the ReturnDateType flag</div>
</div></div>
</div>
<a id="method_DATETIMENOW"></a><div class="element clickable method public method_DATETIMENOW" data-toggle="collapse" data-target=".method_DATETIMENOW .collapse">
<h2>DATETIMENOW</h2>
<pre>DATETIMENOW() : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the current date and time.
The NOW function is useful when you need to display the current date and time on a worksheet or
calculate a value based on the current date and time, and have that value updated each time you
open the worksheet.</p>

<p>NOTE: When used in a Cell Formula, MS Excel changes the cell format so that it matches the date
and time format of your regional settings. PHPExcel does not change cell formatting in this way.</p>

<p>Excel Function:
    NOW()</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Date/Time Functions</td>
</tr>
</table>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Excel date/time serial value, PHP date/time serial value or PHP date/time object,
					depending on the value of the ReturnDateType flag</div>
</div></div>
</div>
<a id="method_DATEVALUE"></a><div class="element clickable method public method_DATEVALUE" data-toggle="collapse" data-target=".method_DATEVALUE .collapse">
<h2>DATEVALUE</h2>
<pre>DATEVALUE(string $dateValue) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns a value that represents a particular date.
Use DATEVALUE to convert a date represented by a text string to an Excel or PHP date/time stamp
value.</p>

<p>NOTE: When used in a Cell Formula, MS Excel changes the cell format so that it matches the date
format of your regional settings. PHPExcel does not change cell formatting in this way.</p>

<p>Excel Function:
    DATEVALUE(dateValue)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Date/Time Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>string</code><p>Text that represents a date in a Microsoft Excel date format.
                                For example, "1/30/2008" or "30-Jan-2008" are text strings within
                                quotation marks that represent dates. Using the default date
                                system in Excel for Windows, date_text must represent a date from
                                January 1, 1900, to December 31, 9999. Using the default date
                                system in Excel for the Macintosh, date_text must represent a date
                                from January 1, 1904, to December 31, 9999. DATEVALUE returns the
                                #VALUE! error value if date_text is out of this range.</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Excel date/time serial value, PHP date/time serial value or PHP date/time object,
					depending on the value of the ReturnDateType flag</div>
</div></div>
</div>
<a id="method_DAYOFMONTH"></a><div class="element clickable method public method_DAYOFMONTH" data-toggle="collapse" data-target=".method_DAYOFMONTH .collapse">
<h2>DAYOFMONTH</h2>
<pre>DAYOFMONTH(mixed $dateValue) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the day of the month, for a specified date. The day is given as an integer
ranging from 1 to 31.</p>

<p>Excel Function:
    DAY(dateValue)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                PHP DateTime object, or a standard date string</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Day of the month</div>
</div></div>
</div>
<a id="method_DAYOFWEEK"></a><div class="element clickable method public method_DAYOFWEEK" data-toggle="collapse" data-target=".method_DAYOFWEEK .collapse">
<h2>DAYOFWEEK</h2>
<pre>DAYOFWEEK(mixed $dateValue, int $style) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the day of the week for a specified date. The day is given as an integer
ranging from 0 to 7 (dependent on the requested style).</p>

<p>Excel Function:
    WEEKDAY(dateValue[,style])</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                PHP DateTime object, or a standard date string</p>
</div>
<div class="subelement argument">
<h4>$style</h4>
<code>int</code><p>A number that determines the type of return value
                                    1 or omitted    Numbers 1 (Sunday) through 7 (Saturday).
                                    2               Numbers 1 (Monday) through 7 (Sunday).
                                    3               Numbers 0 (Monday) through 6 (Sunday).</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Day of the week value</div>
</div></div>
</div>
<a id="method_DAYS360"></a><div class="element clickable method public method_DAYS360" data-toggle="collapse" data-target=".method_DAYS360 .collapse">
<h2>DAYS360</h2>
<pre>DAYS360(mixed $startDate, mixed $endDate, boolean $method) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the number of days between two dates based on a 360-day year (twelve 30-day months),
which is used in some accounting calculations. Use this function to help compute payments if
your accounting system is based on twelve 30-day months.</p>

<p>Excel Function:
    DAYS360(startDate,endDate[,method])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Date/Time Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$startDate</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                    PHP DateTime object, or a standard date string</p>
</div>
<div class="subelement argument">
<h4>$endDate</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                    PHP DateTime object, or a standard date string</p>
</div>
<div class="subelement argument">
<h4>$method</h4>
<code>boolean</code><p>US or European Method
                                    FALSE or omitted: U.S. (NASD) method. If the starting date is
                                    the last day of a month, it becomes equal to the 30th of the
                                    same month. If the ending date is the last day of a month and
                                    the starting date is earlier than the 30th of a month, the
                                    ending date becomes equal to the 1st of the next month;
                                    otherwise the ending date becomes equal to the 30th of the
                                    same month.
                                    TRUE: European method. Starting dates and ending dates that
                                    occur on the 31st of a month become equal to the 30th of the
                                    same month.</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>Number of days between start date and end date</div>
</div></div>
</div>
<a id="method_EDATE"></a><div class="element clickable method public method_EDATE" data-toggle="collapse" data-target=".method_EDATE .collapse">
<h2>EDATE</h2>
<pre>EDATE(mixed $dateValue, int $adjustmentMonths) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the serial number that represents the date that is the indicated number of months
before or after a specified date (the start_date).
Use EDATE to calculate maturity dates or due dates that fall on the same day of the month
as the date of issue.</p>

<p>Excel Function:
    EDATE(dateValue,adjustmentMonths)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                    PHP DateTime object, or a standard date string</p>
</div>
<div class="subelement argument">
<h4>$adjustmentMonths</h4>
<code>int</code><p>The number of months before or after start_date.
									A positive value for months yields a future date;
									a negative value yields a past date.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Excel date/time serial value, PHP date/time serial value or PHP date/time object,
					depending on the value of the ReturnDateType flag</div>
</div></div>
</div>
<a id="method_EOMONTH"></a><div class="element clickable method public method_EOMONTH" data-toggle="collapse" data-target=".method_EOMONTH .collapse">
<h2>EOMONTH</h2>
<pre>EOMONTH(mixed $dateValue, int $adjustmentMonths) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the date value for the last day of the month that is the indicated number of months
before or after start_date.
Use EOMONTH to calculate maturity dates or due dates that fall on the last day of the month.</p>

<p>Excel Function:
    EOMONTH(dateValue,adjustmentMonths)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                    PHP DateTime object, or a standard date string</p>
</div>
<div class="subelement argument">
<h4>$adjustmentMonths</h4>
<code>int</code><p>The number of months before or after start_date.
									A positive value for months yields a future date;
									a negative value yields a past date.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Excel date/time serial value, PHP date/time serial value or PHP date/time object,
					depending on the value of the ReturnDateType flag</div>
</div></div>
</div>
<a id="method_HOUROFDAY"></a><div class="element clickable method public method_HOUROFDAY" data-toggle="collapse" data-target=".method_HOUROFDAY .collapse">
<h2>HOUROFDAY</h2>
<pre>HOUROFDAY(mixed $timeValue) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the hour of a time value.
The hour is given as an integer, ranging from 0 (12:00 A.M.) to 23 (11:00 P.M.).</p>

<p>Excel Function:
    HOUR(timeValue)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$timeValue</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                PHP DateTime object, or a standard time string</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Hour</div>
</div></div>
</div>
<a id="method_MINUTEOFHOUR"></a><div class="element clickable method public method_MINUTEOFHOUR" data-toggle="collapse" data-target=".method_MINUTEOFHOUR .collapse">
<h2>MINUTEOFHOUR</h2>
<pre>MINUTEOFHOUR(mixed $timeValue) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the minutes of a time value.
The minute is given as an integer, ranging from 0 to 59.</p>

<p>Excel Function:
    MINUTE(timeValue)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$timeValue</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                PHP DateTime object, or a standard time string</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Minute</div>
</div></div>
</div>
<a id="method_MONTHOFYEAR"></a><div class="element clickable method public method_MONTHOFYEAR" data-toggle="collapse" data-target=".method_MONTHOFYEAR .collapse">
<h2>MONTHOFYEAR</h2>
<pre>MONTHOFYEAR(mixed $dateValue) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the month of a date represented by a serial number.
The month is given as an integer, ranging from 1 (January) to 12 (December).</p>

<p>Excel Function:
    MONTH(dateValue)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                PHP DateTime object, or a standard date string</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Month of the year</div>
</div></div>
</div>
<a id="method_NETWORKDAYS"></a><div class="element clickable method public method_NETWORKDAYS" data-toggle="collapse" data-target=".method_NETWORKDAYS .collapse">
<h2>NETWORKDAYS</h2>
<pre>NETWORKDAYS(mixed $startDate, mixed $endDate) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the number of whole working days between start_date and end_date. Working days
exclude weekends and any dates identified in holidays.
Use NETWORKDAYS to calculate employee benefits that accrue based on the number of days
worked during a specific term.</p>

<p>Excel Function:
    NETWORKDAYS(startDate,endDate[,holidays[,holiday[,...]]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Date/Time Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$startDate</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                        PHP DateTime object, or a standard date string</p>
</div>
<div class="subelement argument">
<h4>$endDate</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                        PHP DateTime object, or a standard date string</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>Interval between the dates</div>
</div></div>
</div>
<a id="method_SECONDOFMINUTE"></a><div class="element clickable method public method_SECONDOFMINUTE" data-toggle="collapse" data-target=".method_SECONDOFMINUTE .collapse">
<h2>SECONDOFMINUTE</h2>
<pre>SECONDOFMINUTE(mixed $timeValue) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the seconds of a time value.
The second is given as an integer in the range 0 (zero) to 59.</p>

<p>Excel Function:
    SECOND(timeValue)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$timeValue</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                PHP DateTime object, or a standard time string</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Second</div>
</div></div>
</div>
<a id="method_TIME"></a><div class="element clickable method public method_TIME" data-toggle="collapse" data-target=".method_TIME .collapse">
<h2>TIME</h2>
<pre>TIME(integer $hour, integer $minute, integer $second) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>The TIME function returns a value that represents a particular time.</p>

<p>NOTE: When used in a Cell Formula, MS Excel changes the cell format so that it matches the time
format of your regional settings. PHPExcel does not change cell formatting in this way.</p>

<p>Excel Function:
    TIME(hour,minute,second)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Date/Time Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$hour</h4>
<code>integer</code><p>A number from 0 (zero) to 32767 representing the hour.
                                Any value greater than 23 will be divided by 24 and the remainder
                                will be treated as the hour value. For example, TIME(27,0,0) =
                                TIME(3,0,0) = .125 or 3:00 AM.</p>
</div>
<div class="subelement argument">
<h4>$minute</h4>
<code>integer</code><p>A number from 0 to 32767 representing the minute.
                                Any value greater than 59 will be converted to hours and minutes.
                                For example, TIME(0,750,0) = TIME(12,30,0) = .520833 or 12:30 PM.</p>
</div>
<div class="subelement argument">
<h4>$second</h4>
<code>integer</code><p>A number from 0 to 32767 representing the second.
                                Any value greater than 59 will be converted to hours, minutes,
                                and seconds. For example, TIME(0,0,2000) = TIME(0,33,22) = .023148
                                or 12:33:20 AM</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Excel date/time serial value, PHP date/time serial value or PHP date/time object,
					depending on the value of the ReturnDateType flag</div>
</div></div>
</div>
<a id="method_TIMEVALUE"></a><div class="element clickable method public method_TIMEVALUE" data-toggle="collapse" data-target=".method_TIMEVALUE .collapse">
<h2>TIMEVALUE</h2>
<pre>TIMEVALUE(string $timeValue) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns a value that represents a particular time.
Use TIMEVALUE to convert a time represented by a text string to an Excel or PHP date/time stamp
value.</p>

<p>NOTE: When used in a Cell Formula, MS Excel changes the cell format so that it matches the time
format of your regional settings. PHPExcel does not change cell formatting in this way.</p>

<p>Excel Function:
    TIMEVALUE(timeValue)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Date/Time Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$timeValue</h4>
<code>string</code><p>A text string that represents a time in any one of the Microsoft
                                Excel time formats; for example, "6:45 PM" and "18:45" text strings
                                within quotation marks that represent time.
                                Date information in time_text is ignored.</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Excel date/time serial value, PHP date/time serial value or PHP date/time object,
					depending on the value of the ReturnDateType flag</div>
</div></div>
</div>
<a id="method_WEEKOFYEAR"></a><div class="element clickable method public method_WEEKOFYEAR" data-toggle="collapse" data-target=".method_WEEKOFYEAR .collapse">
<h2>WEEKOFYEAR</h2>
<pre>WEEKOFYEAR(mixed $dateValue, boolean $method) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the week of the year for a specified date.
The WEEKNUM function considers the week containing January 1 to be the first week of the year.
However, there is a European standard that defines the first week as the one with the majority
of days (four or more) falling in the new year. This means that for years in which there are
three days or less in the first week of January, the WEEKNUM function returns week numbers
that are incorrect according to the European standard.</p>

<p>Excel Function:
    WEEKNUM(dateValue[,style])</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                PHP DateTime object, or a standard date string</p>
</div>
<div class="subelement argument">
<h4>$method</h4>
<code>boolean</code><p>Week begins on Sunday or Monday
									1 or omitted	Week begins on Sunday.
									2				Week begins on Monday.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Week Number</div>
</div></div>
</div>
<a id="method_WORKDAY"></a><div class="element clickable method public method_WORKDAY" data-toggle="collapse" data-target=".method_WORKDAY .collapse">
<h2>WORKDAY</h2>
<pre>WORKDAY(mixed $startDate, integer $endDays) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the date that is the indicated number of working days before or after a date (the
starting date). Working days exclude weekends and any dates identified as holidays.
Use WORKDAY to exclude weekends or holidays when you calculate invoice due dates, expected
delivery times, or the number of days of work performed.</p>

<p>Excel Function:
    WORKDAY(startDate,endDays[,holidays[,holiday[,...]]])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Date/Time Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$startDate</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                    PHP DateTime object, or a standard date string</p>
</div>
<div class="subelement argument">
<h4>$endDays</h4>
<code>integer</code><p>The number of nonweekend and nonholiday days before or after
									startDate. A positive value for days yields a future date; a
									negative value yields a past date.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Excel date/time serial value, PHP date/time serial value or PHP date/time object,
					depending on the value of the ReturnDateType flag</div>
</div></div>
</div>
<a id="method_YEAR"></a><div class="element clickable method public method_YEAR" data-toggle="collapse" data-target=".method_YEAR .collapse">
<h2>YEAR</h2>
<pre>YEAR(mixed $dateValue) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the year corresponding to a date.
The year is returned as an integer in the range 1900-9999.</p>

<p>Excel Function:
    YEAR(dateValue)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                PHP DateTime object, or a standard date string</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Year</div>
</div></div>
</div>
<a id="method_YEARFRAC"></a><div class="element clickable method public method_YEARFRAC" data-toggle="collapse" data-target=".method_YEARFRAC .collapse">
<h2>YEARFRAC</h2>
<pre>YEARFRAC(mixed $startDate, mixed $endDate, integer $method) : float</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Calculates the fraction of the year represented by the number of whole days between two dates
(the start_date and the end_date).
Use the YEARFRAC worksheet function to identify the proportion of a whole year's benefits or
obligations to assign to a specific term.</p>

<p>Excel Function:
    YEARFRAC(startDate,endDate[,method])</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Date/Time Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$startDate</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                PHP DateTime object, or a standard date string</p>
</div>
<div class="subelement argument">
<h4>$endDate</h4>
<code>mixed</code><p>Excel date serial value (float), PHP date timestamp (integer),
                                PHP DateTime object, or a standard date string</p>
</div>
<div class="subelement argument">
<h4>$method</h4>
<code>integer</code><p>Method used for the calculation
                                    0 or omitted    US (NASD) 30/360
                                    1               Actual/actual
                                    2               Actual/360
                                    3               Actual/365
                                    4               European 30/360</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code>fraction of the year</div>
</div></div>
</div>
<a id="method__getDateValue"></a><div class="element clickable method public method__getDateValue" data-toggle="collapse" data-target=".method__getDateValue .collapse">
<h2>_getDateValue</h2>
<pre>_getDateValue(string $dateValue) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Excel date/time serial value, or string if error</div>
</div></div>
</div>
<a id="method__isLeapYear"></a><div class="element clickable method public method__isLeapYear" data-toggle="collapse" data-target=".method__isLeapYear .collapse">
<h2>Identify if a year is a leap year or not</h2>
<pre>_isLeapYear(integer $year) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$year</h4>
<code>integer</code><p>The year to test</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>boolean</code>TRUE if the year is a leap year, otherwise FALSE</div>
</div></div>
</div>
<a id="method__adjustDateByMonths"></a><div class="element clickable method private method__adjustDateByMonths" data-toggle="collapse" data-target=".method__adjustDateByMonths .collapse">
<h2>_adjustDateByMonths()
        </h2>
<pre>_adjustDateByMonths($dateValue, $adjustmentMonths) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$dateValue</h4></div>
<div class="subelement argument"><h4>$adjustmentMonths</h4></div>
</div></div>
</div>
<a id="method__dateDiff360"></a><div class="element clickable method private method__dateDiff360" data-toggle="collapse" data-target=".method__dateDiff360 .collapse">
<h2>Return the number of days between two dates based on a 360 day calendar</h2>
<pre>_dateDiff360(integer $startDay, integer $startMonth, integer $startYear, integer $endDay, integer $endMonth, integer $endYear, boolean $methodUS) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$startDay</h4>
<code>integer</code><p>Day of month of the start date</p></div>
<div class="subelement argument">
<h4>$startMonth</h4>
<code>integer</code><p>Month of the start date</p></div>
<div class="subelement argument">
<h4>$startYear</h4>
<code>integer</code><p>Year of the start date</p></div>
<div class="subelement argument">
<h4>$endDay</h4>
<code>integer</code><p>Day of month of the start date</p></div>
<div class="subelement argument">
<h4>$endMonth</h4>
<code>integer</code><p>Month of the start date</p></div>
<div class="subelement argument">
<h4>$endYear</h4>
<code>integer</code><p>Year of the start date</p></div>
<div class="subelement argument">
<h4>$methodUS</h4>
<code>boolean</code><p>Whether to use the US method or the European method of calculation</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>Number of days between the start date and the end date</div>
</div></div>
</div>
<a id="method__getTimeValue"></a><div class="element clickable method private method__getTimeValue" data-toggle="collapse" data-target=".method__getTimeValue .collapse">
<h2>_getTimeValue</h2>
<pre>_getTimeValue(string $timeValue) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$timeValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Excel date/time serial value, or string if error</div>
</div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:33Z.<br></footer></div>
</div>
</body>
</html>
