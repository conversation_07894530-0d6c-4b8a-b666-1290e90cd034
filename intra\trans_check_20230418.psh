#!/usr/local/bin/php -q
<?
# 피드백 업무연락 중 확인 안한 건 알림
# 매일 9시 실행
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

$dbconn = new DBController($db['posbank_intra']);
if(empty($dbconn->success)) {
	echo "[" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패입니다.";
}
/**********************************************************/

echo date("Y-m-d H:i:s")." - 업무연락 알림\n";

$SQL = "SELECT count(*) FROM HOLIDAY_DATA WHERE HDATE=CURDATE()";
$holiday_chk = $dbconn->query_one($SQL);
if(!$holiday_chk){
	// 토,일요일
	if(in_array(date('w'),array("0","6"))) $holiday_chk = '1';
}
if($holiday_chk){
	echo "휴무일\n";
}else{
	$Checkday = date("Y-m-d-00-00", strtotime ("-7 day"));

	// 금일 휴가자
	$SQL = "SELECT
					group_concat(A.PRS_NUM)
				FROM APPV_HOLI A
					LEFT JOIN PRS_MASTER B ON A.PRS_NUM=B.PRS_NUM
				WHERE
					A.GU2='-'
					AND CURDATE() BETWEEN A.HOLI_SDATE AND A.HOLI_EDATE
					AND A.VIEW_YN<>'N'";
	$prs_num = $dbconn->query_one($SQL);


	$SQL = "SELECT 
					A.REG_NO,A.PRS_NM,A.TITLE,A.CONTENT
					,left(A.IN_DT,10) IN_DT2
					,group_concat(B.PRS_NUM) PRS_NUM2
				FROM trans A
					LEFT JOIN TRANS_TARGET B ON A.REG_NO=B.REG_NO
					LEFT JOIN TRANS_OPEN C ON A.REG_NO=C.REG_NO AND B.PRS_NUM=C.PRS_NUM
				WHERE A.IN_DT >= '".$Checkday."' AND A.FEEDBACK='Y' AND C.SNUM IS NULL ";
	if($prs_num){
		$arr_prs_num = explode(",", $prs_num);
		$SQL .= " and C.PRS_NUM not in ('".implode("','",$arr_prs_num)."') ";
	}
	//$SQL .= " and B.PRS_NUM in ('20180304','1111111111') ";
	$SQL .= " GROUP BY A.REG_NO ";

	$arrRow = $dbconn->query_rows($SQL);

	if($arrRow){
		foreach($arrRow as $key => $row) {
			$content = html_tags($row['CONTENT']);
			$content = mb_strcut($content, 0, 500, 'UTF-8');
			$tmpCode = explode(",",$row['PRS_NUM2']);


			$kakaowork_Params = [];
			$kakaowork_Params['bot_gu'] = "trans"; // 봇구분(trans:업무연락,appv:결재문서,qms:QMS,system:시스템) 값 없을시 기본 봇
			$kakaowork_Params['to'] = $tmpCode;
			$kakaowork_Params['text'] = "미확인 업무연락이 있습니다.";
			$kakaowork_Params['header'] = "업무연락";
			$kakaowork_Params['header_color'] = "blue";
			$kakaowork_Params['msg'][0] = ["제목",$row['TITLE']];
			$kakaowork_Params['msg'][1] = ["보낸사람",$row['PRS_NM']];
			$kakaowork_Params['msg'][2] = ["문서번호",$row['REG_NO']];
			$kakaowork_Params['msg'][2] = ["보낸일자",$row['IN_DT2']];
			$kakaowork_Params['msg'][4] = ["간략내용",$content];
			$kakaowork_Params['button'] = ["확인하러가기","https://i.posbank.com/login.html?pageType=trans&pageCode=".$row['REG_NO']];

			// 카카오워크 보내기
			$rs = kakaowork_send($kakaowork_Params);
			echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
		}
	}
}



## 스케즐 처리 상황 intra DB에 저장
crontab_execution(86400, "인트라넷 업무연락 미확인 알림");

echo date("Y-m-d H:i:s")." - 끝\n";
?>
