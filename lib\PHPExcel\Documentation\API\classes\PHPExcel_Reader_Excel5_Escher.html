<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Reader_Excel5_Escher</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_Reader_Excel5_Escher instance"><span class="description">Create a new PHPExcel_Reader_Excel5_Escher instance</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_load" title="load :: Load Escher stream data."><span class="description">Load Escher stream data.</span><pre>load()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="method private "><a href="#method__readBSE" title="_readBSE :: Read BSE record"><span class="description">Read BSE record</span><pre>_readBSE()</pre></a></li>
<li class="method private "><a href="#method__readBlipJPEG" title="_readBlipJPEG :: Read BlipJPEG record."><span class="description">Read BlipJPEG record.</span><pre>_readBlipJPEG()</pre></a></li>
<li class="method private "><a href="#method__readBlipPNG" title="_readBlipPNG :: Read BlipPNG record."><span class="description">Read BlipPNG record.</span><pre>_readBlipPNG()</pre></a></li>
<li class="method private "><a href="#method__readBstoreContainer" title="_readBstoreContainer :: Read BstoreContainer record (Blip Store Container)"><span class="description">Read BstoreContainer record (Blip Store Container)</span><pre>_readBstoreContainer()</pre></a></li>
<li class="method private "><a href="#method__readClientAnchor" title="_readClientAnchor :: Read ClientAnchor record."><span class="description">Read ClientAnchor record.</span><pre>_readClientAnchor()</pre></a></li>
<li class="method private "><a href="#method__readClientData" title="_readClientData :: Read ClientData record"><span class="description">Read ClientData record</span><pre>_readClientData()</pre></a></li>
<li class="method private "><a href="#method__readClientTextbox" title="_readClientTextbox :: Read ClientTextbox record"><span class="description">Read ClientTextbox record</span><pre>_readClientTextbox()</pre></a></li>
<li class="method private "><a href="#method__readDefault" title="_readDefault :: Read a generic record"><span class="description">Read a generic record</span><pre>_readDefault()</pre></a></li>
<li class="method private "><a href="#method__readDg" title="_readDg :: Read Dg record (Drawing)"><span class="description">Read Dg record (Drawing)</span><pre>_readDg()</pre></a></li>
<li class="method private "><a href="#method__readDgContainer" title="_readDgContainer :: Read DgContainer record (Drawing Container)"><span class="description">Read DgContainer record (Drawing Container)</span><pre>_readDgContainer()</pre></a></li>
<li class="method private "><a href="#method__readDgg" title="_readDgg :: Read Dgg record (Drawing Group)"><span class="description">Read Dgg record (Drawing Group)</span><pre>_readDgg()</pre></a></li>
<li class="method private "><a href="#method__readDggContainer" title="_readDggContainer :: Read DggContainer record (Drawing Group Container)"><span class="description">Read DggContainer record (Drawing Group Container)</span><pre>_readDggContainer()</pre></a></li>
<li class="method private "><a href="#method__readOPT" title="_readOPT :: Read OPT record."><span class="description">Read OPT record.</span><pre>_readOPT()</pre></a></li>
<li class="method private "><a href="#method__readOfficeArtRGFOPTE" title="_readOfficeArtRGFOPTE :: Read OfficeArtRGFOPTE table of property-value pairs"><span class="description">Read OfficeArtRGFOPTE table of property-value pairs</span><pre>_readOfficeArtRGFOPTE()</pre></a></li>
<li class="method private "><a href="#method__readSp" title="_readSp :: Read Sp record (Shape)"><span class="description">Read Sp record (Shape)</span><pre>_readSp()</pre></a></li>
<li class="method private "><a href="#method__readSpContainer" title="_readSpContainer :: Read SpContainer record (Shape Container)"><span class="description">Read SpContainer record (Shape Container)</span><pre>_readSpContainer()</pre></a></li>
<li class="method private "><a href="#method__readSpgr" title="_readSpgr :: Read Spgr record (Shape Group)"><span class="description">Read Spgr record (Shape Group)</span><pre>_readSpgr()</pre></a></li>
<li class="method private "><a href="#method__readSpgrContainer" title="_readSpgrContainer :: Read SpgrContainer record (Shape Group Container)"><span class="description">Read SpgrContainer record (Shape Group Container)</span><pre>_readSpgrContainer()</pre></a></li>
<li class="method private "><a href="#method__readSplitMenuColors" title="_readSplitMenuColors :: Read SplitMenuColors record"><span class="description">Read SplitMenuColors record</span><pre>_readSplitMenuColors()</pre></a></li>
<li class="method private "><a href="#method__readTertiaryOPT" title="_readTertiaryOPT :: Read TertiaryOPT record"><span class="description">Read TertiaryOPT record</span><pre>_readTertiaryOPT()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__data" title="$_data :: Escher stream data (binary)"><span class="description"></span><pre>$_data</pre></a></li>
<li class="property private "><a href="#property__dataSize" title="$_dataSize :: Size in bytes of the Escher stream data"><span class="description"></span><pre>$_dataSize</pre></a></li>
<li class="property private "><a href="#property__object" title="$_object :: The object to be returned by the reader."><span class="description"></span><pre>$_object</pre></a></li>
<li class="property private "><a href="#property__pos" title="$_pos :: Current position of stream pointer in Escher stream data"><span class="description"></span><pre>$_pos</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_BLIPJPEG" title="BLIPJPEG :: "><span class="description">BLIPJPEG</span><pre>BLIPJPEG</pre></a></li>
<li class="constant  "><a href="#constant_BLIPPNG" title="BLIPPNG :: "><span class="description">BLIPPNG</span><pre>BLIPPNG</pre></a></li>
<li class="constant  "><a href="#constant_BSE" title="BSE :: "><span class="description">BSE</span><pre>BSE</pre></a></li>
<li class="constant  "><a href="#constant_BSTORECONTAINER" title="BSTORECONTAINER :: "><span class="description">BSTORECONTAINER</span><pre>BSTORECONTAINER</pre></a></li>
<li class="constant  "><a href="#constant_CLIENTANCHOR" title="CLIENTANCHOR :: "><span class="description">CLIENTANCHOR</span><pre>CLIENTANCHOR</pre></a></li>
<li class="constant  "><a href="#constant_CLIENTDATA" title="CLIENTDATA :: "><span class="description">CLIENTDATA</span><pre>CLIENTDATA</pre></a></li>
<li class="constant  "><a href="#constant_CLIENTTEXTBOX" title="CLIENTTEXTBOX :: "><span class="description">CLIENTTEXTBOX</span><pre>CLIENTTEXTBOX</pre></a></li>
<li class="constant  "><a href="#constant_DG" title="DG :: "><span class="description">DG</span><pre>DG</pre></a></li>
<li class="constant  "><a href="#constant_DGCONTAINER" title="DGCONTAINER :: "><span class="description">DGCONTAINER</span><pre>DGCONTAINER</pre></a></li>
<li class="constant  "><a href="#constant_DGG" title="DGG :: "><span class="description">DGG</span><pre>DGG</pre></a></li>
<li class="constant  "><a href="#constant_DGGCONTAINER" title="DGGCONTAINER :: "><span class="description">DGGCONTAINER</span><pre>DGGCONTAINER</pre></a></li>
<li class="constant  "><a href="#constant_OPT" title="OPT :: "><span class="description">OPT</span><pre>OPT</pre></a></li>
<li class="constant  "><a href="#constant_SP" title="SP :: "><span class="description">SP</span><pre>SP</pre></a></li>
<li class="constant  "><a href="#constant_SPCONTAINER" title="SPCONTAINER :: "><span class="description">SPCONTAINER</span><pre>SPCONTAINER</pre></a></li>
<li class="constant  "><a href="#constant_SPGR" title="SPGR :: "><span class="description">SPGR</span><pre>SPGR</pre></a></li>
<li class="constant  "><a href="#constant_SPGRCONTAINER" title="SPGRCONTAINER :: "><span class="description">SPGRCONTAINER</span><pre>SPGRCONTAINER</pre></a></li>
<li class="constant  "><a href="#constant_SPLITMENUCOLORS" title="SPLITMENUCOLORS :: "><span class="description">SPLITMENUCOLORS</span><pre>SPLITMENUCOLORS</pre></a></li>
<li class="constant  "><a href="#constant_TERTIARYOPT" title="TERTIARYOPT :: "><span class="description">TERTIARYOPT</span><pre>TERTIARYOPT</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Reader_Excel5_Escher"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Reader_Excel5_Escher.html">PHPExcel_Reader_Excel5_Escher</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Reader_Excel5_Escher</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Reader.Excel5.html">PHPExcel_Reader_Excel5</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_Reader_Excel5_Escher instance</h2>
<pre>__construct(mixed $object) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$object</h4>
<code>mixed</code>
</div>
</div></div>
</div>
<a id="method_load"></a><div class="element clickable method public method_load" data-toggle="collapse" data-target=".method_load .collapse">
<h2>Load Escher stream data.</h2>
<pre>load(string $data) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>May be a partial Escher stream.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$data</h4>
<code>string</code>
</div>
</div></div>
</div>
<a id="method__readBSE"></a><div class="element clickable method private method__readBSE" data-toggle="collapse" data-target=".method__readBSE .collapse">
<h2>Read BSE record</h2>
<pre>_readBSE() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readBlipJPEG"></a><div class="element clickable method private method__readBlipJPEG" data-toggle="collapse" data-target=".method__readBlipJPEG .collapse">
<h2>Read BlipJPEG record.</h2>
<pre>_readBlipJPEG() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>Holds raw JPEG image data</p></div></div></div>
</div>
<a id="method__readBlipPNG"></a><div class="element clickable method private method__readBlipPNG" data-toggle="collapse" data-target=".method__readBlipPNG .collapse">
<h2>Read BlipPNG record.</h2>
<pre>_readBlipPNG() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>Holds raw PNG image data</p></div></div></div>
</div>
<a id="method__readBstoreContainer"></a><div class="element clickable method private method__readBstoreContainer" data-toggle="collapse" data-target=".method__readBstoreContainer .collapse">
<h2>Read BstoreContainer record (Blip Store Container)</h2>
<pre>_readBstoreContainer() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readClientAnchor"></a><div class="element clickable method private method__readClientAnchor" data-toggle="collapse" data-target=".method__readClientAnchor .collapse">
<h2>Read ClientAnchor record.</h2>
<pre>_readClientAnchor() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>This record holds information about where the shape is anchored in worksheet</p></div></div></div>
</div>
<a id="method__readClientData"></a><div class="element clickable method private method__readClientData" data-toggle="collapse" data-target=".method__readClientData .collapse">
<h2>Read ClientData record</h2>
<pre>_readClientData() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readClientTextbox"></a><div class="element clickable method private method__readClientTextbox" data-toggle="collapse" data-target=".method__readClientTextbox .collapse">
<h2>Read ClientTextbox record</h2>
<pre>_readClientTextbox() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readDefault"></a><div class="element clickable method private method__readDefault" data-toggle="collapse" data-target=".method__readDefault .collapse">
<h2>Read a generic record</h2>
<pre>_readDefault() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readDg"></a><div class="element clickable method private method__readDg" data-toggle="collapse" data-target=".method__readDg .collapse">
<h2>Read Dg record (Drawing)</h2>
<pre>_readDg() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readDgContainer"></a><div class="element clickable method private method__readDgContainer" data-toggle="collapse" data-target=".method__readDgContainer .collapse">
<h2>Read DgContainer record (Drawing Container)</h2>
<pre>_readDgContainer() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readDgg"></a><div class="element clickable method private method__readDgg" data-toggle="collapse" data-target=".method__readDgg .collapse">
<h2>Read Dgg record (Drawing Group)</h2>
<pre>_readDgg() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readDggContainer"></a><div class="element clickable method private method__readDggContainer" data-toggle="collapse" data-target=".method__readDggContainer .collapse">
<h2>Read DggContainer record (Drawing Group Container)</h2>
<pre>_readDggContainer() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readOPT"></a><div class="element clickable method private method__readOPT" data-toggle="collapse" data-target=".method__readOPT .collapse">
<h2>Read OPT record.</h2>
<pre>_readOPT() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>This record may occur within DggContainer record or SpContainer</p></div></div></div>
</div>
<a id="method__readOfficeArtRGFOPTE"></a><div class="element clickable method private method__readOfficeArtRGFOPTE" data-toggle="collapse" data-target=".method__readOfficeArtRGFOPTE .collapse">
<h2>Read OfficeArtRGFOPTE table of property-value pairs</h2>
<pre>_readOfficeArtRGFOPTE(string $data, int $n) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$data</h4>
<code>string</code><p>Binary data</p></div>
<div class="subelement argument">
<h4>$n</h4>
<code>int</code><p>Number of properties</p></div>
</div></div>
</div>
<a id="method__readSp"></a><div class="element clickable method private method__readSp" data-toggle="collapse" data-target=".method__readSp .collapse">
<h2>Read Sp record (Shape)</h2>
<pre>_readSp() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readSpContainer"></a><div class="element clickable method private method__readSpContainer" data-toggle="collapse" data-target=".method__readSpContainer .collapse">
<h2>Read SpContainer record (Shape Container)</h2>
<pre>_readSpContainer() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readSpgr"></a><div class="element clickable method private method__readSpgr" data-toggle="collapse" data-target=".method__readSpgr .collapse">
<h2>Read Spgr record (Shape Group)</h2>
<pre>_readSpgr() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readSpgrContainer"></a><div class="element clickable method private method__readSpgrContainer" data-toggle="collapse" data-target=".method__readSpgrContainer .collapse">
<h2>Read SpgrContainer record (Shape Group Container)</h2>
<pre>_readSpgrContainer() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readSplitMenuColors"></a><div class="element clickable method private method__readSplitMenuColors" data-toggle="collapse" data-target=".method__readSplitMenuColors .collapse">
<h2>Read SplitMenuColors record</h2>
<pre>_readSplitMenuColors() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method__readTertiaryOPT"></a><div class="element clickable method private method__readTertiaryOPT" data-toggle="collapse" data-target=".method__readTertiaryOPT .collapse">
<h2>Read TertiaryOPT record</h2>
<pre>_readTertiaryOPT() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__data"> </a><div class="element clickable property private property__data" data-toggle="collapse" data-target=".property__data .collapse">
<h2></h2>
<pre>$_data : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__dataSize"> </a><div class="element clickable property private property__dataSize" data-toggle="collapse" data-target=".property__dataSize .collapse">
<h2></h2>
<pre>$_dataSize : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__object"> </a><div class="element clickable property private property__object" data-toggle="collapse" data-target=".property__object .collapse">
<h2></h2>
<pre>$_object : mixed</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>Modified during load.</p></div></div></div>
</div>
<a id="property__pos"> </a><div class="element clickable property private property__pos" data-toggle="collapse" data-target=".property__pos .collapse">
<h2></h2>
<pre>$_pos : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_BLIPJPEG"> </a><div class="element clickable constant  constant_BLIPJPEG" data-toggle="collapse" data-target=".constant_BLIPJPEG .collapse">
<h2>BLIPJPEG</h2>
<pre>BLIPJPEG </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BLIPPNG"> </a><div class="element clickable constant  constant_BLIPPNG" data-toggle="collapse" data-target=".constant_BLIPPNG .collapse">
<h2>BLIPPNG</h2>
<pre>BLIPPNG </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BSE"> </a><div class="element clickable constant  constant_BSE" data-toggle="collapse" data-target=".constant_BSE .collapse">
<h2>BSE</h2>
<pre>BSE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BSTORECONTAINER"> </a><div class="element clickable constant  constant_BSTORECONTAINER" data-toggle="collapse" data-target=".constant_BSTORECONTAINER .collapse">
<h2>BSTORECONTAINER</h2>
<pre>BSTORECONTAINER </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CLIENTANCHOR"> </a><div class="element clickable constant  constant_CLIENTANCHOR" data-toggle="collapse" data-target=".constant_CLIENTANCHOR .collapse">
<h2>CLIENTANCHOR</h2>
<pre>CLIENTANCHOR </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CLIENTDATA"> </a><div class="element clickable constant  constant_CLIENTDATA" data-toggle="collapse" data-target=".constant_CLIENTDATA .collapse">
<h2>CLIENTDATA</h2>
<pre>CLIENTDATA </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CLIENTTEXTBOX"> </a><div class="element clickable constant  constant_CLIENTTEXTBOX" data-toggle="collapse" data-target=".constant_CLIENTTEXTBOX .collapse">
<h2>CLIENTTEXTBOX</h2>
<pre>CLIENTTEXTBOX </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_DG"> </a><div class="element clickable constant  constant_DG" data-toggle="collapse" data-target=".constant_DG .collapse">
<h2>DG</h2>
<pre>DG </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_DGCONTAINER"> </a><div class="element clickable constant  constant_DGCONTAINER" data-toggle="collapse" data-target=".constant_DGCONTAINER .collapse">
<h2>DGCONTAINER</h2>
<pre>DGCONTAINER </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_DGG"> </a><div class="element clickable constant  constant_DGG" data-toggle="collapse" data-target=".constant_DGG .collapse">
<h2>DGG</h2>
<pre>DGG </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_DGGCONTAINER"> </a><div class="element clickable constant  constant_DGGCONTAINER" data-toggle="collapse" data-target=".constant_DGGCONTAINER .collapse">
<h2>DGGCONTAINER</h2>
<pre>DGGCONTAINER </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPT"> </a><div class="element clickable constant  constant_OPT" data-toggle="collapse" data-target=".constant_OPT .collapse">
<h2>OPT</h2>
<pre>OPT </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_SP"> </a><div class="element clickable constant  constant_SP" data-toggle="collapse" data-target=".constant_SP .collapse">
<h2>SP</h2>
<pre>SP </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_SPCONTAINER"> </a><div class="element clickable constant  constant_SPCONTAINER" data-toggle="collapse" data-target=".constant_SPCONTAINER .collapse">
<h2>SPCONTAINER</h2>
<pre>SPCONTAINER </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_SPGR"> </a><div class="element clickable constant  constant_SPGR" data-toggle="collapse" data-target=".constant_SPGR .collapse">
<h2>SPGR</h2>
<pre>SPGR </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_SPGRCONTAINER"> </a><div class="element clickable constant  constant_SPGRCONTAINER" data-toggle="collapse" data-target=".constant_SPGRCONTAINER .collapse">
<h2>SPGRCONTAINER</h2>
<pre>SPGRCONTAINER </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_SPLITMENUCOLORS"> </a><div class="element clickable constant  constant_SPLITMENUCOLORS" data-toggle="collapse" data-target=".constant_SPLITMENUCOLORS .collapse">
<h2>SPLITMENUCOLORS</h2>
<pre>SPLITMENUCOLORS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TERTIARYOPT"> </a><div class="element clickable constant  constant_TERTIARYOPT" data-toggle="collapse" data-target=".constant_TERTIARYOPT .collapse">
<h2>TERTIARYOPT</h2>
<pre>TERTIARYOPT </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:35Z.<br></footer></div>
</div>
</body>
</html>
