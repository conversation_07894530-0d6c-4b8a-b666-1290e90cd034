#!/usr/local/bin/php -q
<?php
// 작업중 2025-01-23 jjs
	// 0 9 * * * php -q /home/<USER>/sperp/change_mant2.psh
	# 변경점 단계별 진행 2주 지연 알림
	$ROOT_PATH = "/home/<USER>";
	include($ROOT_PATH . "/inc/func.php");
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/Encode.php");
	include($ROOT_PATH . "/inc/func_state.php");

	$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
	if(empty($dbconn_sperp_posbank->success)) {
		echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
	}

	$dbconn_posbank_intra = new DBController($db['posbank_intra']);
	if(empty($dbconn_posbank_intra->success)) {
		echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패";
	}
	/**********************************************************/

	if(in_array(date('w'),array("0","6"))){
		echo date("Y-m-d") . " - 휴무일\n";
		## 스케즐 처리 상황 intra DB에 저장
		crontab_execution(86400, "변경점 단계 진행 2주 지연 알림");
		exit;
	}

	$SQL = "SELECT NAME FROM HOLIDAY_DATA WHERE to_char(HDATE,'YYYYMMDD')='".date('Ymd')."'";
	$HOLIDAY_NM = $dbconn_sperp_posbank->query_one($SQL);
	if($HOLIDAY_NM){
		echo date("Y-m-d") . " - 휴무일(".$HOLIDAY_NM.")\n";
		## 스케즐 처리 상황 intra DB에 저장
		crontab_execution(86400, "변경점 단계 진행 2주 지연 알림");
		exit;
	}
	$arr_data = [];

	$SQL = "select BAS_CODE,BAS_OP4 from BAS where BAS_CODE in ('E006','E007','E008','E011','E036','E072','E076','E079','E086') ";
	$arr_st = $dbconn_sperp_posbank->query_array($SQL,"BAS_CODE","BAS_OP4");

	$arr_st2 = [];
	if($arr_st){
		foreach($arr_st as $key => $op4) {
			$arr_st2[$key] = explode(",", $op4);
		}
	}

	

//print_r($arr_st2['E006']);
	// 8:요청	0:작성	APPV:A:결재지시	B:검토	APPV:B:검토결재	C:재고	D:승인
	// 상태(8:요청,0:접수,A:ECR,B:PCN,C:IOS,D:PCCB,9:보류,Z:삭제)
/*-- E076	변경점관리(PCCB) 승인처리 사원
-- E086	변경점관리(ECN) 요청 담당자설정(지정가능)
4	E006	변경점관리(ECN) 알림 보낼사원	
5	E007	변경점관리(PCN) 알림 보낼사원	
6	E011	변경점관리(IOS) 알림 보낼사원	
7	E008	변경점관리(PCCB) 알림 보낼사원	
8	E036	변경점관리 업체답변 답변 알림 보낼사원
33	E072	변경점 관리 >변경점 요청시 알림 보낼사원	
34	E079	변경점관리 > 변경점 3개월 지연알림 보낼사*/



	// 요청
	$SQL = "
		SELECT
			STATE
			,'요청' STATE_NM
			,RCT_CODE||HDATE||HNO HID
			,NOTICE_GU
			,F_BAS(SCOPE_BAS,'NAME') SCOPE_NM  -- 변경범위
			,F_BAS(TARGET_BAS,'NAME') TARGET_NM -- 변경사항
			-- ,REQUEST_MEMO
			,TO_CHAR(REQUEST_DATE,'YYYY-MM-DD') NDATE
			,TO_CHAR(REG_IDATE,'YYYY-MM-DD') REG_IDATE2
			,F_STNAME(STCODE) AS STNAME
		FROM CHANGE_MANT
		WHERE 
			STATE='8' 
			and HDATE >= '********' 
			and REQUEST_DATE < SYSDATE - 14 
			and STCODE is null
		ORDER BY HDATE DESC ";
	$arrRow = $dbconn_sperp_posbank->query_rows($SQL);
	if($arr_st2['E072']){
		foreach($arr_st2['E072'] as $st) {
			if($arrRow){
				foreach($arrRow as $row) {
					$arr_data[$st][] = $row;
				}
			}
		}
	}


	// 접수(미결재자)
	$SQL = "
		SELECT
			'0' STATE
			,'접수(미결재)' STATE_NM
			,A.RCT_CODE||A.HDATE||A.HNO HID
			,A.NOTICE_GU
			,F_BAS(A.SCOPE_BAS,'NAME') SCOPE_NM  -- 변경범위
			,F_BAS(A.TARGET_BAS,'NAME') TARGET_NM -- 변경사항
			-- ,ECR_MEMO1
			,B.APPV_STCODE
			,TO_CHAR(B.REG_IDATE,'YYYY-MM-DD') NDATE
			,TO_CHAR(A.REG_IDATE,'YYYY-MM-DD') REG_IDATE2
			,F_STNAME(A.STCODE) AS STNAME
		FROM CHANGE_MANT A
			left join CHANGE_MANT_APPV B on A.RCT_CODE=B.RCT_CODE and A.HDATE=B.HDATE and A.HNO=B.HNO and B.GU='A'
		WHERE 
			A.STATE in ('0','1')
			and A.HDATE >= '********' 
			and B.REG_IDATE < SYSDATE - 14 
			and B.STATE='0'
		ORDER BY A.HDATE DESC ";
	$arrRow = $dbconn_sperp_posbank->query_rows($SQL);
	if($arrRow){
		foreach($arrRow as $key => $row) {
			$arr_data[$row['APPV_STCODE']][] = $row;
		}
	}

	// ECN
	$SQL = "
		SELECT
			STATE
			,'ECN' STATE_NM
			,RCT_CODE||HDATE||HNO HID
			,NOTICE_GU
			,F_BAS(SCOPE_BAS,'NAME') SCOPE_NM  -- 변경범위
			,F_BAS(TARGET_BAS,'NAME') TARGET_NM -- 변경사항
			-- ,ECR_MEMO1
			,TO_CHAR(REG_DATE,'YYYY-MM-DD') NDATE
			,TO_CHAR(REG_IDATE,'YYYY-MM-DD') REG_IDATE2
			,F_STNAME(STCODE) AS STNAME
		FROM CHANGE_MANT
		WHERE 
			STATE='A'
			and HDATE >= '********' 
			and REG_DATE < SYSDATE - 14 
		ORDER BY HDATE DESC ";
	$arrRow = $dbconn_sperp_posbank->query_rows($SQL);
	if($arr_st2['E007']){
		foreach($arr_st2['E007'] as $st) {
			if($arrRow){
				foreach($arrRow as $row) {
					$arr_data[$st][] = $row;
				}
			}
		}
	}


	// 결재검토(미결재자)
	$SQL = "
		select
			A.STATE
			,'결재검토(미결재)' STATE_NM
			,A.RCT_CODE||A.HDATE||A.HNO HID
			,A.NOTICE_GU
			,F_BAS(A.SCOPE_BAS,'NAME') SCOPE_NM  -- 변경범위
			,F_BAS(A.TARGET_BAS,'NAME') TARGET_NM -- 변경사항
			-- ,ECR_MEMO1
			,B.APPV_STCODE
			,TO_CHAR(B.REG_IDATE,'YYYY-MM-DD') NDATE
			,TO_CHAR(A.REG_IDATE,'YYYY-MM-DD') REG_IDATE2
			,F_STNAME(A.STCODE) AS STNAME
		from CHANGE_MANT A
			left join CHANGE_MANT_APPV B on A.RCT_CODE=B.RCT_CODE and A.HDATE=B.HDATE and A.HNO=B.HNO and B.GU='B'
		where 
			A.STATE='B'
			and A.HDATE >= '********' 
			and B.REG_IDATE < SYSDATE - 14 
			and B.STATE='0'
		order by A.HDATE desc ";
	$arrRow = $dbconn_sperp_posbank->query_rows($SQL);
	if($arrRow){
		foreach($arrRow as $key => $row) {
			$arr_data[$row['APPV_STCODE']][] = $row;
		}
	}


	// PCN, 검토결재 완료
	$SQL = "
		select
			'B2' STATE
			,'PCN' STATE_NM
			,A.RCT_CODE||A.HDATE||A.HNO HID
			,A.NOTICE_GU
			,F_BAS(A.SCOPE_BAS,'NAME') SCOPE_NM  -- 변경범위
			,F_BAS(A.TARGET_BAS,'NAME') TARGET_NM -- 변경사항
			,TO_CHAR(max(B.REG_DATE),'YYYY-MM-DD') NDATE
			,TO_CHAR(A.REG_IDATE,'YYYY-MM-DD') REG_IDATE2
			,F_STNAME(A.STCODE) AS STNAME
		from CHANGE_MANT A
			left join CHANGE_MANT_APPV B on A.RCT_CODE=B.RCT_CODE and A.HDATE=B.HDATE and A.HNO=B.HNO and B.GU='B'
		where 
			A.STATE='B'
			and A.HDATE >= '********' 
		group by A.RCT_CODE,A.HDATE,A.HNO,A.STATE,A.NOTICE_GU,A.SCOPE_BAS,A.TARGET_BAS,A.REG_IDATE,A.STCODE
		having sum(decode(B.STATE,'1',0,1))=0 and sum(decode(B.STATE,'1',1,0))>0 and max(B.REG_DATE) < SYSDATE - 14 
		order by A.HDATE desc ";
	$arrRow = $dbconn_sperp_posbank->query_rows($SQL);
	if($arr_st2['E011']){
		foreach($arr_st2['E011'] as $st) {
			if($arrRow){
				foreach($arrRow as $row) {
					$arr_data[$st][] = $row;
				}
			}
		}
	}


	// IOS
	$SQL = "
		SELECT
			STATE
			,'IOS' STATE_NM
			,RCT_CODE||HDATE||HNO HID
			,NOTICE_GU
			,F_BAS(SCOPE_BAS,'NAME') SCOPE_NM  -- 변경범위
			,F_BAS(TARGET_BAS,'NAME') TARGET_NM -- 변경사항
			-- ,ECR_MEMO1
			,TO_CHAR(REG_DATE_C,'YYYY-MM-DD') NDATE
			,TO_CHAR(REG_IDATE,'YYYY-MM-DD') REG_IDATE2
			,F_STNAME(STCODE) AS STNAME
		FROM CHANGE_MANT
		WHERE 
			STATE='C' 
			and HDATE >= '********' 
			and REG_DATE_C < SYSDATE - 14 
		ORDER BY HDATE DESC ";
	$arrRow = $dbconn_sperp_posbank->query_rows($SQL);
	if($arr_st2['E076']){
		foreach($arr_st2['E076'] as $st) {
			if($arrRow){
				foreach($arrRow as $row) {
					$arr_data[$st][] = $row;
				}
			}
		}
	}

	$style = "font-size:12px;line-height:25px;border:1px solid #333333;padding:3px;line-height:120%;";
	$style .= "text-overflow:ellipsis; table-layout:fixed;  overflow-x:hidden; overflow-y:hidden; white-space:nowrap;";
	if($arr_data){
		foreach($arr_data as $st_code => $row) {
			$title = "[변경점 처리 2주 지연 알림] " . sizeof($row) . "건";
			$title .= " / " . $st_code;


			$content = "<div>\n";
			$content .= "<div style=\"font-size:12px;line-height:50px;\"><b>변경점 처리가 2주 이상 지연된 내역</b></div>\n";
			$content .= "<div><a href=\"https://www.spqms.co.kr/?pageCode=MTE1MzM=\" target=\"_blank\">[변경점 관리 보러가기]</a></div>";
			$content .= "<table>";
			$content .= "<tr>";
			$content .= "<th style=\"".$style."width:20px;\">No</th>";
			$content .= "<th style=\"".$style."width:110px;\">고유번호</th>";
			$content .= "<th style=\"".$style."width:25px;\">공지<br>구분</th>";
			$content .= "<th style=\"".$style."width:35px;\">변경<br>범위</th>";
			$content .= "<th style=\"".$style."\">변경사항</th>";
			$content .= "<th style=\"".$style."width:70px;\">상태</th>";
			$content .= "<th style=\"".$style."width:70px;\">접수일자</th>";
			$content .= "<th style=\"".$style."\">접수자</th>";
			$content .= "</tr>\n";
			if($row){
				foreach($row as $key => $row2) {
					$link = "https://www.spqms.co.kr/?pageCode=MTE1MzM=&searHID=".$row2['HID']."";
					$NOTICE_GU_NM = "";
					if($row2['NOTICE_GU']=="A") $NOTICE_GU_NM = "사내공지";
					if($row2['NOTICE_GU']=="B") $NOTICE_GU_NM = "대외공지";

					$content .= "<tr>";
					$content .= "<td style=\"".$style." text-align:center;\">".($key+1)."</td>";
					$content .= "<td style=\"".$style." text-align:center;\"><a href=\"".$link."\" target=\"_blank\">".$row2['HID']."</a></td>";
					$content .= "<td style=\"".$style." text-align:center;\">".$NOTICE_GU_NM."</td>";
					$content .= "<td style=\"".$style." letter-spacing:-1px; text-align:center;\">".$row2['SCOPE_NM']."</td>";
					$content .= "<td style=\"".$style."\">".$row2['TARGET_NM']."</td>";
					$content .= "<td style=\"".$style." text-align:center;\">".$row2['STATE_NM']."</td>";
					$content .= "<td style=\"".$style." text-align:center;\">".$row2['REG_IDATE2']."</td>";
					$content .= "<td style=\"".$style."\">".$row2['STNAME']."</td>";
					$content .= "</tr>\n";
				}
			}
			$content .= "</table>\n";
			$content .= "</div>";

if(sizeof($row)>60){
			//인트라넷 업무연락 보내는 함수(ERP 사원코드)
			$rs = intra_send_erp('',["100695"],$title,$content);
			echo date("Y-m-d H:i:s")." 업무연락 발송 \n";
			echo $title . "\n";
			//echo "(".$stcode_str.") \n";
			echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
}
		}
	}
exit;



exit;

	$SQLST = "
		SELECT DISTINCT(STCODE) AS STCODE FROM (
			SELECT H.STCODE AS STCODE FROM CHANGE_MANT H WHERE H.STATE IN ('A','B','C') AND H.HDATE >= '********' AND  H.HDATE < '".$Checkday."'
			UNION ALL
			SELECT H.STCODE_B AS STCODE FROM CHANGE_MANT H WHERE H.STATE IN ('A','B','C') AND H.HDATE >= '********' AND  H.HDATE < '".$Checkday."' AND H.STCODE_B IS NOT NULL
			UNION ALL
			SELECT H.STCODE_C AS STCODE FROM CHANGE_MANT H WHERE H.STATE IN ('A','B','C') AND H.HDATE >= '********' AND  H.HDATE < '".$Checkday."' AND H.STCODE_C IS NOT NULL
			UNION ALL 
			SELECT H.STCODE_D AS STCODE FROM CHANGE_MANT H WHERE H.STATE IN ('A','B','C') AND H.HDATE >= '********' AND  H.HDATE < '".$Checkday."'AND H.STCODE_D IS NOT NULL
			UNION ALL 
			SELECT 
			    DISTINCT (A.APPV_STCODE) AS STCODE
			FROM
			    CHANGE_MANT H
			    LEFT OUTER JOIN CHANGE_MANT_APPV A ON A.RCT_CODE=H.RCT_CODE AND A.HDATE=H.HDATE AND A.HNO = H.HNO
			WHERE H.STATE IN ('A','B','C') AND H.HDATE >= '********' AND  H.HDATE < '".$Checkday."' AND A.STATE = '0'
		) S1	
	";
	$arrSTRow = $dbconn_sperp_posbank->query_rows($SQLST);
	$arr_ST_TEMP02 = array();
	if ($arrSTRow) {

		foreach ($arrSTRow AS $k => $v) {
		    // print_r($v);
			$arr_ST_TEMP02[] = $v["STCODE"];
		}	
	}

	// 발송 대상자
	$SQL2 = "SELECT BAS_OP4 as STCODE FROM BAS WHERE BAS_CODE='E079'";
	$stcode_str = $dbconn_sperp_posbank->query_one($SQL2);

// $stcode_str = "100994"; // 신현주
	$arr_ST_TEMP01 = explode(",", $stcode_str);

	$arr_ST = array_merge($arr_ST_TEMP01, $arr_ST_TEMP02);


//print_r($arr_ST);
// exit;

	$style = "font-size:12px;line-height:25px;border:1px solid #333333;padding:3px;line-height:120%;";
	$style .= "text-overflow:ellipsis; table-layout:fixed;  overflow-x:hidden; overflow-y:hidden; white-space:nowrap;";
	$title = "[변경점 검증 3개월 지연] " . sizeof($arrRow) . "건";
	$content = "<div>\n";
	$content .= "<div style=\"font-size:12px;line-height:50px;\"><b>변경점 처리가 3개월 이상 지연된 내역</b></div>\n";
	$content .= "<div><a href=\"https://www.spqms.co.kr/?pageCode=MTE1MzM=\" target=\"_blank\">[변경점 관리 보러가기]</a></div>";
	$content .= "<table>";
	$content .= "<tr>";
	$content .= "<th style=\"".$style."width:20px;\">No</th>";
	$content .= "<th style=\"".$style."width:110px;\">고유번호</th>";
	$content .= "<th style=\"".$style."width:25px;\">공지<br>구분</th>";
	$content .= "<th style=\"".$style."width:35px;\">변경<br>범위</th>";
	$content .= "<th style=\"".$style."\">변경사항</th>";
	$content .= "<th style=\"".$style."width:70px;\">상태</th>";
	$content .= "<th style=\"".$style."width:70px;\">접수일자</th>";
	$content .= "<th style=\"".$style."\">접수자</th>";
	$content .= "</tr>\n";
	if($arrRow){
		foreach($arrRow as $key => $row) {
			$link = "https://www.spqms.co.kr/?pageCode=MTE1MzM=&searHID=".$row['HID']."";

			$content .= "<tr>";
			$content .= "<td style=\"".$style." text-align:center;\">".($key+1)."</td>";
			$content .= "<td style=\"".$style." text-align:center;\"><a href=\"".$link."\" target=\"_blank\">".$row['HID']."</a></td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['NOTICE_GU_NM']."</td>";
			$content .= "<td style=\"".$style." letter-spacing:-1px; text-align:center;\">".$row['SCOPE_NM']."</td>";
			$content .= "<td style=\"".$style."\">".$row['TARGET_NM']."</td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['STATE_NM']."</td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['REG_IDATE2']."</td>";
			$content .= "<td style=\"".$style."\">".$row['STNAME']."</td>";
			$content .= "</tr>\n";
		}
	}
	$content .= "</table>\n";
	$content .= "</div>";


	//인트라넷 업무연락 보내는 함수(ERP 사원코드)
	$rs = intra_send_erp('',$arr_ST,$title,$content);
	echo date("Y-m-d H:i:s")." 업무연락 발송 \n";
	echo $title . "\n";
	echo "(".$stcode_str.") \n";
	echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";



	##### End. 2024-09-26. 신규 스케줄링
	###########################################



	## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(86400, "변경점 단계 진행 2주 지연 알림");

	echo date("Y-m-d H:i:s")." - 끝\n";

?>