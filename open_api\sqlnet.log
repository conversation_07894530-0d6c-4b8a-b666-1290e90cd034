

***********************************************************************
Fatal NI connect error 12560, connecting to:
 (DESCRIPTION=(CONNECT_DATA=(SERVICE_NAME=ORCL)(CID=(PROGRAM=php)(HOST=ip-172-31-32-19)(USER=schedule)))(ADDRESS=(PROTOCOL=TCP)(HOST=***************)(PORT=1521)))

  VERSION INFORMATION:
	TNS for Linux: Version ********.0 - Production
	TCP/IP NT Protocol Adapter for Linux: Version ********.0 - Production
  Time: 30-MAY-2023 11:13:11
  Tracing not turned on.
  Tns error struct:
    ns main err code: 12560
    TNS-12560: Message 12560 not found; No message file for product=network, facility=TNS
    ns secondary err code: 0
    nt main err code: 530
    TNS-00530: Message 530 not found; No message file for product=network, facility=TNS
    nt secondary err code: 113
    nt OS err code: 0
