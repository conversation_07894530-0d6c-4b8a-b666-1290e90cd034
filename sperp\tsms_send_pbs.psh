#!/usr/local/bin/php -q
<?
# ARS 접수 데이타 알림톡 발송
# 5분마다 실행
echo date("Y-m-d H:i:s")." - 실행 \n";
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

$dbconn = new DBController($db['sperp_pbs']);
if(empty($dbconn->success)) {
	echo "dbconn error [" . $db['sperp_pbs']['host'] . "] 데이터베이스 연결 실패";
}
/**********************************************************/

$arr_hp1 = [];
$arr_hp2 = [];
$SQL = "SELECT BAS_CODE,BAS_OP5 
			FROM BAS 
			WHERE (BAS_CODE LIKE 'SN%' or BAS_CODE LIKE 'SD%') AND BAS_OP5 IS NOT NULL";
//$SQL = "select 'SN01' BAS_CODE,'01045032846' BAS_OP5 from dual ";
//$SQL .= "union all select 'SD01' BAS_CODE,'01045032846' BAS_OP5 from dual ";
$arrRow = $dbconn->query_rows($SQL);
if($arrRow){
	foreach($arrRow as $key => $row) {
		$hp = trim($row['BAS_OP5']);
		if($hp){
			if(substr($row['BAS_CODE'],0,2)=="SN") $arr_hp1[] = $row['BAS_OP5'];
			if(substr($row['BAS_CODE'],0,2)=="SD") $arr_hp2[] = $row['BAS_OP5'];
		}
	}
}
$hp1 = "";
$hp2 = "";
if($arr_hp1) $hp1 = implode(";",$arr_hp1);
if($arr_hp2) $hp2 = implode(";",$arr_hp2);

$srr_MSG1 = [];
$srr_MSG2 = [];
$sms_yn = [];
$SQL2 = "
	select 
		A.NO,A.REG_DATA,A.PTYPE,A.ARSTYPE,SUBSTRB(C.CT_NAME, 1, 30) CT_NAME 
	from 
		T_SMS A
		left join CID B on RTRIM(A.REG_DATA)=B.PHONE
		left join CT C on B.CT_CODE=C.CT_CODE
	where 
		A.SMS_YN='0' AND A.NO>='4716284' 
	order by A.NO desc";
//$SQL2 = "
//	select 
//		A.NO,A.REG_DATA,A.PTYPE,A.ARSTYPE,SUBSTRB(C.CT_NAME, 1, 30) CT_NAME 
//		from 
//		T_SMS A
//		left join CID B on RTRIM(A.REG_DATA)=B.PHONE
//		left join CT C on B.CT_CODE=C.CT_CODE
//	where 
//		A.NO in ('4716127','4715952')
//	order by A.NO desc";
$arrRow2 = $dbconn->query_rows($SQL2);
if($arrRow2){
	foreach($arrRow2 as $key2 => $row2) { 
		$sms_yn[$row2['NO']] = "1";
		if($row2['PTYPE'] == "1"){ // 야간/공휴일일때만
			if($hp1){
				$srr_MSG1[$key2] = $row2['REG_DATA'] . " - [" . $row2['CT_NAME'] . "]";
				$sms_yn[$row2['NO']] = "2";
			}
		}
		if($row2['PTYPE'] == "0"){ // 주간일때
			if($hp2){
				$srr_MSG2[$key2] = $row2['REG_DATA'] . " - [" . $row2['CT_NAME'] . "]";
				$sms_yn[$row2['NO']] = "2";
			}
		}
	}
}

$setParams = [];
if($hp1 && $srr_MSG1){
	$setParams['SMS_ID'] = "posbank";
	$setParams['msgType'] = "1008";
	$setParams['dispatchPhone'] = "1588-6312";
	$setParams['receptionPhone'] = $hp1;
	$setParams['temCode'] = "posbank_000003";
	$setParams['subject'] = "당직긴급콜";
	$setParams['toMsg'] = "[당직긴급콜]\n" . implode("\n",$srr_MSG1);
	$rs = KakaoTalk_send($setParams);
	echo $hp1 . "/" .$setParams['toMsg'] . "\n";

	// 카톡 안되서 임시로 SMS전송처리 - 2022-10-15 jjs
	//$rs = sms_send($hp1,$setParams['toMsg'],$setParams['dispatchPhone'],$setParams['SMS_ID']);

	echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
}

if($hp2 && $srr_MSG2){
	unset($setParams);
	$setParams['SMS_ID'] = "posbank";
	$setParams['msgType'] = "1008";
	$setParams['dispatchPhone'] = "1588-6312";
	$setParams['receptionPhone'] = $hp2;
	$setParams['temCode'] = "posbank_000003";
	$setParams['subject'] = "당직긴급콜";
	$setParams['toMsg'] = "[당직긴급콜]\n" . implode("\n",$srr_MSG2);
	$rs = KakaoTalk_send($setParams);
	echo $hp2 . "/" .$setParams['toMsg'] . "\n";

	// 카톡 안되서 임시로 SMS전송처리 - 2022-10-15 jjs
	//$rs = sms_send($hp2,$setParams['toMsg'],$setParams['dispatchPhone'],$setParams['SMS_ID']);

	echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
}

$arr_query = [];
if($sms_yn){
	foreach($sms_yn as $key => $value) {
		$arr_query[] = "update T_SMS set SMS_YN = '".$value."' where NO='".$key."'";
	}
}
if($arr_query){
	$rs = $dbconn->iud_query($arr_query);
	echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
}

echo date("Y-m-d H:i:s")." - 알림톡 발송\n";
echo "---------------------------\n";

## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(300, "PBS CID 알림톡 발송");



?>