#!/usr/bin/php -q
<?php
	// 0 9 * * * php -q /home/<USER>/sperp/online_as_sms.psh
	# 온라인 AS 접수 카카오톡 알림톡(SMS) 발송
	$ROOT_PATH = "/home/<USER>";
	include($ROOT_PATH . "/inc/func.php");
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/Encode.php");


	$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
	if(empty($dbconn_sperp_posbank->success)) {
		echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패\n";
	}

	$dbconn_m2g = new DBController($db['m2g']);
	if(empty($dbconn_m2g->success)) {
		echo "dbconn error [" . $db['m2g']['host'] . "] 데이터베이스 연결 실패\n";
	}

    echo "\n카카오톡 알림톡 [".date('Y-m-d H:i:s')."]\n";
    echo "[온라인코드] rs 전송시간 전송타입 전송타입명\n";
// 로그배열
	// $dateKey = date('Y-m-d');
	// $SMS_SEND_RESULT = [];
 

    // //1. 미입고 1일전 SMS
	$waybillState = arrAsWaybillState(2);
	$SQL = "
        SELECT 
            A.RCT_CODE||A.HDATE||A.HNO ONLINE_CODE ,A.RCT_CODE ,A.HDATE ,A.HNO ,A.SNO ,A.AS_ID 
            ,TO_CHAR(A.REG_IDATE, 'YYYYMMDD') AREG_IDATE ,A.STATE ,A.WAYBILL_STATE  ,A.TEL2
            ,PR.PR_NAME ,B.BAS_NAME 
        FROM ASS_ACCEPT A
        LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE 
        LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE 
        WHERE A.STATE IN ({$waybillState}) AND A.WAYBILL_STATE != '1'
	";
    $ASS_ROWS = $dbconn_sperp_posbank->query_rows($SQL);

	// echo "ASS_ROWS : ".$SQL;
	// echo "CNT : ".COUNT($ASS_ROWS);
	// exit;

    if($ASS_ROWS){
        $groupedData = [];
        foreach ($ASS_ROWS as $row) {
            $code = $row['ONLINE_CODE'];
            if (!isset($groupedData[$code])) {
                $groupedData[$code] = [
                    "CNT" => 0,
                    "AREG_IDATE" => $row['AREG_IDATE']
                ];
            }
            $groupedData[$code]["CNT"]++;
        } 
        foreach ($groupedData as $ONLINE_CODE => $data) {
			// echo "[ONLINE_CODE : ".$ONLINE_CODE." ]";
            $DEADLINE = getASBusinessDate($data['AREG_IDATE'], 1);
            if ($DEADLINE === date('Y-m-d')) {
				$rs = AS_KAKAO_SMS_BY_RECEPTION($ONLINE_CODE,'1');
				// $SMS_SEND_RESULT[$dateKey][] = [
				// 	'SEND_STATE' => $rs['state'],
				// 	'ONLINE_CODE' => $ONLINE_CODE,
				// 	'SEND_DATE' => date('Y-m-d H:i:s'),
				// 	'SEND_TYPE' => '1',
				// 	'SEND_TYPE_NM' => '미입고1일전'
				// ];
                echo "[".$ONLINE_CODE."]"." ".$rs['state']." ". date('Y-m-d H:i:s')." 1  미입고1일전\n";
            }
        }
    }    

	// print_r($SMS_SEND_RESULT);
	// exit;

    //2. 점검료 안내 => 후개발
	//3. 미선택 1일전
    $SQL="
        SELECT 
            A.RCT_CODE||A.HDATE||A.HNO ONLINE_CODE ,A.RCT_CODE ,A.HDATE ,A.HNO ,A.SNO ,A.AS_ID 
            ,TO_CHAR(A.REG_IDATE, 'YYYYMMDD') ,A.STATE ,A.WAYBILL_STATE  ,A.TEL2
            ,PR.PR_NAME ,B.BAS_NAME ,TO_CHAR(A.SEND_ESTIMATE_DATE, 'YYYY/MM/DD') SEND_ESTIMATE_DATE
        FROM ASS_ACCEPT A
        LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE 
        LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE 
        LEFT JOIN AS_PAYMENT F2 ON A.RCT_CODE = F2.RCT_CODE AND A.HDATE=F2.HDATE AND A.HNO = F2.HNO AND F2.SERVICE_TYPE ='1'
        WHERE F2.RCT_CODE IS NULL AND A.SEND_ESTIMATE_DATE IS NOT NULL AND A.STATE ='25'
        ORDER BY RCT_CODE DESC, HDATE DESC, HNO DESC
    ";
    $ASS_ROWS = $dbconn_sperp_posbank->query_rows($SQL);
    if($ASS_ROWS){
        $groupedData = [];
        foreach ($ASS_ROWS as $row) {
            $code = $row['ONLINE_CODE'];
            if (!isset($groupedData[$code])) {
                $groupedData[$code] = [
                    "CNT" => 0,
                    "SEND_ESTIMATE_DATE" => $row['SEND_ESTIMATE_DATE']
                ];
            }
            $groupedData[$code]["CNT"]++;
        } 
        foreach ($groupedData as $ONLINE_CODE => $data) {
            $DEADLINE = getASBusinessDate($data['SEND_ESTIMATE_DATE'], 3);
            if ($DEADLINE === date('Y-m-d')) {
                $rs = AS_KAKAO_SMS_BY_RECEPTION($ONLINE_CODE,'3');
				// $SMS_SEND_RESULT[$dateKey][] = [
				// 	'SEND_STATE' => $rs['state'],
				// 	'ONLINE_CODE' => $ONLINE_CODE,
				// 	'SEND_DATE' => date('Y-m-d H:i:s'),
				// 	'SEND_TYPE' => '3',
				// 	'SEND_TYPE_NM' => '미선택1일전'
				// ];
                echo "[".$ONLINE_CODE."]"." ".$rs['state']." ". date('Y-m-d H:i:s')." 3  미선택1일전\n";
            }
        }
    }    

	// $rs = AS_KAKAO_SMS_BY_RECEPTION($ONLINE_CODEtmp,'3');
	// print_r($SMS_SEND_RESULT);
	// exit;

	//4-1. 미결제 7일차 /4-2. 미결제 14일차/4-3. 미결제 20일차
    $SQL ="
        SELECT 
            A.RCT_CODE||A.HDATE||A.HNO ONLINE_CODE ,A.RCT_CODE ,A.HDATE ,A.HNO ,A.SNO ,A.AS_ID 
            ,TO_CHAR(A.REG_IDATE, 'YYYYMMDD') ,A.STATE ,A.WAYBILL_STATE  ,A.TEL2
            ,PR.PR_NAME ,B.BAS_NAME ,TO_CHAR(A.SEND_ESTIMATE_DATE, 'YYYY/MM/DD')
            , TO_CHAR(F2.REG_IDATE, 'YYYY/MM/DD') F2REG_IDATE
        FROM ASS_ACCEPT A
        LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE 
        LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE 
        LEFT JOIN AS_PAYMENT F2 ON A.RCT_CODE = F2.RCT_CODE AND A.HDATE=F2.HDATE AND A.HNO = F2.HNO AND F2.SERVICE_TYPE ='1'
        WHERE A.STATE ='30'
        ORDER BY RCT_CODE DESC, HDATE DESC, HNO DESC
    ";  
    $ASS_ROWS = $dbconn_sperp_posbank->query_rows($SQL);
    if($ASS_ROWS){
        $groupedData = [];
        foreach ($ASS_ROWS as $row) {
            $code = $row['ONLINE_CODE'];
            if (!isset($groupedData[$code])) {
                $groupedData[$code] = [
                    "CNT" => 0,
                    "F2REG_IDATE" => $row['F2REG_IDATE']
                ];
            }
            $groupedData[$code]["CNT"]++;
        } 
        foreach ($groupedData as $ONLINE_CODE => $data) {
            $DEADLINE1 = getASBusinessDate($data['F2REG_IDATE'], 5);
			// echo "[onlinecode : ".$ONLINE_CODE." , deacline1 : ".$DEADLINE1."]";
            $DEADLINE2 = getASBusinessDate($data['F2REG_IDATE'], 6);
            $DEADLINE3 = getASBusinessDate($data['F2REG_IDATE'], 7);
            if ($DEADLINE1 === date('Y-m-d')) {
                $rs = AS_KAKAO_SMS_BY_RECEPTION($ONLINE_CODE,'4_1');
				// $SMS_SEND_RESULT[$dateKey][] = [
				// 	'SEND_STATE' => $rs['state'],
				// 	'ONLINE_CODE' => $ONLINE_CODE,
				// 	'SEND_DATE' => date('Y-m-d H:i:s'),
				// 	'SEND_TYPE' => '4_1',
				// 	'SEND_TYPE_NM' => '미결제7일차'
				// ];
                
                echo "[".$ONLINE_CODE."]"." ".$rs['state']." ". date('Y-m-d H:i:s')." 4_1  미결제7일차\n";
            }else if ($DEADLINE2 === date('Y-m-d')) {
                $rs = AS_KAKAO_SMS_BY_RECEPTION($ONLINE_CODE,'4_2');
				// $SMS_SEND_RESULT[$dateKey][] = [
				// 	'SEND_STATE' => $rs['state'],
				// 	'ONLINE_CODE' => $ONLINE_CODE,
				// 	'SEND_DATE' => date('Y-m-d H:i:s'),
				// 	'SEND_TYPE' => '4_2',
				// 	'SEND_TYPE_NM' => '미결제14일차'
				// ];
                echo "[".$ONLINE_CODE."]"." ".$rs['state']." ". date('Y-m-d H:i:s')." 4_2  미결제14일차\n";
            }else if ($DEADLINE3 === date('Y-m-d')) {
                $rs = AS_KAKAO_SMS_BY_RECEPTION($ONLINE_CODE,'4_3');
				// $SMS_SEND_RESULT[$dateKey][] = [
				// 	'SEND_STATE' => $rs['state'],
				// 	'ONLINE_CODE' => $ONLINE_CODE,
				// 	'SEND_DATE' => date('Y-m-d H:i:s'),
				// 	'SEND_TYPE' => '4_3',
				// 	'SEND_TYPE_NM' => '미결제마감1일전'
				// ];
                echo "[".$ONLINE_CODE."]"." ".$rs['state']." ". date('Y-m-d H:i:s')." 4_3  미결제마감1일전\n";
            }
        }
    }    

	
	// print_r($SMS_SEND_RESULT);
	// exit; 

// CMD 로그확인용
	// print_r($SMS_SEND_RESULT);
    // exit;
	##### End. 2025.02.25 KSH(101141) 신규 스케줄링
	###########################################


	// ## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(86400, "온라인 AS 접수 카카오톡 알림톡(SMS) 발송");

	echo date("Y-m-d H:i:s")." - 끝\n";
?>
