<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_CachedObjectStorageFactory</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_finalize" title="finalize :: Clear the cache storage"><span class="description">Clear the cache storage</span><pre>finalize()</pre></a></li>
<li class="method public "><a href="#method_getAllCacheStorageMethods" title="getAllCacheStorageMethods :: Return the list of all possible cache storage methods"><span class="description">Return the list of all possible cache storage methods</span><pre>getAllCacheStorageMethods()</pre></a></li>
<li class="method public "><a href="#method_getCacheStorageClass" title="getCacheStorageClass :: Return the current cache storage class"><span class="description">Return the current cache storage class</span><pre>getCacheStorageClass()</pre></a></li>
<li class="method public "><a href="#method_getCacheStorageMethod" title="getCacheStorageMethod :: Return the current cache storage method"><span class="description">Return the current cache storage method</span><pre>getCacheStorageMethod()</pre></a></li>
<li class="method public "><a href="#method_getCacheStorageMethods" title="getCacheStorageMethods :: Return the list of all available cache storage methods"><span class="description">Return the list of all available cache storage methods</span><pre>getCacheStorageMethods()</pre></a></li>
<li class="method public "><a href="#method_getInstance" title="getInstance :: Initialise the cache storage"><span class="description">Initialise the cache storage</span><pre>getInstance()</pre></a></li>
<li class="method public "><a href="#method_initialize" title="initialize :: Identify the cache storage method to use"><span class="description">Identify the cache storage method to use</span><pre>initialize()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__cacheStorageClass" title="$_cacheStorageClass :: Name of the class used for cell cacheing"><span class="description"></span><pre>$_cacheStorageClass</pre></a></li>
<li class="property private "><a href="#property__cacheStorageMethod" title="$_cacheStorageMethod :: Name of the method used for cell cacheing"><span class="description"></span><pre>$_cacheStorageMethod</pre></a></li>
<li class="property private "><a href="#property__storageMethodDefaultParameters" title="$_storageMethodDefaultParameters :: Default arguments for each cache storage method"><span class="description"></span><pre>$_storageMethodDefaultParameters</pre></a></li>
<li class="property private "><a href="#property__storageMethodParameters" title="$_storageMethodParameters :: Arguments for the active cache storage method"><span class="description"></span><pre>$_storageMethodParameters</pre></a></li>
<li class="property private "><a href="#property__storageMethods" title="$_storageMethods :: List of all possible cache storage methods"><span class="description"></span><pre>$_storageMethods</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_cache_igbinary" title="cache_igbinary :: "><span class="description">cache_igbinary</span><pre>cache_igbinary</pre></a></li>
<li class="constant  "><a href="#constant_cache_in_memory" title="cache_in_memory :: "><span class="description">cache_in_memory</span><pre>cache_in_memory</pre></a></li>
<li class="constant  "><a href="#constant_cache_in_memory_gzip" title="cache_in_memory_gzip :: "><span class="description">cache_in_memory_gzip</span><pre>cache_in_memory_gzip</pre></a></li>
<li class="constant  "><a href="#constant_cache_in_memory_serialized" title="cache_in_memory_serialized :: "><span class="description">cache_in_memory_serialized</span><pre>cache_in_memory_serialized</pre></a></li>
<li class="constant  "><a href="#constant_cache_to_apc" title="cache_to_apc :: "><span class="description">cache_to_apc</span><pre>cache_to_apc</pre></a></li>
<li class="constant  "><a href="#constant_cache_to_discISAM" title="cache_to_discISAM :: "><span class="description">cache_to_discISAM</span><pre>cache_to_discISAM</pre></a></li>
<li class="constant  "><a href="#constant_cache_to_memcache" title="cache_to_memcache :: "><span class="description">cache_to_memcache</span><pre>cache_to_memcache</pre></a></li>
<li class="constant  "><a href="#constant_cache_to_phpTemp" title="cache_to_phpTemp :: "><span class="description">cache_to_phpTemp</span><pre>cache_to_phpTemp</pre></a></li>
<li class="constant  "><a href="#constant_cache_to_sqlite" title="cache_to_sqlite :: "><span class="description">cache_to_sqlite</span><pre>cache_to_sqlite</pre></a></li>
<li class="constant  "><a href="#constant_cache_to_sqlite3" title="cache_to_sqlite3 :: "><span class="description">cache_to_sqlite3</span><pre>cache_to_sqlite3</pre></a></li>
<li class="constant  "><a href="#constant_cache_to_wincache" title="cache_to_wincache :: "><span class="description">cache_to_wincache</span><pre>cache_to_wincache</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_CachedObjectStorageFactory"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_CachedObjectStorageFactory.html">PHPExcel_CachedObjectStorageFactory</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_CachedObjectStorageFactory</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.CachedObjectStorage.html">PHPExcel_CachedObjectStorage</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_finalize"></a><div class="element clickable method public method_finalize" data-toggle="collapse" data-target=".method_finalize .collapse">
<h2>Clear the cache storage</h2>
<pre>finalize() </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_getAllCacheStorageMethods"></a><div class="element clickable method public method_getAllCacheStorageMethods" data-toggle="collapse" data-target=".method_getAllCacheStorageMethods .collapse">
<h2>Return the list of all possible cache storage methods</h2>
<pre>getAllCacheStorageMethods() : string[]</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string[]</code></div>
</div></div>
</div>
<a id="method_getCacheStorageClass"></a><div class="element clickable method public method_getCacheStorageClass" data-toggle="collapse" data-target=".method_getCacheStorageClass .collapse">
<h2>Return the current cache storage class</h2>
<pre>getCacheStorageClass() : <a href="../classes/PHPExcel_CachedObjectStorage_ICache.html">\PHPExcel_CachedObjectStorage_ICache</a> | NULL</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code><a href="../classes/PHPExcel_CachedObjectStorage_ICache.html">\PHPExcel_CachedObjectStorage_ICache</a></code><code>NULL</code>
</div>
</div></div>
</div>
<a id="method_getCacheStorageMethod"></a><div class="element clickable method public method_getCacheStorageMethod" data-toggle="collapse" data-target=".method_getCacheStorageMethod .collapse">
<h2>Return the current cache storage method</h2>
<pre>getCacheStorageMethod() : string | NULL</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code><code>NULL</code>
</div>
</div></div>
</div>
<a id="method_getCacheStorageMethods"></a><div class="element clickable method public method_getCacheStorageMethods" data-toggle="collapse" data-target=".method_getCacheStorageMethods .collapse">
<h2>Return the list of all available cache storage methods</h2>
<pre>getCacheStorageMethods() : string[]</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string[]</code></div>
</div></div>
</div>
<a id="method_getInstance"></a><div class="element clickable method public method_getInstance" data-toggle="collapse" data-target=".method_getInstance .collapse">
<h2>Initialise the cache storage</h2>
<pre>getInstance(\PHPExcel_Worksheet $parent) : <a href="../classes/PHPExcel_CachedObjectStorage_ICache.html">\PHPExcel_CachedObjectStorage_ICache</a></pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$parent</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><p>Enable cell caching for this worksheet</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_CachedObjectStorage_ICache.html">\PHPExcel_CachedObjectStorage_ICache</a></code></div>
</div></div>
</div>
<a id="method_initialize"></a><div class="element clickable method public method_initialize" data-toggle="collapse" data-target=".method_initialize .collapse">
<h2>Identify the cache storage method to use</h2>
<pre>initialize(string $method, array $arguments) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$method</h4>
<code>string</code><p>Name of the method to use for cell cacheing</p></div>
<div class="subelement argument">
<h4>$arguments</h4>
<code>array</code><p>of mixed    $arguments    Additional arguments to pass to the cell caching class
                                       when instantiating</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__cacheStorageClass"> </a><div class="element clickable property private property__cacheStorageClass" data-toggle="collapse" data-target=".property__cacheStorageClass .collapse">
<h2></h2>
<pre>$_cacheStorageClass : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__cacheStorageMethod"> </a><div class="element clickable property private property__cacheStorageMethod" data-toggle="collapse" data-target=".property__cacheStorageMethod .collapse">
<h2></h2>
<pre>$_cacheStorageMethod : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__storageMethodDefaultParameters"> </a><div class="element clickable property private property__storageMethodDefaultParameters" data-toggle="collapse" data-target=".property__storageMethodDefaultParameters .collapse">
<h2></h2>
<pre>$_storageMethodDefaultParameters : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__storageMethodParameters"> </a><div class="element clickable property private property__storageMethodParameters" data-toggle="collapse" data-target=".property__storageMethodParameters .collapse">
<h2></h2>
<pre>$_storageMethodParameters : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__storageMethods"> </a><div class="element clickable property private property__storageMethods" data-toggle="collapse" data-target=".property__storageMethods .collapse">
<h2></h2>
<pre>$_storageMethods : string[]</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_cache_igbinary"> </a><div class="element clickable constant  constant_cache_igbinary" data-toggle="collapse" data-target=".constant_cache_igbinary .collapse">
<h2>cache_igbinary</h2>
<pre>cache_igbinary </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_cache_in_memory"> </a><div class="element clickable constant  constant_cache_in_memory" data-toggle="collapse" data-target=".constant_cache_in_memory .collapse">
<h2>cache_in_memory</h2>
<pre>cache_in_memory </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_cache_in_memory_gzip"> </a><div class="element clickable constant  constant_cache_in_memory_gzip" data-toggle="collapse" data-target=".constant_cache_in_memory_gzip .collapse">
<h2>cache_in_memory_gzip</h2>
<pre>cache_in_memory_gzip </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_cache_in_memory_serialized"> </a><div class="element clickable constant  constant_cache_in_memory_serialized" data-toggle="collapse" data-target=".constant_cache_in_memory_serialized .collapse">
<h2>cache_in_memory_serialized</h2>
<pre>cache_in_memory_serialized </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_cache_to_apc"> </a><div class="element clickable constant  constant_cache_to_apc" data-toggle="collapse" data-target=".constant_cache_to_apc .collapse">
<h2>cache_to_apc</h2>
<pre>cache_to_apc </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_cache_to_discISAM"> </a><div class="element clickable constant  constant_cache_to_discISAM" data-toggle="collapse" data-target=".constant_cache_to_discISAM .collapse">
<h2>cache_to_discISAM</h2>
<pre>cache_to_discISAM </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_cache_to_memcache"> </a><div class="element clickable constant  constant_cache_to_memcache" data-toggle="collapse" data-target=".constant_cache_to_memcache .collapse">
<h2>cache_to_memcache</h2>
<pre>cache_to_memcache </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_cache_to_phpTemp"> </a><div class="element clickable constant  constant_cache_to_phpTemp" data-toggle="collapse" data-target=".constant_cache_to_phpTemp .collapse">
<h2>cache_to_phpTemp</h2>
<pre>cache_to_phpTemp </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_cache_to_sqlite"> </a><div class="element clickable constant  constant_cache_to_sqlite" data-toggle="collapse" data-target=".constant_cache_to_sqlite .collapse">
<h2>cache_to_sqlite</h2>
<pre>cache_to_sqlite </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_cache_to_sqlite3"> </a><div class="element clickable constant  constant_cache_to_sqlite3" data-toggle="collapse" data-target=".constant_cache_to_sqlite3 .collapse">
<h2>cache_to_sqlite3</h2>
<pre>cache_to_sqlite3 </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_cache_to_wincache"> </a><div class="element clickable constant  constant_cache_to_wincache" data-toggle="collapse" data-target=".constant_cache_to_wincache .collapse">
<h2>cache_to_wincache</h2>
<pre>cache_to_wincache </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:33Z.<br></footer></div>
</div>
</body>
</html>
