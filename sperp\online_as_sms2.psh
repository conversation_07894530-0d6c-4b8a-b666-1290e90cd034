#!/usr/bin/php -q
<?php
	// 0 9 * * * php -q /home/<USER>/sperp/online_as_sms2.psh
	# 온라인 AS 접수 카카오톡 알림톡(SMS) 발송2_수리완료
	$ROOT_PATH = "/home/<USER>";
	include($ROOT_PATH . "/inc/func.php");
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/Encode.php");


	$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
	if(empty($dbconn_sperp_posbank->success)) {
		echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
	}

	$dbconn_m2g = new DBController($db['m2g']);
	if(empty($dbconn_m2g->success)) {
		echo "dbconn error [" . $db['m2g']['host'] . "] 데이터베이스 연결 실패";
	}

// 로그배열
	// $dateKey = date('Y-m-d H');
	// $SMS_SEND_RESULT = [];
 
 
    echo "\n카카오톡 알림톡 [".date('Y-m-d H:i:s')."]\n";
    echo "[AS_ID] rs 전송시간 전송타입 전송타입명\n";

	//5. 수리완료
    $SQL ="
        SELECT 
            A.RCT_CODE||A.HDATE||A.HNO ONLINE_CODE ,A.RCT_CODE ,A.HDATE ,A.HNO ,A.SNO ,A.AS_ID 
            ,TO_CHAR(A.REG_IDATE, 'YYYYMMDD') ,A.STATE ,A.WAYBILL_STATE  ,A.TEL2
            ,PR.PR_NAME ,B.BAS_NAME ,TO_CHAR(A.SEND_ESTIMATE_DATE, 'YYYY/MM/DD')
            , TO_CHAR(F2.REG_IDATE, 'YYYY/MM/DD') F2REG_IDATE
        FROM ASS_ACCEPT A
        LEFT JOIN PR ON A.PR_CODE = PR.PR_CODE 
        LEFT JOIN BAS B ON A.PR_GROUP = B.BAS_CODE 
        LEFT JOIN AS_PAYMENT F2 ON A.RCT_CODE = F2.RCT_CODE AND A.HDATE=F2.HDATE AND A.HNO = F2.HNO AND F2.SERVICE_TYPE ='1'
        WHERE A.STATE ='40' AND A.COMPLETE_SMS IS NULL
        ORDER BY RCT_CODE DESC, HDATE DESC, HNO DESC
    ";  
    $ASS_ROWS = $dbconn_sperp_posbank->query_rows($SQL);
    if($ASS_ROWS){ 
        foreach($ASS_ROWS AS $row){
            $rs = AS_KAKAO_SMS_BY_PRODUCT($row['AS_ID'],'1');
			// $SMS_SEND_RESULT[$dateKey][] = [
			// 	'SEND_STATE' => $rs['state'],
			// 	'ONLINE_CODE' => $ONLINE_CODE,
			// 	'SEND_DATE' => date('Y-m-d H:i:s'),
			// 	'SEND_TYPE' => '5',
			// 	'SEND_TYPE_NM' => '수리완료'
			// ];
			echo "[".$row['AS_ID']."]"." ".$rs['state']." ". date('Y-m-d H:i:s')." 5  수리완료\n";
			if($rs['state']){
				$SQL = "UPDATE ASS_ACCEPT  SET COMPLETE_SMS = '1' WHERE AS_ID = '".$row['AS_ID']."'";
				$rs = $dbconn_sperp_posbank->iud_query($SQL);
				// $SMS_SEND_RESULT[$dateKey][] = [
				// 	'SEND_STATE' => $rs['state'],
				// 	'ONLINE_CODE' => $ONLINE_CODE,
				// 	'SEND_DATE' => date('Y-m-d H:i:s'),
				// 	'SEND_TYPE' => '5_1',
				// 	'SEND_TYPE_NM' => 'ASS_ACCEPT COMPLETE_SMS UPDATE'
				// ];
                echo "[".$row['AS_ID']."]"." ".$rs['state']." ". date('Y-m-d H:i:s')." 5_1  ASS_ACCEPT COMPLETE_SMS UPDATE\n";
			}
        }
    } 

// CMD 로그확인용
	// print_r($SMS_SEND_RESULT);
	// exit;
	##### End. 2025.02.25 KSH(101141) 신규 스케줄링
	###########################################


	// ## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(86400, "온라인 AS 접수 카카오톡 알림톡(SMS) 발송2_수리완료");

	echo date("Y-m-d H:i:s")." - 끝\n";
?>
