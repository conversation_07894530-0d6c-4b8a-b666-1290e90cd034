#!/usr/local/bin/php -q
<?
// 0 9 * * * php -q /home/<USER>/sperp/doch.psh
# 불량이슈 처리기한 알림
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/inc/Encode.php");

$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
if(empty($dbconn_sperp_posbank->success)) {
	echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
}

$dbconn_posbank_intra = new DBController($db['posbank_intra']);
if(empty($dbconn_posbank_intra->success)) {
	echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패";
}
/**********************************************************/

if(in_array(date('w'),array("0","6"))){
	echo date("Y-m-d") . " - 휴무일\n";
	## 스케즐 처리 상황 intra DB에 저장 
	crontab_execution(86400, "웹ERP 프로젝트 미승인건");
	exit;
}

$SQL = "SELECT NAME FROM HOLIDAY_DATA WHERE to_char(HDATE,'YYYYMMDD')='".date('Ymd')."'";
$HOLIDAY_NM = $dbconn_sperp_posbank->query_one($SQL);
if($HOLIDAY_NM){
	echo date("Y-m-d") . " - 휴무일(".$HOLIDAY_NM.")\n";
	## 스케즐 처리 상황 intra DB에 저장
	crontab_execution(86400, "웹ERP 프로젝트 미승인건");
	exit;
}

$where = '';
// 금일 휴가자
$SQL = "SELECT
				group_concat(DISTINCT A.PRS_NUM)
			FROM APPV_HOLI A
				LEFT JOIN PRS_MASTER B ON A.PRS_NUM=B.PRS_NUM
			WHERE
				A.GU2='-'
				AND CURDATE() BETWEEN A.HOLI_SDATE AND A.HOLI_EDATE
				AND A.VIEW_YN<>'N'
	";
$prs_num = $dbconn_posbank_intra->query_one($SQL);
if($prs_num){
	$arr_prs_num = explode(",", $prs_num);
	$where = " and PRS_NUM not in ('".implode("','",$arr_prs_num)."') ";
}

$SQL = "
	SELECT PRS_NUM, ERP_CODE, PRS_NM, SIL_CODE, SIL_NM
	  FROM prs_master
	 WHERE LE_PO IN ('팀장','사업부장','부문장')
	   AND sil_code IN ('2003','2008','2009')
	   AND FLAG = '1'
	   AND ERP_CODE <> '100272'
	   $where
	   
	 UNION ALL
	 
	 SELECT PRS_NUM, ERP_CODE, PRS_NM, SIL_CODE, SIL_NM 
	   FROM prs_master 
	  WHERE sil_code IN (SELECT SIL_CODE FROM sil_code WHERE LINK_CODE IN ('2003','2008','2009'))
		AND LE_PO IN ('팀장','사업부장','부문장')
		AND FLAG = '1'
		$where
";
$rs = $dbconn_posbank_intra->query_rows($SQL);

//최초
//"********"	"100125"	"김양규"	"2003"	"연구소1부문"
//"********"	"100643"	"임창노"	"3012"	"소프트웨어사업부"
//"********"	"100721"	"김구택"	"2008"	"연구소2부문"
//"********"	"100695"	"장진식"	"3012"	"소프트웨어사업부"
//"********"	"101126"	"김상일"	"3012"	"소프트웨어사업부"
//"*********"	"101003"	"조범준"	"3007"	"회로설계1팀"
//"********"	"100977"	"이상엽"	"3008"	"기구설계팀"
//"********"	"101163"	"임광빈"	"3036"	"회로설계2팀"
//"********"	"100605"	"박준형"	"3037"	"ES팀"
//"********"	"101036"	"홍성익"	"3038"	"RS팀"
//"********"	"101035"	"김병곤"	"3039"	"PS팀"
//"********"	"100719"	"김명수"	"3040"	"DS팀"
//"********"	"100645"	"장주봉"	"3041"	"회로설계3팀"

//25.01.22
//"********"	"100643"	"임창노"	"3012"	"전산지원팀"
//"********"	"100721"	"김구택"	"2008"	"연구소2부문"
//"********"	"100695"	"장진식"	"2009"	"연구소3부문"
//"*********"	"101003"	"조범준"	"2003"	"연구소1부문"
//"********"	"101126"	"김상일"	"3046"	"솔루션팀"
//"********"	"100645"	"장주봉"	"3007"	"회로설계1팀"
//"********"	"100977"	"이상엽"	"3008"	"기구설계팀"
//"********"	"101163"	"임광빈"	"3036"	"회로설계2팀"
//"********"	"100605"	"박준형"	"3037"	"ES팀"
//"********"	"101036"	"홍성익"	"3038"	"RS팀"
//"********"	"101035"	"김병곤"	"3039"	"PS팀"
//"********"	"100719"	"김명수"	"3040"	"DS팀"




$date = new DateTime(); 
$date->modify('-1 week'); 
$weekAgo = $date->format('Ymd'); 
$weekAgoString = (string)$weekAgo; 
foreach($rs as $idx => $val){
	$in_stcode = '';
	$QUERY = '';
	$SQL = '';
	//연구소1부문장, 연구소2부문장의 팀장들
	if($val['SIL_CODE'] == '2003' || $val['SIL_CODE'] == '2008' || $val['SIL_CODE'] == '2009'){
		
		$QUERY = "
			SELECT ERP_CODE 
			  FROM prs_master 
		     WHERE sil_code IN (SELECT SIL_CODE FROM sil_code WHERE LINK_CODE = '".$val['SIL_CODE']."')
		       AND LE_PO IN ('팀장','사업부장','부문장')
		       AND FLAG = '1'
		";
	
//	}else if($val['SIL_CODE'] == '3012'){ //소프트웨어 사업부
//		
//		$QUERY = "
//			 SELECT ERP_CODE, PART_CODE
//			   FROM prs_master 
//			  WHERE sil_code  = '".$val['SIL_CODE']."'
//				AND LE_PO IN ('팀장')
//				AND FLAG = '1'
//		";	
//		$saup = $dbconn_posbank_intra->query_rows($QUERY);
//		foreach($saup as $item=>$row){
//			if($val['ERP_CODE'] == $row['ERP_CODE']){
//				$QUERY = "
//					 SELECT ERP_CODE 
//					   FROM prs_master 
//					  WHERE sil_code = '".$val['SIL_CODE']."'
//					    AND part_code = '".$row['PART_CODE']."'
//						AND PART_HEAD_YN = 'N'
//						AND FLAG = '1'
//				";
//			}
//		}
		
	}else{ //연구소1부문, 연구소2부문, 연구소3부문 팀장들의 팀원
		
		$QUERY = "
			 SELECT ERP_CODE 
			   FROM prs_master 
			  WHERE sil_code = '".$val['SIL_CODE']."'
				AND PART_HEAD_YN = 'N'
				AND FLAG = '1'
		";
	}
	
	$rows = $dbconn_posbank_intra->query_rows($QUERY);
	foreach($rows as $idx2 => $val2){
		$in_stcode .= ",'".$val2['ERP_CODE']."'";
	}
	$in_stcode = ltrim($in_stcode, ', ');
		
	$SQL = "
		SELECT PJWT_STCODE, B.ST_NAME, COUNT(*) AS CNT
		  FROM PROJECT_WORK_TIME A
		 INNER JOIN ST B
			ON A.PJWT_STCODE = B.ST_CODE
		 WHERE PJWT_STCODE IN (".$in_stcode.")
		   AND PJWT_DATE < '".$weekAgoString."' 
		   AND PJWT_DATE > '********'
		   AND CONFIRM_YN = 'N'
		 GROUP BY PJWT_STCODE, B.ST_NAME
	";
	$erp_rs = $dbconn_sperp_posbank->query_rows($SQL);
	

	if($erp_rs){
		//print_r($val['ERP_CODE'].$val['PRS_NM'].'===>'.$QUERY);
		//print_r($val['ERP_CODE'].$val['PRS_NM'].'===>'.$SQL);
		//print_r($erp_rs);
		$stcode = '';
		$title = "[프로젝트] 작업일지 장기 미승인 건.";
		$content = "<div style=\"font-size:12px;\">";
		$content .= "	<div><b>7일 이전 작업일지 미승인건이 존재합니다. (웹ERP->프로젝트관리->작업일지승인관리) 에서 승인 바랍니다.</b></div>";
		$content .= "	<div style=\"padding:5px;margin:10px 0;border:1px solid #dddddd;width:50%;padding-left:20px;\">";
		foreach($erp_rs as $index=>$value){
			$content .= "		<div>".$value['ST_NAME']." : ".$value['CNT']."건</div>";
		}
		$content .= "		<div style=\"padding-top:10px;\"><a href=\"https://erp.posbank.com/?pageCode=MTI3Mzc=\" ><b>[승인하러가기]</b></a></div>";
		$content .= "	</div>";
		$content .= "</div>";

		$stcode=$val['ERP_CODE'];
		//$stcode="101126";

		//인트라넷 업무연락 보내는 함수(ERP 사원코드)
		$rs = intra_send_erp('',[$stcode],$title,$content);
		echo date("Y-m-d H:i:s")." 업무연락 발송(NEW) - ".$title . "(".$stcode.$val['PRS_NM'].") - ";
		echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";

		// 구글워크스테이션 웹훅 보내기
		$goolgework_Params = [];
		$goolgework_Params['PROGRAM'] = "sperp";
		$goolgework_Params['GU'] = "ERP";
		$goolgework_Params['ID'] = [$stcode];
		$goolgework_Params['PREVIEW'] = $title;
		$goolgework_Params['TITLE'] = "[프로젝트] 작업일지 장기 미승인 건.";
		$goolgework_Params['MESSAGE'][] = ["7일 이전 작업일지 미승인건이 존재합니다. (웹ERP->프로젝트관리->작업일지승인관리) 에서 승인 바랍니다."];
		$goolgework_Params['BUTTONS'][0]['name'] = "승인하러가기";
		$goolgework_Params['BUTTONS'][0]['link'] = "https://erp.posbank.com/?pageCode=MTI3Mzc=";

		$rs = goolgework_send($goolgework_Params);

		echo date("Y-m-d H:i:s")." 구글챗 발송(NEW) - ".$title . "(".$stcode.$val['PRS_NM'].") - ";
		echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
		
	}
	
}


echo date("Y-m-d H:i:s")." 스케줄 실행 완료\n";
//echo '**************';
//print_r($SQL);
//exit;


//echo "[".$stcode."]\n";

## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(86400, "웹ERP 프로젝트 미승인건");
?>
