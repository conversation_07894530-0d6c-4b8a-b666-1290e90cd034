#!/usr/local/bin/php -q
<?
# 운행일지 미등록 건 알림
# 매일 9시 실행
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

$dbconn = new DBController($db['posbank_intra']);
if(empty($dbconn->success)) {
	echo "[" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패입니다.";
}

$dbconn2 = new DBController($db['chrome_push']);
if(empty($dbconn2->success)) {
	echo "[" . $db['chrome_push']['host'] . "] 데이터베이스 연결 실패입니다.";
}
/**********************************************************/

echo date("Y-m-d H:i:s")." - 운행일지 미등록 알림\n";

$SQL = "SELECT count(*) FROM HOLIDAY_DATA WHERE HDATE=CURDATE()";
$holiday_chk = $dbconn->query_one($SQL);
if(!$holiday_chk){
	// 토,일요일
	if(in_array(date('w'),array("0","6"))) $holiday_chk = '1';
}
if($holiday_chk){
	echo "휴무일\n";
}else{
	$Checkday = date("Y-m-d-00-00", strtotime ("-7 day"));

	// 금일 휴가자
	$SQL = "SELECT
					group_concat(DISTINCT A.PRS_NUM)
				FROM APPV_HOLI A
					LEFT JOIN PRS_MASTER B ON A.PRS_NUM=B.PRS_NUM
				WHERE
					A.GU2='-'
					AND CURDATE() BETWEEN A.HOLI_SDATE AND A.HOLI_EDATE
					AND A.VIEW_YN<>'N'";
	$prs_num = $dbconn->query_one($SQL);


	$SQL = "select 
					HDATE,PRS_NUM,PURPOSE,PLACE_S,PLACE_E 
				from car_allocation 
				where 
					HDATE<CURDATE() 
					and STATE='1' 
					and MILEAGE_R<1";
	if($prs_num){
		$arr_prs_num = explode(",", $prs_num);
		$SQL .= " and PRS_NUM not in ('".implode("','",$arr_prs_num)."') ";
	}


//	$SQL = "select 
//					HDATE,PRS_NUM,PURPOSE,PLACE_S,PLACE_E 
//				from car_allocation 
//				where 
//					HDATE<CURDATE() 
//					and STATE='1' 
//					and PRS_NUM='20180304' limit 2";


	$arrRow = $dbconn->query_rows($SQL);

	if($arrRow){
		foreach($arrRow as $key => $row) {
			$MESSAGE = [];
			$MESSAGE[] = ["법인차량 운행일지 미등록건이 있습니다."];
			$MESSAGE[] = ["<b>운행일자 : </b><font color=#555555>".$row['HDATE']."</font>"];
			$MESSAGE[] = ["<b>사용목적 : </b><font color=#555555>".$row['PURPOSE']."</font>"];
			$MESSAGE[] = ["<b>출발지 : </b><font color=#555555>".$row['PLACE_S']."</font>"];
			$MESSAGE[] = ["<b>도착지 : </b><font color=#555555>".$row['PLACE_E']."</font>"];


			$query = "insert into goolge_webhooks (PROGRAM, GU, ID, TITLE, PREVIEW, MSG_TYPE, MESSAGE, BUTTONS, STATE, IDATE, UDATE) values (";
			$query .= "'intranet', 'trans', '[\"".$row['PRS_NUM']."\"]' ";
			$query .= ",'운행일지' ";
			$query .= ",'법인차량 운행일지 미등록건이 있습니다.' ";
			$query .= ",'1' ";
			$query .= ",'". json_encode($MESSAGE, JSON_UNESCAPED_UNICODE) . "' ";
			$query .= ",'' ";
			$query .= ",'0' ";
			$query .= ",now() ";
			$query .= ",now() ";
			$query .= ") ";
			$rs = $dbconn2->query($query);
			echo $row['PRS_NUM'] . " - " . json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
		}
	}
}



## 스케즐 처리 상황 intra DB에 저장
crontab_execution(86400, "법인차량 운행일지 미등록 알림");

echo date("Y-m-d H:i:s")." - 끝\n";
?>
