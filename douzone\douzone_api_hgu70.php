#!/usr/local/bin/php -q
<?php
/**
 * 더존 자동전표 API 연동 - 회계자료 (HGU70)
 * 기존 sp_douzon_dl_hgu70.psh를 더존 API 호출 방식으로 변환
 * PDO 드라이버 문제 해결을 위해 기존 DBController 사용
 */

// 동적 경로 설정 - Windows와 Linux 모두 지원
$ROOT_PATH = dirname(__DIR__);
$_SERVER['DOCUMENT_ROOT'] = $ROOT_PATH;
define('ROOT_PATH', $ROOT_PATH);

include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/douzone/api_config.php");
// HGU70용 간단한 API 헬퍼 클래스 (PH23과 동일)
class DouzoneApiHelper {
    private $apiUrl;
    private $authToken;
    private $companyCode;
    private $logFile;
    
    public function __construct($apiUrl, $authToken, $companyCode) {
        $this->apiUrl = $apiUrl;
        $this->authToken = $authToken;
        $this->companyCode = $companyCode;
        $this->logFile = '/home/<USER>/log/douzone_api_' . date('Ymd') . '.log';
    }
    
    private function getHashKey() {
        // API 설정에서 hash_key를 가져옴
        $api_config = getDouzoneApiConfig();
        return $api_config['hash_key'];
    }
    
    private function getCallerName() {
        $api_config = getDouzoneApiConfig();
        return $api_config['caller_name'];
    }
    
    private function getGroupSeq() {
        $api_config = getDouzoneApiConfig();
        return $api_config['group_seq'];
    }
    
    public function registerAutoVoucher($data) {
        $endpoint = '/apiproxy/api11A37';
        $url = $this->apiUrl . $endpoint;
        
        // wehago-sign 인증에 필요한 값들
        $timestamp = time();
        $transactionId = uniqid('', true);
        
        // Simple_debug.php 방식: authToken + transactionId + timestamp + endpoint
        $message = $this->authToken . $transactionId . $timestamp . $endpoint;
        $wehagoSign = base64_encode(hash_hmac('sha256', $message, $this->getHashKey(), true));
        
        // 디버깅용 로그
        $this->log("Wehago Sign Debug:");
        $this->log("- AccessToken: " . substr($this->authToken, 0, 10) . "...");
        $this->log("- TransactionId: " . $transactionId);
        $this->log("- Timestamp: " . $timestamp);
        $this->log("- URL: " . $url);
        $this->log("- Message: " . substr($message, 0, 50) . "...");
        $this->log("- WehagoSign: " . $wehagoSign);
        
        $requestData = [
            'items' => []
        ];
        
        foreach ($data as $item) {
            $requestData['items'][] = [
                'coCd' => $this->companyCode,
                'inDivCd' => $item['inDivCd'],
                'acctTy' => $item['acctTy'] ?? '3',
                'acctFg' => $item['acctFg'] ?? '1000',
                'refDt' => $item['refDt'],
                'trCd' => $item['trCd'],
                'trNm' => $item['trNm'],
                'regNb' => $item['regNb'] ?? '',
                'taxFg' => $item['taxFg'],
                'clsgAm' => $item['clsgAm'],
                'clsvAm' => $item['clsvAm'],
                'clshAm' => $item['clshAm'],
                'bankAm' => $item['bankAm'] ?? 0,
                'misuAm' => $item['misuAm'],
                'baNb' => $item['baNb'] ?? '',
                'isuDoc' => $item['isuDoc'] ?? '',
                'rmkDc' => $item['rmkDc'],
                'attrCd' => $item['attrCd'] ?? '',
                'ctDept' => $item['ctDept'] ?? '',
                'pjtCd' => $item['pjtCd'] ?? '',
                'approKey' => $item['approKey'],
                'jeonjaYn' => $item['jeonjaYn'] ?? '',
                'cardCd' => $item['cardCd'] ?? '',
                'dummy1' => $item['dummy1'] ?? '',
                'ctNb' => $item['ctNb'] ?? '',
                'dummy2' => $item['dummy2'] ?? '',
                'ctQt' => $item['ctQt'] ?? 0,
                'issNo' => $item['issNo'] ?? ''
            ];
        }
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->authToken,
            'Accept: application/json',
            'wehago-sign: ' . $wehagoSign,
            'transaction-id: ' . $transactionId,
            'timestamp: ' . $timestamp,
            'callerName: ' . $this->getCallerName(),
            'groupSeq: ' . $this->getGroupSeq()
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $this->log("CURL Error: " . $error);
            return ['success' => false, 'error' => $error];
        }
        
        if ($httpCode !== 200) {
            $this->log("HTTP Error: {$httpCode} - {$response}");
            return ['success' => false, 'error' => "HTTP {$httpCode}", 'response' => $response];
        }
        
        $result = json_decode($response, true);
        $this->log("API Response: " . $response);
        
        return ['success' => true, 'result' => $result];
    }
    
    public function logApiResult($result, $context = '') {
        $message = "[" . date('Y-m-d H:i:s') . "] {$context}: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($this->logFile, $message, FILE_APPEND);
        echo $message;
    }
    
    private function log($message) {
        $logMessage = "[" . date('Y-m-d H:i:s') . "] " . $message . "\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
    }
}

// SPERP 데이터베이스 연결
$dbconn_e = new DBController($db['sperp_posbank']);
if(empty($dbconn_e->success)) {
    echo "[" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패입니다.";
    exit;
}

// API 설정 (api_config.php에서 로드)
$api_config = getDouzoneApiConfig();

$douzone_api = new DouzoneApiHelper(
    $api_config['api_url'],
    $api_config['auth_token'],
    $api_config['company_code']
);

echo date("Y-m-d H:i:s")." - API 전송 시작 (회계자료)\\n";

// 회계자료 조회 SQL (기존 sp_douzon_dl_hgu70.psh 1-15행과 동일)
$SQL = "SELECT KEY,
        ROW_NUMBER() OVER(PARTITION BY KEY ORDER BY KEY, SNO) AS SNO,
        RCT_CODE, HDATE, CTCODE, CTNAME, CT_NAME, DAMT, CAMT, CT_LINK, CT_LINK2,
        REMA, HNO, SNO AS ORIG_SNO, HGU, DL_LINK, FNCODE, REMB, REMC, CT_CODE, MASTLINK,
        THDATE, TMSTYLE, ACSTYLE, HAMT, HVAT
    FROM (
        SELECT (A.HDATE || A.HNO) AS KEY, A.RCT_CODE, A.HDATE, A.CTCODE, A.CTNAME, B.CT_NAME, A.DAMT, A.CAMT, B.CT_LINK, B.CT_LINK2,
               A.REMA, A.HNO, A.SNO, A.HGU, A.DL_LINK, A.FNCODE, A.REMB, A.REMC, B.CT_CODE, A.MASTLINK,
               C.HDATE AS THDATE, C.TMSTYLE, C.ACSTYLE, C.HAMT, C.HVAT
        FROM DL A
        LEFT JOIN CT B ON A.CTCODE = B.CT_CODE
        LEFT JOIN TMH C ON (C.RCT_CODE = SUBSTR(A.REMB, 1, 4)
                           AND C.HDATE = SUBSTR(A.REMB, 5, 8)
                           AND C.HGU = SUBSTR(A.REMB, 13, 2)
                           AND C.HNO = SUBSTR(A.REMB, 15, 4))
        WHERE A.RCT_CODE = '1000'
          AND A.HDATE >= TO_CHAR(add_months(sysdate, -1), 'YYYYMM') || '01'
          AND A.HDATE <= TO_CHAR(last_day(sysdate), 'YYYYMMDD')
          AND A.HGU = '70'
    ) ORDER BY HDATE, HNO, HGU, ORIG_SNO";

$rows = $dbconn_e->query_rows($SQL);
$total_count = 0;
$success_count = 0;
$error_count = 0;

if($rows) {
    // 키별로 그룹화하여 API 호출
    $grouped_data = array();
    
    foreach($rows as $row) {
        $key = $row['KEY'];
        if (!isset($grouped_data[$key])) {
            $grouped_data[$key] = array();
        }
        $grouped_data[$key][] = $row;
    }
    
    foreach($grouped_data as $key => $voucher_lines) {
        $total_count++;
        
        // 이미 처리된 데이터인지 확인 (API 테스트를 위해 주석 처리)
        $first_line = $voucher_lines[0];
        /*
        if (!empty($first_line['DL_LINK'])) {
            echo "이미 처리됨: {$key}\n";
            continue;
        }
        */
        
        // API 데이터 구성
        $api_data = array();
        
        foreach($voucher_lines as $line) {
            // 차대 구분 계산 (기존 77-91행 로직)
            $drcrFg = 'D';
            if (floatval($line['DAMT']) == 0 && floatval($line['CAMT']) == 0 && 
                (isset($line['FN_LINK']) && ($line['FN_LINK'] == '25500' || $line['FN_LINK'] == '13500'))) {
                // 연계되어진것이 매출이라면
                if (substr($line['REMB'], 12, 2) == '13') {
                    $drcrFg = 'C';
                } else {
                    $drcrFg = 'D';
                }
            } else {
                $drcrFg = (floatval($line['DAMT']) == 0) ? 'C' : 'D';
            }
            
            // 금액 계산
            $amount = ($drcrFg == 'D') ? floatval($line['DAMT']) : floatval($line['CAMT']);
            
            // 세금 계산 (HGU70은 복잡한 세금 로직 있음 - 147-196행)
            $taxFg = '';
            $pjtCd = '';
            $pjtNm = '';
            $ctAm = 0;
            $ctDeal = '';
            $dealNm = '';
            
            // 부가세예수금이나 부가세대급금의 경우 (122-196행 로직)
            if (($drcrFg == 'C' && isset($line['FN_LINK']) && $line['FN_LINK'] == '25500') ||
                ($drcrFg == 'D' && isset($line['FN_LINK']) && $line['FN_LINK'] == '13500')) {
                
                $pjtCd = '1000';
                $pjtNm = '(주)포스뱅크본사';
                
                if (!empty($line['THDATE'])) {
                    $ctAm = floatval($line['HAMT']);
                } else {
                    $ctAm = floatval($line['REMC']);
                }
                
                // TMSTYLE 기반 세금 분류 (147-196행)
                switch ($line['TMSTYLE']) {
                    case 'S': // 과세
                        switch ($line['ACSTYLE']) {
                            case '1': // 일반
                                $taxFg = ($line['FN_LINK'] == '13500') ? '21' : '11';
                                $dealNm = ($line['FN_LINK'] == '13500') ? '과세매입' : '과세매출';
                                break;
                            case '2': // 신용카드
                                $taxFg = ($line['FN_LINK'] == '13500') ? '27' : '17';
                                $dealNm = ($line['FN_LINK'] == '13500') ? '카드매입' : '카드매출';
                                break;
                            case '3': // 현금영수증
                                $taxFg = ($line['FN_LINK'] == '13500') ? '28' : '31';
                                $dealNm = ($line['FN_LINK'] == '13500') ? '수입' : '현금과세';
                                break;
                            case '4': // 소매
                                $taxFg = ($line['FN_LINK'] == '13500') ? '25' : '14';
                                $dealNm = ($line['FN_LINK'] == '13500') ? '현금영수증매입' : '건별매출';
                                break;
                            default:
                                $taxFg = ($line['FN_LINK'] == '13500') ? '21' : '11';
                                $dealNm = ($line['FN_LINK'] == '13500') ? '과세매입' : '과세매출';
                                break;
                        }
                        break;
                    case 'O': // 영세
                        switch ($line['ACSTYLE']) {
                            case '4': // 소매
                                $taxFg = ($line['FN_LINK'] == '13500') ? '25' : '16';
                                $dealNm = ($line['FN_LINK'] == '13500') ? '수입' : '수출';
                                break;
                            default:
                                $taxFg = ($line['FN_LINK'] == '13500') ? '22' : '12';
                                $dealNm = ($line['FN_LINK'] == '13500') ? '영세매입' : '영세매출';
                                break;
                        }
                        break;
                    case 'Z': // 면세
                        switch ($line['ACSTYLE']) {
                            case '1': // 일반
                                $taxFg = ($line['FN_LINK'] == '13500') ? '23' : '13';
                                $dealNm = ($line['FN_LINK'] == '13500') ? '면세매입' : '면세매출';
                                break;
                            case '2': // 신용카드
                                $taxFg = ($line['FN_LINK'] == '13500') ? '23' : '18';
                                $dealNm = ($line['FN_LINK'] == '13500') ? '면세매입' : '면세카드매출';
                                break;
                            case '3': // 현금영수증
                                $taxFg = ($line['FN_LINK'] == '13500') ? '23' : '32';
                                $dealNm = ($line['FN_LINK'] == '13500') ? '면세매입' : '현금면세';
                                break;
                            default:
                                $taxFg = ($line['FN_LINK'] == '13500') ? '23' : '13';
                                $dealNm = ($line['FN_LINK'] == '13500') ? '면세매입' : '면세매출';
                                break;
                        }
                        break;
                    default:
                        $taxFg = ($line['FN_LINK'] == '13500') ? '21' : '11';
                        $dealNm = ($line['FN_LINK'] == '13500') ? '과세매입' : '과세매출';
                        break;
                }
                $ctDeal = $taxFg;
            }
            
            $api_data[] = array(
                'refDt' => $line['HDATE'],
                'trCd' => $line['CT_LINK'] ?? $line['CT_CODE'],
                'trNm' => $line['CT_NAME'] ?? $line['CTNAME'],
                'taxFg' => '',  // 빈 값으로 시도
                'clsgAm' => $amount,
                'clsvAm' => 0,
                'clshAm' => $amount,
                'misuAm' => $amount,
                'rmkDc' => $line['REMA'],
                'approKey' => $key,
                'inDivCd' => '1000',
                'acctTy' => '3', // 회계
                'acctFg' => '1000'
            );
        }
        
        if (!empty($api_data)) {
            // API 호출
            $result = $douzone_api->registerAutoVoucher($api_data);
            
            // 결과 처리
            if ($result['success']) {
                $response_data = $result['result'];
                
                if (isset($response_data['resultCode']) && $response_data['resultCode'] == '0') {
                    $success_count++;
                    
                    // 성공시 DL_LINK 업데이트
                    $sperp_cpyid = "3" . $first_line['HNO']; // HGU70은 3 prefix
                    $update_sql = "UPDATE DL 
                                  SET DL_LINK = '".$sperp_cpyid."'
                                  WHERE RCT_CODE = '1000' 
                                    AND HDATE = '".$first_line['HDATE']."'
                                    AND HNO = '".$first_line['HNO']."'
                                    AND HGU = '70'";
                    
                    $update_result = $dbconn_e->iud_query($update_sql);
                    echo "성공: {$key} - {$first_line['REMA']}\\n";
                    
                } else {
                    $error_count++;
                    echo "API 응답 오류: {$key} - " . json_encode($response_data, JSON_UNESCAPED_UNICODE) . "\\n";
                    $douzone_api->logApiResult($result, "회계자료 {$key}");
                }
                
            } else {
                $error_count++;
                echo "API 호출 실패: {$key} - {$result['error']}\\n";
                $douzone_api->logApiResult($result, "회계자료 {$key}");
            }
        }
        
        // API 호출 간격 조절
        usleep(100000); // 0.1초 대기
    }
}

echo date("Y-m-d H:i:s")." - API 전송 완료\\n";
echo "전체: {$total_count}건, 성공: {$success_count}건, 실패: {$error_count}건\\n";
echo "---------------------------\\n";

// 스케줄 처리 상황 monitor DB에 저장
crontab_execution(86400, "더존 API 전송 완료 (회계)");

?>