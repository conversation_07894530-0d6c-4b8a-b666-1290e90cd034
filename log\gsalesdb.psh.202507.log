<br>
 ----------------------------<br>
 N_DAY : 20250716<br>
 THIS_15 : 20250715<br>
 THIS_16 : 20250716<br>
 strYMD : 20250716<br>
 ----------------------------Array
(
    [0] => 
	UPDATE GSALES_DB 
		SET CLOSE_CHK = 'F' 
		WHERE (CLOSE_CHK = 'I' OR CLOSE_CHK IS NULL) AND ORDER_CHK = '1' 
	 AND SHIP_YMD != 'TBD' AND SHIP_YMD <=  TO_CHAR(TRUNC(SYSDATE, 'MM') + 14,'YYYYMMDD')
	
)
2025-07-16 05:00:02 - 0건 등록
2025-07-16 05:00:02 - 끝
