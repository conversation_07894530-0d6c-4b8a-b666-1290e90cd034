#!/usr/local/bin/php -q
<?php
/**
 * 더존 자동전표 API 연동 - 매출전표 (PH13)
 * 기존 sp_duzon_ph13_api.psh를 PHP로 변환
 * PDO 드라이버 문제 해결을 위해 MySQLi 사용
 */

// 동적 경로 설정 - Windows와 Linux 모두 지원
$ROOT_PATH = dirname(__DIR__);
$_SERVER['DOCUMENT_ROOT'] = $ROOT_PATH;
define('ROOT_PATH', $ROOT_PATH);

include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/douzone/api_config.php");
// PH13용 wehago-sign 인증 API 헬퍼 클래스
class DouzoneApiHelper {
    private $apiUrl;
    private $authToken;
    private $companyCode;
    private $logFile;
    
    public function __construct($config) {
        $this->apiUrl = $config['api_url'];
        $this->authToken = $config['auth_token'];
        $this->companyCode = $config['company_code'];
        $this->logFile = '/home/<USER>/log/douzone_api_' . date('Ymd') . '.log';
    }
    
    private function getHashKey() {
        $api_config = getDouzoneApiConfig();
        return $api_config['hash_key'];
    }
    
    private function getCallerName() {
        $api_config = getDouzoneApiConfig();
        return $api_config['caller_name'];
    }
    
    private function getGroupSeq() {
        $api_config = getDouzoneApiConfig();
        return $api_config['group_seq'];
    }
    
    public function registerAutoVoucher($data) {
        $endpoint = '/apiproxy/api11A37';
        $url = $this->apiUrl . $endpoint;
        
        // wehago-sign 인증에 필요한 값들
        $timestamp = time();
        $transactionId = uniqid('', true);
        
        // Simple_debug.php 방식: authToken + transactionId + timestamp + endpoint
        $message = $this->authToken . $transactionId . $timestamp . $endpoint;
        $wehagoSign = base64_encode(hash_hmac('sha256', $message, $this->getHashKey(), true));
        
        $requestData = [
            'items' => []
        ];
        
        foreach ($data as $item) {
            $requestData['items'][] = [
                'coCd' => $this->companyCode,
                'inDivCd' => $item['inDivCd'],
                'acctTy' => $item['acctTy'] ?? '1',
                'acctFg' => $item['acctFg'] ?? '1000',
                'refDt' => $item['refDt'],
                'trCd' => $item['trCd'],
                'trNm' => $item['trNm'],
                'regNb' => $item['regNb'] ?? '',
                'taxFg' => $item['taxFg'],
                'clsgAm' => $item['clsgAm'],
                'clsvAm' => $item['clsvAm'],
                'clshAm' => $item['clshAm'],
                'bankAm' => $item['bankAm'] ?? 0,
                'misuAm' => $item['misuAm'],
                'baNb' => $item['baNb'] ?? '',
                'isuDoc' => $item['isuDoc'] ?? '',
                'rmkDc' => $item['rmkDc'],
                'attrCd' => $item['attrCd'] ?? '',
                'ctDept' => $item['ctDept'] ?? '',
                'pjtCd' => $item['pjtCd'] ?? '',
                'approKey' => $item['approKey'],
                'jeonjaYn' => $item['jeonjaYn'] ?? '',
                'cardCd' => $item['cardCd'] ?? '',
                'dummy1' => $item['dummy1'] ?? '',
                'ctNb' => $item['ctNb'] ?? '',
                'dummy2' => $item['dummy2'] ?? '',
                'ctQt' => $item['ctQt'] ?? 0,
                'issNo' => $item['issNo'] ?? ''
            ];
        }
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->authToken,
            'Accept: application/json',
            'wehago-sign: ' . $wehagoSign,
            'transaction-id: ' . $transactionId,
            'timestamp: ' . $timestamp,
            'callerName: ' . $this->getCallerName(),
            'groupSeq: ' . $this->getGroupSeq()
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            $this->log("CURL Error: " . $error);
            return ['success' => false, 'error' => $error];
        }
        
        if ($httpCode !== 200) {
            $this->log("HTTP Error: {$httpCode} - {$response}");
            return ['success' => false, 'error' => "HTTP {$httpCode}", 'response' => $response];
        }
        
        $result = json_decode($response, true);
        $this->log("API Response: " . $response);
        
        return ['success' => true, 'result' => $result];
    }
    
    public function logApiResult($result, $context = '') {
        $message = "[" . date('Y-m-d H:i:s') . "] {$context}: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($this->logFile, $message, FILE_APPEND);
        echo $message;
    }
    
    private function log($message) {
        $logMessage = "[" . date('Y-m-d H:i:s') . "] " . $message . "\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
    }
}

// SPERP 데이터베이스 연결 (기존 DBController 사용 - PDO 드라이버 문제 없음)
$dbconn_e = new DBController($db['sperp_posbank']);
if(empty($dbconn_e->success)) {
    echo "[" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패입니다.";
    exit;
}

// API 설정 (api_config.php에서 로드)
$api_config = getDouzoneApiConfig();

$douzone_api = new DouzoneApiHelper($api_config);

echo date("Y-m-d H:i:s")." - API 전송 시작 (매출전표)\n";

// 매출 데이터 조회 SQL (기존과 동일)
$SQL = "SELECT KEY,
        ROW_NUMBER() OVER(PARTITION BY KEY ORDER BY KEY, GU2,FN_CD) AS SNO,RCT_CODE,HDATE,CT_CODE,CT_NAME,AMT,VAT,AMT+VAT CUAMT,AMT2,CT_LINK,CT_LINK2,  
        PRNAME,HNO,HGU,DZ_LINK,TMSTYLE,ACSTYLE,FN_CD,FN_LINK,SPERP_CPYID,GU1,
    CASE  WHEN GU1='11' THEN '과세매출'
            WHEN GU1='17' THEN '카드매출'
            WHEN GU1='31' THEN '현금과세'
            WHEN GU1='14' THEN '건별매출'
            WHEN GU1='16' THEN '수출'
            WHEN GU1='12' THEN '영세매출'
            WHEN GU1='13' THEN '면세매출'
            WHEN GU1='18' THEN '면세카드매출'
            WHEN GU1='32' THEN '현금면세'
    END  GU1_NAME, CHA,DECODE(CHA,'D','3','4') CHA_NAME
     FROM(
         -- 매출 금액
         SELECT (A.HDATE || A.HNO) AS KEY, A.RCT_CODE, A.HDATE, A.CT_CODE, C.CT_NAME, D.AMT, 0 VAT, 
                0 AMT2,CT_LINK,C.CT_LINK2,  
                B.PRNAME, A.HNO, A.HGU,A.DZ_LINK, A.TMSTYLE, A.ACSTYLE, D.FN_CD, D.FN_LINK,'2'|| A.HNO SPERP_CPYID,
                CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '11'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '17'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '31'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '14'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '11'
                     WHEN A.TMSTYLE='O' AND A.ACSTYLE='4' THEN '16'
                     WHEN A.TMSTYLE='O' AND A.ACSTYLE<>'4' THEN '12'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='1' THEN '13'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='2' THEN '18'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='3' THEN '32'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE>'3' THEN '13'
                END  GU1,
                CASE WHEN D.FN_CD='21107001' THEN 'C'
                     WHEN D.FN_CD='11105001' THEN 'D'
                     ELSE 'C'
                END CHA,
                '1' GU2
         FROM TMH A, TMS B, CT C,  
              ( 
               SELECT  A.TMHID, (C.BAS_OP4) AS FN_CD, nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
                       SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT
               FROM PD13 A 
                    LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE 
                    LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%' 
                    LEFT JOIN FN D ON C.BAS_OP4 = D.FN_CD1 || D.FN_CD2
               WHERE A.TMHID IS NOT NULL 
                 AND A.STATE = '8' 
                 AND SUBSTR(A.TMHID, 5,8) >= TO_CHAR(sysdate - 45, 'YYYYMMDD')
               GROUP BY A.TMHID, C.BAS_OP4,nvl(D.FN_LINK2,D.FN_LINK)
              ) D 
         WHERE A.RCT_CODE = '1000' 
           AND A.HDATE >= TO_CHAR(sysdate - 45, 'YYYYMMDD')
           AND A.TCHK <> 'Y'  
           AND A.RCTYPE = 'C'  
           AND A.HGU = '13'  
           AND A.RCT_CODE = B.RCT_CODE 
           AND A.HDATE = B.HDATE 
           AND A.HNO = B.HNO 
           AND A.HGU = B.HGU 
           AND B.SNO = 1 
           AND A.CT_CODE = C.CT_CODE (+)
           AND SUBSTR(D.TMHID, 0, 4) = A.RCT_CODE 
           AND SUBSTR(D.TMHID, 5, 8) = A.HDATE 
           AND SUBSTR(D.TMHID, 13, 2) = A.HGU 
           AND SUBSTR(D.TMHID, 15, 4) = A.HNO 
           AND (D.AMT + D.VAT) <> 0 
         
         UNION ALL
         -- 부가세예수금
         SELECT (A.HDATE || A.HNO) AS KEY, A.RCT_CODE, A.HDATE, A.CT_CODE, C.CT_NAME, 0 AMT, SUM(D.VAT) VAT, 
                 SUM(D.AMT2) AMT2,C.CT_LINK,C.CT_LINK2,  
                B.PRNAME, A.HNO, A.HGU,A.DZ_LINK, A.TMSTYLE, A.ACSTYLE, '21107001' FN_CD, '25500' FN_LINK,'2'|| A.HNO SPERP_CPYID,
                CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '11'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '17'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '31'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '14'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '11'
                     WHEN A.TMSTYLE='O' AND A.ACSTYLE='4' THEN '16'
                     WHEN A.TMSTYLE='O' AND A.ACSTYLE<>'4' THEN '12'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='1' THEN '13'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='2' THEN '18'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='3' THEN '32'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE>'3' THEN '13'
                END  GU1,
                'C' CHA,
                '2' GU2
         FROM TMH A, TMS B, CT C,  
              ( 
               SELECT  A.TMHID, (C.BAS_OP4) AS FN_CD, nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
                       SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT,SUM(A.AMT) AS AMT2
               FROM PD13 A 
                    LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE 
                    LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%' 
                    LEFT JOIN FN D ON C.BAS_OP4 = D.FN_CD1 || D.FN_CD2
               WHERE A.TMHID IS NOT NULL 
                 AND A.STATE = '8' 
                 AND SUBSTR(A.TMHID, 5,8) >= TO_CHAR(sysdate - 45, 'YYYYMMDD')
               GROUP BY A.TMHID, C.BAS_OP4,nvl(D.FN_LINK2,D.FN_LINK)
              ) D 
         WHERE A.RCT_CODE = '1000' 
           AND A.HDATE >= TO_CHAR(sysdate - 45, 'YYYYMMDD')
           AND A.TCHK <> 'Y'  
           AND A.RCTYPE = 'C'  
           AND A.HGU = '13'  
           AND A.RCT_CODE = B.RCT_CODE 
           AND A.HDATE = B.HDATE 
           AND A.HNO = B.HNO 
           AND A.HGU = B.HGU 
           AND B.SNO = 1 
           AND A.CT_CODE = C.CT_CODE (+)
           AND SUBSTR(D.TMHID, 0, 4) = A.RCT_CODE 
           AND SUBSTR(D.TMHID, 5, 8) = A.HDATE 
           AND SUBSTR(D.TMHID, 13, 2) = A.HGU 
           AND SUBSTR(D.TMHID, 15, 4) = A.HNO 
           AND (D.AMT + D.VAT) <> 0 
           GROUP BY (A.HDATE || A.HNO),A.RCT_CODE,A.HDATE, A.CT_CODE, C.CT_NAME,C.CT_LINK,C.CT_LINK2,  
                B.PRNAME, A.HNO, A.HGU, C.CT_LINK, A.DZ_LINK, A.TMSTYLE, A.ACSTYLE,'2'|| A.HNO,
                A.TMSTYLE,A.ACSTYLE
        
        UNION ALL
        -- 외상매출금
        SELECT (A.HDATE || A.HNO) AS KEY, A.RCT_CODE, A.HDATE, A.CT_CODE, C.CT_NAME,  SUM(D.AMT+D.VAT) AMT, 0 VAT, 
                0 AMT2,C.CT_LINK,C.CT_LINK2,  
                B.PRNAME, A.HNO, A.HGU,A.DZ_LINK, A.TMSTYLE, A.ACSTYLE, '11105001' FN_CD, '10800' FN_LINK,'2'|| A.HNO SPERP_CPYID,
                CASE WHEN A.TMSTYLE='S' AND A.ACSTYLE='1' THEN '11'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='2' THEN '17'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='3' THEN '31'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE='4' THEN '14'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE>'4' THEN '11'
                     WHEN A.TMSTYLE='O' AND A.ACSTYLE='4' THEN '16'
                     WHEN A.TMSTYLE='O' AND A.ACSTYLE<>'4' THEN '12'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='1' THEN '13'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='2' THEN '18'
                     WHEN A.TMSTYLE='Z' AND A.ACSTYLE='3' THEN '32'
                     WHEN A.TMSTYLE='S' AND A.ACSTYLE>'3' THEN '13'
                END  GU1,
                'D' CHA,
                '3' GU2
         FROM TMH A, TMS B, CT C,  
              ( 
               SELECT  A.TMHID, (C.BAS_OP4) AS FN_CD, nvl(D.FN_LINK2,D.FN_LINK) FN_LINK,
                       SUM(A.AMT) AS AMT, SUM(A.VAT) AS VAT
               FROM PD13 A 
                    LEFT JOIN PRKIND B ON A.PR_CODE = B.PR_CODE 
                    LEFT JOIN BAS C ON B.PR_WJS = C.BAS_CODE AND C.BAS_CODE LIKE 'C4%' 
                    LEFT JOIN FN D ON C.BAS_OP4 = D.FN_CD1 || D.FN_CD2
               WHERE A.TMHID IS NOT NULL 
                 AND A.STATE = '8' 
                 AND SUBSTR(A.TMHID, 5,8) >= TO_CHAR(sysdate - 45, 'YYYYMMDD')
               GROUP BY A.TMHID, C.BAS_OP4,nvl(D.FN_LINK2,D.FN_LINK)
              ) D 
         WHERE A.RCT_CODE = '1000' 
           AND A.HDATE >= TO_CHAR(sysdate - 45, 'YYYYMMDD')
           AND A.TCHK <> 'Y'  
           AND A.RCTYPE = 'C'  
           AND A.HGU = '13'  
           AND A.RCT_CODE = B.RCT_CODE 
           AND A.HDATE = B.HDATE 
           AND A.HNO = B.HNO 
           AND A.HGU = B.HGU 
           AND B.SNO = 1 
           AND A.CT_CODE = C.CT_CODE (+)
           AND SUBSTR(D.TMHID, 0, 4) = A.RCT_CODE 
           AND SUBSTR(D.TMHID, 5, 8) = A.HDATE 
           AND SUBSTR(D.TMHID, 13, 2) = A.HGU 
           AND SUBSTR(D.TMHID, 15, 4) = A.HNO 
           AND (D.AMT + D.VAT) <> 0 
           GROUP BY (A.HDATE || A.HNO),A.RCT_CODE,A.HDATE, A.CT_CODE, C.CT_NAME,C.CT_LINK,C.CT_LINK2,  
                B.PRNAME, A.HNO, A.HGU, C.CT_LINK, A.DZ_LINK, A.TMSTYLE, A.ACSTYLE,'2'|| A.HNO,
                A.TMSTYLE,A.ACSTYLE
    ) ORDER BY HDATE,HNO,HGU,GU2,FN_CD";

$rows = $dbconn_e->query_rows($SQL);
$total_count = 0;
$success_count = 0;
$error_count = 0;

if($rows) {
    // 키별로 그룹화하여 API 호출
    $grouped_data = array();
    
    foreach($rows as $row) {
        $key = $row['KEY'];
        if (!isset($grouped_data[$key])) {
            $grouped_data[$key] = array();
        }
        $grouped_data[$key][] = $row;
    }
    
    foreach($grouped_data as $key => $voucher_lines) {
        $total_count++;
        
        // 이미 처리된 데이터인지 확인 (API 테스트를 위해 주석 처리)
        $first_line = $voucher_lines[0];
        /*
        if (!empty($first_line['DZ_LINK'])) {
            echo "이미 처리됨: {$key}\n";
            continue;
        }
        */
        
        // API 데이터 구성
        $api_data = array();
        
        foreach($voucher_lines as $line) {
            // 부가세 계산
            $supplyAmount = floatval($line['AMT']); // 공급가액
            $vatAmount = floatval($line['VAT']);    // 부가세
            $totalAmount = floatval($line['CUAMT']);// 합계액
            
            $api_data[] = array(
                'refDt' => $line['HDATE'],
                'trCd' => $line['CT_LINK'],
                'trNm' => $line['CT_NAME'],
                'taxFg' => $line['GU1'],
                'clsgAm' => $supplyAmount,
                'clsvAm' => $vatAmount,
                'clshAm' => $totalAmount,
                'misuAm' => $totalAmount,
                'rmkDc' => $line['PRNAME'],
                'approKey' => $key,
                'inDivCd' => '1000',
                'acctTy' => '1', // 매출
                'acctFg' => '1000'
            );
        }
        
        if (!empty($api_data)) {
            // API 호출
            $result = $douzone_api->registerAutoVoucher($api_data);
            
            // 결과 처리
            if ($result['success']) {
                $response_data = $result['result'];
                
                // API 응답 구조에 따라 성공 여부 판단 (실제 API 스펙에 맞게 수정 필요)
                if (isset($response_data['resultCode']) && $response_data['resultCode'] == '0') {
                    $success_count++;
                    
                    // 성공시 DZ_LINK 업데이트 (테스트 중 주석 처리)
                    /*
                    $update_sql = "UPDATE TMH 
                                  SET DZ_LINK = '".$first_line['SPERP_CPYID']."'
                                  WHERE RCT_CODE = '1000' 
                                    AND HDATE = '".$first_line['HDATE']."'
                                    AND HNO = '".$first_line['HNO']."'
                                    AND HGU = '13'";
                    
                    $update_result = $dbconn_e->iud_query($update_sql);
                    */
                    echo "성공: {$key} - {$first_line['PRNAME']}\n";
                    
                } else {
                    $error_count++;
                    echo "API 응답 오류: {$key} - " . json_encode($response_data, JSON_UNESCAPED_UNICODE) . "\n";
                    $douzone_api->logApiResult($result, "매출전표 {$key}");
                }
                
            } else {
                $error_count++;
                echo "API 호출 실패: {$key} - {$result['error']}\n";
                $douzone_api->logApiResult($result, "매출전표 {$key}");
            }
        }
        
        // API 호출 간격 조절 (부하 방지)
        usleep(100000); // 0.1초 대기
    }
}

echo date("Y-m-d H:i:s")." - API 전송 완료\n";
echo "전체: {$total_count}건, 성공: {$success_count}건, 실패: {$error_count}건\n";
echo "---------------------------\n";

// 스케줄 처리 상황 monitor DB에 저장
crontab_execution(86400, "더존 API 전송 완료 (매출)");

?>