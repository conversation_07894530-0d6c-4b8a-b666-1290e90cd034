<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Calculation_LookupRef</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_CELL_ADDRESS" title="CELL_ADDRESS :: CELL_ADDRESS"><span class="description">CELL_ADDRESS</span><pre>CELL_ADDRESS()</pre></a></li>
<li class="method public "><a href="#method_CHOOSE" title="CHOOSE :: CHOOSE"><span class="description">CHOOSE</span><pre>CHOOSE()</pre></a></li>
<li class="method public "><a href="#method_COLUMN" title="COLUMN :: COLUMN"><span class="description">COLUMN</span><pre>COLUMN()</pre></a></li>
<li class="method public "><a href="#method_COLUMNS" title="COLUMNS :: COLUMNS"><span class="description">COLUMNS</span><pre>COLUMNS()</pre></a></li>
<li class="method public "><a href="#method_HLOOKUP" title="HLOOKUP :: HLOOKUP
The HLOOKUP function searches for value in the top-most row of lookup_array and returns the value in the same column based on the index_number."><span class="description">HLOOKUP
The HLOOKUP function searches for value in the top-most row of lookup_array and returns the value in the same column based on the index_number.</span><pre>HLOOKUP()</pre></a></li>
<li class="method public "><a href="#method_HYPERLINK" title="HYPERLINK :: HYPERLINK"><span class="description">HYPERLINK</span><pre>HYPERLINK()</pre></a></li>
<li class="method public "><a href="#method_INDEX" title="INDEX :: INDEX"><span class="description">INDEX</span><pre>INDEX()</pre></a></li>
<li class="method public "><a href="#method_INDIRECT" title="INDIRECT :: INDIRECT"><span class="description">INDIRECT</span><pre>INDIRECT()</pre></a></li>
<li class="method public "><a href="#method_LOOKUP" title="LOOKUP :: LOOKUP
The LOOKUP function searches for value either from a one-row or one-column range or from an array."><span class="description">LOOKUP
The LOOKUP function searches for value either from a one-row or one-column range or from an array.</span><pre>LOOKUP()</pre></a></li>
<li class="method public "><a href="#method_MATCH" title="MATCH :: MATCH"><span class="description">MATCH</span><pre>MATCH()</pre></a></li>
<li class="method public "><a href="#method_OFFSET" title="OFFSET :: OFFSET"><span class="description">OFFSET</span><pre>OFFSET()</pre></a></li>
<li class="method public "><a href="#method_ROW" title="ROW :: ROW"><span class="description">ROW</span><pre>ROW()</pre></a></li>
<li class="method public "><a href="#method_ROWS" title="ROWS :: ROWS"><span class="description">ROWS</span><pre>ROWS()</pre></a></li>
<li class="method public "><a href="#method_TRANSPOSE" title="TRANSPOSE :: TRANSPOSE"><span class="description">TRANSPOSE</span><pre>TRANSPOSE()</pre></a></li>
<li class="method public "><a href="#method_VLOOKUP" title="VLOOKUP :: VLOOKUP
The VLOOKUP function searches for value in the left-most column of lookup_array and returns the value in the same row based on the index_number."><span class="description">VLOOKUP
The VLOOKUP function searches for value in the left-most column of lookup_array and returns the value in the same row based on the index_number.</span><pre>VLOOKUP()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul><li class="method private "><a href="#method__vlookupSort" title="_vlookupSort :: "><span class="description">_vlookupSort()
        </span><pre>_vlookupSort()</pre></a></li></ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Calculation_LookupRef"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Calculation_LookupRef.html">PHPExcel_Calculation_LookupRef</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Calculation_LookupRef</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Calculation.html">PHPExcel_Calculation</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_CELL_ADDRESS"></a><div class="element clickable method public method_CELL_ADDRESS" data-toggle="collapse" data-target=".method_CELL_ADDRESS .collapse">
<h2>CELL_ADDRESS</h2>
<pre>CELL_ADDRESS(\row $row, \column $column, \relativity $relativity, \referenceStyle $referenceStyle, \sheetText $sheetText) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Creates a cell address as text, given specified row and column numbers.</p>

<p>Excel Function:
    =ADDRESS(row, column, [relativity], [referenceStyle], [sheetText])</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$row</h4>
<code>\row</code><p>Row number to use in the cell reference</p></div>
<div class="subelement argument">
<h4>$column</h4>
<code>\column</code><p>Column number to use in the cell reference</p></div>
<div class="subelement argument">
<h4>$relativity</h4>
<code>\relativity</code><p>Flag indicating the type of reference to return
							1 or omitted	Absolute
							2				Absolute row; relative column
							3				Relative row; absolute column
							4				Relative</p></div>
<div class="subelement argument">
<h4>$referenceStyle</h4>
<code>\referenceStyle</code><p>A logical value that specifies the A1 or R1C1 reference style.
                            TRUE or omitted     CELL_ADDRESS returns an A1-style reference
                            FALSE               CELL_ADDRESS returns an R1C1-style reference</p>
</div>
<div class="subelement argument">
<h4>$sheetText</h4>
<code>\sheetText</code><p>Optional Name of worksheet to use</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_CHOOSE"></a><div class="element clickable method public method_CHOOSE" data-toggle="collapse" data-target=".method_CHOOSE .collapse">
<h2>CHOOSE</h2>
<pre>CHOOSE() : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Uses lookup_value to return a value from the list of value arguments.
Use CHOOSE to select one of up to 254 values based on the lookup_value.</p>

<p>Excel Function:
    =CHOOSE(index_num, value1, [value2], ...)</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>The selected value</div>
</div></div>
</div>
<a id="method_COLUMN"></a><div class="element clickable method public method_COLUMN" data-toggle="collapse" data-target=".method_COLUMN .collapse">
<h2>COLUMN</h2>
<pre>COLUMN(\cellAddress $cellAddress) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the column number of the given cell reference
If the cell reference is a range of cells, COLUMN returns the column numbers of each column in the reference as a horizontal array.
If cell reference is omitted, and the function is being called through the calculation engine, then it is assumed to be the
    reference of the cell in which the COLUMN function appears; otherwise this function returns 0.</p>

<p>Excel Function:
    =COLUMN([cellAddress])</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$cellAddress</h4>
<code>\cellAddress</code><p>A reference to a range of cells for which you want the column numbers</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>or array of integer</div>
</div></div>
</div>
<a id="method_COLUMNS"></a><div class="element clickable method public method_COLUMNS" data-toggle="collapse" data-target=".method_COLUMNS .collapse">
<h2>COLUMNS</h2>
<pre>COLUMNS(\cellAddress $cellAddress) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the number of columns in an array or reference.</p>

<p>Excel Function:
    =COLUMNS(cellAddress)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$cellAddress</h4>
<code>\cellAddress</code><p>An array or array formula, or a reference to a range of cells for which you want the number of columns</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>The number of columns in cellAddress</div>
</div></div>
</div>
<a id="method_HLOOKUP"></a><div class="element clickable method public method_HLOOKUP" data-toggle="collapse" data-target=".method_HLOOKUP .collapse">
<h2>HLOOKUP
The HLOOKUP function searches for value in the top-most row of lookup_array and returns the value in the same column based on the index_number.</h2>
<pre>HLOOKUP(\lookup_value $lookup_value, \lookup_array $lookup_array, \index_number $index_number, \not_exact_match $not_exact_match) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$lookup_value</h4>
<code>\lookup_value</code><p>The value that you want to match in lookup_array</p></div>
<div class="subelement argument">
<h4>$lookup_array</h4>
<code>\lookup_array</code><p>The range of cells being searched</p></div>
<div class="subelement argument">
<h4>$index_number</h4>
<code>\index_number</code><p>The row number in table_array from which the matching value must be returned. The first row is 1.</p></div>
<div class="subelement argument">
<h4>$not_exact_match</h4>
<code>\not_exact_match</code><p>Determines if you are looking for an exact match based on lookup_value.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>The value of the found cell</div>
</div></div>
</div>
<a id="method_HYPERLINK"></a><div class="element clickable method public method_HYPERLINK" data-toggle="collapse" data-target=".method_HYPERLINK .collapse">
<h2>HYPERLINK</h2>
<pre>HYPERLINK(string $linkURL, string $displayName, \PHPExcel_Cell $pCell) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Excel Function:
    =HYPERLINK(linkURL,displayName)</p></div>
<table class="table table-bordered">
<tr>
<th>access</th>
<td>public</td>
</tr>
<tr>
<th>category</th>
<td>Logical Functions</td>
</tr>
</table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$linkURL</h4>
<code>string</code><p>Value to check, is also the value returned when no error</p></div>
<div class="subelement argument">
<h4>$displayName</h4>
<code>string</code><p>Value to return when testValue is an error condition</p></div>
<div class="subelement argument">
<h4>$pCell</h4>
<code><a href="../classes/PHPExcel_Cell.html">\PHPExcel_Cell</a></code><p>The cell to set the hyperlink in</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>The value of $displayName (or $linkURL if $displayName was blank)</div>
</div></div>
</div>
<a id="method_INDEX"></a><div class="element clickable method public method_INDEX" data-toggle="collapse" data-target=".method_INDEX .collapse">
<h2>INDEX</h2>
<pre>INDEX(\range_array $arrayValues, \row_num $rowNum, \column_num $columnNum) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Uses an index to choose a value from a reference or array</p>

<p>Excel Function:
    =INDEX(range_array, row_num, [column_num])</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$arrayValues</h4>
<code>\range_array</code><p>A range of cells or an array constant</p></div>
<div class="subelement argument">
<h4>$rowNum</h4>
<code>\row_num</code><p>The row in array from which to return a value. If row_num is omitted, column_num is required.</p></div>
<div class="subelement argument">
<h4>$columnNum</h4>
<code>\column_num</code><p>The column in array from which to return a value. If column_num is omitted, row_num is required.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>the value of a specified cell or array of cells</div>
</div></div>
</div>
<a id="method_INDIRECT"></a><div class="element clickable method public method_INDIRECT" data-toggle="collapse" data-target=".method_INDIRECT .collapse">
<h2>INDIRECT</h2>
<pre>INDIRECT(\cellAddress $cellAddress, \PHPExcel_Cell $pCell) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the reference specified by a text string.
References are immediately evaluated to display their contents.</p>

<p>Excel Function:
    =INDIRECT(cellAddress)</p>

<p>NOTE - INDIRECT() does not yet support the optional a1 parameter introduced in Excel 2010</p></div>
<table class="table table-bordered"><tr>
<th>todo</th>
<td>Support for the optional a1 parameter introduced in Excel 2010</td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$cellAddress</h4>
<code>\cellAddress</code><p>The cell address of the current cell (containing this formula)</p>
</div>
<div class="subelement argument">
<h4>$pCell</h4>
<code><a href="../classes/PHPExcel_Cell.html">\PHPExcel_Cell</a></code><p>The current cell (containing this formula)</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>The cells referenced by cellAddress</div>
</div></div>
</div>
<a id="method_LOOKUP"></a><div class="element clickable method public method_LOOKUP" data-toggle="collapse" data-target=".method_LOOKUP .collapse">
<h2>LOOKUP
The LOOKUP function searches for value either from a one-row or one-column range or from an array.</h2>
<pre>LOOKUP(\lookup_value $lookup_value, \lookup_vector $lookup_vector, \result_vector $result_vector) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$lookup_value</h4>
<code>\lookup_value</code><p>The value that you want to match in lookup_array</p></div>
<div class="subelement argument">
<h4>$lookup_vector</h4>
<code>\lookup_vector</code><p>The range of cells being searched</p></div>
<div class="subelement argument">
<h4>$result_vector</h4>
<code>\result_vector</code><p>The column from which the matching value must be returned</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>The value of the found cell</div>
</div></div>
</div>
<a id="method_MATCH"></a><div class="element clickable method public method_MATCH" data-toggle="collapse" data-target=".method_MATCH .collapse">
<h2>MATCH</h2>
<pre>MATCH(\lookup_value $lookup_value, \lookup_array $lookup_array, \match_type $match_type) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>The MATCH function searches for a specified item in a range of cells</p>

<p>Excel Function:
    =MATCH(lookup_value, lookup_array, [match_type])</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$lookup_value</h4>
<code>\lookup_value</code><p>The value that you want to match in lookup_array</p></div>
<div class="subelement argument">
<h4>$lookup_array</h4>
<code>\lookup_array</code><p>The range of cells being searched</p></div>
<div class="subelement argument">
<h4>$match_type</h4>
<code>\match_type</code><p>The number -1, 0, or 1. -1 means above, 0 means exact match, 1 means below. If match_type is 1 or -1, the list has to be ordered.</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>The relative position of the found item</div>
</div></div>
</div>
<a id="method_OFFSET"></a><div class="element clickable method public method_OFFSET" data-toggle="collapse" data-target=".method_OFFSET .collapse">
<h2>OFFSET</h2>
<pre>OFFSET(\cellAddress $cellAddress, \rows $rows, \cols $columns, \height $height, \width $width) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns a reference to a range that is a specified number of rows and columns from a cell or range of cells.
The reference that is returned can be a single cell or a range of cells. You can specify the number of rows and
the number of columns to be returned.</p>

<p>Excel Function:
    =OFFSET(cellAddress, rows, cols, [height], [width])</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$cellAddress</h4>
<code>\cellAddress</code><p>The reference from which you want to base the offset. Reference must refer to a cell or
                            range of adjacent cells; otherwise, OFFSET returns the #VALUE! error value.</p>
</div>
<div class="subelement argument">
<h4>$rows</h4>
<code>\rows</code><p>The number of rows, up or down, that you want the upper-left cell to refer to.
                            Using 5 as the rows argument specifies that the upper-left cell in the reference is
                            five rows below reference. Rows can be positive (which means below the starting reference)
                            or negative (which means above the starting reference).</p>
</div>
<div class="subelement argument">
<h4>$columns</h4>
<code>\cols</code><p>The number of columns, to the left or right, that you want the upper-left cell of the result
                            to refer to. Using 5 as the cols argument specifies that the upper-left cell in the
                            reference is five columns to the right of reference. Cols can be positive (which means
                            to the right of the starting reference) or negative (which means to the left of the
                            starting reference).</p>
</div>
<div class="subelement argument">
<h4>$height</h4>
<code>\height</code><p>The height, in number of rows, that you want the returned reference to be. Height must be a positive number.</p></div>
<div class="subelement argument">
<h4>$width</h4>
<code>\width</code><p>The width, in number of columns, that you want the returned reference to be. Width must be a positive number.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>A reference to a cell or range of cells</div>
</div></div>
</div>
<a id="method_ROW"></a><div class="element clickable method public method_ROW" data-toggle="collapse" data-target=".method_ROW .collapse">
<h2>ROW</h2>
<pre>ROW(\cellAddress $cellAddress) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the row number of the given cell reference
If the cell reference is a range of cells, ROW returns the row numbers of each row in the reference as a vertical array.
If cell reference is omitted, and the function is being called through the calculation engine, then it is assumed to be the
    reference of the cell in which the ROW function appears; otherwise this function returns 0.</p>

<p>Excel Function:
    =ROW([cellAddress])</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$cellAddress</h4>
<code>\cellAddress</code><p>A reference to a range of cells for which you want the row numbers</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>or array of integer</div>
</div></div>
</div>
<a id="method_ROWS"></a><div class="element clickable method public method_ROWS" data-toggle="collapse" data-target=".method_ROWS .collapse">
<h2>ROWS</h2>
<pre>ROWS(\cellAddress $cellAddress) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Returns the number of rows in an array or reference.</p>

<p>Excel Function:
    =ROWS(cellAddress)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$cellAddress</h4>
<code>\cellAddress</code><p>An array or array formula, or a reference to a range of cells for which you want the number of rows</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>The number of rows in cellAddress</div>
</div></div>
</div>
<a id="method_TRANSPOSE"></a><div class="element clickable method public method_TRANSPOSE" data-toggle="collapse" data-target=".method_TRANSPOSE .collapse">
<h2>TRANSPOSE</h2>
<pre>TRANSPOSE(array $matrixData) : array</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$matrixData</h4>
<code>array</code><p>A matrix of values</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>array</code>Unlike the Excel TRANSPOSE function, which will only work on a single row or column, this function will transpose a full matrix.</div>
</div></div>
</div>
<a id="method_VLOOKUP"></a><div class="element clickable method public method_VLOOKUP" data-toggle="collapse" data-target=".method_VLOOKUP .collapse">
<h2>VLOOKUP
The VLOOKUP function searches for value in the left-most column of lookup_array and returns the value in the same row based on the index_number.</h2>
<pre>VLOOKUP(\lookup_value $lookup_value, \lookup_array $lookup_array, \index_number $index_number, \not_exact_match $not_exact_match) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$lookup_value</h4>
<code>\lookup_value</code><p>The value that you want to match in lookup_array</p></div>
<div class="subelement argument">
<h4>$lookup_array</h4>
<code>\lookup_array</code><p>The range of cells being searched</p></div>
<div class="subelement argument">
<h4>$index_number</h4>
<code>\index_number</code><p>The column number in table_array from which the matching value must be returned. The first column is 1.</p></div>
<div class="subelement argument">
<h4>$not_exact_match</h4>
<code>\not_exact_match</code><p>Determines if you are looking for an exact match based on lookup_value.</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>The value of the found cell</div>
</div></div>
</div>
<a id="method__vlookupSort"></a><div class="element clickable method private method__vlookupSort" data-toggle="collapse" data-target=".method__vlookupSort .collapse">
<h2>_vlookupSort()
        </h2>
<pre>_vlookupSort($a, $b) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$a</h4></div>
<div class="subelement argument"><h4>$b</h4></div>
</div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:34Z.<br></footer></div>
</div>
</body>
</html>
