<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250601 | sEdate : 20250713<br>
<br>
 MAX_DAY  : 20250711<br>
 DIFF_DAY  : 42<br>
 20250601 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250602 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 이미 등록되어있음......<br>
 20250711 이미 등록되어있음......<br>
 20250711 이미 등록되어있음......<br>
 20250711 이미 등록되어있음......<br>
 20250711 이미 등록되어있음......<br>
 20250712 주말일 경우 패스.....<br>
 20250713 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250602 | sEdate : 20250714<br>
<br>
 MAX_DAY  : 20250711<br>
 DIFF_DAY  : 42<br>
 20250602 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250603 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250711 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 이미 등록되어있음......<br>
 20250711 이미 등록되어있음......<br>
 20250711 이미 등록되어있음......<br>
 20250711 이미 등록되어있음......<br>
 20250711 이미 등록되어있음......<br>
 20250712 주말일 경우 패스.....<br>
 20250713 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250602001<br>
 입력 SLUS_ID  : 20250602002<br>
 입력 SLUS_ID  : 20250602003<br>
 입력 SLUS_ID  : 20250602004<br>
 입력 SLUS_ID  : 20250602005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250603 | sEdate : 20250715<br>
<br>
 MAX_DAY  : 20250714<br>
 DIFF_DAY  : 42<br>
 20250603 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250604 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250714 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 이미 등록되어있음......<br>
 20250714 이미 등록되어있음......<br>
 20250714 이미 등록되어있음......<br>
 20250714 이미 등록되어있음......<br>
 20250714 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250603001<br>
 입력 SLUS_ID  : 20250603002<br>
 입력 SLUS_ID  : 20250603003<br>
 입력 SLUS_ID  : 20250603004<br>
 입력 SLUS_ID  : 20250603005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250604 | sEdate : 20250716<br>
<br>
 MAX_DAY  : 20250715<br>
 DIFF_DAY  : 42<br>
 20250604 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250605 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250715 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 이미 등록되어있음......<br>
 20250715 이미 등록되어있음......<br>
 20250715 이미 등록되어있음......<br>
 20250715 이미 등록되어있음......<br>
 20250715 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250604001<br>
 입력 SLUS_ID  : 20250604002<br>
 입력 SLUS_ID  : 20250604003<br>
 입력 SLUS_ID  : 20250604004<br>
 입력 SLUS_ID  : 20250604005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250605 | sEdate : 20250717<br>
<br>
 MAX_DAY  : 20250716<br>
 DIFF_DAY  : 42<br>
 20250605 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250606 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250716 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 이미 등록되어있음......<br>
 20250716 이미 등록되어있음......<br>
 20250716 이미 등록되어있음......<br>
 20250716 이미 등록되어있음......<br>
 20250716 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250605001<br>
 입력 SLUS_ID  : 20250605002<br>
 입력 SLUS_ID  : 20250605003<br>
 입력 SLUS_ID  : 20250605004<br>
 입력 SLUS_ID  : 20250605005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250606 | sEdate : 20250718<br>
<br>
 MAX_DAY  : 20250717<br>
 DIFF_DAY  : 42<br>
 20250606 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250607 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250717 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 이미 등록되어있음......<br>
 20250717 이미 등록되어있음......<br>
 20250717 이미 등록되어있음......<br>
 20250717 이미 등록되어있음......<br>
 20250717 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250606001<br>
 입력 SLUS_ID  : 20250606002<br>
 입력 SLUS_ID  : 20250606003<br>
 입력 SLUS_ID  : 20250606004<br>
 입력 SLUS_ID  : 20250606005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250607 | sEdate : 20250719<br>
<br>
 MAX_DAY  : 20250718<br>
 DIFF_DAY  : 42<br>
 20250607 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250608 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 이미 등록되어있음......<br>
 20250718 이미 등록되어있음......<br>
 20250718 이미 등록되어있음......<br>
 20250718 이미 등록되어있음......<br>
 20250718 이미 등록되어있음......<br>
 20250719 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250608 | sEdate : 20250720<br>
<br>
 MAX_DAY  : 20250718<br>
 DIFF_DAY  : 42<br>
 20250608 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250609 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 이미 등록되어있음......<br>
 20250718 이미 등록되어있음......<br>
 20250718 이미 등록되어있음......<br>
 20250718 이미 등록되어있음......<br>
 20250718 이미 등록되어있음......<br>
 20250719 주말일 경우 패스.....<br>
 20250720 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250609 | sEdate : 20250721<br>
<br>
 MAX_DAY  : 20250718<br>
 DIFF_DAY  : 42<br>
 20250609 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250610 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250718 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 이미 등록되어있음......<br>
 20250718 이미 등록되어있음......<br>
 20250718 이미 등록되어있음......<br>
 20250718 이미 등록되어있음......<br>
 20250718 이미 등록되어있음......<br>
 20250719 주말일 경우 패스.....<br>
 20250720 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250609001<br>
 입력 SLUS_ID  : 20250609002<br>
 입력 SLUS_ID  : 20250609003<br>
 입력 SLUS_ID  : 20250609004<br>
 입력 SLUS_ID  : 20250609005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250610 | sEdate : 20250722<br>
<br>
 MAX_DAY  : 20250721<br>
 DIFF_DAY  : 42<br>
 20250610 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250611 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250721 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 이미 등록되어있음......<br>
 20250721 이미 등록되어있음......<br>
 20250721 이미 등록되어있음......<br>
 20250721 이미 등록되어있음......<br>
 20250721 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250610001<br>
 입력 SLUS_ID  : 20250610002<br>
 입력 SLUS_ID  : 20250610003<br>
 입력 SLUS_ID  : 20250610004<br>
 입력 SLUS_ID  : 20250610005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250611 | sEdate : 20250723<br>
<br>
 MAX_DAY  : 20250722<br>
 DIFF_DAY  : 42<br>
 20250611 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250612 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250722 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 이미 등록되어있음......<br>
 20250722 이미 등록되어있음......<br>
 20250722 이미 등록되어있음......<br>
 20250722 이미 등록되어있음......<br>
 20250722 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250611001<br>
 입력 SLUS_ID  : 20250611002<br>
 입력 SLUS_ID  : 20250611003<br>
 입력 SLUS_ID  : 20250611004<br>
 입력 SLUS_ID  : 20250611005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250612 | sEdate : 20250724<br>
<br>
 MAX_DAY  : 20250723<br>
 DIFF_DAY  : 42<br>
 20250612 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250613 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250723 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 이미 등록되어있음......<br>
 20250723 이미 등록되어있음......<br>
 20250723 이미 등록되어있음......<br>
 20250723 이미 등록되어있음......<br>
 20250723 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250612001<br>
 입력 SLUS_ID  : 20250612002<br>
 입력 SLUS_ID  : 20250612003<br>
 입력 SLUS_ID  : 20250612004<br>
 입력 SLUS_ID  : 20250612005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250613 | sEdate : 20250725<br>
<br>
 MAX_DAY  : 20250724<br>
 DIFF_DAY  : 42<br>
 20250613 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250614 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250724 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 이미 등록되어있음......<br>
 20250724 이미 등록되어있음......<br>
 20250724 이미 등록되어있음......<br>
 20250724 이미 등록되어있음......<br>
 20250724 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250613001<br>
 입력 SLUS_ID  : 20250613002<br>
 입력 SLUS_ID  : 20250613003<br>
 입력 SLUS_ID  : 20250613004<br>
 입력 SLUS_ID  : 20250613005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250614 | sEdate : 20250726<br>
<br>
 MAX_DAY  : 20250725<br>
 DIFF_DAY  : 42<br>
 20250614 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250615 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 이미 등록되어있음......<br>
 20250725 이미 등록되어있음......<br>
 20250725 이미 등록되어있음......<br>
 20250725 이미 등록되어있음......<br>
 20250725 이미 등록되어있음......<br>
 20250726 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250615 | sEdate : 20250727<br>
<br>
 MAX_DAY  : 20250725<br>
 DIFF_DAY  : 42<br>
 20250615 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250616 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 이미 등록되어있음......<br>
 20250725 이미 등록되어있음......<br>
 20250725 이미 등록되어있음......<br>
 20250725 이미 등록되어있음......<br>
 20250725 이미 등록되어있음......<br>
 20250726 주말일 경우 패스.....<br>
 20250727 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250616 | sEdate : 20250728<br>
<br>
 MAX_DAY  : 20250725<br>
 DIFF_DAY  : 42<br>
 20250616 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250617 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250725 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 이미 등록되어있음......<br>
 20250725 이미 등록되어있음......<br>
 20250725 이미 등록되어있음......<br>
 20250725 이미 등록되어있음......<br>
 20250725 이미 등록되어있음......<br>
 20250726 주말일 경우 패스.....<br>
 20250727 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250616001<br>
 입력 SLUS_ID  : 20250616002<br>
 입력 SLUS_ID  : 20250616003<br>
 입력 SLUS_ID  : 20250616004<br>
 입력 SLUS_ID  : 20250616005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250617 | sEdate : 20250729<br>
<br>
 MAX_DAY  : 20250728<br>
 DIFF_DAY  : 42<br>
 20250617 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250618 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250728 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 이미 등록되어있음......<br>
 20250728 이미 등록되어있음......<br>
 20250728 이미 등록되어있음......<br>
 20250728 이미 등록되어있음......<br>
 20250728 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250617001<br>
 입력 SLUS_ID  : 20250617002<br>
 입력 SLUS_ID  : 20250617003<br>
 입력 SLUS_ID  : 20250617004<br>
 입력 SLUS_ID  : 20250617005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250618 | sEdate : 20250730<br>
<br>
 MAX_DAY  : 20250729<br>
 DIFF_DAY  : 42<br>
 20250618 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250619 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250729 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 이미 등록되어있음......<br>
 20250729 이미 등록되어있음......<br>
 20250729 이미 등록되어있음......<br>
 20250729 이미 등록되어있음......<br>
 20250729 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250618001<br>
 입력 SLUS_ID  : 20250618002<br>
 입력 SLUS_ID  : 20250618003<br>
 입력 SLUS_ID  : 20250618004<br>
 입력 SLUS_ID  : 20250618005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250619 | sEdate : 20250731<br>
<br>
 MAX_DAY  : 20250730<br>
 DIFF_DAY  : 42<br>
 20250619 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250620 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250730 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 이미 등록되어있음......<br>
 20250730 이미 등록되어있음......<br>
 20250730 이미 등록되어있음......<br>
 20250730 이미 등록되어있음......<br>
 20250730 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250619001<br>
 입력 SLUS_ID  : 20250619002<br>
 입력 SLUS_ID  : 20250619003<br>
 입력 SLUS_ID  : 20250619004<br>
 입력 SLUS_ID  : 20250619005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250620 | sEdate : 20250801<br>
<br>
 MAX_DAY  : 20250731<br>
 DIFF_DAY  : 42<br>
 20250620 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250621 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250731 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 이미 등록되어있음......<br>
 20250731 이미 등록되어있음......<br>
 20250731 이미 등록되어있음......<br>
 20250731 이미 등록되어있음......<br>
 20250731 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250620001<br>
 입력 SLUS_ID  : 20250620002<br>
 입력 SLUS_ID  : 20250620003<br>
 입력 SLUS_ID  : 20250620004<br>
 입력 SLUS_ID  : 20250620005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250621 | sEdate : 20250802<br>
<br>
 MAX_DAY  : 20250801<br>
 DIFF_DAY  : 42<br>
 20250621 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250622 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 이미 등록되어있음......<br>
 20250801 이미 등록되어있음......<br>
 20250801 이미 등록되어있음......<br>
 20250801 이미 등록되어있음......<br>
 20250801 이미 등록되어있음......<br>
 20250802 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250622 | sEdate : 20250803<br>
<br>
 MAX_DAY  : 20250801<br>
 DIFF_DAY  : 42<br>
 20250622 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250623 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 이미 등록되어있음......<br>
 20250801 이미 등록되어있음......<br>
 20250801 이미 등록되어있음......<br>
 20250801 이미 등록되어있음......<br>
 20250801 이미 등록되어있음......<br>
 20250802 주말일 경우 패스.....<br>
 20250803 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250623 | sEdate : 20250804<br>
<br>
 MAX_DAY  : 20250801<br>
 DIFF_DAY  : 42<br>
 20250623 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250624 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250801 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 이미 등록되어있음......<br>
 20250801 이미 등록되어있음......<br>
 20250801 이미 등록되어있음......<br>
 20250801 이미 등록되어있음......<br>
 20250801 이미 등록되어있음......<br>
 20250802 주말일 경우 패스.....<br>
 20250803 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250623001<br>
 입력 SLUS_ID  : 20250623002<br>
 입력 SLUS_ID  : 20250623003<br>
 입력 SLUS_ID  : 20250623004<br>
 입력 SLUS_ID  : 20250623005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250624 | sEdate : 20250805<br>
<br>
 MAX_DAY  : 20250804<br>
 DIFF_DAY  : 42<br>
 20250624 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250625 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250804 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 이미 등록되어있음......<br>
 20250804 이미 등록되어있음......<br>
 20250804 이미 등록되어있음......<br>
 20250804 이미 등록되어있음......<br>
 20250804 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250624001<br>
 입력 SLUS_ID  : 20250624002<br>
 입력 SLUS_ID  : 20250624003<br>
 입력 SLUS_ID  : 20250624004<br>
 입력 SLUS_ID  : 20250624005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250625 | sEdate : 20250806<br>
<br>
 MAX_DAY  : 20250805<br>
 DIFF_DAY  : 42<br>
 20250625 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250626 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250805 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 이미 등록되어있음......<br>
 20250805 이미 등록되어있음......<br>
 20250805 이미 등록되어있음......<br>
 20250805 이미 등록되어있음......<br>
 20250805 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250625001<br>
 입력 SLUS_ID  : 20250625002<br>
 입력 SLUS_ID  : 20250625003<br>
 입력 SLUS_ID  : 20250625004<br>
 입력 SLUS_ID  : 20250625005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250626 | sEdate : 20250807<br>
<br>
 MAX_DAY  : 20250806<br>
 DIFF_DAY  : 42<br>
 20250626 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250627 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250806 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 이미 등록되어있음......<br>
 20250806 이미 등록되어있음......<br>
 20250806 이미 등록되어있음......<br>
 20250806 이미 등록되어있음......<br>
 20250806 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250626001<br>
 입력 SLUS_ID  : 20250626002<br>
 입력 SLUS_ID  : 20250626003<br>
 입력 SLUS_ID  : 20250626004<br>
 입력 SLUS_ID  : 20250626005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250627 | sEdate : 20250808<br>
<br>
 MAX_DAY  : 20250807<br>
 DIFF_DAY  : 42<br>
 20250627 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250628 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250807 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 이미 등록되어있음......<br>
 20250807 이미 등록되어있음......<br>
 20250807 이미 등록되어있음......<br>
 20250807 이미 등록되어있음......<br>
 20250807 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250627001<br>
 입력 SLUS_ID  : 20250627002<br>
 입력 SLUS_ID  : 20250627003<br>
 입력 SLUS_ID  : 20250627004<br>
 입력 SLUS_ID  : 20250627005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250628 | sEdate : 20250809<br>
<br>
 MAX_DAY  : 20250808<br>
 DIFF_DAY  : 42<br>
 20250628 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250629 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 이미 등록되어있음......<br>
 20250808 이미 등록되어있음......<br>
 20250808 이미 등록되어있음......<br>
 20250808 이미 등록되어있음......<br>
 20250808 이미 등록되어있음......<br>
 20250809 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250629 | sEdate : 20250810<br>
<br>
 MAX_DAY  : 20250808<br>
 DIFF_DAY  : 42<br>
 20250629 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250630 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 이미 등록되어있음......<br>
 20250808 이미 등록되어있음......<br>
 20250808 이미 등록되어있음......<br>
 20250808 이미 등록되어있음......<br>
 20250808 이미 등록되어있음......<br>
 20250809 주말일 경우 패스.....<br>
 20250810 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250630 | sEdate : 20250811<br>
<br>
 MAX_DAY  : 20250808<br>
 DIFF_DAY  : 42<br>
 20250630 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250701 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250702 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250703 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250704 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250705 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250706 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250707 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250708 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250709 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250710 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250711 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250712 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250713 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250714 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250715 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250716 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250717 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250718 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250719 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250720 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250721 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250722 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250723 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250724 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250725 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250726 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250727 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250728 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250729 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250730 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250731 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250801 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250808 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 이미 등록되어있음......<br>
 20250808 이미 등록되어있음......<br>
 20250808 이미 등록되어있음......<br>
 20250808 이미 등록되어있음......<br>
 20250808 이미 등록되어있음......<br>
 20250809 주말일 경우 패스.....<br>
 20250810 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250630001<br>
 입력 SLUS_ID  : 20250630002<br>
 입력 SLUS_ID  : 20250630003<br>
 입력 SLUS_ID  : 20250630004<br>
 입력 SLUS_ID  : 20250630005