<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_OLERead</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_getStream" title="getStream :: Extract binary stream data"><span class="description">Extract binary stream data</span><pre>getStream()</pre></a></li>
<li class="method public "><a href="#method_read" title="read :: Read the file"><span class="description">Read the file</span><pre>read()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="method private "><a href="#method__GetInt4d" title="_GetInt4d :: Read 4 bytes of data at specified position"><span class="description">Read 4 bytes of data at specified position</span><pre>_GetInt4d()</pre></a></li>
<li class="method private "><a href="#method__readData" title="_readData :: Read a standard stream (by joining sectors using information from SAT)"><span class="description">Read a standard stream (by joining sectors using information from SAT)</span><pre>_readData()</pre></a></li>
<li class="method private "><a href="#method__readPropertySets" title="_readPropertySets :: Read entries in the directory stream."><span class="description">Read entries in the directory stream.</span><pre>_readPropertySets()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul>
<li class="property public "><a href="#property_documentSummaryInformation" title="$documentSummaryInformation :: "><span class="description"></span><pre>$documentSummaryInformation</pre></a></li>
<li class="property public "><a href="#property_summaryInformation" title="$summaryInformation :: "><span class="description"></span><pre>$summaryInformation</pre></a></li>
<li class="property public "><a href="#property_wrkbook" title="$wrkbook :: "><span class="description"></span><pre>$wrkbook</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul><li class="property private "><a href="#property_data" title="$data :: "><span class="description"></span><pre>$data</pre></a></li></ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_BIG_BLOCK_DEPOT_BLOCKS_POS" title="BIG_BLOCK_DEPOT_BLOCKS_POS :: "><span class="description">BIG_BLOCK_DEPOT_BLOCKS_POS</span><pre>BIG_BLOCK_DEPOT_BLOCKS_POS</pre></a></li>
<li class="constant  "><a href="#constant_BIG_BLOCK_SIZE" title="BIG_BLOCK_SIZE :: "><span class="description">BIG_BLOCK_SIZE</span><pre>BIG_BLOCK_SIZE</pre></a></li>
<li class="constant  "><a href="#constant_EXTENSION_BLOCK_POS" title="EXTENSION_BLOCK_POS :: "><span class="description">EXTENSION_BLOCK_POS</span><pre>EXTENSION_BLOCK_POS</pre></a></li>
<li class="constant  "><a href="#constant_IDENTIFIER_OLE" title="IDENTIFIER_OLE :: "><span class="description">IDENTIFIER_OLE</span><pre>IDENTIFIER_OLE</pre></a></li>
<li class="constant  "><a href="#constant_NUM_BIG_BLOCK_DEPOT_BLOCKS_POS" title="NUM_BIG_BLOCK_DEPOT_BLOCKS_POS :: "><span class="description">NUM_BIG_BLOCK_DEPOT_BLOCKS_POS</span><pre>NUM_BIG_BLOCK_DEPOT_BLOCKS_POS</pre></a></li>
<li class="constant  "><a href="#constant_NUM_EXTENSION_BLOCK_POS" title="NUM_EXTENSION_BLOCK_POS :: "><span class="description">NUM_EXTENSION_BLOCK_POS</span><pre>NUM_EXTENSION_BLOCK_POS</pre></a></li>
<li class="constant  "><a href="#constant_PROPERTY_STORAGE_BLOCK_SIZE" title="PROPERTY_STORAGE_BLOCK_SIZE :: "><span class="description">PROPERTY_STORAGE_BLOCK_SIZE</span><pre>PROPERTY_STORAGE_BLOCK_SIZE</pre></a></li>
<li class="constant  "><a href="#constant_ROOT_START_BLOCK_POS" title="ROOT_START_BLOCK_POS :: "><span class="description">ROOT_START_BLOCK_POS</span><pre>ROOT_START_BLOCK_POS</pre></a></li>
<li class="constant  "><a href="#constant_SIZE_OF_NAME_POS" title="SIZE_OF_NAME_POS :: "><span class="description">SIZE_OF_NAME_POS</span><pre>SIZE_OF_NAME_POS</pre></a></li>
<li class="constant  "><a href="#constant_SIZE_POS" title="SIZE_POS :: "><span class="description">SIZE_POS</span><pre>SIZE_POS</pre></a></li>
<li class="constant  "><a href="#constant_SMALL_BLOCK_DEPOT_BLOCK_POS" title="SMALL_BLOCK_DEPOT_BLOCK_POS :: "><span class="description">SMALL_BLOCK_DEPOT_BLOCK_POS</span><pre>SMALL_BLOCK_DEPOT_BLOCK_POS</pre></a></li>
<li class="constant  "><a href="#constant_SMALL_BLOCK_SIZE" title="SMALL_BLOCK_SIZE :: "><span class="description">SMALL_BLOCK_SIZE</span><pre>SMALL_BLOCK_SIZE</pre></a></li>
<li class="constant  "><a href="#constant_SMALL_BLOCK_THRESHOLD" title="SMALL_BLOCK_THRESHOLD :: "><span class="description">SMALL_BLOCK_THRESHOLD</span><pre>SMALL_BLOCK_THRESHOLD</pre></a></li>
<li class="constant  "><a href="#constant_START_BLOCK_POS" title="START_BLOCK_POS :: "><span class="description">START_BLOCK_POS</span><pre>START_BLOCK_POS</pre></a></li>
<li class="constant  "><a href="#constant_TYPE_POS" title="TYPE_POS :: "><span class="description">TYPE_POS</span><pre>TYPE_POS</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_OLERead"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_OLERead.html">PHPExcel_Shared_OLERead</a>
</li>
</ul>
<div class="element class"><div class="details">
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_getStream"></a><div class="element clickable method public method_getStream" data-toggle="collapse" data-target=".method_getStream .collapse">
<h2>Extract binary stream data</h2>
<pre>getStream($stream) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$stream</h4></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_read"></a><div class="element clickable method public method_read" data-toggle="collapse" data-target=".method_read .collapse">
<h2>Read the file</h2>
<pre>read($sFileName) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$sFileName</h4><p>string Filename</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Reader_Exception.html">\PHPExcel_Reader_Exception</a></code></th>
<td></td>
</tr></table>
</div></div>
</div>
<a id="method__GetInt4d"></a><div class="element clickable method private method__GetInt4d" data-toggle="collapse" data-target=".method__GetInt4d .collapse">
<h2>Read 4 bytes of data at specified position</h2>
<pre>_GetInt4d(string $data, int $pos) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$data</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$pos</h4>
<code>int</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method__readData"></a><div class="element clickable method private method__readData" data-toggle="collapse" data-target=".method__readData .collapse">
<h2>Read a standard stream (by joining sectors using information from SAT)</h2>
<pre>_readData(int $bl) : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$bl</h4>
<code>int</code><p>Sector ID where the stream starts</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Data for standard stream</div>
</div></div>
</div>
<a id="method__readPropertySets"></a><div class="element clickable method private method__readPropertySets" data-toggle="collapse" data-target=".method__readPropertySets .collapse">
<h2>Read entries in the directory stream.</h2>
<pre>_readPropertySets() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property_documentSummaryInformation"> </a><div class="element clickable property public property_documentSummaryInformation" data-toggle="collapse" data-target=".property_documentSummaryInformation .collapse">
<h2></h2>
<pre>$documentSummaryInformation </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_summaryInformation"> </a><div class="element clickable property public property_summaryInformation" data-toggle="collapse" data-target=".property_summaryInformation .collapse">
<h2></h2>
<pre>$summaryInformation </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_wrkbook"> </a><div class="element clickable property public property_wrkbook" data-toggle="collapse" data-target=".property_wrkbook .collapse">
<h2></h2>
<pre>$wrkbook </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_data"> </a><div class="element clickable property private property_data" data-toggle="collapse" data-target=".property_data .collapse">
<h2></h2>
<pre>$data </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_BIG_BLOCK_DEPOT_BLOCKS_POS"> </a><div class="element clickable constant  constant_BIG_BLOCK_DEPOT_BLOCKS_POS" data-toggle="collapse" data-target=".constant_BIG_BLOCK_DEPOT_BLOCKS_POS .collapse">
<h2>BIG_BLOCK_DEPOT_BLOCKS_POS</h2>
<pre>BIG_BLOCK_DEPOT_BLOCKS_POS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_BIG_BLOCK_SIZE"> </a><div class="element clickable constant  constant_BIG_BLOCK_SIZE" data-toggle="collapse" data-target=".constant_BIG_BLOCK_SIZE .collapse">
<h2>BIG_BLOCK_SIZE</h2>
<pre>BIG_BLOCK_SIZE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_EXTENSION_BLOCK_POS"> </a><div class="element clickable constant  constant_EXTENSION_BLOCK_POS" data-toggle="collapse" data-target=".constant_EXTENSION_BLOCK_POS .collapse">
<h2>EXTENSION_BLOCK_POS</h2>
<pre>EXTENSION_BLOCK_POS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_IDENTIFIER_OLE"> </a><div class="element clickable constant  constant_IDENTIFIER_OLE" data-toggle="collapse" data-target=".constant_IDENTIFIER_OLE .collapse">
<h2>IDENTIFIER_OLE</h2>
<pre>IDENTIFIER_OLE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_NUM_BIG_BLOCK_DEPOT_BLOCKS_POS"> </a><div class="element clickable constant  constant_NUM_BIG_BLOCK_DEPOT_BLOCKS_POS" data-toggle="collapse" data-target=".constant_NUM_BIG_BLOCK_DEPOT_BLOCKS_POS .collapse">
<h2>NUM_BIG_BLOCK_DEPOT_BLOCKS_POS</h2>
<pre>NUM_BIG_BLOCK_DEPOT_BLOCKS_POS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_NUM_EXTENSION_BLOCK_POS"> </a><div class="element clickable constant  constant_NUM_EXTENSION_BLOCK_POS" data-toggle="collapse" data-target=".constant_NUM_EXTENSION_BLOCK_POS .collapse">
<h2>NUM_EXTENSION_BLOCK_POS</h2>
<pre>NUM_EXTENSION_BLOCK_POS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_PROPERTY_STORAGE_BLOCK_SIZE"> </a><div class="element clickable constant  constant_PROPERTY_STORAGE_BLOCK_SIZE" data-toggle="collapse" data-target=".constant_PROPERTY_STORAGE_BLOCK_SIZE .collapse">
<h2>PROPERTY_STORAGE_BLOCK_SIZE</h2>
<pre>PROPERTY_STORAGE_BLOCK_SIZE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_ROOT_START_BLOCK_POS"> </a><div class="element clickable constant  constant_ROOT_START_BLOCK_POS" data-toggle="collapse" data-target=".constant_ROOT_START_BLOCK_POS .collapse">
<h2>ROOT_START_BLOCK_POS</h2>
<pre>ROOT_START_BLOCK_POS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_SIZE_OF_NAME_POS"> </a><div class="element clickable constant  constant_SIZE_OF_NAME_POS" data-toggle="collapse" data-target=".constant_SIZE_OF_NAME_POS .collapse">
<h2>SIZE_OF_NAME_POS</h2>
<pre>SIZE_OF_NAME_POS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_SIZE_POS"> </a><div class="element clickable constant  constant_SIZE_POS" data-toggle="collapse" data-target=".constant_SIZE_POS .collapse">
<h2>SIZE_POS</h2>
<pre>SIZE_POS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_SMALL_BLOCK_DEPOT_BLOCK_POS"> </a><div class="element clickable constant  constant_SMALL_BLOCK_DEPOT_BLOCK_POS" data-toggle="collapse" data-target=".constant_SMALL_BLOCK_DEPOT_BLOCK_POS .collapse">
<h2>SMALL_BLOCK_DEPOT_BLOCK_POS</h2>
<pre>SMALL_BLOCK_DEPOT_BLOCK_POS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_SMALL_BLOCK_SIZE"> </a><div class="element clickable constant  constant_SMALL_BLOCK_SIZE" data-toggle="collapse" data-target=".constant_SMALL_BLOCK_SIZE .collapse">
<h2>SMALL_BLOCK_SIZE</h2>
<pre>SMALL_BLOCK_SIZE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_SMALL_BLOCK_THRESHOLD"> </a><div class="element clickable constant  constant_SMALL_BLOCK_THRESHOLD" data-toggle="collapse" data-target=".constant_SMALL_BLOCK_THRESHOLD .collapse">
<h2>SMALL_BLOCK_THRESHOLD</h2>
<pre>SMALL_BLOCK_THRESHOLD </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_START_BLOCK_POS"> </a><div class="element clickable constant  constant_START_BLOCK_POS" data-toggle="collapse" data-target=".constant_START_BLOCK_POS .collapse">
<h2>START_BLOCK_POS</h2>
<pre>START_BLOCK_POS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TYPE_POS"> </a><div class="element clickable constant  constant_TYPE_POS" data-toggle="collapse" data-target=".constant_TYPE_POS .collapse">
<h2>TYPE_POS</h2>
<pre>TYPE_POS </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div></div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:37Z.<br></footer></div>
</div>
</body>
</html>
