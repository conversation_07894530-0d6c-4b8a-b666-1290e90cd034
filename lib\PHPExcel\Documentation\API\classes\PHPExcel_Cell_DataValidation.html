<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Cell_DataValidation</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___clone" title="__clone :: Implement PHP __clone to create a deep clone, not just a shallow copy."><span class="description">Implement PHP __clone to create a deep clone, not just a shallow copy.</span><pre>__clone()</pre></a></li>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_Cell_DataValidation"><span class="description">Create a new PHPExcel_Cell_DataValidation</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_getAllowBlank" title="getAllowBlank :: Get Allow Blank"><span class="description">Get Allow Blank</span><pre>getAllowBlank()</pre></a></li>
<li class="method public "><a href="#method_getError" title="getError :: Get Error"><span class="description">Get Error</span><pre>getError()</pre></a></li>
<li class="method public "><a href="#method_getErrorStyle" title="getErrorStyle :: Get Error style"><span class="description">Get Error style</span><pre>getErrorStyle()</pre></a></li>
<li class="method public "><a href="#method_getErrorTitle" title="getErrorTitle :: Get Error title"><span class="description">Get Error title</span><pre>getErrorTitle()</pre></a></li>
<li class="method public "><a href="#method_getFormula1" title="getFormula1 :: Get Formula 1"><span class="description">Get Formula 1</span><pre>getFormula1()</pre></a></li>
<li class="method public "><a href="#method_getFormula2" title="getFormula2 :: Get Formula 2"><span class="description">Get Formula 2</span><pre>getFormula2()</pre></a></li>
<li class="method public "><a href="#method_getHashCode" title="getHashCode :: Get hash code"><span class="description">Get hash code</span><pre>getHashCode()</pre></a></li>
<li class="method public "><a href="#method_getOperator" title="getOperator :: Get Operator"><span class="description">Get Operator</span><pre>getOperator()</pre></a></li>
<li class="method public "><a href="#method_getPrompt" title="getPrompt :: Get Prompt"><span class="description">Get Prompt</span><pre>getPrompt()</pre></a></li>
<li class="method public "><a href="#method_getPromptTitle" title="getPromptTitle :: Get Prompt title"><span class="description">Get Prompt title</span><pre>getPromptTitle()</pre></a></li>
<li class="method public "><a href="#method_getShowDropDown" title="getShowDropDown :: Get Show DropDown"><span class="description">Get Show DropDown</span><pre>getShowDropDown()</pre></a></li>
<li class="method public "><a href="#method_getShowErrorMessage" title="getShowErrorMessage :: Get Show ErrorMessage"><span class="description">Get Show ErrorMessage</span><pre>getShowErrorMessage()</pre></a></li>
<li class="method public "><a href="#method_getShowInputMessage" title="getShowInputMessage :: Get Show InputMessage"><span class="description">Get Show InputMessage</span><pre>getShowInputMessage()</pre></a></li>
<li class="method public "><a href="#method_getType" title="getType :: Get Type"><span class="description">Get Type</span><pre>getType()</pre></a></li>
<li class="method public "><a href="#method_setAllowBlank" title="setAllowBlank :: Set Allow Blank"><span class="description">Set Allow Blank</span><pre>setAllowBlank()</pre></a></li>
<li class="method public "><a href="#method_setError" title="setError :: Set Error"><span class="description">Set Error</span><pre>setError()</pre></a></li>
<li class="method public "><a href="#method_setErrorStyle" title="setErrorStyle :: Set Error style"><span class="description">Set Error style</span><pre>setErrorStyle()</pre></a></li>
<li class="method public "><a href="#method_setErrorTitle" title="setErrorTitle :: Set Error title"><span class="description">Set Error title</span><pre>setErrorTitle()</pre></a></li>
<li class="method public "><a href="#method_setFormula1" title="setFormula1 :: Set Formula 1"><span class="description">Set Formula 1</span><pre>setFormula1()</pre></a></li>
<li class="method public "><a href="#method_setFormula2" title="setFormula2 :: Set Formula 2"><span class="description">Set Formula 2</span><pre>setFormula2()</pre></a></li>
<li class="method public "><a href="#method_setOperator" title="setOperator :: Set Operator"><span class="description">Set Operator</span><pre>setOperator()</pre></a></li>
<li class="method public "><a href="#method_setPrompt" title="setPrompt :: Set Prompt"><span class="description">Set Prompt</span><pre>setPrompt()</pre></a></li>
<li class="method public "><a href="#method_setPromptTitle" title="setPromptTitle :: Set Prompt title"><span class="description">Set Prompt title</span><pre>setPromptTitle()</pre></a></li>
<li class="method public "><a href="#method_setShowDropDown" title="setShowDropDown :: Set Show DropDown"><span class="description">Set Show DropDown</span><pre>setShowDropDown()</pre></a></li>
<li class="method public "><a href="#method_setShowErrorMessage" title="setShowErrorMessage :: Set Show ErrorMessage"><span class="description">Set Show ErrorMessage</span><pre>setShowErrorMessage()</pre></a></li>
<li class="method public "><a href="#method_setShowInputMessage" title="setShowInputMessage :: Set Show InputMessage"><span class="description">Set Show InputMessage</span><pre>setShowInputMessage()</pre></a></li>
<li class="method public "><a href="#method_setType" title="setType :: Set Type"><span class="description">Set Type</span><pre>setType()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__allowBlank" title="$_allowBlank :: Allow Blank"><span class="description"></span><pre>$_allowBlank</pre></a></li>
<li class="property private "><a href="#property__error" title="$_error :: Error"><span class="description"></span><pre>$_error</pre></a></li>
<li class="property private "><a href="#property__errorStyle" title="$_errorStyle :: Error style"><span class="description"></span><pre>$_errorStyle</pre></a></li>
<li class="property private "><a href="#property__errorTitle" title="$_errorTitle :: Error title"><span class="description"></span><pre>$_errorTitle</pre></a></li>
<li class="property private "><a href="#property__formula1" title="$_formula1 :: Formula 1"><span class="description"></span><pre>$_formula1</pre></a></li>
<li class="property private "><a href="#property__formula2" title="$_formula2 :: Formula 2"><span class="description"></span><pre>$_formula2</pre></a></li>
<li class="property private "><a href="#property__operator" title="$_operator :: Operator"><span class="description"></span><pre>$_operator</pre></a></li>
<li class="property private "><a href="#property__prompt" title="$_prompt :: Prompt"><span class="description"></span><pre>$_prompt</pre></a></li>
<li class="property private "><a href="#property__promptTitle" title="$_promptTitle :: Prompt title"><span class="description"></span><pre>$_promptTitle</pre></a></li>
<li class="property private "><a href="#property__showDropDown" title="$_showDropDown :: Show DropDown"><span class="description"></span><pre>$_showDropDown</pre></a></li>
<li class="property private "><a href="#property__showErrorMessage" title="$_showErrorMessage :: Show ErrorMessage"><span class="description"></span><pre>$_showErrorMessage</pre></a></li>
<li class="property private "><a href="#property__showInputMessage" title="$_showInputMessage :: Show InputMessage"><span class="description"></span><pre>$_showInputMessage</pre></a></li>
<li class="property private "><a href="#property__type" title="$_type :: Type"><span class="description"></span><pre>$_type</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_OPERATOR_BETWEEN" title="OPERATOR_BETWEEN :: "><span class="description">OPERATOR_BETWEEN</span><pre>OPERATOR_BETWEEN</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_EQUAL" title="OPERATOR_EQUAL :: "><span class="description">OPERATOR_EQUAL</span><pre>OPERATOR_EQUAL</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_GREATERTHAN" title="OPERATOR_GREATERTHAN :: "><span class="description">OPERATOR_GREATERTHAN</span><pre>OPERATOR_GREATERTHAN</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_GREATERTHANOREQUAL" title="OPERATOR_GREATERTHANOREQUAL :: "><span class="description">OPERATOR_GREATERTHANOREQUAL</span><pre>OPERATOR_GREATERTHANOREQUAL</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_LESSTHAN" title="OPERATOR_LESSTHAN :: "><span class="description">OPERATOR_LESSTHAN</span><pre>OPERATOR_LESSTHAN</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_LESSTHANOREQUAL" title="OPERATOR_LESSTHANOREQUAL :: "><span class="description">OPERATOR_LESSTHANOREQUAL</span><pre>OPERATOR_LESSTHANOREQUAL</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_NOTBETWEEN" title="OPERATOR_NOTBETWEEN :: "><span class="description">OPERATOR_NOTBETWEEN</span><pre>OPERATOR_NOTBETWEEN</pre></a></li>
<li class="constant  "><a href="#constant_OPERATOR_NOTEQUAL" title="OPERATOR_NOTEQUAL :: "><span class="description">OPERATOR_NOTEQUAL</span><pre>OPERATOR_NOTEQUAL</pre></a></li>
<li class="constant  "><a href="#constant_STYLE_INFORMATION" title="STYLE_INFORMATION :: "><span class="description">STYLE_INFORMATION</span><pre>STYLE_INFORMATION</pre></a></li>
<li class="constant  "><a href="#constant_STYLE_STOP" title="STYLE_STOP :: "><span class="description">STYLE_STOP</span><pre>STYLE_STOP</pre></a></li>
<li class="constant  "><a href="#constant_STYLE_WARNING" title="STYLE_WARNING :: "><span class="description">STYLE_WARNING</span><pre>STYLE_WARNING</pre></a></li>
<li class="constant  "><a href="#constant_TYPE_CUSTOM" title="TYPE_CUSTOM :: "><span class="description">TYPE_CUSTOM</span><pre>TYPE_CUSTOM</pre></a></li>
<li class="constant  "><a href="#constant_TYPE_DATE" title="TYPE_DATE :: "><span class="description">TYPE_DATE</span><pre>TYPE_DATE</pre></a></li>
<li class="constant  "><a href="#constant_TYPE_DECIMAL" title="TYPE_DECIMAL :: "><span class="description">TYPE_DECIMAL</span><pre>TYPE_DECIMAL</pre></a></li>
<li class="constant  "><a href="#constant_TYPE_LIST" title="TYPE_LIST :: "><span class="description">TYPE_LIST</span><pre>TYPE_LIST</pre></a></li>
<li class="constant  "><a href="#constant_TYPE_NONE" title="TYPE_NONE :: "><span class="description">TYPE_NONE</span><pre>TYPE_NONE</pre></a></li>
<li class="constant  "><a href="#constant_TYPE_TEXTLENGTH" title="TYPE_TEXTLENGTH :: "><span class="description">TYPE_TEXTLENGTH</span><pre>TYPE_TEXTLENGTH</pre></a></li>
<li class="constant  "><a href="#constant_TYPE_TIME" title="TYPE_TIME :: "><span class="description">TYPE_TIME</span><pre>TYPE_TIME</pre></a></li>
<li class="constant  "><a href="#constant_TYPE_WHOLE" title="TYPE_WHOLE :: "><span class="description">TYPE_WHOLE</span><pre>TYPE_WHOLE</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Cell_DataValidation"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Cell_DataValidation.html">PHPExcel_Cell_DataValidation</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Cell_DataValidation</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Cell.html">PHPExcel_Cell</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___clone"></a><div class="element clickable method public method___clone" data-toggle="collapse" data-target=".method___clone .collapse">
<h2>Implement PHP __clone to create a deep clone, not just a shallow copy.</h2>
<pre>__clone() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_Cell_DataValidation</h2>
<pre>__construct() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_getAllowBlank"></a><div class="element clickable method public method_getAllowBlank" data-toggle="collapse" data-target=".method_getAllowBlank .collapse">
<h2>Get Allow Blank</h2>
<pre>getAllowBlank() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getError"></a><div class="element clickable method public method_getError" data-toggle="collapse" data-target=".method_getError .collapse">
<h2>Get Error</h2>
<pre>getError() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getErrorStyle"></a><div class="element clickable method public method_getErrorStyle" data-toggle="collapse" data-target=".method_getErrorStyle .collapse">
<h2>Get Error style</h2>
<pre>getErrorStyle() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getErrorTitle"></a><div class="element clickable method public method_getErrorTitle" data-toggle="collapse" data-target=".method_getErrorTitle .collapse">
<h2>Get Error title</h2>
<pre>getErrorTitle() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getFormula1"></a><div class="element clickable method public method_getFormula1" data-toggle="collapse" data-target=".method_getFormula1 .collapse">
<h2>Get Formula 1</h2>
<pre>getFormula1() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getFormula2"></a><div class="element clickable method public method_getFormula2" data-toggle="collapse" data-target=".method_getFormula2 .collapse">
<h2>Get Formula 2</h2>
<pre>getFormula2() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getHashCode"></a><div class="element clickable method public method_getHashCode" data-toggle="collapse" data-target=".method_getHashCode .collapse">
<h2>Get hash code</h2>
<pre>getHashCode() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Hash code</div>
</div></div>
</div>
<a id="method_getOperator"></a><div class="element clickable method public method_getOperator" data-toggle="collapse" data-target=".method_getOperator .collapse">
<h2>Get Operator</h2>
<pre>getOperator() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getPrompt"></a><div class="element clickable method public method_getPrompt" data-toggle="collapse" data-target=".method_getPrompt .collapse">
<h2>Get Prompt</h2>
<pre>getPrompt() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getPromptTitle"></a><div class="element clickable method public method_getPromptTitle" data-toggle="collapse" data-target=".method_getPromptTitle .collapse">
<h2>Get Prompt title</h2>
<pre>getPromptTitle() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getShowDropDown"></a><div class="element clickable method public method_getShowDropDown" data-toggle="collapse" data-target=".method_getShowDropDown .collapse">
<h2>Get Show DropDown</h2>
<pre>getShowDropDown() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getShowErrorMessage"></a><div class="element clickable method public method_getShowErrorMessage" data-toggle="collapse" data-target=".method_getShowErrorMessage .collapse">
<h2>Get Show ErrorMessage</h2>
<pre>getShowErrorMessage() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getShowInputMessage"></a><div class="element clickable method public method_getShowInputMessage" data-toggle="collapse" data-target=".method_getShowInputMessage .collapse">
<h2>Get Show InputMessage</h2>
<pre>getShowInputMessage() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getType"></a><div class="element clickable method public method_getType" data-toggle="collapse" data-target=".method_getType .collapse">
<h2>Get Type</h2>
<pre>getType() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_setAllowBlank"></a><div class="element clickable method public method_setAllowBlank" data-toggle="collapse" data-target=".method_setAllowBlank .collapse">
<h2>Set Allow Blank</h2>
<pre>setAllowBlank(boolean $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<a id="method_setError"></a><div class="element clickable method public method_setError" data-toggle="collapse" data-target=".method_setError .collapse">
<h2>Set Error</h2>
<pre>setError(string $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<a id="method_setErrorStyle"></a><div class="element clickable method public method_setErrorStyle" data-toggle="collapse" data-target=".method_setErrorStyle .collapse">
<h2>Set Error style</h2>
<pre>setErrorStyle(string $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<a id="method_setErrorTitle"></a><div class="element clickable method public method_setErrorTitle" data-toggle="collapse" data-target=".method_setErrorTitle .collapse">
<h2>Set Error title</h2>
<pre>setErrorTitle(string $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<a id="method_setFormula1"></a><div class="element clickable method public method_setFormula1" data-toggle="collapse" data-target=".method_setFormula1 .collapse">
<h2>Set Formula 1</h2>
<pre>setFormula1(string $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<a id="method_setFormula2"></a><div class="element clickable method public method_setFormula2" data-toggle="collapse" data-target=".method_setFormula2 .collapse">
<h2>Set Formula 2</h2>
<pre>setFormula2(string $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<a id="method_setOperator"></a><div class="element clickable method public method_setOperator" data-toggle="collapse" data-target=".method_setOperator .collapse">
<h2>Set Operator</h2>
<pre>setOperator(string $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<a id="method_setPrompt"></a><div class="element clickable method public method_setPrompt" data-toggle="collapse" data-target=".method_setPrompt .collapse">
<h2>Set Prompt</h2>
<pre>setPrompt(string $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<a id="method_setPromptTitle"></a><div class="element clickable method public method_setPromptTitle" data-toggle="collapse" data-target=".method_setPromptTitle .collapse">
<h2>Set Prompt title</h2>
<pre>setPromptTitle(string $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<a id="method_setShowDropDown"></a><div class="element clickable method public method_setShowDropDown" data-toggle="collapse" data-target=".method_setShowDropDown .collapse">
<h2>Set Show DropDown</h2>
<pre>setShowDropDown(boolean $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<a id="method_setShowErrorMessage"></a><div class="element clickable method public method_setShowErrorMessage" data-toggle="collapse" data-target=".method_setShowErrorMessage .collapse">
<h2>Set Show ErrorMessage</h2>
<pre>setShowErrorMessage(boolean $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<a id="method_setShowInputMessage"></a><div class="element clickable method public method_setShowInputMessage" data-toggle="collapse" data-target=".method_setShowInputMessage .collapse">
<h2>Set Show InputMessage</h2>
<pre>setShowInputMessage(boolean $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<a id="method_setType"></a><div class="element clickable method public method_setType" data-toggle="collapse" data-target=".method_setType .collapse">
<h2>Set Type</h2>
<pre>setType(string $value) : <a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Cell_DataValidation.html">\PHPExcel_Cell_DataValidation</a></code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__allowBlank"> </a><div class="element clickable property private property__allowBlank" data-toggle="collapse" data-target=".property__allowBlank .collapse">
<h2></h2>
<pre>$_allowBlank : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__error"> </a><div class="element clickable property private property__error" data-toggle="collapse" data-target=".property__error .collapse">
<h2></h2>
<pre>$_error : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__errorStyle"> </a><div class="element clickable property private property__errorStyle" data-toggle="collapse" data-target=".property__errorStyle .collapse">
<h2></h2>
<pre>$_errorStyle : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__errorTitle"> </a><div class="element clickable property private property__errorTitle" data-toggle="collapse" data-target=".property__errorTitle .collapse">
<h2></h2>
<pre>$_errorTitle : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__formula1"> </a><div class="element clickable property private property__formula1" data-toggle="collapse" data-target=".property__formula1 .collapse">
<h2></h2>
<pre>$_formula1 : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__formula2"> </a><div class="element clickable property private property__formula2" data-toggle="collapse" data-target=".property__formula2 .collapse">
<h2></h2>
<pre>$_formula2 : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__operator"> </a><div class="element clickable property private property__operator" data-toggle="collapse" data-target=".property__operator .collapse">
<h2></h2>
<pre>$_operator : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__prompt"> </a><div class="element clickable property private property__prompt" data-toggle="collapse" data-target=".property__prompt .collapse">
<h2></h2>
<pre>$_prompt : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__promptTitle"> </a><div class="element clickable property private property__promptTitle" data-toggle="collapse" data-target=".property__promptTitle .collapse">
<h2></h2>
<pre>$_promptTitle : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__showDropDown"> </a><div class="element clickable property private property__showDropDown" data-toggle="collapse" data-target=".property__showDropDown .collapse">
<h2></h2>
<pre>$_showDropDown : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__showErrorMessage"> </a><div class="element clickable property private property__showErrorMessage" data-toggle="collapse" data-target=".property__showErrorMessage .collapse">
<h2></h2>
<pre>$_showErrorMessage : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__showInputMessage"> </a><div class="element clickable property private property__showInputMessage" data-toggle="collapse" data-target=".property__showInputMessage .collapse">
<h2></h2>
<pre>$_showInputMessage : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__type"> </a><div class="element clickable property private property__type" data-toggle="collapse" data-target=".property__type .collapse">
<h2></h2>
<pre>$_type : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_OPERATOR_BETWEEN"> </a><div class="element clickable constant  constant_OPERATOR_BETWEEN" data-toggle="collapse" data-target=".constant_OPERATOR_BETWEEN .collapse">
<h2>OPERATOR_BETWEEN</h2>
<pre>OPERATOR_BETWEEN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_EQUAL"> </a><div class="element clickable constant  constant_OPERATOR_EQUAL" data-toggle="collapse" data-target=".constant_OPERATOR_EQUAL .collapse">
<h2>OPERATOR_EQUAL</h2>
<pre>OPERATOR_EQUAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_GREATERTHAN"> </a><div class="element clickable constant  constant_OPERATOR_GREATERTHAN" data-toggle="collapse" data-target=".constant_OPERATOR_GREATERTHAN .collapse">
<h2>OPERATOR_GREATERTHAN</h2>
<pre>OPERATOR_GREATERTHAN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_GREATERTHANOREQUAL"> </a><div class="element clickable constant  constant_OPERATOR_GREATERTHANOREQUAL" data-toggle="collapse" data-target=".constant_OPERATOR_GREATERTHANOREQUAL .collapse">
<h2>OPERATOR_GREATERTHANOREQUAL</h2>
<pre>OPERATOR_GREATERTHANOREQUAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_LESSTHAN"> </a><div class="element clickable constant  constant_OPERATOR_LESSTHAN" data-toggle="collapse" data-target=".constant_OPERATOR_LESSTHAN .collapse">
<h2>OPERATOR_LESSTHAN</h2>
<pre>OPERATOR_LESSTHAN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_LESSTHANOREQUAL"> </a><div class="element clickable constant  constant_OPERATOR_LESSTHANOREQUAL" data-toggle="collapse" data-target=".constant_OPERATOR_LESSTHANOREQUAL .collapse">
<h2>OPERATOR_LESSTHANOREQUAL</h2>
<pre>OPERATOR_LESSTHANOREQUAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_NOTBETWEEN"> </a><div class="element clickable constant  constant_OPERATOR_NOTBETWEEN" data-toggle="collapse" data-target=".constant_OPERATOR_NOTBETWEEN .collapse">
<h2>OPERATOR_NOTBETWEEN</h2>
<pre>OPERATOR_NOTBETWEEN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_OPERATOR_NOTEQUAL"> </a><div class="element clickable constant  constant_OPERATOR_NOTEQUAL" data-toggle="collapse" data-target=".constant_OPERATOR_NOTEQUAL .collapse">
<h2>OPERATOR_NOTEQUAL</h2>
<pre>OPERATOR_NOTEQUAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_STYLE_INFORMATION"> </a><div class="element clickable constant  constant_STYLE_INFORMATION" data-toggle="collapse" data-target=".constant_STYLE_INFORMATION .collapse">
<h2>STYLE_INFORMATION</h2>
<pre>STYLE_INFORMATION </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_STYLE_STOP"> </a><div class="element clickable constant  constant_STYLE_STOP" data-toggle="collapse" data-target=".constant_STYLE_STOP .collapse">
<h2>STYLE_STOP</h2>
<pre>STYLE_STOP </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_STYLE_WARNING"> </a><div class="element clickable constant  constant_STYLE_WARNING" data-toggle="collapse" data-target=".constant_STYLE_WARNING .collapse">
<h2>STYLE_WARNING</h2>
<pre>STYLE_WARNING </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TYPE_CUSTOM"> </a><div class="element clickable constant  constant_TYPE_CUSTOM" data-toggle="collapse" data-target=".constant_TYPE_CUSTOM .collapse">
<h2>TYPE_CUSTOM</h2>
<pre>TYPE_CUSTOM </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TYPE_DATE"> </a><div class="element clickable constant  constant_TYPE_DATE" data-toggle="collapse" data-target=".constant_TYPE_DATE .collapse">
<h2>TYPE_DATE</h2>
<pre>TYPE_DATE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TYPE_DECIMAL"> </a><div class="element clickable constant  constant_TYPE_DECIMAL" data-toggle="collapse" data-target=".constant_TYPE_DECIMAL .collapse">
<h2>TYPE_DECIMAL</h2>
<pre>TYPE_DECIMAL </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TYPE_LIST"> </a><div class="element clickable constant  constant_TYPE_LIST" data-toggle="collapse" data-target=".constant_TYPE_LIST .collapse">
<h2>TYPE_LIST</h2>
<pre>TYPE_LIST </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TYPE_NONE"> </a><div class="element clickable constant  constant_TYPE_NONE" data-toggle="collapse" data-target=".constant_TYPE_NONE .collapse">
<h2>TYPE_NONE</h2>
<pre>TYPE_NONE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TYPE_TEXTLENGTH"> </a><div class="element clickable constant  constant_TYPE_TEXTLENGTH" data-toggle="collapse" data-target=".constant_TYPE_TEXTLENGTH .collapse">
<h2>TYPE_TEXTLENGTH</h2>
<pre>TYPE_TEXTLENGTH </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TYPE_TIME"> </a><div class="element clickable constant  constant_TYPE_TIME" data-toggle="collapse" data-target=".constant_TYPE_TIME .collapse">
<h2>TYPE_TIME</h2>
<pre>TYPE_TIME </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_TYPE_WHOLE"> </a><div class="element clickable constant  constant_TYPE_WHOLE" data-toggle="collapse" data-target=".constant_TYPE_WHOLE .collapse">
<h2>TYPE_WHOLE</h2>
<pre>TYPE_WHOLE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:34Z.<br></footer></div>
</div>
</body>
</html>
