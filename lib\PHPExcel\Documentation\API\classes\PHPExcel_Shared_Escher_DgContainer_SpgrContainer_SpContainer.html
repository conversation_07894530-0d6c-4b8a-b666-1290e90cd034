<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_Escher_DgContainer_SpgrContainer_SpContainer</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_getEndCoordinates" title="getEndCoordinates :: Get cell coordinates of bottom-right corner of shape"><span class="description">Get cell coordinates of bottom-right corner of shape</span><pre>getEndCoordinates()</pre></a></li>
<li class="method public "><a href="#method_getEndOffsetX" title="getEndOffsetX :: Get offset in x-direction of bottom-right corner of shape measured in 1/1024 of column width"><span class="description">Get offset in x-direction of bottom-right corner of shape measured in 1/1024 of column width</span><pre>getEndOffsetX()</pre></a></li>
<li class="method public "><a href="#method_getEndOffsetY" title="getEndOffsetY :: Get offset in y-direction of bottom-right corner of shape measured in 1/256 of row height"><span class="description">Get offset in y-direction of bottom-right corner of shape measured in 1/256 of row height</span><pre>getEndOffsetY()</pre></a></li>
<li class="method public "><a href="#method_getNestingLevel" title="getNestingLevel :: Get the nesting level of this spContainer."><span class="description">Get the nesting level of this spContainer.</span><pre>getNestingLevel()</pre></a></li>
<li class="method public "><a href="#method_getOPT" title="getOPT :: Get an option for the Shape Group Container"><span class="description">Get an option for the Shape Group Container</span><pre>getOPT()</pre></a></li>
<li class="method public "><a href="#method_getOPTCollection" title="getOPTCollection :: Get the collection of options"><span class="description">Get the collection of options</span><pre>getOPTCollection()</pre></a></li>
<li class="method public "><a href="#method_getParent" title="getParent :: Get the parent Shape Group Container"><span class="description">Get the parent Shape Group Container</span><pre>getParent()</pre></a></li>
<li class="method public "><a href="#method_getSpFlag" title="getSpFlag :: Get the shape flag"><span class="description">Get the shape flag</span><pre>getSpFlag()</pre></a></li>
<li class="method public "><a href="#method_getSpId" title="getSpId :: Get the shape index"><span class="description">Get the shape index</span><pre>getSpId()</pre></a></li>
<li class="method public "><a href="#method_getSpType" title="getSpType :: Get the shape type"><span class="description">Get the shape type</span><pre>getSpType()</pre></a></li>
<li class="method public "><a href="#method_getSpgr" title="getSpgr :: Get whether this is a group shape"><span class="description">Get whether this is a group shape</span><pre>getSpgr()</pre></a></li>
<li class="method public "><a href="#method_getStartCoordinates" title="getStartCoordinates :: Get cell coordinates of upper-left corner of shape"><span class="description">Get cell coordinates of upper-left corner of shape</span><pre>getStartCoordinates()</pre></a></li>
<li class="method public "><a href="#method_getStartOffsetX" title="getStartOffsetX :: Get offset in x-direction of upper-left corner of shape measured in 1/1024 of column width"><span class="description">Get offset in x-direction of upper-left corner of shape measured in 1/1024 of column width</span><pre>getStartOffsetX()</pre></a></li>
<li class="method public "><a href="#method_getStartOffsetY" title="getStartOffsetY :: Get offset in y-direction of upper-left corner of shape measured in 1/256 of row height"><span class="description">Get offset in y-direction of upper-left corner of shape measured in 1/256 of row height</span><pre>getStartOffsetY()</pre></a></li>
<li class="method public "><a href="#method_setEndCoordinates" title="setEndCoordinates :: Set cell coordinates of bottom-right corner of shape"><span class="description">Set cell coordinates of bottom-right corner of shape</span><pre>setEndCoordinates()</pre></a></li>
<li class="method public "><a href="#method_setEndOffsetX" title="setEndOffsetX :: Set offset in x-direction of bottom-right corner of shape measured in 1/1024 of column width"><span class="description">Set offset in x-direction of bottom-right corner of shape measured in 1/1024 of column width</span><pre>setEndOffsetX()</pre></a></li>
<li class="method public "><a href="#method_setEndOffsetY" title="setEndOffsetY :: Set offset in y-direction of bottom-right corner of shape measured in 1/256 of row height"><span class="description">Set offset in y-direction of bottom-right corner of shape measured in 1/256 of row height</span><pre>setEndOffsetY()</pre></a></li>
<li class="method public "><a href="#method_setOPT" title="setOPT :: Set an option for the Shape Group Container"><span class="description">Set an option for the Shape Group Container</span><pre>setOPT()</pre></a></li>
<li class="method public "><a href="#method_setParent" title="setParent :: Set parent Shape Group Container"><span class="description">Set parent Shape Group Container</span><pre>setParent()</pre></a></li>
<li class="method public "><a href="#method_setSpFlag" title="setSpFlag :: Set the shape flag"><span class="description">Set the shape flag</span><pre>setSpFlag()</pre></a></li>
<li class="method public "><a href="#method_setSpId" title="setSpId :: Set the shape index"><span class="description">Set the shape index</span><pre>setSpId()</pre></a></li>
<li class="method public "><a href="#method_setSpType" title="setSpType :: Set the shape type"><span class="description">Set the shape type</span><pre>setSpType()</pre></a></li>
<li class="method public "><a href="#method_setSpgr" title="setSpgr :: Set whether this is a group shape"><span class="description">Set whether this is a group shape</span><pre>setSpgr()</pre></a></li>
<li class="method public "><a href="#method_setStartCoordinates" title="setStartCoordinates :: Set cell coordinates of upper-left corner of shape"><span class="description">Set cell coordinates of upper-left corner of shape</span><pre>setStartCoordinates()</pre></a></li>
<li class="method public "><a href="#method_setStartOffsetX" title="setStartOffsetX :: Set offset in x-direction of upper-left corner of shape measured in 1/1024 of column width"><span class="description">Set offset in x-direction of upper-left corner of shape measured in 1/1024 of column width</span><pre>setStartOffsetX()</pre></a></li>
<li class="method public "><a href="#method_setStartOffsetY" title="setStartOffsetY :: Set offset in y-direction of upper-left corner of shape measured in 1/256 of row height"><span class="description">Set offset in y-direction of upper-left corner of shape measured in 1/256 of row height</span><pre>setStartOffsetY()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__OPT" title="$_OPT :: Array of options"><span class="description"></span><pre>$_OPT</pre></a></li>
<li class="property private "><a href="#property__endCoordinates" title="$_endCoordinates :: Cell coordinates of bottom-right corner of shape, e.g."><span class="description"></span><pre>$_endCoordinates</pre></a></li>
<li class="property private "><a href="#property__endOffsetX" title="$_endOffsetX :: Horizontal offset of bottom-right corner of shape measured in 1/1024 of column width"><span class="description"></span><pre>$_endOffsetX</pre></a></li>
<li class="property private "><a href="#property__endOffsetY" title="$_endOffsetY :: Vertical offset of bottom-right corner of shape measured in 1/256 of row height"><span class="description"></span><pre>$_endOffsetY</pre></a></li>
<li class="property private "><a href="#property__parent" title="$_parent :: Parent Shape Group Container"><span class="description"></span><pre>$_parent</pre></a></li>
<li class="property private "><a href="#property__spFlag" title="$_spFlag :: Shape flag"><span class="description"></span><pre>$_spFlag</pre></a></li>
<li class="property private "><a href="#property__spId" title="$_spId :: Shape index (usually group shape has index 0, and the rest: 1,2,3."><span class="description"></span><pre>$_spId</pre></a></li>
<li class="property private "><a href="#property__spType" title="$_spType :: Shape type"><span class="description"></span><pre>$_spType</pre></a></li>
<li class="property private "><a href="#property__spgr" title="$_spgr :: Is this a group shape?"><span class="description"></span><pre>$_spgr</pre></a></li>
<li class="property private "><a href="#property__startCoordinates" title="$_startCoordinates :: Cell coordinates of upper-left corner of shape, e.g."><span class="description"></span><pre>$_startCoordinates</pre></a></li>
<li class="property private "><a href="#property__startOffsetX" title="$_startOffsetX :: Horizontal offset of upper-left corner of shape measured in 1/1024 of column width"><span class="description"></span><pre>$_startOffsetX</pre></a></li>
<li class="property private "><a href="#property__startOffsetY" title="$_startOffsetY :: Vertical offset of upper-left corner of shape measured in 1/256 of row height"><span class="description"></span><pre>$_startOffsetY</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_Escher_DgContainer_SpgrContainer_SpContainer"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_Escher_DgContainer_SpgrContainer_SpContainer.html">PHPExcel_Shared_Escher_DgContainer_SpgrContainer_SpContainer</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Shared_Escher_DgContainer_SpgrContainer_SpContainer</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Shared.Escher.html">PHPExcel_Shared_Escher</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_getEndCoordinates"></a><div class="element clickable method public method_getEndCoordinates" data-toggle="collapse" data-target=".method_getEndCoordinates .collapse">
<h2>Get cell coordinates of bottom-right corner of shape</h2>
<pre>getEndCoordinates() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getEndOffsetX"></a><div class="element clickable method public method_getEndOffsetX" data-toggle="collapse" data-target=".method_getEndOffsetX .collapse">
<h2>Get offset in x-direction of bottom-right corner of shape measured in 1/1024 of column width</h2>
<pre>getEndOffsetX() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_getEndOffsetY"></a><div class="element clickable method public method_getEndOffsetY" data-toggle="collapse" data-target=".method_getEndOffsetY .collapse">
<h2>Get offset in y-direction of bottom-right corner of shape measured in 1/256 of row height</h2>
<pre>getEndOffsetY() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_getNestingLevel"></a><div class="element clickable method public method_getNestingLevel" data-toggle="collapse" data-target=".method_getNestingLevel .collapse">
<h2>Get the nesting level of this spContainer.</h2>
<pre>getNestingLevel() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>This is the number of spgrContainers between this spContainer and
the dgContainer. A value of 1 = immediately within first spgrContainer
Higher nesting level occurs if and only if spContainer is part of a shape group</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Nesting level</div>
</div></div>
</div>
<a id="method_getOPT"></a><div class="element clickable method public method_getOPT" data-toggle="collapse" data-target=".method_getOPT .collapse">
<h2>Get an option for the Shape Group Container</h2>
<pre>getOPT(int $property) : mixed</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$property</h4>
<code>int</code><p>The number specifies the option</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>mixed</code></div>
</div></div>
</div>
<a id="method_getOPTCollection"></a><div class="element clickable method public method_getOPTCollection" data-toggle="collapse" data-target=".method_getOPTCollection .collapse">
<h2>Get the collection of options</h2>
<pre>getOPTCollection() : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>array</code></div>
</div></div>
</div>
<a id="method_getParent"></a><div class="element clickable method public method_getParent" data-toggle="collapse" data-target=".method_getParent .collapse">
<h2>Get the parent Shape Group Container</h2>
<pre>getParent() : <a href="../classes/PHPExcel_Shared_Escher_DgContainer_SpgrContainer.html">\PHPExcel_Shared_Escher_DgContainer_SpgrContainer</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Shared_Escher_DgContainer_SpgrContainer.html">\PHPExcel_Shared_Escher_DgContainer_SpgrContainer</a></code></div>
</div></div>
</div>
<a id="method_getSpFlag"></a><div class="element clickable method public method_getSpFlag" data-toggle="collapse" data-target=".method_getSpFlag .collapse">
<h2>Get the shape flag</h2>
<pre>getSpFlag() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_getSpId"></a><div class="element clickable method public method_getSpId" data-toggle="collapse" data-target=".method_getSpId .collapse">
<h2>Get the shape index</h2>
<pre>getSpId() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_getSpType"></a><div class="element clickable method public method_getSpType" data-toggle="collapse" data-target=".method_getSpType .collapse">
<h2>Get the shape type</h2>
<pre>getSpType() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_getSpgr"></a><div class="element clickable method public method_getSpgr" data-toggle="collapse" data-target=".method_getSpgr .collapse">
<h2>Get whether this is a group shape</h2>
<pre>getSpgr() : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getStartCoordinates"></a><div class="element clickable method public method_getStartCoordinates" data-toggle="collapse" data-target=".method_getStartCoordinates .collapse">
<h2>Get cell coordinates of upper-left corner of shape</h2>
<pre>getStartCoordinates() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getStartOffsetX"></a><div class="element clickable method public method_getStartOffsetX" data-toggle="collapse" data-target=".method_getStartOffsetX .collapse">
<h2>Get offset in x-direction of upper-left corner of shape measured in 1/1024 of column width</h2>
<pre>getStartOffsetX() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_getStartOffsetY"></a><div class="element clickable method public method_getStartOffsetY" data-toggle="collapse" data-target=".method_getStartOffsetY .collapse">
<h2>Get offset in y-direction of upper-left corner of shape measured in 1/256 of row height</h2>
<pre>getStartOffsetY() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_setEndCoordinates"></a><div class="element clickable method public method_setEndCoordinates" data-toggle="collapse" data-target=".method_setEndCoordinates .collapse">
<h2>Set cell coordinates of bottom-right corner of shape</h2>
<pre>setEndCoordinates(string $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
</div></div>
</div>
<a id="method_setEndOffsetX"></a><div class="element clickable method public method_setEndOffsetX" data-toggle="collapse" data-target=".method_setEndOffsetX .collapse">
<h2>Set offset in x-direction of bottom-right corner of shape measured in 1/1024 of column width</h2>
<pre>setEndOffsetX($endOffsetX) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$endOffsetX</h4></div>
</div></div>
</div>
<a id="method_setEndOffsetY"></a><div class="element clickable method public method_setEndOffsetY" data-toggle="collapse" data-target=".method_setEndOffsetY .collapse">
<h2>Set offset in y-direction of bottom-right corner of shape measured in 1/256 of row height</h2>
<pre>setEndOffsetY(int $endOffsetY) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$endOffsetY</h4>
<code>int</code>
</div>
</div></div>
</div>
<a id="method_setOPT"></a><div class="element clickable method public method_setOPT" data-toggle="collapse" data-target=".method_setOPT .collapse">
<h2>Set an option for the Shape Group Container</h2>
<pre>setOPT(int $property, mixed $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$property</h4>
<code>int</code><p>The number specifies the option</p></div>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code>
</div>
</div></div>
</div>
<a id="method_setParent"></a><div class="element clickable method public method_setParent" data-toggle="collapse" data-target=".method_setParent .collapse">
<h2>Set parent Shape Group Container</h2>
<pre>setParent(<a href="../classes/PHPExcel_Shared_Escher_DgContainer_SpgrContainer.html">\PHPExcel_Shared_Escher_DgContainer_SpgrContainer</a> $parent) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$parent</h4>
<code><a href="../classes/PHPExcel_Shared_Escher_DgContainer_SpgrContainer.html">\PHPExcel_Shared_Escher_DgContainer_SpgrContainer</a></code>
</div>
</div></div>
</div>
<a id="method_setSpFlag"></a><div class="element clickable method public method_setSpFlag" data-toggle="collapse" data-target=".method_setSpFlag .collapse">
<h2>Set the shape flag</h2>
<pre>setSpFlag(int $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>int</code>
</div>
</div></div>
</div>
<a id="method_setSpId"></a><div class="element clickable method public method_setSpId" data-toggle="collapse" data-target=".method_setSpId .collapse">
<h2>Set the shape index</h2>
<pre>setSpId(int $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>int</code>
</div>
</div></div>
</div>
<a id="method_setSpType"></a><div class="element clickable method public method_setSpType" data-toggle="collapse" data-target=".method_setSpType .collapse">
<h2>Set the shape type</h2>
<pre>setSpType(int $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>int</code>
</div>
</div></div>
</div>
<a id="method_setSpgr"></a><div class="element clickable method public method_setSpgr" data-toggle="collapse" data-target=".method_setSpgr .collapse">
<h2>Set whether this is a group shape</h2>
<pre>setSpgr(boolean $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>boolean</code>
</div>
</div></div>
</div>
<a id="method_setStartCoordinates"></a><div class="element clickable method public method_setStartCoordinates" data-toggle="collapse" data-target=".method_setStartCoordinates .collapse">
<h2>Set cell coordinates of upper-left corner of shape</h2>
<pre>setStartCoordinates(string $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
</div></div>
</div>
<a id="method_setStartOffsetX"></a><div class="element clickable method public method_setStartOffsetX" data-toggle="collapse" data-target=".method_setStartOffsetX .collapse">
<h2>Set offset in x-direction of upper-left corner of shape measured in 1/1024 of column width</h2>
<pre>setStartOffsetX(int $startOffsetX) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$startOffsetX</h4>
<code>int</code>
</div>
</div></div>
</div>
<a id="method_setStartOffsetY"></a><div class="element clickable method public method_setStartOffsetY" data-toggle="collapse" data-target=".method_setStartOffsetY .collapse">
<h2>Set offset in y-direction of upper-left corner of shape measured in 1/256 of row height</h2>
<pre>setStartOffsetY(int $startOffsetY) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$startOffsetY</h4>
<code>int</code>
</div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__OPT"> </a><div class="element clickable property private property__OPT" data-toggle="collapse" data-target=".property__OPT .collapse">
<h2></h2>
<pre>$_OPT : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__endCoordinates"> </a><div class="element clickable property private property__endCoordinates" data-toggle="collapse" data-target=".property__endCoordinates .collapse">
<h2></h2>
<pre>$_endCoordinates : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>'B2'</p></div></div></div>
</div>
<a id="property__endOffsetX"> </a><div class="element clickable property private property__endOffsetX" data-toggle="collapse" data-target=".property__endOffsetX .collapse">
<h2></h2>
<pre>$_endOffsetX : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__endOffsetY"> </a><div class="element clickable property private property__endOffsetY" data-toggle="collapse" data-target=".property__endOffsetY .collapse">
<h2></h2>
<pre>$_endOffsetY : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__parent"> </a><div class="element clickable property private property__parent" data-toggle="collapse" data-target=".property__parent .collapse">
<h2></h2>
<pre>$_parent : <a href="../classes/PHPExcel_Shared_Escher_DgContainer_SpgrContainer.html">\PHPExcel_Shared_Escher_DgContainer_SpgrContainer</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__spFlag"> </a><div class="element clickable property private property__spFlag" data-toggle="collapse" data-target=".property__spFlag .collapse">
<h2></h2>
<pre>$_spFlag : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__spId"> </a><div class="element clickable property private property__spId" data-toggle="collapse" data-target=".property__spId .collapse">
<h2></h2>
<pre>$_spId : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>..)</p></div></div></div>
</div>
<a id="property__spType"> </a><div class="element clickable property private property__spType" data-toggle="collapse" data-target=".property__spType .collapse">
<h2></h2>
<pre>$_spType : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__spgr"> </a><div class="element clickable property private property__spgr" data-toggle="collapse" data-target=".property__spgr .collapse">
<h2></h2>
<pre>$_spgr : boolean</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__startCoordinates"> </a><div class="element clickable property private property__startCoordinates" data-toggle="collapse" data-target=".property__startCoordinates .collapse">
<h2></h2>
<pre>$_startCoordinates : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>'A1'</p></div></div></div>
</div>
<a id="property__startOffsetX"> </a><div class="element clickable property private property__startOffsetX" data-toggle="collapse" data-target=".property__startOffsetX .collapse">
<h2></h2>
<pre>$_startOffsetX : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__startOffsetY"> </a><div class="element clickable property private property__startOffsetY" data-toggle="collapse" data-target=".property__startOffsetY .collapse">
<h2></h2>
<pre>$_startOffsetY : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:36Z.<br></footer></div>
</div>
</body>
</html>
