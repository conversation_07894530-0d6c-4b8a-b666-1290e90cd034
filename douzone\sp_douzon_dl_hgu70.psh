 SELECT (A.HDATE || <PERSON><PERSON>) AS KEY, <PERSON><PERSON>, <PERSON><PERSON>, A.CTNAME, B.CT_NAME, A.DAMT, A.CAMT, B.CT_LINK, B.CT_LINK2,
         A.REMA, A.<PERSON>, A.<PERSON>, <PERSON><PERSON>, B.CT_LINK, A.<PERSON>_<PERSON>K, A.FNCODE, A.REMB, A.REMC, B.CT_CODE, A.MASTLINK,
         C.HDATE AS THDATE, C.TMSTYLE, C.ACSTYLE, C.HAMT, C.HVAT
 FROM DL A, CT B, TMH C
 WHERE A.RCT_CODE = '1000'
   AND A.HDATE BETWEEN '20250701' AND '20250731'
   AND A.CTCODE = B.CT_CODE (+)
   AND A.HGU = '70'
   AND C.RCT_CODE (+)= SUBSTR(A.REMB, 1, 4)
   AND C.HDATE (+)= SUBSTR(A.REMB, 5, 8)
   AND C.HGU (+)= SUBSTR(<PERSON><PERSON>, 13, 2)
   AND <PERSON><PERSON>HNO (+)= SUBSTR(<PERSON><PERSON>, 15, 4)
 ORDER BY A.<PERSON>, <PERSON><PERSON>, A.SNO, A.HGU, A.SNO

;


Rem - 계정과목을 불러온다
    GoSub Sub_Fn_Call

    Rem - 더존에 등록되어진 값을 불러온다
    GoSub Sub_DUZON_Call

    Rem - 은행코드를 불러온다
    GoSub Sub_BK_Call

    Rem++++++++++++++++++++++++++++++++++++++
    GLO_MDI_PROGRESS.Visible = True
    GLO_MDI_PROGRESS.Max = TmpRS.RecordCount
    GLO_MDI_PROGRESS.Value = 0
    Rem++++++++++++++++++++++++++++++++++++++
    FLEX(0).Redraw = False
    Do Until TmpRS.EOF

        Erase StrTmp

        Rem-변수값
        Rem - strTmp(0) : 계정명칭
        Rem - strTmp(1) : 더존 계정링크값
        Rem - strTmp(2) : 더존 등록상태값
        Rem - strTmp(3) : 은행 거래처 코드
        Rem - strTmp(4) : 은행 거래처 명칭
        Rem - strTmp(5) : 거래처 더존연동코드

        Rem*****************************************
        AdoFn.Filter = "FNCODE = '" & ZFormat(TmpRS!FNCODE) & "'"
        If AdoFn.RecordCount > 0 Then
            StrTmp(0) = "  (" & ZFormat(AdoFn!FN_NAME) & ") - " & IIf(ZFormat(AdoFn!FN_FIND) = "", ZFormat(AdoFn!FN_SNAME), ZFormat(AdoFn!FN_FIND))
            StrTmp(1) = ZFormat(AdoFn!FN_LINK)
        End If
        Rem*****************************************

        StrTmp(3) = ZFormat(TmpRS!CT_CODE)
        StrTmp(4) = ZFormat(TmpRS!CT_NAME)

        If LUSER.RCODE = "3000" Then
            StrTmp(5) = ZFormat(TmpRS!CT_LINK2)
        Else
            StrTmp(5) = ZFormat(TmpRS!CT_LINK)
        End If

        If Left(ZFormat(TmpRS!FNCODE), 6) = "111030" Or ZFormat(TmpRS!FNCODE) = "11105500" Then
            AdoBk.Filter = "BK_CODE = '" & ZFormat(TmpRS!MASTLINK) & "'"
            If AdoBk.RecordCount > 0 Then
                StrTmp(3) = ZFormat(AdoBk!CT_CODE)
                StrTmp(4) = ZFormat(AdoBk!CT_NAME) & "(" & ZFormat(AdoBk!BK_NAME) & ")"
                If LUSER.RCODE = "3000" Then
                    StrTmp(5) = ZFormat(AdoBk!BK_LINK2)
                Else
                    StrTmp(5) = ZFormat(AdoBk!BK_LINK)
                End If

            End If
        End If
        Rem*****************************************************************

        Rem*****************************************************************
        Rem - 대변,차변구분을 구한다
        Rem - 면세나 영세일경우 예수금이나 대급금을 0원으로 처리하는 것때문에
        If Val(ZFormat(TmpRS!DAMT)) = 0 And Val(ZFormat(TmpRS!CAMT)) = 0 And _
           (StrTmp(1) = "25500" Or StrTmp(1) = "13500") Then

            Rem - 연계되어진것이 매출이라면
            If Mid(ZFormat(TmpRS!REMB), 13, 2) = "13" Then
                strDCtype = "C"
            Else
                strDCtype = "D"
            End If
        Else
            strDCtype = IIf(Val(ZFormat(TmpRS!DAMT)) = 0, "C", "D")
        End If
        Rem*****************************************************************

        strGrid = ""
        strGrid = strGrid & ZFormat(TmpRS!Key) & vbTab
        strGrid = strGrid & "" & vbTab
        strGrid = strGrid & ZFormat(TmpRS!HDATE, "&&&&-&&-&&") & vbTab
        strGrid = strGrid & "회계자료" & vbTab
        strGrid = strGrid & IIf(StrTmp(4) = "", ZFormat(TmpRS!CTNAME), StrTmp(4)) & vbTab
        strGrid = strGrid & StrTmp(0) & vbTab

        strGrid = strGrid & ZFormat(TmpRS!DAMT, "#,0") & vbTab
        strGrid = strGrid & ZFormat(TmpRS!CAMT, "#,0") & vbTab

        strGrid = strGrid & IIf(StrTmp(1) = "", "X", "") & vbTab
        strGrid = strGrid & IIf(StrTmp(5) = "" And ZFormat(TmpRS!CT_NAME) <> "", "X", "") & vbTab   ''9번째
        strGrid = strGrid & ZFormat(TmpRS!REMA) & vbTab
        strGrid = strGrid & "" & vbTab

        strGrid = strGrid & "" & vbTab
        strGrid = strGrid & "" & vbTab
        strGrid = strGrid & "" & vbTab
        strGrid = strGrid & "" & vbTab
        strGrid = strGrid & "" & vbTab
        strGrid = strGrid & "" & vbTab

        strGrid = strGrid & ZFormat(TmpRS!HDATE) & vbTab
        strGrid = strGrid & ZFormat(TmpRS!HNO) & vbTab
        strGrid = strGrid & ZFormat(TmpRS!SNO) & vbTab

        Rem**********************************************************************
        If (strDCtype = "C" And StrTmp(1) = "25500") Or _
           (strDCtype = "D" And StrTmp(1) = "13500") Then

            strGrid = strGrid & IIf(StrTmp(1) = "13500", "23", "13") & vbTab
            strGrid = strGrid & StrTmp(1) & vbTab
            strGrid = strGrid & StrTmp(5) & vbTab   ''23번쨰

            If GLO_SPERP_CPYID = "1000" Then
                strGrid = strGrid & "3" & ZFormat(TmpRS!HNO) & vbTab
            Else
                strGrid = strGrid & "8" & ZFormat(TmpRS!HNO) & vbTab
            End If

            strGrid = strGrid & StrTmp(2) & vbTab

            If ZFormat(TmpRS!THDATE) = "" Then
                strGrid = strGrid & Left(Replace(ZFormat(TmpRS!REMB), "-", ""), 8) & vbTab
                strGrid = strGrid & Val(ZFormat(TmpRS!REMC)) & vbTab
            Else
                strGrid = strGrid & ZFormat(TmpRS!THDATE) & vbTab
                strGrid = strGrid & Val(ZFormat(TmpRS!HAMT)) & vbTab
            End If

            strGrid = strGrid & StrTmp(3) & vbTab

            Select Case ZFormat(TmpRS!TMSTYLE)
                Case "S"    '과세
                    Select Case ZFormat(TmpRS!ACSTYLE)
                        Case "1"            '일반
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "21", "11") & vbTab
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "과세매입", "과세매출") & vbTab
                        Case "2"            '신용카드
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "27", "17") & vbTab
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "카드매입", "카드매출") & vbTab
                        Case "3"            '현금영수증
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "28", "31") & vbTab
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "수입", "현금과세") & vbTab
                        Case "4"            '소매
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "25", "14") & vbTab
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "현금영수증매입", "건별매출") & vbTab
                        Case Else           '나머지들
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "21", "11") & vbTab
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "과세매입", "과세매출") & vbTab
                    End Select

                Case "O"    '영세
                    Select Case ZFormat(TmpRS!ACSTYLE)
                        Case "4"            '소매
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "25", "16") & vbTab
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "수입", "수출") & vbTab
                        Case Else
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "22", "12") & vbTab
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "영세매입", "영세매출") & vbTab
                    End Select

                Case "Z"    '면세
                    Select Case ZFormat(TmpRS!ACSTYLE)
                        Case "1"            '일반
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "23", "13") & vbTab
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "면세매입", "면세매출") & vbTab
                        Case "2"            '신용카드
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "23", "18") & vbTab
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "면세매입", "면세카드매출") & vbTab
                        Case "3"            '현금영수증
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "23", "32") & vbTab
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "면세매입", "현금면세") & vbTab

                        Case Else           '나머지들
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "23", "13") & vbTab
                            strGrid = strGrid & IIf(StrTmp(1) = "13500", "면세매입", "면세매출") & vbTab
                    End Select
                Case Else
                    strGrid = strGrid & IIf(StrTmp(1) = "13500", "21", "11") & vbTab
                    strGrid = strGrid & IIf(StrTmp(1) = "13500", "과세매입", "과세매출") & vbTab
            End Select

        Else
            strGrid = strGrid & ZFormat(TmpRS!HGU) & vbTab
            strGrid = strGrid & StrTmp(1) & vbTab
            strGrid = strGrid & StrTmp(5) & vbTab   ''23번쨰

            If GLO_SPERP_CPYID = "1000" Then
                strGrid = strGrid & "3" & ZFormat(TmpRS!HNO) & vbTab
            Else
                strGrid = strGrid & "8" & ZFormat(TmpRS!HNO) & vbTab
            End If


            strGrid = strGrid & StrTmp(2) & vbTab
            strGrid = strGrid & "" & vbTab
            strGrid = strGrid & "" & vbTab
            strGrid = strGrid & StrTmp(3) & vbTab
            strGrid = strGrid & "" & vbTab
            strGrid = strGrid & "" & vbTab
        End If
        strGrid = strGrid & strDCtype & vbTab   '대차구분

        FLEX(0).AddItem strGrid

        FLEX(0).Cell(flexcpBackColor, FLEX(0).Rows - 1, 0, FLEX(0).Rows - 1, FLEX(0).Cols - 1) = objColor

        If FLEX(0).TextMatrix(FLEX(0).Rows - 1, 8) <> "" Then FLEX(0).Cell(flexcpForeColor, FLEX(0).Rows - 1, 8) = vbRed
        If FLEX(0).TextMatrix(FLEX(0).Rows - 1, 9) <> "" Then FLEX(0).Cell(flexcpForeColor, FLEX(0).Rows - 1, 9) = vbRed
        Rem*****************************************************************

        Rem******************************************************************************
        If strTmp2 <> ZFormat(TmpRS!HDATE) & ZFormat(TmpRS!HNO) & ZFormat(TmpRS!HGU) Then
            lngTmp2 = lngTmp2 + 1
            If objColor <> vbWhite Then
                objColor = vbWhite
            Else
                objColor = &HDDFFD9
            End If
        End If
        strTmp2 = ZFormat(TmpRS!HDATE) & ZFormat(TmpRS!HNO) & ZFormat(TmpRS!HGU)

        FLEX(0).Cell(flexcpBackColor, FLEX(0).Rows - 1, 0, FLEX(0).Rows - 1, FLEX(0).Cols - 1) = objColor
        Rem******************************************************************************

        TmpRS.MoveNext

        If FLEX(0).Rows = 2 Or FLEX(0).Rows = 30 Then FLEX(0).AutoSize 5, FLEX(0).Cols - 1, , 30

        Rem++++++++++++++++++++++++++++++++++++++
        GLO_MDI_PROGRESS.Value = GLO_MDI_PROGRESS.Value + 1
        Rem++++++++++++++++++++++++++++++++++++++
    Loop

    If FLEX(0).Rows < 20 Then FLEX(0).AutoSize 5, FLEX(0).Cols - 1, , 30

    Rem******************************************
    Rem - 21번째의 값중 회계값이 아니고 매입또는 매출로 설정되어 있다면 같은색깔은 모두 값을 바꾼다
    For lngtmp1 = FLEX(0).FixedRows To FLEX(0).Rows - 1
        strTmp1 = Trim(FLEX(0).TextMatrix(lngtmp1, 21))
        If strTmp1 <> "70" Then
            For lngTmp2 = lngtmp1 To FLEX(0).Rows - 1
                If FLEX(0).Cell(flexcpBackColor, lngtmp1, 21) <> FLEX(0).Cell(flexcpBackColor, lngTmp2, 21) Then Exit For
                FLEX(0).TextMatrix(lngTmp2, 21) = strTmp1
            Next

            For lngTmp2 = lngtmp1 To FLEX(0).FixedRows Step -1
                If FLEX(0).Cell(flexcpBackColor, lngtmp1, 21) <> FLEX(0).Cell(flexcpBackColor, lngTmp2, 21) Then Exit For
                FLEX(0).TextMatrix(lngTmp2, 21) = strTmp1
            Next
        End If
    Next
    Rem******************************************

    Rem******************************************

    Rem++++++++++++++++++++++++++++++++++++++
    If AdoDz.RecordCount > 0 Then
        GLO_MDI_PROGRESS.Visible = True
        GLO_MDI_PROGRESS.Max = AdoDz.RecordCount
        GLO_MDI_PROGRESS.Value = 0
    End If
    Rem++++++++++++++++++++++++++++++++++++++
    Do Until AdoDz.EOF
'    매입 1 = 6
'    매출 2 = 7
'    회계 3 = 8
'    수불 4 = 9
        If GLO_SPERP_CPYID = "1000" Then
            strTmp1 = ZFormat(AdoDz!IN_DT) & Format(Val(AdoDz!IN_SQ) - 30000, "0000")
        Else
            strTmp1 = ZFormat(AdoDz!IN_DT) & Format(Val(AdoDz!IN_SQ) - 80000, "0000")
        End If



        lngtmp1 = FLEX(0).FindRow(strTmp1, , 0)

        If lngtmp1 > 0 Then
            FLEX(0).TextMatrix(lngtmp1, 25) = ZFormat(AdoDz!ISU_DT)

            If ZFormat(AdoDz!ISU_DT) = "00000000" Then
                FLEX(0).TextMatrix(lngtmp1, 11) = "등록완료"
                FLEX(0).Cell(flexcpForeColor, lngtmp1, 11) = vbBlue
            End If

            If ZFormat(AdoDz!ISU_DT) <> "00000000" And ZFormat(AdoDz!ISU_DT) <> "" Then
                FLEX(0).TextMatrix(lngtmp1, 11) = "재등록 불가"
                FLEX(0).Cell(flexcpForeColor, lngtmp1, 11) = vbRed
            End If
        End If

        AdoDz.MoveNext

        Rem++++++++++++++++++++++++++++++++++++++
        GLO_MDI_PROGRESS.Value = GLO_MDI_PROGRESS.Value + 1
        Rem++++++++++++++++++++++++++++++++++++++
    Loop
    AdoDz.Close: Set AdoDz = Nothing
    Rem******************************************

    Rem++++++++++++++++++++++++++++++++++++++
    GLO_MDI_PROGRESS.Visible = False
    Rem++++++++++++++++++++++++++++++++++++++

    FLEX(0).Redraw = True

    Rem++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    GLO_MDI_MSGPANEL = lngTmp2 & "건의 자료를 검색하였습니다..... "
    GLO_MDI_TIMER.Enabled = True
    Rem++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

    Exit Sub


Rem================================
Sub_DUZON_Call:

    Rem*****************************************************************************
    tpSQL = ""
    tpSQL = tpSQL & " SELECT A.IN_DT, A.IN_SQ, A.LN_SQ, A.ISU_DT, A.ISU_SQ " & vbLf
    tpSQL = tpSQL & " FROM SAUTODOCUD A " & vbLf
    tpSQL = tpSQL & " WHERE A.CO_CD = '" & GLO_DUZON_CPYID & "'  AND A.IN_DIV_CD = '" & GLO_DUZON_DIVCD & "'  " & vbLf
    tpSQL = tpSQL & "   AND A.IN_DT BETWEEN '" & Replace(RDate(0).Value, "-", "") & "' AND '" & Replace(RDate(1).Value, "-", "") & "' " & vbLf
    tpSQL = tpSQL & "   AND ( A.IN_SQ BETWEEN 30000 AND 39999 OR  A.IN_SQ BETWEEN 80000 AND 89999 ) " & vbLf

    Set AdoDz = New ADODB.Recordset
    AdoDz.Open tpSQL, SQLDB, adOpenStatic, adLockReadOnly
    Rem*****************************************************************************

    Return


Rem================================
Sub_Fn_Call:

    Rem*****************************************************************************
    tpSQL = ""
    tpSQL = tpSQL & " SELECT (FN_CD1 || FN_CD2) AS FNCODE, A.FN_NAME, A.FN_FIND, A.FN_SNAME, nvl(A.FN_LINK2,A.FN_LINK) FN_LINK" & vbLf
    tpSQL = tpSQL & " FROM FN A " & vbLf

    Set AdoFn = ORADB.DB_TmpRS(tpSQL)
    Rem*****************************************************************************

    Return


Rem================================
Sub_BK_Call:

    Rem*****************************************************************************
    tpSQL = ""
    tpSQL = tpSQL & " SELECT A.BK_CODE, A.BK_NAME, B.CT_CODE, B.CT_NAME, A.BK_LINK, A.BK_LINK2" & vbLf
    tpSQL = tpSQL & " FROM BK A, CT B " & vbLf
    tpSQL = tpSQL & " WHERE A.BANK_CODE = B.CT_CODE " & vbLf

    Set AdoBk = ORADB.DB_TmpRS(tpSQL)
    Rem*****************************************************************************

    Return

Rem================================
Error_Rtn:
    DispError Err
    DispError2 tpSQL
End Sub