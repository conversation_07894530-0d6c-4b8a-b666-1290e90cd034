<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_Date</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_ExcelToPHP" title="ExcelToPHP :: Convert a date from Excel to PHP"><span class="description">Convert a date from Excel to PHP</span><pre>ExcelToPHP()</pre></a></li>
<li class="method public "><a href="#method_ExcelToPHPObject" title="ExcelToPHPObject :: Convert a date from Excel to a PHP Date/Time object"><span class="description">Convert a date from Excel to a PHP Date/Time object</span><pre>ExcelToPHPObject()</pre></a></li>
<li class="method public "><a href="#method_FormattedPHPToExcel" title="FormattedPHPToExcel :: FormattedPHPToExcel"><span class="description">FormattedPHPToExcel</span><pre>FormattedPHPToExcel()</pre></a></li>
<li class="method public "><a href="#method_PHPToExcel" title="PHPToExcel :: Convert a date from PHP to Excel"><span class="description">Convert a date from PHP to Excel</span><pre>PHPToExcel()</pre></a></li>
<li class="method public "><a href="#method_dayStringToNumber" title="dayStringToNumber :: "><span class="description">dayStringToNumber()
        </span><pre>dayStringToNumber()</pre></a></li>
<li class="method public "><a href="#method_getExcelCalendar" title="getExcelCalendar :: Return the Excel calendar (Windows 1900 or Mac 1904)"><span class="description">Return the Excel calendar (Windows 1900 or Mac 1904)</span><pre>getExcelCalendar()</pre></a></li>
<li class="method public "><a href="#method_isDateTime" title="isDateTime :: Is a given cell a date/time?"><span class="description">Is a given cell a date/time?</span><pre>isDateTime()</pre></a></li>
<li class="method public "><a href="#method_isDateTimeFormat" title="isDateTimeFormat :: Is a given number format a date/time?"><span class="description">Is a given number format a date/time?</span><pre>isDateTimeFormat()</pre></a></li>
<li class="method public "><a href="#method_isDateTimeFormatCode" title="isDateTimeFormatCode :: Is a given number format code a date/time?"><span class="description">Is a given number format code a date/time?</span><pre>isDateTimeFormatCode()</pre></a></li>
<li class="method public "><a href="#method_monthStringToNumber" title="monthStringToNumber :: "><span class="description">monthStringToNumber()
        </span><pre>monthStringToNumber()</pre></a></li>
<li class="method public "><a href="#method_setExcelCalendar" title="setExcelCalendar :: Set the Excel calendar (Windows 1900 or Mac 1904)"><span class="description">Set the Excel calendar (Windows 1900 or Mac 1904)</span><pre>setExcelCalendar()</pre></a></li>
<li class="method public "><a href="#method_stringToExcel" title="stringToExcel :: Convert a date/time string to Excel time"><span class="description">Convert a date/time string to Excel time</span><pre>stringToExcel()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul>
<li class="property public "><a href="#property__monthNames" title="$_monthNames :: "><span class="description"></span><pre>$_monthNames</pre></a></li>
<li class="property public "><a href="#property__numberSuffixes" title="$_numberSuffixes :: "><span class="description"></span><pre>$_numberSuffixes</pre></a></li>
</ul>
</li>
<li class="nav-header protected">» Protected
                    <ul><li class="property protected "><a href="#property__excelBaseDate" title="$_excelBaseDate :: "><span class="description"></span><pre>$_excelBaseDate</pre></a></li></ul>
</li>
<li class="nav-header private">» Private
                    <ul><li class="property private "><a href="#property_possibleDateFormatCharacters" title="$possibleDateFormatCharacters :: "><span class="description"></span><pre>$possibleDateFormatCharacters</pre></a></li></ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_CALENDAR_MAC_1904" title="CALENDAR_MAC_1904 :: "><span class="description">CALENDAR_MAC_1904</span><pre>CALENDAR_MAC_1904</pre></a></li>
<li class="constant  "><a href="#constant_CALENDAR_WINDOWS_1900" title="CALENDAR_WINDOWS_1900 :: constants"><span class="description">constants</span><pre>CALENDAR_WINDOWS_1900</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_Date"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_Date.html">PHPExcel_Shared_Date</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Shared_Date</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Shared.html">PHPExcel_Shared</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_ExcelToPHP"></a><div class="element clickable method public method_ExcelToPHP" data-toggle="collapse" data-target=".method_ExcelToPHP .collapse">
<h2>Convert a date from Excel to PHP</h2>
<pre>ExcelToPHP(\long $dateValue, boolean $adjustToTimezone, string $timezone) : \long</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>\long</code><p>Excel date/time value</p>
</div>
<div class="subelement argument">
<h4>$adjustToTimezone</h4>
<code>boolean</code><p>Flag indicating whether $dateValue should be treated as
                                                a UST timestamp, or adjusted to UST</p>
</div>
<div class="subelement argument">
<h4>$timezone</h4>
<code>string</code><p>The timezone for finding the adjustment from UST</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>\long</code>PHP serialized date/time</div>
</div></div>
</div>
<a id="method_ExcelToPHPObject"></a><div class="element clickable method public method_ExcelToPHPObject" data-toggle="collapse" data-target=".method_ExcelToPHPObject .collapse">
<h2>Convert a date from Excel to a PHP Date/Time object</h2>
<pre>ExcelToPHPObject(integer $dateValue) : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>integer</code><p>Excel date/time value</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>PHP date/time object</div>
</div></div>
</div>
<a id="method_FormattedPHPToExcel"></a><div class="element clickable method public method_FormattedPHPToExcel" data-toggle="collapse" data-target=".method_FormattedPHPToExcel .collapse">
<h2>FormattedPHPToExcel</h2>
<pre>FormattedPHPToExcel(\long $year, \long $month, \long $day, \long $hours, \long $minutes, \long $seconds) : \long</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$year</h4>
<code>\long</code>
</div>
<div class="subelement argument">
<h4>$month</h4>
<code>\long</code>
</div>
<div class="subelement argument">
<h4>$day</h4>
<code>\long</code>
</div>
<div class="subelement argument">
<h4>$hours</h4>
<code>\long</code>
</div>
<div class="subelement argument">
<h4>$minutes</h4>
<code>\long</code>
</div>
<div class="subelement argument">
<h4>$seconds</h4>
<code>\long</code>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>\long</code>Excel date/time value</div>
</div></div>
</div>
<a id="method_PHPToExcel"></a><div class="element clickable method public method_PHPToExcel" data-toggle="collapse" data-target=".method_PHPToExcel .collapse">
<h2>Convert a date from PHP to Excel</h2>
<pre>PHPToExcel(mixed $dateValue, boolean $adjustToTimezone, string $timezone) : mixed</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>mixed</code><p>PHP serialized date/time or date object</p>
</div>
<div class="subelement argument">
<h4>$adjustToTimezone</h4>
<code>boolean</code><p>Flag indicating whether $dateValue should be treated as
                                                a UST timestamp, or adjusted to UST</p>
</div>
<div class="subelement argument">
<h4>$timezone</h4>
<code>string</code><p>The timezone for finding the adjustment from UST</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>mixed</code>Excel date/time value
						or boolean FALSE on failure</div>
</div></div>
</div>
<a id="method_dayStringToNumber"></a><div class="element clickable method public method_dayStringToNumber" data-toggle="collapse" data-target=".method_dayStringToNumber .collapse">
<h2>dayStringToNumber()
        </h2>
<pre>dayStringToNumber($day) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$day</h4></div>
</div></div>
</div>
<a id="method_getExcelCalendar"></a><div class="element clickable method public method_getExcelCalendar" data-toggle="collapse" data-target=".method_getExcelCalendar .collapse">
<h2>Return the Excel calendar (Windows 1900 or Mac 1904)</h2>
<pre>getExcelCalendar() : integer</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>integer</code>Excel base date (1900 or 1904)</div>
</div></div>
</div>
<a id="method_isDateTime"></a><div class="element clickable method public method_isDateTime" data-toggle="collapse" data-target=".method_isDateTime .collapse">
<h2>Is a given cell a date/time?</h2>
<pre>isDateTime(\PHPExcel_Cell $pCell) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pCell</h4>
<code><a href="../classes/PHPExcel_Cell.html">\PHPExcel_Cell</a></code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_isDateTimeFormat"></a><div class="element clickable method public method_isDateTimeFormat" data-toggle="collapse" data-target=".method_isDateTimeFormat .collapse">
<h2>Is a given number format a date/time?</h2>
<pre>isDateTimeFormat(\PHPExcel_Style_NumberFormat $pFormat) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pFormat</h4>
<code><a href="../classes/PHPExcel_Style_NumberFormat.html">\PHPExcel_Style_NumberFormat</a></code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_isDateTimeFormatCode"></a><div class="element clickable method public method_isDateTimeFormatCode" data-toggle="collapse" data-target=".method_isDateTimeFormatCode .collapse">
<h2>Is a given number format code a date/time?</h2>
<pre>isDateTimeFormatCode(string $pFormatCode) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pFormatCode</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_monthStringToNumber"></a><div class="element clickable method public method_monthStringToNumber" data-toggle="collapse" data-target=".method_monthStringToNumber .collapse">
<h2>monthStringToNumber()
        </h2>
<pre>monthStringToNumber($month) </pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$month</h4></div>
</div></div>
</div>
<a id="method_setExcelCalendar"></a><div class="element clickable method public method_setExcelCalendar" data-toggle="collapse" data-target=".method_setExcelCalendar .collapse">
<h2>Set the Excel calendar (Windows 1900 or Mac 1904)</h2>
<pre>setExcelCalendar(integer $baseDate) : boolean</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$baseDate</h4>
<code>integer</code><p>Excel base date (1900 or 1904)</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>boolean</code>Success or failure</div>
</div></div>
</div>
<a id="method_stringToExcel"></a><div class="element clickable method public method_stringToExcel" data-toggle="collapse" data-target=".method_stringToExcel .collapse">
<h2>Convert a date/time string to Excel time</h2>
<pre>stringToExcel(string $dateValue) : float | FALSE</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$dateValue</h4>
<code>string</code><p>Examples: '2009-12-31', '2009-12-31 15:59', '2009-12-31 15:59:10'</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>float</code><code>FALSE</code>Excel date/time serial value</div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__monthNames"> </a><div class="element clickable property public property__monthNames" data-toggle="collapse" data-target=".property__monthNames .collapse">
<h2></h2>
<pre>$_monthNames </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__numberSuffixes"> </a><div class="element clickable property public property__numberSuffixes" data-toggle="collapse" data-target=".property__numberSuffixes .collapse">
<h2></h2>
<pre>$_numberSuffixes </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__excelBaseDate"> </a><div class="element clickable property protected property__excelBaseDate" data-toggle="collapse" data-target=".property__excelBaseDate .collapse">
<h2></h2>
<pre>$_excelBaseDate </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_possibleDateFormatCharacters"> </a><div class="element clickable property private property_possibleDateFormatCharacters" data-toggle="collapse" data-target=".property_possibleDateFormatCharacters .collapse">
<h2></h2>
<pre>$possibleDateFormatCharacters </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_CALENDAR_MAC_1904"> </a><div class="element clickable constant  constant_CALENDAR_MAC_1904" data-toggle="collapse" data-target=".constant_CALENDAR_MAC_1904 .collapse">
<h2>CALENDAR_MAC_1904</h2>
<pre>CALENDAR_MAC_1904 </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_CALENDAR_WINDOWS_1900"> </a><div class="element clickable constant  constant_CALENDAR_WINDOWS_1900" data-toggle="collapse" data-target=".constant_CALENDAR_WINDOWS_1900 .collapse">
<h2>constants</h2>
<pre>CALENDAR_WINDOWS_1900 </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:36Z.<br></footer></div>
</div>
</body>
</html>
