<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PclZip</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_PclZip" title="PclZip :: "><span class="description">PclZip()
        </span><pre>PclZip()</pre></a></li>
<li class="method public "><a href="#method_add" title="add :: "><span class="description">add()
        </span><pre>add()</pre></a></li>
<li class="method public "><a href="#method_create" title="create :: "><span class="description">create()
        </span><pre>create()</pre></a></li>
<li class="method public "><a href="#method_delete" title="delete :: "><span class="description">delete()
        </span><pre>delete()</pre></a></li>
<li class="method public "><a href="#method_deleteByIndex" title="deleteByIndex :: "><span class="description">deleteByIndex()
        </span><pre>deleteByIndex()</pre></a></li>
<li class="method public "><a href="#method_duplicate" title="duplicate :: "><span class="description">duplicate()
        </span><pre>duplicate()</pre></a></li>
<li class="method public "><a href="#method_errorCode" title="errorCode :: "><span class="description">errorCode()
        </span><pre>errorCode()</pre></a></li>
<li class="method public "><a href="#method_errorInfo" title="errorInfo :: "><span class="description">errorInfo()
        </span><pre>errorInfo()</pre></a></li>
<li class="method public "><a href="#method_errorName" title="errorName :: "><span class="description">errorName()
        </span><pre>errorName()</pre></a></li>
<li class="method public "><a href="#method_extract" title="extract :: "><span class="description">extract()
        </span><pre>extract()</pre></a></li>
<li class="method public "><a href="#method_extractByIndex" title="extractByIndex :: "><span class="description">extractByIndex()
        </span><pre>extractByIndex()</pre></a></li>
<li class="method public "><a href="#method_listContent" title="listContent :: "><span class="description">listContent()
        </span><pre>listContent()</pre></a></li>
<li class="method public "><a href="#method_merge" title="merge :: "><span class="description">merge()
        </span><pre>merge()</pre></a></li>
<li class="method public "><a href="#method_privAdd" title="privAdd :: "><span class="description">privAdd()
        </span><pre>privAdd()</pre></a></li>
<li class="method public "><a href="#method_privAddFile" title="privAddFile :: "><span class="description">privAddFile()
        </span><pre>privAddFile()</pre></a></li>
<li class="method public "><a href="#method_privAddFileList" title="privAddFileList :: "><span class="description">privAddFileList()
        </span><pre>privAddFileList()</pre></a></li>
<li class="method public "><a href="#method_privAddFileUsingTempFile" title="privAddFileUsingTempFile :: "><span class="description">privAddFileUsingTempFile()
        </span><pre>privAddFileUsingTempFile()</pre></a></li>
<li class="method public "><a href="#method_privAddList" title="privAddList :: "><span class="description">privAddList()
        </span><pre>privAddList()</pre></a></li>
<li class="method public "><a href="#method_privCalculateStoredFilename" title="privCalculateStoredFilename :: "><span class="description">privCalculateStoredFilename()
        </span><pre>privCalculateStoredFilename()</pre></a></li>
<li class="method public "><a href="#method_privCheckFileHeaders" title="privCheckFileHeaders :: "><span class="description">privCheckFileHeaders()
        </span><pre>privCheckFileHeaders()</pre></a></li>
<li class="method public "><a href="#method_privCheckFormat" title="privCheckFormat :: "><span class="description">privCheckFormat()
        </span><pre>privCheckFormat()</pre></a></li>
<li class="method public "><a href="#method_privCloseFd" title="privCloseFd :: "><span class="description">privCloseFd()
        </span><pre>privCloseFd()</pre></a></li>
<li class="method public "><a href="#method_privConvertHeader2FileInfo" title="privConvertHeader2FileInfo :: "><span class="description">privConvertHeader2FileInfo()
        </span><pre>privConvertHeader2FileInfo()</pre></a></li>
<li class="method public "><a href="#method_privCreate" title="privCreate :: "><span class="description">privCreate()
        </span><pre>privCreate()</pre></a></li>
<li class="method public "><a href="#method_privDeleteByRule" title="privDeleteByRule :: "><span class="description">privDeleteByRule()
        </span><pre>privDeleteByRule()</pre></a></li>
<li class="method public "><a href="#method_privDirCheck" title="privDirCheck :: "><span class="description">privDirCheck()
        </span><pre>privDirCheck()</pre></a></li>
<li class="method public "><a href="#method_privDisableMagicQuotes" title="privDisableMagicQuotes :: "><span class="description">privDisableMagicQuotes()
        </span><pre>privDisableMagicQuotes()</pre></a></li>
<li class="method public "><a href="#method_privDuplicate" title="privDuplicate :: "><span class="description">privDuplicate()
        </span><pre>privDuplicate()</pre></a></li>
<li class="method public "><a href="#method_privErrorLog" title="privErrorLog :: "><span class="description">privErrorLog()
        </span><pre>privErrorLog()</pre></a></li>
<li class="method public "><a href="#method_privErrorReset" title="privErrorReset :: "><span class="description">privErrorReset()
        </span><pre>privErrorReset()</pre></a></li>
<li class="method public "><a href="#method_privExtractByRule" title="privExtractByRule :: "><span class="description">privExtractByRule()
        </span><pre>privExtractByRule()</pre></a></li>
<li class="method public "><a href="#method_privExtractFile" title="privExtractFile :: "><span class="description">privExtractFile()
        </span><pre>privExtractFile()</pre></a></li>
<li class="method public "><a href="#method_privExtractFileAsString" title="privExtractFileAsString :: "><span class="description">privExtractFileAsString()
        </span><pre>privExtractFileAsString()</pre></a></li>
<li class="method public "><a href="#method_privExtractFileInOutput" title="privExtractFileInOutput :: "><span class="description">privExtractFileInOutput()
        </span><pre>privExtractFileInOutput()</pre></a></li>
<li class="method public "><a href="#method_privExtractFileUsingTempFile" title="privExtractFileUsingTempFile :: "><span class="description">privExtractFileUsingTempFile()
        </span><pre>privExtractFileUsingTempFile()</pre></a></li>
<li class="method public "><a href="#method_privFileDescrExpand" title="privFileDescrExpand :: "><span class="description">privFileDescrExpand()
        </span><pre>privFileDescrExpand()</pre></a></li>
<li class="method public "><a href="#method_privFileDescrParseAtt" title="privFileDescrParseAtt :: "><span class="description">privFileDescrParseAtt()
        </span><pre>privFileDescrParseAtt()</pre></a></li>
<li class="method public "><a href="#method_privList" title="privList :: "><span class="description">privList()
        </span><pre>privList()</pre></a></li>
<li class="method public "><a href="#method_privMerge" title="privMerge :: "><span class="description">privMerge()
        </span><pre>privMerge()</pre></a></li>
<li class="method public "><a href="#method_privOpenFd" title="privOpenFd :: "><span class="description">privOpenFd()
        </span><pre>privOpenFd()</pre></a></li>
<li class="method public "><a href="#method_privOptionDefaultThreshold" title="privOptionDefaultThreshold :: "><span class="description">privOptionDefaultThreshold()
        </span><pre>privOptionDefaultThreshold()</pre></a></li>
<li class="method public "><a href="#method_privParseOptions" title="privParseOptions :: "><span class="description">privParseOptions()
        </span><pre>privParseOptions()</pre></a></li>
<li class="method public "><a href="#method_privReadCentralFileHeader" title="privReadCentralFileHeader :: "><span class="description">privReadCentralFileHeader()
        </span><pre>privReadCentralFileHeader()</pre></a></li>
<li class="method public "><a href="#method_privReadEndCentralDir" title="privReadEndCentralDir :: "><span class="description">privReadEndCentralDir()
        </span><pre>privReadEndCentralDir()</pre></a></li>
<li class="method public "><a href="#method_privReadFileHeader" title="privReadFileHeader :: "><span class="description">privReadFileHeader()
        </span><pre>privReadFileHeader()</pre></a></li>
<li class="method public "><a href="#method_privSwapBackMagicQuotes" title="privSwapBackMagicQuotes :: "><span class="description">privSwapBackMagicQuotes()
        </span><pre>privSwapBackMagicQuotes()</pre></a></li>
<li class="method public "><a href="#method_privWriteCentralFileHeader" title="privWriteCentralFileHeader :: "><span class="description">privWriteCentralFileHeader()
        </span><pre>privWriteCentralFileHeader()</pre></a></li>
<li class="method public "><a href="#method_privWriteCentralHeader" title="privWriteCentralHeader :: "><span class="description">privWriteCentralHeader()
        </span><pre>privWriteCentralHeader()</pre></a></li>
<li class="method public "><a href="#method_privWriteFileHeader" title="privWriteFileHeader :: "><span class="description">privWriteFileHeader()
        </span><pre>privWriteFileHeader()</pre></a></li>
<li class="method public "><a href="#method_properties" title="properties :: "><span class="description">properties()
        </span><pre>properties()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul>
<li class="property public "><a href="#property_error_code" title="$error_code :: "><span class="description"></span><pre>$error_code</pre></a></li>
<li class="property public "><a href="#property_error_string" title="$error_string :: "><span class="description"></span><pre>$error_string</pre></a></li>
<li class="property public "><a href="#property_magic_quotes_status" title="$magic_quotes_status :: "><span class="description"></span><pre>$magic_quotes_status</pre></a></li>
<li class="property public "><a href="#property_zip_fd" title="$zip_fd :: "><span class="description"></span><pre>$zip_fd</pre></a></li>
<li class="property public "><a href="#property_zipname" title="$zipname :: "><span class="description"></span><pre>$zipname</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PclZip"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PclZip.html">PclZip</a>
</li>
</ul>
<div class="element class"><div class="details">
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_PclZip"></a><div class="element clickable method public method_PclZip" data-toggle="collapse" data-target=".method_PclZip .collapse">
<h2>PclZip()
        </h2>
<pre>PclZip($p_zipname) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_zipname</h4></div>
</div></div>
</div>
<a id="method_add"></a><div class="element clickable method public method_add" data-toggle="collapse" data-target=".method_add .collapse">
<h2>add()
        </h2>
<pre>add($p_filelist) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_filelist</h4></div>
</div></div>
</div>
<a id="method_create"></a><div class="element clickable method public method_create" data-toggle="collapse" data-target=".method_create .collapse">
<h2>create()
        </h2>
<pre>create($p_filelist) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_filelist</h4></div>
</div></div>
</div>
<a id="method_delete"></a><div class="element clickable method public method_delete" data-toggle="collapse" data-target=".method_delete .collapse">
<h2>delete()
        </h2>
<pre>delete() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_deleteByIndex"></a><div class="element clickable method public method_deleteByIndex" data-toggle="collapse" data-target=".method_deleteByIndex .collapse">
<h2>deleteByIndex()
        </h2>
<pre>deleteByIndex($p_index) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_index</h4></div>
</div></div>
</div>
<a id="method_duplicate"></a><div class="element clickable method public method_duplicate" data-toggle="collapse" data-target=".method_duplicate .collapse">
<h2>duplicate()
        </h2>
<pre>duplicate($p_archive) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_archive</h4></div>
</div></div>
</div>
<a id="method_errorCode"></a><div class="element clickable method public method_errorCode" data-toggle="collapse" data-target=".method_errorCode .collapse">
<h2>errorCode()
        </h2>
<pre>errorCode() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_errorInfo"></a><div class="element clickable method public method_errorInfo" data-toggle="collapse" data-target=".method_errorInfo .collapse">
<h2>errorInfo()
        </h2>
<pre>errorInfo($p_full) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_full</h4></div>
</div></div>
</div>
<a id="method_errorName"></a><div class="element clickable method public method_errorName" data-toggle="collapse" data-target=".method_errorName .collapse">
<h2>errorName()
        </h2>
<pre>errorName($p_with_code) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_with_code</h4></div>
</div></div>
</div>
<a id="method_extract"></a><div class="element clickable method public method_extract" data-toggle="collapse" data-target=".method_extract .collapse">
<h2>extract()
        </h2>
<pre>extract() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_extractByIndex"></a><div class="element clickable method public method_extractByIndex" data-toggle="collapse" data-target=".method_extractByIndex .collapse">
<h2>extractByIndex()
        </h2>
<pre>extractByIndex($p_index) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_index</h4></div>
</div></div>
</div>
<a id="method_listContent"></a><div class="element clickable method public method_listContent" data-toggle="collapse" data-target=".method_listContent .collapse">
<h2>listContent()
        </h2>
<pre>listContent() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_merge"></a><div class="element clickable method public method_merge" data-toggle="collapse" data-target=".method_merge .collapse">
<h2>merge()
        </h2>
<pre>merge($p_archive_to_add) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_archive_to_add</h4></div>
</div></div>
</div>
<a id="method_privAdd"></a><div class="element clickable method public method_privAdd" data-toggle="collapse" data-target=".method_privAdd .collapse">
<h2>privAdd()
        </h2>
<pre>privAdd($p_filedescr_list, $p_result_list, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_filedescr_list</h4></div>
<div class="subelement argument"><h4>$p_result_list</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privAddFile"></a><div class="element clickable method public method_privAddFile" data-toggle="collapse" data-target=".method_privAddFile .collapse">
<h2>privAddFile()
        </h2>
<pre>privAddFile($p_filedescr, $p_header, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_filedescr</h4></div>
<div class="subelement argument"><h4>$p_header</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privAddFileList"></a><div class="element clickable method public method_privAddFileList" data-toggle="collapse" data-target=".method_privAddFileList .collapse">
<h2>privAddFileList()
        </h2>
<pre>privAddFileList($p_filedescr_list, $p_result_list, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_filedescr_list</h4></div>
<div class="subelement argument"><h4>$p_result_list</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privAddFileUsingTempFile"></a><div class="element clickable method public method_privAddFileUsingTempFile" data-toggle="collapse" data-target=".method_privAddFileUsingTempFile .collapse">
<h2>privAddFileUsingTempFile()
        </h2>
<pre>privAddFileUsingTempFile($p_filedescr, $p_header, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_filedescr</h4></div>
<div class="subelement argument"><h4>$p_header</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privAddList"></a><div class="element clickable method public method_privAddList" data-toggle="collapse" data-target=".method_privAddList .collapse">
<h2>privAddList()
        </h2>
<pre>privAddList($p_filedescr_list, $p_result_list, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_filedescr_list</h4></div>
<div class="subelement argument"><h4>$p_result_list</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privCalculateStoredFilename"></a><div class="element clickable method public method_privCalculateStoredFilename" data-toggle="collapse" data-target=".method_privCalculateStoredFilename .collapse">
<h2>privCalculateStoredFilename()
        </h2>
<pre>privCalculateStoredFilename($p_filedescr, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_filedescr</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privCheckFileHeaders"></a><div class="element clickable method public method_privCheckFileHeaders" data-toggle="collapse" data-target=".method_privCheckFileHeaders .collapse">
<h2>privCheckFileHeaders()
        </h2>
<pre>privCheckFileHeaders($p_local_header, $p_central_header) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_local_header</h4></div>
<div class="subelement argument"><h4>$p_central_header</h4></div>
</div></div>
</div>
<a id="method_privCheckFormat"></a><div class="element clickable method public method_privCheckFormat" data-toggle="collapse" data-target=".method_privCheckFormat .collapse">
<h2>privCheckFormat()
        </h2>
<pre>privCheckFormat($p_level) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_level</h4></div>
</div></div>
</div>
<a id="method_privCloseFd"></a><div class="element clickable method public method_privCloseFd" data-toggle="collapse" data-target=".method_privCloseFd .collapse">
<h2>privCloseFd()
        </h2>
<pre>privCloseFd() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_privConvertHeader2FileInfo"></a><div class="element clickable method public method_privConvertHeader2FileInfo" data-toggle="collapse" data-target=".method_privConvertHeader2FileInfo .collapse">
<h2>privConvertHeader2FileInfo()
        </h2>
<pre>privConvertHeader2FileInfo($p_header, $p_info) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_header</h4></div>
<div class="subelement argument"><h4>$p_info</h4></div>
</div></div>
</div>
<a id="method_privCreate"></a><div class="element clickable method public method_privCreate" data-toggle="collapse" data-target=".method_privCreate .collapse">
<h2>privCreate()
        </h2>
<pre>privCreate($p_filedescr_list, $p_result_list, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_filedescr_list</h4></div>
<div class="subelement argument"><h4>$p_result_list</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privDeleteByRule"></a><div class="element clickable method public method_privDeleteByRule" data-toggle="collapse" data-target=".method_privDeleteByRule .collapse">
<h2>privDeleteByRule()
        </h2>
<pre>privDeleteByRule($p_result_list, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_result_list</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privDirCheck"></a><div class="element clickable method public method_privDirCheck" data-toggle="collapse" data-target=".method_privDirCheck .collapse">
<h2>privDirCheck()
        </h2>
<pre>privDirCheck($p_dir, $p_is_dir) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_dir</h4></div>
<div class="subelement argument"><h4>$p_is_dir</h4></div>
</div></div>
</div>
<a id="method_privDisableMagicQuotes"></a><div class="element clickable method public method_privDisableMagicQuotes" data-toggle="collapse" data-target=".method_privDisableMagicQuotes .collapse">
<h2>privDisableMagicQuotes()
        </h2>
<pre>privDisableMagicQuotes() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_privDuplicate"></a><div class="element clickable method public method_privDuplicate" data-toggle="collapse" data-target=".method_privDuplicate .collapse">
<h2>privDuplicate()
        </h2>
<pre>privDuplicate($p_archive_filename) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_archive_filename</h4></div>
</div></div>
</div>
<a id="method_privErrorLog"></a><div class="element clickable method public method_privErrorLog" data-toggle="collapse" data-target=".method_privErrorLog .collapse">
<h2>privErrorLog()
        </h2>
<pre>privErrorLog($p_error_code, $p_error_string) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_error_code</h4></div>
<div class="subelement argument"><h4>$p_error_string</h4></div>
</div></div>
</div>
<a id="method_privErrorReset"></a><div class="element clickable method public method_privErrorReset" data-toggle="collapse" data-target=".method_privErrorReset .collapse">
<h2>privErrorReset()
        </h2>
<pre>privErrorReset() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_privExtractByRule"></a><div class="element clickable method public method_privExtractByRule" data-toggle="collapse" data-target=".method_privExtractByRule .collapse">
<h2>privExtractByRule()
        </h2>
<pre>privExtractByRule($p_file_list, $p_path, $p_remove_path, $p_remove_all_path, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_file_list</h4></div>
<div class="subelement argument"><h4>$p_path</h4></div>
<div class="subelement argument"><h4>$p_remove_path</h4></div>
<div class="subelement argument"><h4>$p_remove_all_path</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privExtractFile"></a><div class="element clickable method public method_privExtractFile" data-toggle="collapse" data-target=".method_privExtractFile .collapse">
<h2>privExtractFile()
        </h2>
<pre>privExtractFile($p_entry, $p_path, $p_remove_path, $p_remove_all_path, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_entry</h4></div>
<div class="subelement argument"><h4>$p_path</h4></div>
<div class="subelement argument"><h4>$p_remove_path</h4></div>
<div class="subelement argument"><h4>$p_remove_all_path</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privExtractFileAsString"></a><div class="element clickable method public method_privExtractFileAsString" data-toggle="collapse" data-target=".method_privExtractFileAsString .collapse">
<h2>privExtractFileAsString()
        </h2>
<pre>privExtractFileAsString($p_entry, $p_string, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_entry</h4></div>
<div class="subelement argument"><h4>$p_string</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privExtractFileInOutput"></a><div class="element clickable method public method_privExtractFileInOutput" data-toggle="collapse" data-target=".method_privExtractFileInOutput .collapse">
<h2>privExtractFileInOutput()
        </h2>
<pre>privExtractFileInOutput($p_entry, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_entry</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privExtractFileUsingTempFile"></a><div class="element clickable method public method_privExtractFileUsingTempFile" data-toggle="collapse" data-target=".method_privExtractFileUsingTempFile .collapse">
<h2>privExtractFileUsingTempFile()
        </h2>
<pre>privExtractFileUsingTempFile($p_entry, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_entry</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privFileDescrExpand"></a><div class="element clickable method public method_privFileDescrExpand" data-toggle="collapse" data-target=".method_privFileDescrExpand .collapse">
<h2>privFileDescrExpand()
        </h2>
<pre>privFileDescrExpand($p_filedescr_list, $p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_filedescr_list</h4></div>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privFileDescrParseAtt"></a><div class="element clickable method public method_privFileDescrParseAtt" data-toggle="collapse" data-target=".method_privFileDescrParseAtt .collapse">
<h2>privFileDescrParseAtt()
        </h2>
<pre>privFileDescrParseAtt($p_file_list, $p_filedescr, $v_options, $v_requested_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_file_list</h4></div>
<div class="subelement argument"><h4>$p_filedescr</h4></div>
<div class="subelement argument"><h4>$v_options</h4></div>
<div class="subelement argument"><h4>$v_requested_options</h4></div>
</div></div>
</div>
<a id="method_privList"></a><div class="element clickable method public method_privList" data-toggle="collapse" data-target=".method_privList .collapse">
<h2>privList()
        </h2>
<pre>privList($p_list) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_list</h4></div>
</div></div>
</div>
<a id="method_privMerge"></a><div class="element clickable method public method_privMerge" data-toggle="collapse" data-target=".method_privMerge .collapse">
<h2>privMerge()
        </h2>
<pre>privMerge($p_archive_to_add) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_archive_to_add</h4></div>
</div></div>
</div>
<a id="method_privOpenFd"></a><div class="element clickable method public method_privOpenFd" data-toggle="collapse" data-target=".method_privOpenFd .collapse">
<h2>privOpenFd()
        </h2>
<pre>privOpenFd($p_mode) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_mode</h4></div>
</div></div>
</div>
<a id="method_privOptionDefaultThreshold"></a><div class="element clickable method public method_privOptionDefaultThreshold" data-toggle="collapse" data-target=".method_privOptionDefaultThreshold .collapse">
<h2>privOptionDefaultThreshold()
        </h2>
<pre>privOptionDefaultThreshold($p_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_options</h4></div>
</div></div>
</div>
<a id="method_privParseOptions"></a><div class="element clickable method public method_privParseOptions" data-toggle="collapse" data-target=".method_privParseOptions .collapse">
<h2>privParseOptions()
        </h2>
<pre>privParseOptions($p_options_list, $p_size, $v_result_list, $v_requested_options) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_options_list</h4></div>
<div class="subelement argument"><h4>$p_size</h4></div>
<div class="subelement argument"><h4>$v_result_list</h4></div>
<div class="subelement argument"><h4>$v_requested_options</h4></div>
</div></div>
</div>
<a id="method_privReadCentralFileHeader"></a><div class="element clickable method public method_privReadCentralFileHeader" data-toggle="collapse" data-target=".method_privReadCentralFileHeader .collapse">
<h2>privReadCentralFileHeader()
        </h2>
<pre>privReadCentralFileHeader($p_header) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_header</h4></div>
</div></div>
</div>
<a id="method_privReadEndCentralDir"></a><div class="element clickable method public method_privReadEndCentralDir" data-toggle="collapse" data-target=".method_privReadEndCentralDir .collapse">
<h2>privReadEndCentralDir()
        </h2>
<pre>privReadEndCentralDir($p_central_dir) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_central_dir</h4></div>
</div></div>
</div>
<a id="method_privReadFileHeader"></a><div class="element clickable method public method_privReadFileHeader" data-toggle="collapse" data-target=".method_privReadFileHeader .collapse">
<h2>privReadFileHeader()
        </h2>
<pre>privReadFileHeader($p_header) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_header</h4></div>
</div></div>
</div>
<a id="method_privSwapBackMagicQuotes"></a><div class="element clickable method public method_privSwapBackMagicQuotes" data-toggle="collapse" data-target=".method_privSwapBackMagicQuotes .collapse">
<h2>privSwapBackMagicQuotes()
        </h2>
<pre>privSwapBackMagicQuotes() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method_privWriteCentralFileHeader"></a><div class="element clickable method public method_privWriteCentralFileHeader" data-toggle="collapse" data-target=".method_privWriteCentralFileHeader .collapse">
<h2>privWriteCentralFileHeader()
        </h2>
<pre>privWriteCentralFileHeader($p_header) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_header</h4></div>
</div></div>
</div>
<a id="method_privWriteCentralHeader"></a><div class="element clickable method public method_privWriteCentralHeader" data-toggle="collapse" data-target=".method_privWriteCentralHeader .collapse">
<h2>privWriteCentralHeader()
        </h2>
<pre>privWriteCentralHeader($p_nb_entries, $p_size, $p_offset, $p_comment) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_nb_entries</h4></div>
<div class="subelement argument"><h4>$p_size</h4></div>
<div class="subelement argument"><h4>$p_offset</h4></div>
<div class="subelement argument"><h4>$p_comment</h4></div>
</div></div>
</div>
<a id="method_privWriteFileHeader"></a><div class="element clickable method public method_privWriteFileHeader" data-toggle="collapse" data-target=".method_privWriteFileHeader .collapse">
<h2>privWriteFileHeader()
        </h2>
<pre>privWriteFileHeader($p_header) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_header</h4></div>
</div></div>
</div>
<a id="method_properties"></a><div class="element clickable method public method_properties" data-toggle="collapse" data-target=".method_properties .collapse">
<h2>properties()
        </h2>
<pre>properties() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property_error_code"> </a><div class="element clickable property public property_error_code" data-toggle="collapse" data-target=".property_error_code .collapse">
<h2></h2>
<pre>$error_code </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_error_string"> </a><div class="element clickable property public property_error_string" data-toggle="collapse" data-target=".property_error_string .collapse">
<h2></h2>
<pre>$error_string </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_magic_quotes_status"> </a><div class="element clickable property public property_magic_quotes_status" data-toggle="collapse" data-target=".property_magic_quotes_status .collapse">
<h2></h2>
<pre>$magic_quotes_status </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_zip_fd"> </a><div class="element clickable property public property_zip_fd" data-toggle="collapse" data-target=".property_zip_fd .collapse">
<h2></h2>
<pre>$zip_fd </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property_zipname"> </a><div class="element clickable property public property_zipname" data-toggle="collapse" data-target=".property_zipname .collapse">
<h2></h2>
<pre>$zipname </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div></div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:37Z.<br></footer></div>
</div>
</body>
</html>
