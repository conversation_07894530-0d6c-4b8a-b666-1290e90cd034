<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250801 | sEdate : 20250912<br>
<br>
 MAX_DAY  : 20250911<br>
 DIFF_DAY  : 42<br>
 20250801 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250802 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250911 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 이미 등록되어있음......<br>
 20250911 이미 등록되어있음......<br>
 20250911 이미 등록되어있음......<br>
 20250911 이미 등록되어있음......<br>
 20250911 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250801001<br>
 입력 SLUS_ID  : 20250801002<br>
 입력 SLUS_ID  : 20250801003<br>
 입력 SLUS_ID  : 20250801004<br>
 입력 SLUS_ID  : 20250801005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250802 | sEdate : 20250913<br>
<br>
 MAX_DAY  : 20250912<br>
 DIFF_DAY  : 42<br>
 20250802 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250803 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 이미 등록되어있음......<br>
 20250912 이미 등록되어있음......<br>
 20250912 이미 등록되어있음......<br>
 20250912 이미 등록되어있음......<br>
 20250912 이미 등록되어있음......<br>
 20250913 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250803 | sEdate : 20250914<br>
<br>
 MAX_DAY  : 20250912<br>
 DIFF_DAY  : 42<br>
 20250803 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250804 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 이미 등록되어있음......<br>
 20250912 이미 등록되어있음......<br>
 20250912 이미 등록되어있음......<br>
 20250912 이미 등록되어있음......<br>
 20250912 이미 등록되어있음......<br>
 20250913 주말일 경우 패스.....<br>
 20250914 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250804 | sEdate : 20250915<br>
<br>
 MAX_DAY  : 20250912<br>
 DIFF_DAY  : 42<br>
 20250804 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250805 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250912 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 이미 등록되어있음......<br>
 20250912 이미 등록되어있음......<br>
 20250912 이미 등록되어있음......<br>
 20250912 이미 등록되어있음......<br>
 20250912 이미 등록되어있음......<br>
 20250913 주말일 경우 패스.....<br>
 20250914 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250804001<br>
 입력 SLUS_ID  : 20250804002<br>
 입력 SLUS_ID  : 20250804003<br>
 입력 SLUS_ID  : 20250804004<br>
 입력 SLUS_ID  : 20250804005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250805 | sEdate : 20250916<br>
<br>
 MAX_DAY  : 20250915<br>
 DIFF_DAY  : 42<br>
 20250805 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250806 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250915 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 이미 등록되어있음......<br>
 20250915 이미 등록되어있음......<br>
 20250915 이미 등록되어있음......<br>
 20250915 이미 등록되어있음......<br>
 20250915 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250805001<br>
 입력 SLUS_ID  : 20250805002<br>
 입력 SLUS_ID  : 20250805003<br>
 입력 SLUS_ID  : 20250805004<br>
 입력 SLUS_ID  : 20250805005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250806 | sEdate : 20250917<br>
<br>
 MAX_DAY  : 20250916<br>
 DIFF_DAY  : 42<br>
 20250806 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250807 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250916 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 이미 등록되어있음......<br>
 20250916 이미 등록되어있음......<br>
 20250916 이미 등록되어있음......<br>
 20250916 이미 등록되어있음......<br>
 20250916 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250806001<br>
 입력 SLUS_ID  : 20250806002<br>
 입력 SLUS_ID  : 20250806003<br>
 입력 SLUS_ID  : 20250806004<br>
 입력 SLUS_ID  : 20250806005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250807 | sEdate : 20250918<br>
<br>
 MAX_DAY  : 20250917<br>
 DIFF_DAY  : 42<br>
 20250807 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250808 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250917 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 이미 등록되어있음......<br>
 20250917 이미 등록되어있음......<br>
 20250917 이미 등록되어있음......<br>
 20250917 이미 등록되어있음......<br>
 20250917 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250807001<br>
 입력 SLUS_ID  : 20250807002<br>
 입력 SLUS_ID  : 20250807003<br>
 입력 SLUS_ID  : 20250807004<br>
 입력 SLUS_ID  : 20250807005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250808 | sEdate : 20250919<br>
<br>
 MAX_DAY  : 20250918<br>
 DIFF_DAY  : 42<br>
 20250808 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250809 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 < 20250918 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250918 이미 등록되어있음......<br>
 20250918 이미 등록되어있음......<br>
 20250918 이미 등록되어있음......<br>
 20250918 이미 등록되어있음......<br>
 20250918 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250808001<br>
 입력 SLUS_ID  : 20250808002<br>
 입력 SLUS_ID  : 20250808003<br>
 입력 SLUS_ID  : 20250808004<br>
 입력 SLUS_ID  : 20250808005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250809 | sEdate : 20250920<br>
<br>
 MAX_DAY  : 20250919<br>
 DIFF_DAY  : 42<br>
 20250809 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250810 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250918 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250919 이미 등록되어있음......<br>
 20250919 이미 등록되어있음......<br>
 20250919 이미 등록되어있음......<br>
 20250919 이미 등록되어있음......<br>
 20250919 이미 등록되어있음......<br>
 20250920 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250810 | sEdate : 20250921<br>
<br>
 MAX_DAY  : 20250919<br>
 DIFF_DAY  : 42<br>
 20250810 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250811 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250918 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250919 이미 등록되어있음......<br>
 20250919 이미 등록되어있음......<br>
 20250919 이미 등록되어있음......<br>
 20250919 이미 등록되어있음......<br>
 20250919 이미 등록되어있음......<br>
 20250920 주말일 경우 패스.....<br>
 20250921 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250811 | sEdate : 20250922<br>
<br>
 MAX_DAY  : 20250919<br>
 DIFF_DAY  : 42<br>
 20250811 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250812 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250918 < 20250919 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250919 이미 등록되어있음......<br>
 20250919 이미 등록되어있음......<br>
 20250919 이미 등록되어있음......<br>
 20250919 이미 등록되어있음......<br>
 20250919 이미 등록되어있음......<br>
 20250920 주말일 경우 패스.....<br>
 20250921 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250811001<br>
 입력 SLUS_ID  : 20250811002<br>
 입력 SLUS_ID  : 20250811003<br>
 입력 SLUS_ID  : 20250811004<br>
 입력 SLUS_ID  : 20250811005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250812 | sEdate : 20250923<br>
<br>
 MAX_DAY  : 20250922<br>
 DIFF_DAY  : 42<br>
 20250812 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250813 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250918 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250919 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250920 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250921 < 20250922 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250922 이미 등록되어있음......<br>
 20250922 이미 등록되어있음......<br>
 20250922 이미 등록되어있음......<br>
 20250922 이미 등록되어있음......<br>
 20250922 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250812001<br>
 입력 SLUS_ID  : 20250812002<br>
 입력 SLUS_ID  : 20250812003<br>
 입력 SLUS_ID  : 20250812004<br>
 입력 SLUS_ID  : 20250812005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250813 | sEdate : 20250924<br>
<br>
 MAX_DAY  : 20250923<br>
 DIFF_DAY  : 42<br>
 20250813 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250814 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250918 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250919 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250920 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250921 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250922 < 20250923 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250923 이미 등록되어있음......<br>
 20250923 이미 등록되어있음......<br>
 20250923 이미 등록되어있음......<br>
 20250923 이미 등록되어있음......<br>
 20250923 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250813001<br>
 입력 SLUS_ID  : 20250813002<br>
 입력 SLUS_ID  : 20250813003<br>
 입력 SLUS_ID  : 20250813004<br>
 입력 SLUS_ID  : 20250813005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250814 | sEdate : 20250925<br>
<br>
 MAX_DAY  : 20250924<br>
 DIFF_DAY  : 42<br>
 20250814 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250815 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250918 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250919 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250920 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250921 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250922 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250923 < 20250924 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250924 이미 등록되어있음......<br>
 20250924 이미 등록되어있음......<br>
 20250924 이미 등록되어있음......<br>
 20250924 이미 등록되어있음......<br>
 20250924 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250814001<br>
 입력 SLUS_ID  : 20250814002<br>
 입력 SLUS_ID  : 20250814003<br>
 입력 SLUS_ID  : 20250814004<br>
 입력 SLUS_ID  : 20250814005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250815 | sEdate : 20250926<br>
<br>
 MAX_DAY  : 20250925<br>
 DIFF_DAY  : 42<br>
 20250815 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250816 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250918 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250919 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250920 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250921 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250922 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250923 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250924 < 20250925 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250925 이미 등록되어있음......<br>
 20250925 이미 등록되어있음......<br>
 20250925 이미 등록되어있음......<br>
 20250925 이미 등록되어있음......<br>
 20250925 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250815001<br>
 입력 SLUS_ID  : 20250815002<br>
 입력 SLUS_ID  : 20250815003<br>
 입력 SLUS_ID  : 20250815004<br>
 입력 SLUS_ID  : 20250815005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250816 | sEdate : 20250927<br>
<br>
 MAX_DAY  : 20250926<br>
 DIFF_DAY  : 42<br>
 20250816 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250817 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250918 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250919 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250920 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250921 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250922 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250923 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250924 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250925 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250926 이미 등록되어있음......<br>
 20250926 이미 등록되어있음......<br>
 20250926 이미 등록되어있음......<br>
 20250926 이미 등록되어있음......<br>
 20250926 이미 등록되어있음......<br>
 20250927 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250817 | sEdate : 20250928<br>
<br>
 MAX_DAY  : 20250926<br>
 DIFF_DAY  : 42<br>
 20250817 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250818 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250918 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250919 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250920 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250921 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250922 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250923 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250924 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250925 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250926 이미 등록되어있음......<br>
 20250926 이미 등록되어있음......<br>
 20250926 이미 등록되어있음......<br>
 20250926 이미 등록되어있음......<br>
 20250926 이미 등록되어있음......<br>
 20250927 주말일 경우 패스.....<br>
 20250928 주말일 경우 패스.....
Notice: Undefined variable: sw in /home/<USER>/inc/db_controller.php on line 159

Notice: Undefined variable: cnt in /home/<USER>/inc/db_controller.php on line 160

Notice: Undefined variable: ErrorMsg in /home/<USER>/inc/db_controller.php on line 161
<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250818 | sEdate : 20250929<br>
<br>
 MAX_DAY  : 20250926<br>
 DIFF_DAY  : 42<br>
 20250818 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250819 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250918 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250919 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250920 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250921 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250922 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250923 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250924 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250925 < 20250926 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250926 이미 등록되어있음......<br>
 20250926 이미 등록되어있음......<br>
 20250926 이미 등록되어있음......<br>
 20250926 이미 등록되어있음......<br>
 20250926 이미 등록되어있음......<br>
 20250927 주말일 경우 패스.....<br>
 20250928 주말일 경우 패스.....<br>
 입력 SLUS_ID  : 20250818001<br>
 입력 SLUS_ID  : 20250818002<br>
 입력 SLUS_ID  : 20250818003<br>
 입력 SLUS_ID  : 20250818004<br>
 입력 SLUS_ID  : 20250818005<br>
 --------------------------------------------------- <br>
<br>
 sSdate : 20250819 | sEdate : 20250930<br>
<br>
 MAX_DAY  : 20250929<br>
 DIFF_DAY  : 42<br>
 20250819 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250820 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250821 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250822 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250823 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250824 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250825 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250826 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250827 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250828 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250829 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250830 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250831 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250901 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250902 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250903 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250904 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250905 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250906 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250907 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250908 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250909 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250910 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250911 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250912 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250913 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250914 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250915 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250916 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250917 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250918 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250919 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250920 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250921 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250922 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250923 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250924 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250925 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250926 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250927 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250928 < 20250929 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...<br>
 20250929 이미 등록되어있음......<br>
 20250929 이미 등록되어있음......<br>
 20250929 이미 등록되어있음......<br>
 20250929 이미 등록되어있음......<br>
 20250929 이미 등록되어있음......<br>
 입력 SLUS_ID  : 20250819001<br>
 입력 SLUS_ID  : 20250819002<br>
 입력 SLUS_ID  : 20250819003<br>
 입력 SLUS_ID  : 20250819004<br>
 입력 SLUS_ID  : 20250819005