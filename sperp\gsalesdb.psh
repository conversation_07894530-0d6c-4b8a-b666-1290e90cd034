#!/usr/local/bin/php -q
<?php
/*
	## SPERP > 영업관리 > 글로벌 Sales DB > 매월 15일이 마감날짜 익월데이터는 수정불가. 
	작업일 : 2024.06.10. 현주가
	경로 : /home/<USER>/sperp/gsalesdb.psh

	홈페이지 URL : https://erp.posbank.com/?pageCode=MTI0MTY=


*/
// 0 5 * * * php -q /home/<USER>/sperp/gsalesdb.psh
# 불량이슈 처리기한 알림
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/inc/Encode.php");

$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
if(empty($dbconn_sperp_posbank->success)) {
	echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
}






/*
휴무일체크는 필요없을듯.....
if(in_array(date('w'),array("0","6"))){
	echo date("Y-m-d") . " - 휴무일\n";
	## 스케즐 처리 상황 intra DB에 저장 
	crontab_execution(86400, "ERP 불량이슈 처리기한 알람");
	exit;
}



$SQL = "SELECT NAME FROM HOLIDAY_DATA WHERE to_char(HDATE,'YYYYMMDD')='".date('Ymd')."'";
$HOLIDAY_NM = $dbconn_sperp_posbank->query_one($SQL);
if($HOLIDAY_NM){
	echo date("Y-m-d") . " - 휴무일(".$HOLIDAY_NM.")\n";
	## 스케즐 처리 상황 intra DB에 저장 
	crontab_execution(86400, "ERP 불량이슈 처리기한 알람");
	exit;
}
*/


/*
SELECT (ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -1) + 14) as 15TH_PRE FROM dual;
SELECT TO_CHAR(TRUNC(SYSDATE, 'MM') + 14,'YYYYMMDD') as 15TH_THIS FROM dual;
*/


$SQL = "SELECT TO_CHAR(ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -1) + 14,'YYYYMMDD') AS PRE_15, TO_CHAR(TRUNC(SYSDATE, 'MM') + 14,'YYYYMMDD') as THIS_15, TO_CHAR(SYSDATE,'YYYYMMDD') AS N_DAY, TO_CHAR(TRUNC(SYSDATE, 'MM') + 15,'YYYYMMDD') AS THIS_16 FROM DUAL";
$row_day = $dbconn_sperp_posbank->query_row($SQL);

// 매월 15일이 마감날짜 익월데이터는 수정불가. 
// 매월 15일이 지난날은 계속 마감체크한다...
// if ( ($row_day["N_DAY"] > $row_day["THIS_15"]) && ( ($row_day["N_DAY"]-$row_day["THIS_15"])==1 ) ) {
// echo "<br>\n day : ".$row_day["N_DAY"]." | ".$row_day["THIS_15"];
// if ($row_day["N_DAY"] > $row_day["THIS_15"] ) {


$strYMD = date('Ymd');

echo "<br>\n ----------------------------";
echo "<br>\n N_DAY : ".$row_day["N_DAY"];
echo "<br>\n THIS_15 : ".$row_day["THIS_15"];
echo "<br>\n THIS_16 : ".$row_day["THIS_16"];
echo "<br>\n strYMD : ".$strYMD;
echo "<br>\n ----------------------------";


if ( ($row_day["N_DAY"] > $row_day["THIS_15"]) && ($row_day["THIS_16"] == $strYMD) ) {



// CLOSE_CHK  마감(확정)체크 (F:마감,I:입력,T:임시 )
// ORDER_CHK : 0:예측,1:오더확정,8:엑셀,9:복사,2:경영계획(BP),3:경영계획(BP수정)




	$arr_query["0"] = "
	UPDATE GSALES_DB 
		SET CLOSE_CHK = 'F' 
		WHERE CLOSE_CHK = 'I' AND ORDER_CHK = '1'
	 AND TO_CHAR(REG_IDATE,'YYYYMMDD') <=  TO_CHAR(TRUNC(SYSDATE, 'MM') + 14,'YYYYMMDD') 
	 -- AND TO_CHAR(REG_IDATE,'YYYYMMDD')  > TO_CHAR(ADD_MONTHS(TRUNC(SYSDATE, 'MM'), -1) + 14,'YYYYMMDD')	
	";

	// 2024.07.23. 현주가 선적일 기준으로 변경.

	// CLOSE_CHK : 마감(확정)체크 (F:마감,I:입력,T:임시 )
	// ORDER_CHK : 0:예측,1:오더확정,8:엑셀,9:복사,2:경영계획(BP),3:경영계획(BP수정)
	$arr_query["0"] = "
	UPDATE GSALES_DB 
		SET CLOSE_CHK = 'F' 
		WHERE (CLOSE_CHK = 'I' OR CLOSE_CHK IS NULL) AND ORDER_CHK = '1' 
	 AND SHIP_YMD != 'TBD' AND SHIP_YMD <=  TO_CHAR(TRUNC(SYSDATE, 'MM') + 14,'YYYYMMDD')
	";
print_r($arr_query);


	if($arr_query){
		$rs = $dbconn_sperp_posbank->iud_query($arr_query);
		if($rs['state']){
			$msg = number_format($rs['count']) . "건 등록";
		}else{
			$msg = "전산장애!! " . $rs['error'];
		}
	}else{
		$msg = "없음";
	}
	echo date("Y-m-d H:i:s") . " - " . $msg . "\n";




} else {
	$msg = "금일은 마감날짜가 아닙니다.";
	echo date("Y-m-d H:i:s") . " - " . $msg . "\n";
}







## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(86400, "ERP > 영업관리 > 글로벌 Sales DB 이번달 마감");

echo date("Y-m-d H:i:s")." - 끝\n";
?>