#!/usr/local/bin/php -q
<?php
/*
	## SPERP > 생산관리 > 생산라인 가동 설정 : 생산라인을 기본적으로 6주 생산라인 데이타 추가.
	작업일 : 2023.11.21. 현주가
	경로 : /home/<USER>/sperp/snm_line_use_seting.psh


*/


// 0 9 * * * php -q /home/<USER>/sperp/snm_line_use_seting.psh
// /home/<USER>/sperp/snm_line_use_seting.sh


# IC리더기형상변경현황 만료일 알람
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/inc/Encode.php");

$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);	// 포스뱅크
//$dbconn_sperp_posbank = new DBController($db['sperp_test']);		// 포스뱅크(개발용)

if(empty($dbconn_sperp_posbank->success)) {
	echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
}


$dbconn_posbank_intra = new DBController($db['posbank_intra']);
if(empty($dbconn_posbank_intra->success)) {
	echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패";
}

/**********************************************************/


$sSdate = date("Ymd");
$sEdate = date("Ymd", strtotime ("+6 week"));

echo "<br>\n --------------------------------------------------- <br>\n";
echo "<br>\n sSdate : ".$sSdate." | sEdate : ".$sEdate ."<br>\n";

// 입력되어있는날짜중 마지막날짜가져오기...
$sql = "SELECT MAX(SNM_YMD) AS SNM_YMD from SNM_LINE_USE_SETTING";
$row = $dbconn_sperp_posbank->query_row($sql);
$MAX_DAY = $row["SNM_YMD"];
echo "<br>\n MAX_DAY  : ".$MAX_DAY ;


// 날짜계산
$sql = "select ( to_date('".$sEdate."','YYYYMMDD') - to_date('".$sSdate."','YYYYMMDD') ) AS DIFF_DAY from DUAL";
// echo"<br>\n $sql : ".$sql;
$row = $dbconn_sperp_posbank->query_row($sql);
$DIFF_DAY = $row["DIFF_DAY"];
echo "<br>\n DIFF_DAY  : ".$DIFF_DAY ;

if ($DIFF_DAY <= 0) {
	echo"생산가능 일자가 없습니다.";
	exit;
}
$DIFF_DAY = ($DIFF_DAY+1);


// $sql = "SELECT (NVL(MAX(SLUS_ID),0)+1) AS ID from SNM_LINE_USE_SETTING";
$cYMD = date("Ymd");
/*
$sql = "SELECT (NVL(COUNT(SLUS_ID),0)+1) AS ID from SNM_LINE_USE_SETTING where SLUS_ID LIKE '".$cYMD."%'";
$row = $dbconn_sperp_posbank->query_row($sql);
$CHK_ID = $row["ID"];
*/
$sql = "SELECT (NVL(MAX(SLUS_ID),'".$cYMD."000')+1) AS ID from SNM_LINE_USE_SETTING where SLUS_ID LIKE '".$cYMD."%'";
$row = $dbconn_sperp_posbank->query_row($sql);
$CHK_ID = $row["ID"];
$n	= 0;
$sw	= 0;

$arr_query = array();

// 날짜만큼.
for ( $d=0; $d < $DIFF_DAY ;$d++ ) {
	$strYMD = date("Ymd", strtotime ($sSdate." +".$d."day"));

	if ($strYMD < $MAX_DAY) {
		echo "<br>\n $strYMD < $MAX_DAY 최대 입력날짜보다 작을 경우패스 이미등록되어있다고 보아야함...";
		continue;
	}	


	if(in_array( date('w', strtotime($strYMD)) ,array("0","6"))){
		echo "<br>\n $strYMD 주말일 경우 패스.....";
		continue;
	}

	$SQL = "SELECT NAME FROM HOLIDAY_DATA WHERE to_char(HDATE,'YYYYMMDD')='".$strYMD."'";
	$HOLIDAY_NM = $dbconn_sperp_posbank->query_one($SQL);
	if($HOLIDAY_NM){
		echo "<br>\n $HOLIDAY_NM 휴무일인 경우 패스.....";
		continue;		
	}




	// 생산라인 
	$sql = "SELECT BAS_CODE, BAS_NAME FROM BAS WHERE BAS_CODE LIKE 'ML%' AND BAS_STATE='1' ORDER BY BAS_ORD ASC";
	$rows = $dbconn_sperp_posbank->query_rows($sql);

	foreach($rows as $k=>$v) { 


		// 날짜가 있는지 확인.
		$sqlchk = "SELECT COUNT(*) AS CNT FROM SNM_LINE_USE_SETTING WHERE SNM_YMD = '".$strYMD."' ";
		$rowchk = $dbconn_sperp_posbank->query_row($sqlchk);
		
		if ( $rowchk["CNT"] > 0)  { 
			echo "<br>\n $strYMD 이미 등록되어있음......";
		} else {
			// $ID_NO = str_pad($CHK_ID,3,"0",STR_PAD_LEFT);
			$SLUS_ID = $CHK_ID;
echo "<br>\n 입력 SLUS_ID  : ".$SLUS_ID ;		
			$arr_query[$n] = "INSERT INTO SNM_LINE_USE_SETTING (SLUS_ID, SNM_YMD, SNM_LINE_CODE, HCT_CODE, SNM_USE_STATE, REG_DATE, REG_STCODE, REG_IDATE, REG_ISTCODE ) VALUES ('".$SLUS_ID."', '".$strYMD."', '".$v["BAS_CODE"]."', '100049', '1', SYSDATE, 'SCHEDULE', SYSDATE, 'SCHEDULE' )";
			$n++;
			$CHK_ID++;
		}

	} // End. foreach($rows as $k=>$v) { 

} // End. for ( $d=0; $d < $DIFF_DAY ;$d++ ) {

$rs = $dbconn_sperp_posbank->iud_query($arr_query);
	




## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(86400, "[ERP] 6주 생산라인 데이타 추가.");



?>