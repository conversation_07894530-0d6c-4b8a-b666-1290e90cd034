2025-05-01 - 휴무일(근로자의날)
<br>
 date_NOW : 20250502<br>
 date_30D : 20250731<br>
 date_2M : 20250702<br>
 date_3M : 20250802<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250702' AND '20250802' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 12
    [VAN_NAME] => NICE(NIC)
    [POS_NAME] => Anyshop e2
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => #####MR-10002201
    [EXPIRE_YMD] => 2025-07-20
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '12' 2025-05-03 - 휴무일
2025-05-04 - 휴무일
2025-05-05 - 휴무일(어린이날/석가탄신일)
2025-05-06 - 휴무일((대체휴일))
<br>
 date_NOW : 20250507<br>
 date_30D : 20250805<br>
 date_2M : 20250707<br>
 date_3M : 20250807<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250707' AND '20250807' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 57
    [VAN_NAME] => KFTC(금융결제원)(KFT)
    [POS_NAME] => Anyshop e2
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => DUALPAY_633DK101
    [EXPIRE_YMD] => 2025-07-23
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '57' <br>
 date_NOW : 20250508<br>
 date_30D : 20250806<br>
 date_2M : 20250708<br>
 date_3M : 20250808<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250708' AND '20250808' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 56
    [VAN_NAME] => KFTC(금융결제원)(KFT)
    [POS_NAME] => Anyshop e2
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => DUALPAY_633DK101KFPOSBNKPOPs1001
    [EXPIRE_YMD] => 2025-07-30
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '56' <br>
 date_NOW : 20250509<br>
 date_30D : 20250807<br>
 date_2M : 20250709<br>
 date_3M : 20250809<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250709' AND '20250809' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 58
    [VAN_NAME] => KFTC(금융결제원)(KFT)
    [POS_NAME] => APEXA G
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => DUALPAY633PKK101KFPOSBNKPOPs1001
    [EXPIRE_YMD] => 2025-07-30
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '58' 2025-05-10 - 휴무일
2025-05-11 - 휴무일
<br>
 date_NOW : 20250512<br>
 date_30D : 20250810<br>
 date_2M : 20250712<br>
 date_3M : 20250812<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250712' AND '20250812' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 19
    [VAN_NAME] => 나이스페이먼츠(JTNET)(JTN)
    [POS_NAME] => APEXA A
    [ITEM_NAME] =>   
    [ITEM_NUMBER] => #SP-5100PPBK1001
    [EXPIRE_YMD] => 2025-08-06
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '19' <br>
 date_NOW : 20250513<br>
 date_30D : 20250811<br>
 date_2M : 20250713<br>
 date_3M : 20250813<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250713' AND '20250813' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 11
    [VAN_NAME] => NICE(NIC)
    [POS_NAME] => Anyshop e2
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => #####MR-10001001
    [EXPIRE_YMD] => 2025-08-11
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '11' <br>
 date_NOW : 20250514<br>
 date_30D : 20250812<br>
 date_2M : 20250714<br>
 date_3M : 20250814<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250714' AND '20250814' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 80
    [VAN_NAME] => NICE(NIC)
    [POS_NAME] => Anyshop e2
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => ################
    [EXPIRE_YMD] => 2025-08-11
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '80' <br>
 date_NOW : 20250515<br>
 date_30D : 20250813<br>
 date_2M : 20250715<br>
 date_3M : 20250815<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250715' AND '20250815' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250516<br>
 date_30D : 20250814<br>
 date_2M : 20250716<br>
 date_3M : 20250816<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250716' AND '20250816' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-05-17 - 휴무일
2025-05-18 - 휴무일
<br>
 date_NOW : 20250519<br>
 date_30D : 20250817<br>
 date_2M : 20250719<br>
 date_3M : 20250819<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250719' AND '20250819' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250520<br>
 date_30D : 20250818<br>
 date_2M : 20250720<br>
 date_3M : 20250820<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250720' AND '20250820' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
    [IC_IDX] => 1
    [VAN_NAME] => KSNET(KS)
    [POS_NAME] => Anyshop e2
    [ITEM_NAME] =>  
    [ITEM_NUMBER] => ######KSR-011100KSNETPOSBK011001
    [EXPIRE_YMD] => 2025-08-20
)
<br>
 sql04 : update IC_READER_SHAPE set CERT_STATE = '1'  WHERE IC_IDX = '1' <br>
 date_NOW : 20250521<br>
 date_30D : 20250819<br>
 date_2M : 20250721<br>
 date_3M : 20250821<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250721' AND '20250821' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250522<br>
 date_30D : 20250820<br>
 date_2M : 20250722<br>
 date_3M : 20250822<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250722' AND '20250822' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250523<br>
 date_30D : 20250821<br>
 date_2M : 20250723<br>
 date_3M : 20250823<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250723' AND '20250823' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
2025-05-24 - 휴무일
2025-05-25 - 휴무일
<br>
 date_NOW : 20250526<br>
 date_30D : 20250824<br>
 date_2M : 20250726<br>
 date_3M : 20250826<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250726' AND '20250826' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
<br>
 date_NOW : 20250527<br>
 date_30D : 20250825<br>
 date_2M : 20250727<br>
 date_3M : 20250827<br>
 sql :
SELECT IC_IDX, VAN_NAME, POS_NAME, ITEM_NAME, ITEM_NUMBER, EXPIRE_YMD FROM (
	SELECT 
		IC_IDX, concat( F_BAS(VAN_CODE, 'NAME'),F_BAS(VAN_CODE, 'OP2'))  AS VAN_NAME, F_BAS(POS_CODE, 'NAME') AS POS_NAME, ITEM_NAME, ITEM_NUMBER, TO_CHAR(TO_DATE(EXPIRE_YMD, 'YYYY-MM-DD'), 'YYYY-MM-DD') AS EXPIRE_YMD
	 FROM IC_READER_SHAPE 
	  WHERE CERT_STATE = '0' AND EXPIRE_YMD BETWEEN '20250727' AND '20250827' 
	   ORDER BY EXPIRE_YMD ASC 
) S1 WHERE ROWNUM = 1
Array
(
)
