
[2025-07-28 12:46:27]
수신자              : ["101141"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2145644}
2025-07-28 12:46:30 - 끝

[2025-07-28 12:51:25]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2145646}
2025-07-28 12:51:27 - 끝

[2025-07-28 14:06:19]
수신자              : ["101141"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2145710}
2025-07-28 14:06:21 - 끝

[2025-07-28 14:07:23]
수신자              : ["101141"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2145711}
2025-07-28 14:07:25 - 끝

[2025-07-29 10:00:02]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2146083}
2025-07-29 10:00:04 - 끝

[2025-07-30 10:00:02]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2146706}
2025-07-30 10:00:04 - 끝

[2025-07-31 10:00:02]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2147052}
2025-07-31 10:00:04 - 끝

[2025-08-01 10:00:01]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2147397}
2025-08-01 10:00:04 - 끝

[2025-08-04 10:00:02]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2147694}
2025-08-04 10:00:05 - 끝

[2025-08-04 11:30:24]

Notice: Undefined index: PR_STD in /home/<USER>/sperp/minus_iv_mail_send.psh on line 139
수신자              : ["101141"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2147735}
재고수정 SQL  : "BEGIN update IV set QTY=57215 where RCT_CODE='1000' and HCT_CODE='100010' and PR_CODE='10012235';\nEND ; "
재고수정 IV_RS  : {"state":true,"count":1,"error":""}
2025-08-04 11:30:26 - 끝

[2025-08-04 11:31:25]
수신자              : ["101141"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2147737}
재고수정 SQL  : "BEGIN update IV set QTY=57215 where RCT_CODE='1000' and HCT_CODE='100010' and PR_CODE='10012235';\nEND ; "
재고수정 IV_RS  : {"state":true,"count":1,"error":""}
2025-08-04 11:31:29 - 끝

[2025-08-04 11:35:05]
[6550] ORA-06550: line 1, column 7:
PLS-00103: Encountered the symbol \"END\" when expecting one of the following:

   begin case declare exit for goto if loop mod null pragma
   raise return select update while with <an identifier>
   <a double-quoted delimited-identifier> <a bind variable> <<
   close current delete fetch lock insert open rollback
   savepoint set sql execute commit forall merge pipe
Error Query : BEGIN END ; 

Notice: Undefined index: message in /home/<USER>/inc/db_controller.php on line 206
수신자              : ["101141"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2147738}
재고수정 SQL  : "BEGIN END ; "
재고수정 IV_RS  : {"state":false,"count":0,"error":"[6550] ORA-06550: line 1, column 7:\nPLS-00103: Encountered the symbol \\\"END\\\" when expecting one of the following:\n\n   begin case declare exit for goto if loop mod null pragma\n   raise return select update while with \u003Can identifier\u003E\n   \u003Ca double-quoted delimited-identifier\u003E \u003Ca bind variable\u003E \u003C\u003C\n   close current delete fetch lock insert open rollback\n   savepoint set sql execute commit forall merge pipe\\n"}
2025-08-04 11:35:10 - 끝

[2025-08-04 11:35:45]
수신자              : ["101141"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2147739}

Notice: Undefined variable: sql_str2 in /home/<USER>/sperp/minus_iv_mail_send.psh on line 178
재고수정 SQL  : null

Notice: Undefined variable: iv_rs in /home/<USER>/sperp/minus_iv_mail_send.psh on line 179
재고수정 IV_RS  : null
2025-08-04 11:35:47 - 끝

[2025-08-04 11:37:32]
수신자              : ["101141"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2147740}

Notice: Undefined variable: sql_str2 in /home/<USER>/sperp/minus_iv_mail_send.psh on line 178
재고수정 SQL  : null

Notice: Undefined variable: iv_rs in /home/<USER>/sperp/minus_iv_mail_send.psh on line 179
재고수정 IV_RS  : null
2025-08-04 11:37:35 - 끝

[2025-08-04 11:38:35]
수신자              : ["101141"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2147741}

Notice: Undefined variable: sql_str2 in /home/<USER>/sperp/minus_iv_mail_send.psh on line 178

Notice: Undefined variable: iv_rs in /home/<USER>/sperp/minus_iv_mail_send.psh on line 179
2025-08-04 11:38:38 - 끝

[2025-08-04 11:39:02]
수신자              : ["101141"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2147742}
2025-08-04 11:39:05 - 끝

[2025-08-05 10:00:01]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2148126}
2025-08-05 10:00:04 - 끝

[2025-08-06 10:00:02]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2148371}
2025-08-06 10:00:04 - 끝

[2025-08-07 10:00:01]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2148633}
2025-08-07 10:00:04 - 끝

[2025-08-08 10:00:01]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2148940}
2025-08-08 10:00:04 - 끝

[2025-08-11 10:00:01]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2149305}
2025-08-11 10:00:04 - 끝

[2025-08-12 10:00:02]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2149525}
재고수정 SQL  : "BEGIN update IV set QTY=-725 where RCT_CODE='1000' and HCT_CODE='100033' and PR_CODE='10021116';\nEND ; "
재고수정 IV_RS  : {"state":true,"count":1,"error":""}
2025-08-12 10:00:06 - 끝

[2025-08-13 10:00:01]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2149721}
2025-08-13 10:00:04 - 끝

[2025-08-14 10:00:02]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2149881}
2025-08-14 10:00:05 - 끝

[2025-08-15 10:00:02]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2149973}
2025-08-15 10:00:05 - 끝

[2025-08-18 10:00:03]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2150086}
2025-08-18 10:00:06 - 끝

[2025-08-19 10:00:03]
수신자              : ["101141","100951","100363","100130","100272","100643","100982","100695","100903"]
인트라넷 업무연락 rs : {"result_code":"E000","message":"OK","REG_NO":2150421}
2025-08-19 10:00:06 - 끝
