<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_Escher_DggContainer</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_getBstoreContainer" title="getBstoreContainer :: Get BLIP Store Container"><span class="description">Get BLIP Store Container</span><pre>getBstoreContainer()</pre></a></li>
<li class="method public "><a href="#method_getCDgSaved" title="getCDgSaved :: Get total number of drawings saved"><span class="description">Get total number of drawings saved</span><pre>getCDgSaved()</pre></a></li>
<li class="method public "><a href="#method_getCSpSaved" title="getCSpSaved :: Get total number of shapes saved (including group shapes)"><span class="description">Get total number of shapes saved (including group shapes)</span><pre>getCSpSaved()</pre></a></li>
<li class="method public "><a href="#method_getIDCLs" title="getIDCLs :: Get identifier clusters"><span class="description">Get identifier clusters</span><pre>getIDCLs()</pre></a></li>
<li class="method public "><a href="#method_getOPT" title="getOPT :: Get an option for the drawing group"><span class="description">Get an option for the drawing group</span><pre>getOPT()</pre></a></li>
<li class="method public "><a href="#method_getSpIdMax" title="getSpIdMax :: Get maximum shape index of all shapes in all drawings (plus one)"><span class="description">Get maximum shape index of all shapes in all drawings (plus one)</span><pre>getSpIdMax()</pre></a></li>
<li class="method public "><a href="#method_setBstoreContainer" title="setBstoreContainer :: Set BLIP Store Container"><span class="description">Set BLIP Store Container</span><pre>setBstoreContainer()</pre></a></li>
<li class="method public "><a href="#method_setCDgSaved" title="setCDgSaved :: Set total number of drawings saved"><span class="description">Set total number of drawings saved</span><pre>setCDgSaved()</pre></a></li>
<li class="method public "><a href="#method_setCSpSaved" title="setCSpSaved :: Set total number of shapes saved (including group shapes)"><span class="description">Set total number of shapes saved (including group shapes)</span><pre>setCSpSaved()</pre></a></li>
<li class="method public "><a href="#method_setIDCLs" title="setIDCLs :: Set identifier clusters."><span class="description">Set identifier clusters.</span><pre>setIDCLs()</pre></a></li>
<li class="method public "><a href="#method_setOPT" title="setOPT :: Set an option for the drawing group"><span class="description">Set an option for the drawing group</span><pre>setOPT()</pre></a></li>
<li class="method public "><a href="#method_setSpIdMax" title="setSpIdMax :: Set maximum shape index of all shapes in all drawings (plus one)"><span class="description">Set maximum shape index of all shapes in all drawings (plus one)</span><pre>setSpIdMax()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__IDCLs" title="$_IDCLs :: Array of identifier clusters containg information about the maximum shape identifiers"><span class="description"></span><pre>$_IDCLs</pre></a></li>
<li class="property private "><a href="#property__OPT" title="$_OPT :: Array of options for the drawing group"><span class="description"></span><pre>$_OPT</pre></a></li>
<li class="property private "><a href="#property__bstoreContainer" title="$_bstoreContainer :: BLIP Store Container"><span class="description"></span><pre>$_bstoreContainer</pre></a></li>
<li class="property private "><a href="#property__cDgSaved" title="$_cDgSaved :: Total number of drawings saved"><span class="description"></span><pre>$_cDgSaved</pre></a></li>
<li class="property private "><a href="#property__cSpSaved" title="$_cSpSaved :: Total number of shapes saved (including group shapes)"><span class="description"></span><pre>$_cSpSaved</pre></a></li>
<li class="property private "><a href="#property__spIdMax" title="$_spIdMax :: Maximum shape index of all shapes in all drawings increased by one"><span class="description"></span><pre>$_spIdMax</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_Escher_DggContainer"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_Escher_DggContainer.html">PHPExcel_Shared_Escher_DggContainer</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Shared_Escher_DggContainer</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Shared.Escher.html">PHPExcel_Shared_Escher</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_getBstoreContainer"></a><div class="element clickable method public method_getBstoreContainer" data-toggle="collapse" data-target=".method_getBstoreContainer .collapse">
<h2>Get BLIP Store Container</h2>
<pre>getBstoreContainer() : <a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer</a></code></div>
</div></div>
</div>
<a id="method_getCDgSaved"></a><div class="element clickable method public method_getCDgSaved" data-toggle="collapse" data-target=".method_getCDgSaved .collapse">
<h2>Get total number of drawings saved</h2>
<pre>getCDgSaved() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_getCSpSaved"></a><div class="element clickable method public method_getCSpSaved" data-toggle="collapse" data-target=".method_getCSpSaved .collapse">
<h2>Get total number of shapes saved (including group shapes)</h2>
<pre>getCSpSaved() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_getIDCLs"></a><div class="element clickable method public method_getIDCLs" data-toggle="collapse" data-target=".method_getIDCLs .collapse">
<h2>Get identifier clusters</h2>
<pre>getIDCLs() : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>array</code></div>
</div></div>
</div>
<a id="method_getOPT"></a><div class="element clickable method public method_getOPT" data-toggle="collapse" data-target=".method_getOPT .collapse">
<h2>Get an option for the drawing group</h2>
<pre>getOPT(int $property) : mixed</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$property</h4>
<code>int</code><p>The number specifies the option</p></div>
<h3>Returns</h3>
<div class="subelement response"><code>mixed</code></div>
</div></div>
</div>
<a id="method_getSpIdMax"></a><div class="element clickable method public method_getSpIdMax" data-toggle="collapse" data-target=".method_getSpIdMax .collapse">
<h2>Get maximum shape index of all shapes in all drawings (plus one)</h2>
<pre>getSpIdMax() : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>int</code></div>
</div></div>
</div>
<a id="method_setBstoreContainer"></a><div class="element clickable method public method_setBstoreContainer" data-toggle="collapse" data-target=".method_setBstoreContainer .collapse">
<h2>Set BLIP Store Container</h2>
<pre>setBstoreContainer(<a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer</a> $bstoreContainer) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$bstoreContainer</h4>
<code><a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer</a></code>
</div>
</div></div>
</div>
<a id="method_setCDgSaved"></a><div class="element clickable method public method_setCDgSaved" data-toggle="collapse" data-target=".method_setCDgSaved .collapse">
<h2>Set total number of drawings saved</h2>
<pre>setCDgSaved(int $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>int</code>
</div>
</div></div>
</div>
<a id="method_setCSpSaved"></a><div class="element clickable method public method_setCSpSaved" data-toggle="collapse" data-target=".method_setCSpSaved .collapse">
<h2>Set total number of shapes saved (including group shapes)</h2>
<pre>setCSpSaved(int $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>int</code>
</div>
</div></div>
</div>
<a id="method_setIDCLs"></a><div class="element clickable method public method_setIDCLs" data-toggle="collapse" data-target=".method_setIDCLs .collapse">
<h2>Set identifier clusters.</h2>
<pre>setIDCLs(array $pValue) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>array(<drawingId> => <max shape id>, ...)</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>array</code>
</div>
</div></div>
</div>
<a id="method_setOPT"></a><div class="element clickable method public method_setOPT" data-toggle="collapse" data-target=".method_setOPT .collapse">
<h2>Set an option for the drawing group</h2>
<pre>setOPT(int $property, mixed $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$property</h4>
<code>int</code><p>The number specifies the option</p></div>
<div class="subelement argument">
<h4>$value</h4>
<code>mixed</code>
</div>
</div></div>
</div>
<a id="method_setSpIdMax"></a><div class="element clickable method public method_setSpIdMax" data-toggle="collapse" data-target=".method_setSpIdMax .collapse">
<h2>Set maximum shape index of all shapes in all drawings (plus one)</h2>
<pre>setSpIdMax(int $value) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>int</code>
</div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__IDCLs"> </a><div class="element clickable property private property__IDCLs" data-toggle="collapse" data-target=".property__IDCLs .collapse">
<h2></h2>
<pre>$_IDCLs : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__OPT"> </a><div class="element clickable property private property__OPT" data-toggle="collapse" data-target=".property__OPT .collapse">
<h2></h2>
<pre>$_OPT : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__bstoreContainer"> </a><div class="element clickable property private property__bstoreContainer" data-toggle="collapse" data-target=".property__bstoreContainer .collapse">
<h2></h2>
<pre>$_bstoreContainer : <a href="../classes/PHPExcel_Shared_Escher_DggContainer_BstoreContainer.html">\PHPExcel_Shared_Escher_DggContainer_BstoreContainer</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__cDgSaved"> </a><div class="element clickable property private property__cDgSaved" data-toggle="collapse" data-target=".property__cDgSaved .collapse">
<h2></h2>
<pre>$_cDgSaved : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__cSpSaved"> </a><div class="element clickable property private property__cSpSaved" data-toggle="collapse" data-target=".property__cSpSaved .collapse">
<h2></h2>
<pre>$_cSpSaved : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__spIdMax"> </a><div class="element clickable property private property__spIdMax" data-toggle="collapse" data-target=".property__spIdMax .collapse">
<h2></h2>
<pre>$_spIdMax : int</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:36Z.<br></footer></div>
</div>
</body>
</html>
