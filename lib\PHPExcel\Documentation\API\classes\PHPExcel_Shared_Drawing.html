<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Shared_Drawing</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list"><li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method_EMUToPixels" title="EMUToPixels :: Convert EMU to pixels"><span class="description">Convert EMU to pixels</span><pre>EMUToPixels()</pre></a></li>
<li class="method public "><a href="#method_angleToDegrees" title="angleToDegrees :: Convert angle to degrees"><span class="description">Convert angle to degrees</span><pre>angleToDegrees()</pre></a></li>
<li class="method public "><a href="#method_cellDimensionToPixels" title="cellDimensionToPixels :: Convert column width from (intrinsic) Excel units to pixels"><span class="description">Convert column width from (intrinsic) Excel units to pixels</span><pre>cellDimensionToPixels()</pre></a></li>
<li class="method public "><a href="#method_degreesToAngle" title="degreesToAngle :: Convert degrees to angle"><span class="description">Convert degrees to angle</span><pre>degreesToAngle()</pre></a></li>
<li class="method public "><a href="#method_imagecreatefrombmp" title="imagecreatefrombmp :: Create a new image from file."><span class="description">Create a new image from file.</span><pre>imagecreatefrombmp()</pre></a></li>
<li class="method public "><a href="#method_pixelsToCellDimension" title="pixelsToCellDimension :: Convert pixels to column width."><span class="description">Convert pixels to column width.</span><pre>pixelsToCellDimension()</pre></a></li>
<li class="method public "><a href="#method_pixelsToEMU" title="pixelsToEMU :: Convert pixels to EMU"><span class="description">Convert pixels to EMU</span><pre>pixelsToEMU()</pre></a></li>
<li class="method public "><a href="#method_pixelsToPoints" title="pixelsToPoints :: Convert pixels to points"><span class="description">Convert pixels to points</span><pre>pixelsToPoints()</pre></a></li>
<li class="method public "><a href="#method_pointsToPixels" title="pointsToPixels :: Convert points to pixels"><span class="description">Convert points to pixels</span><pre>pointsToPixels()</pre></a></li>
</ul>
</li></ul>
</div>
<div class="span8">
<a id="\PHPExcel_Shared_Drawing"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Shared_Drawing.html">PHPExcel_Shared_Drawing</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Shared_Drawing</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Shared.html">PHPExcel_Shared</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method_EMUToPixels"></a><div class="element clickable method public method_EMUToPixels" data-toggle="collapse" data-target=".method_EMUToPixels .collapse">
<h2>Convert EMU to pixels</h2>
<pre>EMUToPixels(int $pValue) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>int</code><p>Value in EMU</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Value in pixels</div>
</div></div>
</div>
<a id="method_angleToDegrees"></a><div class="element clickable method public method_angleToDegrees" data-toggle="collapse" data-target=".method_angleToDegrees .collapse">
<h2>Convert angle to degrees</h2>
<pre>angleToDegrees(int $pValue) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>int</code><p>Angle</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Degrees</div>
</div></div>
</div>
<a id="method_cellDimensionToPixels"></a><div class="element clickable method public method_cellDimensionToPixels" data-toggle="collapse" data-target=".method_cellDimensionToPixels .collapse">
<h2>Convert column width from (intrinsic) Excel units to pixels</h2>
<pre>cellDimensionToPixels(float $pValue, \PHPExcel_Style_Font $pDefaultFont) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>float</code><p>Value in cell dimension</p></div>
<div class="subelement argument">
<h4>$pDefaultFont</h4>
<code><a href="../classes/PHPExcel_Style_Font.html">\PHPExcel_Style_Font</a></code><p>Default font of the workbook</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Value in pixels</div>
</div></div>
</div>
<a id="method_degreesToAngle"></a><div class="element clickable method public method_degreesToAngle" data-toggle="collapse" data-target=".method_degreesToAngle .collapse">
<h2>Convert degrees to angle</h2>
<pre>degreesToAngle(int $pValue) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>int</code><p>Degrees</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Angle</div>
</div></div>
</div>
<a id="method_imagecreatefrombmp"></a><div class="element clickable method public method_imagecreatefrombmp" data-toggle="collapse" data-target=".method_imagecreatefrombmp .collapse">
<h2>Create a new image from file.</h2>
<pre>imagecreatefrombmp($p_sFile) : resource</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>By alexander at alexauto dot nl</p></div>
<table class="table table-bordered"><tr>
<th>link</th>
<td><a href="http://www.php.net/manual/en/function.imagecreatefromwbmp.php#86214">http://www.php.net/manual/en/function.imagecreatefromwbmp.php#86214</a></td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument"><h4>$p_sFile</h4></div>
<h3>Returns</h3>
<div class="subelement response"><code>resource</code></div>
</div></div>
</div>
<a id="method_pixelsToCellDimension"></a><div class="element clickable method public method_pixelsToCellDimension" data-toggle="collapse" data-target=".method_pixelsToCellDimension .collapse">
<h2>Convert pixels to column width.</h2>
<pre>pixelsToCellDimension(int $pValue, \PHPExcel_Style_Font $pDefaultFont) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Exact algorithm not known.
By inspection of a real Excel file using Calibri 11, one finds 1000px ~ 142.85546875
This gives a conversion factor of 7. Also, we assume that pixels and font size are proportional.</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>int</code><p>Value in pixels</p></div>
<div class="subelement argument">
<h4>$pDefaultFont</h4>
<code><a href="../classes/PHPExcel_Style_Font.html">\PHPExcel_Style_Font</a></code><p>Default font of the workbook</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Value in cell dimension</div>
</div></div>
</div>
<a id="method_pixelsToEMU"></a><div class="element clickable method public method_pixelsToEMU" data-toggle="collapse" data-target=".method_pixelsToEMU .collapse">
<h2>Convert pixels to EMU</h2>
<pre>pixelsToEMU(int $pValue) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>int</code><p>Value in pixels</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Value in EMU</div>
</div></div>
</div>
<a id="method_pixelsToPoints"></a><div class="element clickable method public method_pixelsToPoints" data-toggle="collapse" data-target=".method_pixelsToPoints .collapse">
<h2>Convert pixels to points</h2>
<pre>pixelsToPoints(int $pValue) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>int</code><p>Value in pixels</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Value in points</div>
</div></div>
</div>
<a id="method_pointsToPixels"></a><div class="element clickable method public method_pointsToPixels" data-toggle="collapse" data-target=".method_pointsToPixels .collapse">
<h2>Convert points to pixels</h2>
<pre>pointsToPixels(int $pValue) : int</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>int</code><p>Value in points</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>int</code>Value in pixels</div>
</div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:36Z.<br></footer></div>
</div>
</body>
</html>
