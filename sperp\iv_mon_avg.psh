#!/usr/local/bin/php -q
<?php





exit;


	// 0 9 * * * php -q /home/<USER>/sperp/iv_mon_avg.psh
	# 안전재고알림
	# ERP > 물류관리 > 재고관리 > 자재재고원평균사용량
	$ROOT_PATH = "/home/<USER>";
	include($ROOT_PATH . "/inc/func.php");
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/Encode.php");
	include($ROOT_PATH . "/inc/func_state.php");

	$dbconn_sperp_posbank = new DBController($db['sperp_posbank']); // 포스뱅크
//	$dbconn_sperp_posbank = new DBController($db['sperp_test']); // 포스뱅크개발

	if(empty($dbconn_sperp_posbank->success)) {
		echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
	}

	$dbconn_posbank_intra = new DBController($db['posbank_intra']);
	if(empty($dbconn_posbank_intra->success)) {
		echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패";
	}
	/**********************************************************/

	if(in_array(date('w'),array("0","6"))){
		echo date("Y-m-d") . " - 휴무일\n";
		## 스케즐 처리 상황 intra DB에 저장
		crontab_execution(86400, "안전재고 기준수량 도달 알림");
		exit;
	}

	$SQL = "SELECT NAME FROM HOLIDAY_DATA WHERE to_char(HDATE,'YYYYMMDD')='".date('Ymd')."'";
	$HOLIDAY_NM = $dbconn_sperp_posbank->query_one($SQL);
	if($HOLIDAY_NM){
		echo date("Y-m-d") . " - 휴무일(".$HOLIDAY_NM.")\n";
		## 스케즐 처리 상황 intra DB에 저장
		crontab_execution(86400, "안전재고 기준수량 도달 알림");
		exit;
	}


	// 발송 대상자
	$SQL2 = "SELECT BAS_OP4 as STCODE FROM BAS WHERE BAS_CODE='E081'";
	$stcode_str = $dbconn_sperp_posbank->query_one($SQL2);



	$stcode_str = "100994"; // 현주가
	$arr_ST = explode(",", $stcode_str);	
	
	$Checkday = date("Ym");


	$SQL = "
		SELECT          
			A.PR_CODE, A.PR_NAME, B.BAS_NAME,  B.BAS_OP2, B.BAS_OP3, D.IV_QTY,  A.IV_LT_DAY
		FROM 
		PR A
                left join PRKIND A2 on A.PR_CODE=A2.PR_CODE
		left join (SELECT PR_CODE, SUM(QTY) AS IV_QTY FROM IV GROUP BY PR_CODE) D on A.PR_CODE=D.PR_CODE  
                left join BAS B on B.BAS_CODE=A.IV_SAFE_CODE
		WHERE
			A.PR_IVCK='0' -- 재고관리유무 0사 1미사
			AND A.PR_END='0' -- 취급구분0취급,1단종,2품절,삭제
			AND A2.PR_WJS = 'C4W' -- 원재료
	                AND D.IV_QTY > 0 AND A.IV_SAFE_CODE IS NOT NULL
	                -- AND (D.IV_QTY > B.BAS_OP2 AND D.IV_QTY < B.BAS_OP3)	
			AND (D.IV_QTY < B.BAS_OP2 OR D.IV_QTY < B.BAS_OP3) 
	";
	$arrRow = $dbconn_sperp_posbank->query_rows($SQL);



	$style = "font-size:12px;line-height:25px;border:1px solid #333333;padding:3px;line-height:120%;";
	$style .= "text-overflow:ellipsis; table-layout:fixed;  overflow-x:hidden; overflow-y:hidden; white-space:nowrap;";
	$title = "[안전재고 기준수량 도달 알림] " . sizeof($arrRow) . "건";

	$content = "<div>\n";
	$content .= "<div style=\"font-size:12px;line-height:50px;\"><b>안전재고 기준수량에 도달된 내역</b></div>\n";
	$content .= "<div><a href=\"https://erp.posbank.com/?pageCode=MTI2MzQ=\" target=\"_blank\">[자재 재고 월평균사용량 보러가기]</a></div>";
	$content .= "<table>";
	$content .= "<tr>";
	$content .= "<th style=\"".$style."width:20px;\">No</th>";
	$content .= "<th style=\"".$style."width:60px;\">품목코드</th>";
	$content .= "<th style=\"".$style.";\">품목명</th>";
	$content .= "<th style=\"".$style."width:30px;\">안전등급</th>";
	$content .= "<th style=\"".$style."width:110px;\">기준수량</th>";
	$content .= "<th style=\"".$style."width:60px;\">재고</th>";
	$content .= "<th style=\"".$style."width:50px;\">리드타임<br>(Day)</th>";

	$content .= "</tr>\n";
	if($arrRow){
		foreach($arrRow as $key => $row) {
			$link = "https://erp.posbank.com/?pageCode=MTI2MzQ=&PRCODE=".$row['PR_CODE']."";

			$content .= "<tr>";
			$content .= "<td style=\"".$style." text-align:center;\">".($key+1)."</td>";
			$content .= "<td style=\"".$style." text-align:center;\"><a href=\"".$link."\" target=\"_blank\">".$row['PR_CODE']."</a></td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['PR_NAME']."</td>";
			$content .= "<td style=\"".$style." letter-spacing:-1px; text-align:center;\">".$row['BAS_NAME']."</td>";
			$content .= "<td style=\"".$style."\">".$row['BAS_OP2']." ~ ".$row['BAS_OP3']."</td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['IV_QTY']."</td>";
			$content .= "<td style=\"".$style." text-align:center;\">".$row['IV_LT_DAY']."</td>";
			$content .= "</tr>\n";
		}
	}
	$content .= "</table>\n";
	$content .= "</div>";


	//인트라넷 업무연락 보내는 함수(ERP 사원코드)
	$rs = intra_send_erp('',$arr_ST,$title,$content);
	echo date("Y-m-d H:i:s")." 업무연락 발송 \n";
	echo $title . "\n";
	echo "(".$stcode_str.") \n";
	echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";



	##### End. 2024-10-17. 신규 스케줄링
	###########################################


	## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(86400, "안전재고 기준수량 도달 알림");

	echo date("Y-m-d H:i:s")." - 끝\n";
?>


