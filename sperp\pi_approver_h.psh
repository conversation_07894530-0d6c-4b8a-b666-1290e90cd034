#!/usr/local/bin/php -q
<?php
	// 0 9 * * * php -q /home/<USER>/sperp/faulty_issue.psh
	# PI전표 승인원 미등록 건
	$ROOT_PATH = "/home/<USER>";
	include($ROOT_PATH . "/inc/func.php");
	include($ROOT_PATH . "/inc/db_controller.php");
	include($ROOT_PATH . "/inc/Encode.php");

	$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
	if(empty($dbconn_sperp_posbank->success)) {
		echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
	}

	$dbconn_posbank_intra = new DBController($db['posbank_intra']);
	if(empty($dbconn_posbank_intra->success)) {
		echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패";
	}
	/**********************************************************/

	if(in_array(date('w'),array("0","6"))){
		echo date("Y-m-d") . " - 휴무일\n";
		## 스케즐 처리 상황 intra DB에 저장
		crontab_execution(86400, "PI전표 승인원 미등록 알람");
		exit;
	}

	$SQL = "SELECT NAME FROM HOLIDAY_DATA WHERE to_char(HDATE,'YYYYMMDD')='".date('Ymd')."'";
	$HOLIDAY_NM = $dbconn_sperp_posbank->query_one($SQL);
	if($HOLIDAY_NM){
		echo date("Y-m-d") . " - 휴무일(".$HOLIDAY_NM.")\n";
		## 스케즐 처리 상황 intra DB에 저장
		crontab_execution(86400, "PI전표 승인원 미등록 알람");
		exit;
	}

	//품목 으로 변경
	/*$SQL = "
		SELECT
			HDATE HID,PONO,PR_CODE,PR_NAME,PR_STD
			,to_char(to_date(HDATE,'YYYY-MM-DD'),'YYYY-MM-DD') HDATE2
		FROM (
			SELECT
				A.HDATE,C.PONO,B.PR_CODE,PR.PR_NAME,PR.PR_STD,
				(SELECT NVL(D.PR_CODE,'0') FROM APPROVER_H D WHERE B.PR_CODE=D.PR_CODE AND D.STATE='1' GROUP BY D.PR_CODE) STATE
			FROM 
				EPIH A
				LEFT JOIN EPID B ON A.RCT_CODE=B.RCT_CODE AND A.HDATE=B.HDATE AND A.HNO=B.HNO
				LEFT JOIN PR ON B.PR_CODE=PR.PR_CODE
				LEFT JOIN PRKIND PK ON (PR.PR_CODE=PK.PR_CODE)
				LEFT JOIN EPOH C ON A.EPONO=C.RCT_CODE||C.HDATE||C.HNO
			WHERE 
				B.FSTATE <> '4'
				AND PK.PR_WJS = 'C4J'
				AND A.HDATE >='********'
			ORDER BY HDATE,PONO
		) WHERE STATE IS NULL ";*/
	// 원재료 포함 2025-04-14 jjs
	$SQL = "
		SELECT
			HDATE HID,PONO,PR_CODE,PR_NAME,PR_STD,PR_MODEL,PR_WJS
			,to_char(to_date(HDATE,'YYYY-MM-DD'),'YYYY-MM-DD') HDATE2
		FROM (
			SELECT
				A.HDATE,C.PONO,B.PR_CODE,PR.PR_NAME,PR.PR_STD,PR.PR_MODEL,PK.PR_WJS,
				CASE WHEN PK.PR_WJS='C4W' THEN 
					(SELECT NVL(D.PR_CODE,'0') FROM APPROVER_H D WHERE B.PR_CODE=D.PR_CODE AND D.STATE='1' GROUP BY D.PR_CODE)
				ELSE 
					(SELECT NVL(E.MD_CODE,'0') FROM APPROVER_MD_H E WHERE PR.PR_MODEL=E.MD_CODE AND E.STATE='1' GROUP BY E.MD_CODE)
				END as STATE
			FROM 
				EPIH A
				LEFT JOIN EPID B ON A.RCT_CODE=B.RCT_CODE AND A.HDATE=B.HDATE AND A.HNO=B.HNO
				LEFT JOIN PR ON B.PR_CODE=PR.PR_CODE
				LEFT JOIN PRKIND PK ON (PR.PR_CODE=PK.PR_CODE)
				LEFT JOIN EPOH C ON A.EPONO=C.RCT_CODE||C.HDATE||C.HNO
			WHERE 
				B.FSTATE <> '4'
				AND PK.PR_WJS in ('C4J','C4W')
				AND A.HDATE >='********'
			ORDER BY B.PR_CODE,A.HDATE,C.PONO
		) WHERE STATE IS NULL ";
	$arrRow = $dbconn_sperp_posbank->query_rows($SQL);

	// 발송 대상자
	$SQL2 = "SELECT BAS_OP4 as STCODE FROM BAS WHERE BAS_CODE='E066'";
	$stcode_str = $dbconn_sperp_posbank->query_one($SQL2);



	//$stcode_str = "100695";
	$arr_ST = explode(",", $stcode_str);

	$style = "stylefont-size:12px;line-height:25px;border:1px solid #333333;padding:3px 5px;white-space:nowrap;";
	$content = "<div>\n";
	$content .= "<div style=\"font-size:12px;line-height:50px;\"><b>승인원 미등록시 생산이 불가하니 승인원 등록해 주세요</b></div>\n";
	$content .= "<table>";
	$content .= "<tr>";
	$content .= "<th style=\"".$style."\">No</th>";
	$content .= "<th style=\"".$style."\">구분</th>";
	$content .= "<th style=\"".$style."\">품목코드</th>";
	$content .= "<th style=\"".$style."\">품목명</th>";
	$content .= "<th style=\"".$style."\">규격</th>";
	$content .= "<th style=\"".$style."\">등록일</th>";
	$content .= "<th style=\"".$style."\">PONO</th>";
	$content .= "</tr>\n";
	$n = 1;
	if($arrRow){
		foreach($arrRow as $key => $row) {
			if($key==0 || $row['PR_CODE']<>$arrRow[$key-1]['PR_CODE']){
				$PR_WJS_NM = ($row['PR_WJS']=='C4W')?"원재료":"제품";
				$content .= "<tr>";
				$content .= "<td style=\"".$style." text-align:center;\">".($n++)."</td>";
				$content .= "<td style=\"".$style."\">".$PR_WJS_NM."</td>";
				$content .= "<td style=\"".$style."\">".$row['PR_CODE']."</td>";
				$content .= "<td style=\"".$style."\">".$row['PR_NAME']."</td>";
				$content .= "<td style=\"".$style."\">".$row['PR_STD']."</td>";
				$content .= "<td style=\"".$style." text-align:center;\">".$row['HDATE2']."</td>";
				$content .= "<td style=\"".$style."\">".$row['PONO']."</td>";
				$content .= "</tr>\n";
			}
		}
	}
	$content .= "</table>\n";
	$content .= "</div>";

	$title = "[PI 등록건 중 승인원 미등록된 품목] " . ($n-1) . "건";
	$title = "승인원 미등록된 품목 긴급 등록 요청 - " . ($n-1) . "건";

	//인트라넷 업무연락 보내는 함수(ERP 사원코드)
	$rs = intra_send_erp('',$arr_ST,$title,$content);
	echo date("Y-m-d H:i:s")." 업무연락 발송 \n";
	echo $title . "\n";
	echo "(".$stcode_str.") \n";
	echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";



	##### End. 2024.04.26. 신규 스케줄링
	###########################################


	## 스케즐 처리 상황 monitor DB에 저장
	crontab_execution(86400, "PI전표 승인원 미등록 알람");

	echo date("Y-m-d H:i:s")." - 끝\n";
?>
