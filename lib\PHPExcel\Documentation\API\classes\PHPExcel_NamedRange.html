<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_NamedRange</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public "><a href="#method___clone" title="__clone :: Implement PHP __clone to create a deep clone, not just a shallow copy."><span class="description">Implement PHP __clone to create a deep clone, not just a shallow copy.</span><pre>__clone()</pre></a></li>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new NamedRange"><span class="description">Create a new NamedRange</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_getLocalOnly" title="getLocalOnly :: Get localOnly"><span class="description">Get localOnly</span><pre>getLocalOnly()</pre></a></li>
<li class="method public "><a href="#method_getName" title="getName :: Get name"><span class="description">Get name</span><pre>getName()</pre></a></li>
<li class="method public "><a href="#method_getRange" title="getRange :: Get range"><span class="description">Get range</span><pre>getRange()</pre></a></li>
<li class="method public "><a href="#method_getScope" title="getScope :: Get scope"><span class="description">Get scope</span><pre>getScope()</pre></a></li>
<li class="method public "><a href="#method_getWorksheet" title="getWorksheet :: Get worksheet"><span class="description">Get worksheet</span><pre>getWorksheet()</pre></a></li>
<li class="method public "><a href="#method_resolveRange" title="resolveRange :: Resolve a named range to a regular cell range"><span class="description">Resolve a named range to a regular cell range</span><pre>resolveRange()</pre></a></li>
<li class="method public "><a href="#method_setLocalOnly" title="setLocalOnly :: Set localOnly"><span class="description">Set localOnly</span><pre>setLocalOnly()</pre></a></li>
<li class="method public "><a href="#method_setName" title="setName :: Set name"><span class="description">Set name</span><pre>setName()</pre></a></li>
<li class="method public "><a href="#method_setRange" title="setRange :: Set range"><span class="description">Set range</span><pre>setRange()</pre></a></li>
<li class="method public "><a href="#method_setScope" title="setScope :: Set scope"><span class="description">Set scope</span><pre>setScope()</pre></a></li>
<li class="method public "><a href="#method_setWorksheet" title="setWorksheet :: Set worksheet"><span class="description">Set worksheet</span><pre>setWorksheet()</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header private">» Private
                    <ul>
<li class="property private "><a href="#property__localOnly" title="$_localOnly :: Is the named range local? (i.e."><span class="description"></span><pre>$_localOnly</pre></a></li>
<li class="property private "><a href="#property__name" title="$_name :: Range name"><span class="description"></span><pre>$_name</pre></a></li>
<li class="property private "><a href="#property__range" title="$_range :: Range of the referenced cells"><span class="description"></span><pre>$_range</pre></a></li>
<li class="property private "><a href="#property__scope" title="$_scope :: Scope"><span class="description"></span><pre>$_scope</pre></a></li>
<li class="property private "><a href="#property__worksheet" title="$_worksheet :: Worksheet on which the named range can be resolved"><span class="description"></span><pre>$_worksheet</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_NamedRange"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_NamedRange.html">PHPExcel_NamedRange</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_NamedRange</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.html">PHPExcel</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___clone"></a><div class="element clickable method public method___clone" data-toggle="collapse" data-target=".method___clone .collapse">
<h2>Implement PHP __clone to create a deep clone, not just a shallow copy.</h2>
<pre>__clone() </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new NamedRange</h2>
<pre>__construct(string $pName, \PHPExcel_Worksheet $pWorksheet, string $pRange, bool $pLocalOnly, <a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a> | null $pScope) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pName</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$pWorksheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code>
</div>
<div class="subelement argument">
<h4>$pRange</h4>
<code>string</code>
</div>
<div class="subelement argument">
<h4>$pLocalOnly</h4>
<code>bool</code>
</div>
<div class="subelement argument">
<h4>$pScope</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><code>null</code><p>Scope. Only applies when $pLocalOnly = true. Null for global scope.</p>
</div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
</div></div>
</div>
<a id="method_getLocalOnly"></a><div class="element clickable method public method_getLocalOnly" data-toggle="collapse" data-target=".method_getLocalOnly .collapse">
<h2>Get localOnly</h2>
<pre>getLocalOnly() : bool</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>bool</code></div>
</div></div>
</div>
<a id="method_getName"></a><div class="element clickable method public method_getName" data-toggle="collapse" data-target=".method_getName .collapse">
<h2>Get name</h2>
<pre>getName() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getRange"></a><div class="element clickable method public method_getRange" data-toggle="collapse" data-target=".method_getRange .collapse">
<h2>Get range</h2>
<pre>getRange() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getScope"></a><div class="element clickable method public method_getScope" data-toggle="collapse" data-target=".method_getScope .collapse">
<h2>Get scope</h2>
<pre>getScope() : <a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a> | null</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><code>null</code>
</div>
</div></div>
</div>
<a id="method_getWorksheet"></a><div class="element clickable method public method_getWorksheet" data-toggle="collapse" data-target=".method_getWorksheet .collapse">
<h2>Get worksheet</h2>
<pre>getWorksheet() : <a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code></div>
</div></div>
</div>
<a id="method_resolveRange"></a><div class="element clickable method public method_resolveRange" data-toggle="collapse" data-target=".method_resolveRange .collapse">
<h2>Resolve a named range to a regular cell range</h2>
<pre>resolveRange(string $pNamedRange, \PHPExcel_Worksheet $pSheet) : <a href="../classes/PHPExcel_NamedRange.html">\PHPExcel_NamedRange</a></pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pNamedRange</h4>
<code>string</code><p>Named range</p></div>
<div class="subelement argument">
<h4>$pSheet</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><code>null</code><p>Scope. Use null for global scope</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_NamedRange.html">\PHPExcel_NamedRange</a></code></div>
</div></div>
</div>
<a id="method_setLocalOnly"></a><div class="element clickable method public method_setLocalOnly" data-toggle="collapse" data-target=".method_setLocalOnly .collapse">
<h2>Set localOnly</h2>
<pre>setLocalOnly(bool $value) : <a href="../classes/PHPExcel_NamedRange.html">\PHPExcel_NamedRange</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>bool</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_NamedRange.html">\PHPExcel_NamedRange</a></code></div>
</div></div>
</div>
<a id="method_setName"></a><div class="element clickable method public method_setName" data-toggle="collapse" data-target=".method_setName .collapse">
<h2>Set name</h2>
<pre>setName(string $value) : <a href="../classes/PHPExcel_NamedRange.html">\PHPExcel_NamedRange</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_NamedRange.html">\PHPExcel_NamedRange</a></code></div>
</div></div>
</div>
<a id="method_setRange"></a><div class="element clickable method public method_setRange" data-toggle="collapse" data-target=".method_setRange .collapse">
<h2>Set range</h2>
<pre>setRange(string $value) : <a href="../classes/PHPExcel_NamedRange.html">\PHPExcel_NamedRange</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_NamedRange.html">\PHPExcel_NamedRange</a></code></div>
</div></div>
</div>
<a id="method_setScope"></a><div class="element clickable method public method_setScope" data-toggle="collapse" data-target=".method_setScope .collapse">
<h2>Set scope</h2>
<pre>setScope(\PHPExcel_Worksheet $value) : <a href="../classes/PHPExcel_NamedRange.html">\PHPExcel_NamedRange</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code><code>null</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_NamedRange.html">\PHPExcel_NamedRange</a></code></div>
</div></div>
</div>
<a id="method_setWorksheet"></a><div class="element clickable method public method_setWorksheet" data-toggle="collapse" data-target=".method_setWorksheet .collapse">
<h2>Set worksheet</h2>
<pre>setWorksheet(\PHPExcel_Worksheet $value) : <a href="../classes/PHPExcel_NamedRange.html">\PHPExcel_NamedRange</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$value</h4>
<code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_NamedRange.html">\PHPExcel_NamedRange</a></code></div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__localOnly"> </a><div class="element clickable property private property__localOnly" data-toggle="collapse" data-target=".property__localOnly .collapse">
<h2></h2>
<pre>$_localOnly : bool</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"><p>can only be used on $this->_worksheet)</p></div></div></div>
</div>
<a id="property__name"> </a><div class="element clickable property private property__name" data-toggle="collapse" data-target=".property__name .collapse">
<h2></h2>
<pre>$_name : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__range"> </a><div class="element clickable property private property__range" data-toggle="collapse" data-target=".property__range .collapse">
<h2></h2>
<pre>$_range : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__scope"> </a><div class="element clickable property private property__scope" data-toggle="collapse" data-target=".property__scope .collapse">
<h2></h2>
<pre>$_scope : <a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__worksheet"> </a><div class="element clickable property private property__worksheet" data-toggle="collapse" data-target=".property__worksheet .collapse">
<h2></h2>
<pre>$_worksheet : <a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:35Z.<br></footer></div>
</div>
</body>
</html>
