# Douzone 연동 시스템

## 개요
SPERP (POSBANK) 시스템과 Amaranth10 (더존 클라우드) 간의 매출/매입 전표 데이터 동기화를 담당하는 시스템입니다. 기존 직접 DB 연동 방식에서 **Amaranth10 API 기반**으로 전환되었습니다.

## 시스템 아키텍처

```
SPERP Oracle DB → API 변환 → Amaranth10 API → 더존 클라우드 회계
```

## 파일 구성

### 🔧 **설정 파일**
- **`api_config.php`**: Amaranth10 API 연결 설정 및 인증 정보
- **`simple_debug.php`**: API 연결 상태 및 인증 테스트

### 📈 **매출전표 시스템**
- **`sp_douzon_ph13_api.php`**: 매출전표 API 처리 시스템
  - SPERP → Amaranth10 API 실시간 연동
  - api11A37 (매입매출자동전표데이터등록) 사용
  - acctTy='1' (매출전표)
- **`sp_duzon_ph13.psh`**: 기존 직접 DB 연동 방식 (레거시)

### 🛒 **매입전표 시스템**  
- **`sp_douzon_ph23_api.php`**: 매입전표 API 처리 시스템
  - SPERP → Amaranth10 API 실시간 연동
  - api11A37 (매입매출자동전표데이터등록) 사용
  - acctTy='2' (매입전표)
- **`sp_duzon_ph23.psh`**: 기존 직접 DB 연동 방식 (레거시)

## 🚀 **API 기반 시스템 특징**

### ✨ **주요 개선사항**
- **실시간 API 연동**: 직접 DB 접근 대신 표준 API 사용
- **클라우드 연동**: Amaranth10 클라우드 서비스 활용
- **보안 강화**: HMAC SHA256 서명 인증
- **중복 방지**: 스마트 중복 체크 시스템
- **에러 핸들링**: 상세한 resultCode 기반 오류 처리

### 🔐 **인증 시스템**
- **서명 방식**: `authToken + transactionId + timestamp + url`
- **해시 알고리즘**: HMAC SHA256
- **헤더 인증**: wehago-sign, transaction-id, Authorization Bearer





## 🔄 **API 처리 흐름**

### 1️⃣ **데이터 조회**
- **SPERP Oracle DB** 연결 및 최근 7일 데이터 조회
- **복합 쿼리**: 기본 전표 + 부가세 + 외상/미지급 데이터 통합

### 2️⃣ **중복 체크**
- **SAUTODOCUD** 테이블에서 기존 처리 건 확인
- **고유키**: `IN_DT + IN_SQ` 조합으로 중복 방지
- **처리 완료 건**: `ISU_DT <> '00000000'` 조건으로 필터링

### 3️⃣ **데이터 변환**
- **SPERP 형식** → **Amaranth10 API 형식** 변환
- **필드 매핑**: 과세구분, 계정코드, 거래처 정보 등
- **JSON 구조**: api11A37 스펙에 맞는 items 배열 생성

### 4️⃣ **API 호출**
- **인증 헤더** 생성 (HMAC SHA256 서명)
- **Amaranth10 API** 실시간 호출
- **응답 처리**: resultCode 기반 성공/실패 판단

### 5️⃣ **결과 처리**
- **성공 시**: 처리 완료 로그 출력
- **실패 시**: 상세 오류 메시지 표시
- **모니터링**: 처리 건수 및 상태 추적

## 📊 **데이터 분류 및 매핑**

### 📈 **매출전표 (acctTy='1')**
| 과세구분 | 설명 | API taxFg | 비고 |
|---------|------|-----------|------|
| GU1='11' | 과세매출 | 11 | 일반 부가세 포함 매출 |
| GU1='17' | 카드매출 | 17 | 신용카드 결제 매출 |
| GU1='31' | 현금과세 | 31 | 현금영수증 발행 매출 |
| GU1='14' | 건별매출 | 14 | 개별 건 처리 매출 |
| GU1='16' | 수출 | 16 | 해외 수출 매출 |
| GU1='12' | 영세매출 | 12 | 영세율 적용 매출 |
| GU1='13' | 면세매출 | 13 | 부가세 면세 매출 |
| GU1='18' | 면세카드매출 | 18 | 카드결제 면세 매출 |
| GU1='32' | 현금면세 | 32 | 현금 면세 매출 |

### 🛒 **매입전표 (acctTy='2')**
| 과세구분 | 설명 | API taxFg | 비고 |
|---------|------|-----------|------|
| GU1='21' | 과세매입 | 21 | 일반 부가세 포함 매입 |
| GU1='27' | 카드매입 | 27 | 신용카드 결제 매입 |
| GU1='28' | 현금영수증매입 | 28 | 현금영수증 발행 매입 |
| GU1='25' | 수입 | 25 | 해외 수입 |
| GU1='22' | 영세매입 | 22 | 영세율 적용 매입 |
| GU1='23' | 면세매입 | 23 | 부가세 면세 매입 |

### 🔗 **특수 계정 매핑**
| 계정구분 | SPERP 코드 | API 매핑 | 설명 |
|---------|-----------|----------|------|
| 부가세예수금 | 21107001 | 25500 | 매출 부가세 |
| 외상매출금 | 11105001 | 10800 | 매출 채권 |
| 부가세대급금 | 11305001 | 13500 | 매입 부가세 |
| 외상매입금 | 21101001 | 25100 | 매입 채무 |
| 선급금 | 11301003 | 13100 | 선급 매입금 |
| 미지급금 | 21105003 | 25300 | 미지급 매입금 |

## ⚙️ **API 시스템 주요 기능**

### 🔄 **실시간 API 연동**
- **Amaranth10 API** 직접 호출로 즉시 전표 등록
- **JSON 기반** 데이터 전송으로 표준화된 연동
- **resultCode 기반** 성공/실패 즉시 확인

### 🛡️ **보안 및 인증**
- **HMAC SHA256** 서명 기반 API 인증
- **토큰 기반** 접근 제어
- **타임스탬프** 기반 재전송 공격 방지

### 🔍 **지능형 중복 방지**
- **DB 기반** 처리 이력 관리
- **고유키 검증**으로 중복 전송 차단
- **증분 처리**로 효율성 극대화

### 📊 **상세 모니터링**
- **단계별 처리 상황** 실시간 출력
- **API 응답 분석** 및 오류 세부사항 표시
- **처리 건수 통계** 및 성공률 추적

## 🚀 **실행 환경**

### **시스템 요구사항**
- **언어**: PHP 7.4+ (CLI 모드)
- **확장**: cURL, JSON, OpenSSL
- **데이터베이스**: Oracle (SPERP), SQL Server (Douzone)

### **실행 방법**
```bash
# 매출전표 API 처리
php sp_douzon_ph13_api.php

# 매입전표 API 처리  
php sp_douzon_ph23_api.php

# API 연결 테스트
php simple_debug.php
```

### **설정 파일**
- **`api_config.php`**: API 접속 정보 및 인증 키
- **`/inc/db_config.php`**: 데이터베이스 연결 설정

## ⚠️ **주의사항**

### **운영 관련**
- **7일 이내** 데이터만 처리 (성능 최적화)
- **회사코드 '2005'**, **회계단위 '1000'** 고정 사용
- **STATE='8'** (확정) 전표만 API 전송

### **보안 관련**
- **API 키 보안** 유지 필수
- **로그 파일** 민감정보 제거
- **네트워크 방화벽** 설정 확인

### **모니터링**
- **API 응답 시간** 모니터링 권장
- **resultCode 0** 외 오류 발생 시 알림 설정
- **일일 처리 건수** 추적 및 이상 징후 감지

## 📋 **관련 테이블 및 API**

### **SPERP Oracle 테이블**
- **TMH, TMS, CT**: 전표 마스터 및 거래처 정보
- **PD13, PD23**: 매출/매입 상세 데이터
- **PRKIND, BAS, FN**: 제품 및 계정 마스터

### **Amaranth10 API**
- **api11A37**: 매입매출자동전표데이터등록
- **api16S08**: 거래처 조회 (테스트용)
- **Authentication**: Bearer Token + HMAC SHA256

### **Douzone 연동 테이블**  
- **SAUTODOCUD**: 자동전표 데이터 (중복 체크용)
- **VA_AUTOSACCT**: 계정 정보 뷰

---

## 🔧 **문제해결 가이드**

### **API 인증 오류**
```bash
# 연결 테스트 실행
php simple_debug.php

# 일반적인 오류 코드:
# 104: Timestamp 누락
# 105: Transaction-Id 누락  
# 112: 유효하지 않은 인증 파라미터
```

### **데이터 변환 오류**
- **과세구분 매핑** 확인: GU1 값이 올바른지 검증
- **계정코드 매핑** 확인: FN_LINK 값이 존재하는지 검증
- **필수 필드** 확인: 거래처, 금액 등 필수 정보 누락 여부

### **성능 최적화**
- **조회 기간 조정**: 기본 7일에서 필요에 따라 단축
- **배치 크기 조정**: 한 번에 처리할 전표 건수 조절
- **DB 인덱스** 확인: TMHID, HDATE 등 주요 필드 인덱스 최적화


https://helpdesk.douzone.com/dev/detail/detailView.do?seq=4348


