#!/usr/bin/php -q
<?
// 20 17 * * * php -q /home/<USER>/sperp/serial.sh
# DSP LICENSE
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");

echo date("Y-m-d H:i:s")." - 실행 \n";

$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
if(empty($dbconn_sperp_posbank->success)) {
	echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
}
/**********************************************************/


// VAN_CODE 추가로 쿼리 변경, 임창노 수석님 2019-10-24 sjlee
$query = "
	insert into DSP_LICENSE (AS_SERIAL, ASNO, MACIP, DSPYN, REG_IDATE, VAN_CODE)

	select
		v.AS_SERIAL, rownum as ASNO,v.OR_MACIP as MACIP,'N' as DSPYN,sysdate as REG_IDATE, v.VAN_CODE
	from
		V_DSP_AS_MASTER v 
		left join DSP_LICENSE d on d.as_serial=v.as_serial
	where
		d.as_serial is null 
		and (
			v.REG_IDATE >= (select max(REG_IDATE-1) from DSP_LICENSE) OR
			v.REG_DATE >= (select max(REG_IDATE-1) from DSP_LICENSE)
		)
";
$rs = $dbconn_sperp_posbank->iud_query($query);

echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
echo date("Y-m-d H:i:s")." - 종료\n";

## 스케즐 처리 상황 monitor DB에 저장 
crontab_execution(86400, "ERP DSP LICENSE");
?>