#!/usr/local/bin/php -q
<?
// error_reporting(E_ALL);
# 피드백 업무연락 중 확인 안한 건 알림
# 매일 9시 실행
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/inc/slackWebhook.php");
include($ROOT_PATH . "/inc/db_config.php");


// sperp = A (국내)
$dbconn_sperp = new DBController($db['sperp_posbank']);
if (!empty($dbconn_sperp->success)) {
    echo "sperp (A) DB 접속 성공\n";
} else {
    echo "sperp (A) DB 접속 실패\n";
}

// europe = B (유럽)
$dbconn_europe = new DBController($db['sperp_posbank_europe']);
if (!empty($dbconn_europe->success)) {
    echo "europe (B) DB 접속 성공\n";
} else {
    echo "europe (B) DB 접속 실패\n";
}




// 1. PR 테이블 컬럼 동기화 (필요시 컬럼 자동 추가)
$a_pr_columns = $dbconn_sperp->query_rows(
    "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = 'PR' AND OWNER = 'PBKADMIN'"
);
$b_pr_columns = $dbconn_europe->query_array(
    "SELECT COLUMN_NAME FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = 'PR' AND OWNER = 'PBKADMIN'",
    'COLUMN_NAME', 'COLUMN_NAME'
);

$alter_sqls = [];
foreach ($a_pr_columns as $col) {
    $name = $col['COLUMN_NAME'];
    if (!isset($b_pr_columns[$name])) {
        $type = $col['DATA_TYPE'];
        if ($type == 'VARCHAR2') {
            $type_str = "VARCHAR2(" . $col['DATA_LENGTH'] . ")";
        } else if ($type == 'NUMBER') {
            if ($col['DATA_PRECISION'] !== null && $col['DATA_SCALE'] !== null) {
                $type_str = "NUMBER(" . $col['DATA_PRECISION'] . "," . $col['DATA_SCALE'] . ")";
            } else if ($col['DATA_PRECISION'] !== null) {
                $type_str = "NUMBER(" . $col['DATA_PRECISION'] . ")";
            } else {
                $type_str = "NUMBER";
            }
        } else if ($type == 'DATE') {
            $type_str = "DATE";
        } else {
            $type_str = $type;
        }
        $alter_sqls[] = "ALTER TABLE PBKADMIN.PR ADD $name $type_str";
    }
}
foreach ($alter_sqls as $sql) {
    echo "[ALTER] $sql\n";
    $dbconn_europe->query($sql);
}

// 2. PR 테이블 MERGE (PL/SQL 블록)
$plsql_pr = <<<EOD
BEGIN
  MERGE INTO PBKADMIN.PR B
  USING (
    SELECT * FROM PBKADMIN.PR@ERP_DB_LINK
  ) A
  ON (B.PR_CODE = A.PR_CODE)
  WHEN MATCHED THEN
    UPDATE SET
      B.PR_NAME = A.PR_NAME,
      B.PR_BCODE = A.PR_BCODE,
      B.PR_STD = A.PR_STD,
      B.PR_IUNIT = A.PR_IUNIT,
      B.PR_MUNIT = A.PR_MUNIT,
      B.PR_CQTY = A.PR_CQTY,
      B.PR_PTNO = A.PR_PTNO,
      B.PR_END = A.PR_END,
      B.PR_IVCK = A.PR_IVCK,
      B.PR_OTAX = A.PR_OTAX,
      B.PR_ITAX = A.PR_ITAX,
      B.PR_IUP = A.PR_IUP,
      B.PR_OUP = A.PR_OUP,
      B.PR_CUP = A.PR_CUP,
      B.PR_LUP = A.PR_LUP,
      B.PR_UPRCODE = A.PR_UPRCODE,
      B.PR_UQTY = A.PR_UQTY,
      B.PR_UDATE = A.PR_UDATE,
      B.PR_SET = A.PR_SET,
      B.PR_ORDER = A.PR_ORDER,
      B.PR_EXPPS = A.PR_EXPPS
  WHEN NOT MATCHED THEN
    INSERT (
      PR_CODE, PR_NAME, PR_BCODE, PR_STD, PR_IUNIT, PR_MUNIT, PR_CQTY,
      PR_PTNO, PR_END, PR_IVCK, PR_OTAX, PR_ITAX, PR_IUP, PR_OUP, PR_CUP,
      PR_LUP, PR_UPRCODE, PR_UQTY, PR_UDATE, PR_SET, PR_ORDER, PR_EXPPS
    )
    VALUES (
      A.PR_CODE, A.PR_NAME, A.PR_BCODE, A.PR_STD, A.PR_IUNIT, A.PR_MUNIT, A.PR_CQTY,
      A.PR_PTNO, A.PR_END, A.PR_IVCK, A.PR_OTAX, A.PR_ITAX, A.PR_IUP, A.PR_OUP, A.PR_CUP,
      A.PR_LUP, A.PR_UPRCODE, A.PR_UQTY, A.PR_UDATE, A.PR_SET, A.PR_ORDER, A.PR_EXPPS
    );
  DBMS_OUTPUT.PUT_LINE('[SUCCESS] PR MERGE 완료');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('[ERROR] ' || SQLERRM);
END;
EOD;

$conn = $dbconn_europe->dbconn;
oci_execute(oci_parse($conn, "BEGIN DBMS_OUTPUT.ENABLE(NULL); END;"));

$stmt = oci_parse($conn, $plsql_pr);
if (!oci_execute($stmt)) {
    $e = oci_error($stmt);
    echo "[OCI ERROR] " . $e['message'] . "\n";
} else {
    oci_commit($conn);
}

$stid = oci_parse($conn, "BEGIN DBMS_OUTPUT.GET_LINE(:line, :status); END;");
while (true) {
    oci_bind_by_name($stid, ":line", $buffer, 1000000);
    oci_bind_by_name($stid, ":status", $status, 5);
    oci_execute($stid);
    if ($status != 0) break;
    if (trim($buffer) !== "") echo $buffer . "\n";
}



// 1. A(원본) 테이블 컬럼 목록 조회
$a_columns = $dbconn_sperp->query_rows(
    "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = 'PRKIND' AND OWNER = 'PBKADMIN'"
);

// 2. B(대상) 테이블 컬럼 목록 조회
$b_columns = $dbconn_europe->query_array(
    "SELECT COLUMN_NAME FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = 'PRKIND' AND OWNER = 'PBKADMIN'",
    'COLUMN_NAME', 'COLUMN_NAME'
);




// 3. 없는 컬럼만 ALTER TABLE로 추가
$alter_sqls = [];
foreach ($a_columns as $col) {
    $name = $col['COLUMN_NAME'];
    if (!isset($b_columns[$name])) {
        // 타입 문자열 생성
        $type = $col['DATA_TYPE'];
        if ($type == 'VARCHAR2') {
            $type_str = "VARCHAR2(" . $col['DATA_LENGTH'] . ")";
        } else if ($type == 'NUMBER') {
            if ($col['DATA_PRECISION'] !== null && $col['DATA_SCALE'] !== null) {
                $type_str = "NUMBER(" . $col['DATA_PRECISION'] . "," . $col['DATA_SCALE'] . ")";
            } else if ($col['DATA_PRECISION'] !== null) {
                $type_str = "NUMBER(" . $col['DATA_PRECISION'] . ")";
            } else {
                $type_str = "NUMBER";
            }
        } else if ($type == 'DATE') {
            $type_str = "DATE";
        } else {
            $type_str = $type;
        }
        $alter_sqls[] = "ALTER TABLE PBKADMIN.PRKIND ADD $name $type_str";
    }
}
foreach ($alter_sqls as $sql) {
    echo "[ALTER] $sql\n";
    $dbconn_europe->query($sql);
}




// 2. PRKIND 테이블 MERGE (이전과 동일하게 실행)
// MERGE 쿼리 (PL/SQL 블록)
$plsql = <<<EOD
BEGIN
  MERGE INTO PBKADMIN.PRKIND B
  USING (
    SELECT * FROM PBKADMIN.PRKIND@ERP_DB_LINK
  ) A
  ON (B.PR_CODE = A.PR_CODE)
  WHEN MATCHED THEN
    UPDATE SET
      B.PR_GP1         = A.PR_GP1,
      B.PR_GP2         = A.PR_GP2,
      B.PR_GP3         = A.PR_GP3,
      B.PR_GP4         = A.PR_GP4,
      B.PR_GP5         = A.PR_GP5,
      B.PR_WJS         = A.PR_WJS,
      B.PR_MARJIN      = A.PR_MARJIN,
      B.CT_CODE        = A.CT_CODE,
      B.PR_ID1         = A.PR_ID1,
      B.PR_ID2         = A.PR_ID2,
      B.REG_STCODE     = A.REG_STCODE,
      B.REG_DATE       = A.REG_DATE,
      B.REG_ISTCODE    = A.REG_ISTCODE,
      B.REG_IDATE      = A.REG_IDATE,
      B.MCT_CODE       = A.MCT_CODE,
      B.PR_GP6         = A.PR_GP6,
      B.PR_GP7         = A.PR_GP7,
      B.PR_GP8         = A.PR_GP8,
      B.PR_GP9         = A.PR_GP9,
      B.OCT_CODE       = A.OCT_CODE,
      B.PR_ST_CODE     = A.PR_ST_CODE,
      B.IV_ST_CODE     = A.IV_ST_CODE,
      B.PR_EURO        = A.PR_EURO,
      B.PR_EURO_DATE   = A.PR_EURO_DATE
  WHEN NOT MATCHED THEN
    INSERT (
      PR_CODE, PR_GP1, PR_GP2, PR_GP3, PR_GP4, PR_GP5,
      PR_WJS, PR_MARJIN, CT_CODE, PR_ID1, PR_ID2, REG_STCODE, REG_DATE,
      REG_ISTCODE, REG_IDATE, MCT_CODE, PR_GP6, PR_GP7, PR_GP8, PR_GP9,
      OCT_CODE, PR_ST_CODE, IV_ST_CODE, PR_EURO, PR_EURO_DATE
    )
    VALUES (
      A.PR_CODE, A.PR_GP1, A.PR_GP2, A.PR_GP3, A.PR_GP4, A.PR_GP5,
      A.PR_WJS, A.PR_MARJIN, A.CT_CODE, A.PR_ID1, A.PR_ID2, A.REG_STCODE, A.REG_DATE,
      A.REG_ISTCODE, A.REG_IDATE, A.MCT_CODE, A.PR_GP6, A.PR_GP7, A.PR_GP8, A.PR_GP9,
      A.OCT_CODE, A.PR_ST_CODE, A.IV_ST_CODE, A.PR_EURO, A.PR_EURO_DATE
    );
  DBMS_OUTPUT.PUT_LINE('[SUCCESS] PRKIND MERGE 완료');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('[ERROR] ' || SQLERRM);
END;
EOD;

// 쿼리 실행
$conn = $dbconn_europe->dbconn;
oci_execute(oci_parse($conn, "BEGIN DBMS_OUTPUT.ENABLE(NULL); END;"));

$stmt = oci_parse($conn, $plsql);
if (!oci_execute($stmt)) {
    $e = oci_error($stmt);
    echo "[OCI ERROR] " . $e['message'] . "\n";
} else {
    oci_commit($conn);
}



$stid = oci_parse($conn, "BEGIN DBMS_OUTPUT.GET_LINE(:line, :status); END;");
while (true) {
    oci_bind_by_name($stid, ":line", $buffer, 1000000);
    oci_bind_by_name($stid, ":status", $status, 5);
    oci_execute($stid);
    if ($status != 0) break;
    if (trim($buffer) !== "") echo $buffer . "\n";
}




// 1. PR_QR 테이블 컬럼 동기화 (필요시 컬럼 자동 추가)
$a_prqr_columns = $dbconn_sperp->query_rows(
    "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = 'PR_QR' AND OWNER = 'PBKADMIN'"
);
$b_prqr_columns = $dbconn_europe->query_array(
    "SELECT COLUMN_NAME FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = 'PR_QR' AND OWNER = 'PBKADMIN'",
    'COLUMN_NAME', 'COLUMN_NAME'
);

$alter_sqls = [];
foreach ($a_prqr_columns as $col) {
    $name = $col['COLUMN_NAME'];
    if (!isset($b_prqr_columns[$name])) {
        $type = $col['DATA_TYPE'];
        if ($type == 'VARCHAR2') {
            $type_str = "VARCHAR2(" . $col['DATA_LENGTH'] . ")";
        } else if ($type == 'NUMBER') {
            if ($col['DATA_PRECISION'] !== null && $col['DATA_SCALE'] !== null) {
                $type_str = "NUMBER(" . $col['DATA_PRECISION'] . "," . $col['DATA_SCALE'] . ")";
            } else if ($col['DATA_PRECISION'] !== null) {
                $type_str = "NUMBER(" . $col['DATA_PRECISION'] . ")";
            } else {
                $type_str = "NUMBER";
            }
        } else if ($type == 'DATE') {
            $type_str = "DATE";
        } else {
            $type_str = $type;
        }
        $alter_sqls[] = "ALTER TABLE PBKADMIN.PR_QR ADD $name $type_str";
    }
}
foreach ($alter_sqls as $sql) {
    echo "[ALTER] $sql\n";
    $dbconn_europe->query($sql);
}

// 2. PR_QR 테이블 MERGE (PL/SQL 블록)
$plsql_prqr = <<<EOD
BEGIN
  MERGE INTO PBKADMIN.PR_QR B
  USING (
    SELECT * FROM PBKADMIN.PR_QR@ERP_DB_LINK
  ) A
  ON (B.PR_CODE = A.PR_CODE)
  WHEN MATCHED THEN
    UPDATE SET
      B.PR_QR_NAME  = A.PR_QR_NAME,
      B.PR_GP1      = A.PR_GP1,
      B.PR_GP2      = A.PR_GP2,
      B.PR_GP3      = A.PR_GP3,
      B.PR_GP4      = A.PR_GP4,
      B.PR_GP5      = A.PR_GP5,
      B.PR_GP6      = A.PR_GP6,
      B.PR_GP7      = A.PR_GP7,
      B.PR_GP8      = A.PR_GP8,
      B.PR_GP9      = A.PR_GP9,
      B.PR_GP10     = A.PR_GP10,
      B.PR_GP11     = A.PR_GP11,
      B.REG_STCODE  = A.REG_STCODE,
      B.REG_DATE    = A.REG_DATE,
      B.REG_ISTCODE = A.REG_ISTCODE,
      B.REG_IDATE   = A.REG_IDATE
  WHEN NOT MATCHED THEN
    INSERT (
      PR_CODE, PR_QR_NAME, PR_GP1, PR_GP2, PR_GP3, PR_GP4, PR_GP5,
      PR_GP6, PR_GP7, PR_GP8, PR_GP9, PR_GP10, PR_GP11,
      REG_STCODE, REG_DATE, REG_ISTCODE, REG_IDATE
    )
    VALUES (
      A.PR_CODE, A.PR_QR_NAME, A.PR_GP1, A.PR_GP2, A.PR_GP3, A.PR_GP4, A.PR_GP5,
      A.PR_GP6, A.PR_GP7, A.PR_GP8, A.PR_GP9, A.PR_GP10, A.PR_GP11,
      A.REG_STCODE, A.REG_DATE, A.REG_ISTCODE, A.REG_IDATE
    );
  DBMS_OUTPUT.PUT_LINE('[SUCCESS] PR_QR MERGE 완료');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('[ERROR] ' || SQLERRM);
END;
EOD;

$conn = $dbconn_europe->dbconn;
oci_execute(oci_parse($conn, "BEGIN DBMS_OUTPUT.ENABLE(NULL); END;"));

$stmt = oci_parse($conn, $plsql_prqr);
if (!oci_execute($stmt)) {
    $e = oci_error($stmt);
    echo "[OCI ERROR] " . $e['message'] . "\n";
} else {
    oci_commit($conn);
}

$stid = oci_parse($conn, "BEGIN DBMS_OUTPUT.GET_LINE(:line, :status); END;");
$has_output = false;
while (true) {
    oci_bind_by_name($stid, ":line", $buffer, 1000000);
    oci_bind_by_name($stid, ":status", $status, 5);
    oci_execute($stid);
    if ($status != 0) break;
    if (trim($buffer) !== "") {
        echo $buffer . "\n";
        $has_output = true;
    }
}
if (!$has_output) {
    echo "[NO DBMS_OUTPUT] PR_QR\n";
}




// 1. BOM 테이블 컬럼 동기화 (필요시 컬럼 자동 추가)
$a_bom_columns = $dbconn_sperp->query_rows(
    "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = 'BOM' AND OWNER = 'PBKADMIN'"
);
$b_bom_columns = $dbconn_europe->query_array(
    "SELECT COLUMN_NAME FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = 'BOM' AND OWNER = 'PBKADMIN'",
    'COLUMN_NAME', 'COLUMN_NAME'
);

$alter_sqls = [];
foreach ($a_bom_columns as $col) {
    $name = $col['COLUMN_NAME'];
    if (!isset($b_bom_columns[$name])) {
        $type = $col['DATA_TYPE'];
        if ($type == 'VARCHAR2') {
            $type_str = "VARCHAR2(" . $col['DATA_LENGTH'] . ")";
        } else if ($type == 'NUMBER') {
            if ($col['DATA_PRECISION'] !== null && $col['DATA_SCALE'] !== null) {
                $type_str = "NUMBER(" . $col['DATA_PRECISION'] . "," . $col['DATA_SCALE'] . ")";
            } else if ($col['DATA_PRECISION'] !== null) {
                $type_str = "NUMBER(" . $col['DATA_PRECISION'] . ")";
            } else {
                $type_str = "NUMBER";
            }
        } else if ($type == 'DATE') {
            $type_str = "DATE";
        } else {
            $type_str = $type;
        }
        $alter_sqls[] = "ALTER TABLE PBKADMIN.BOM ADD $name $type_str";
    }
}
foreach ($alter_sqls as $sql) {
    echo "[ALTER] $sql\n";
    $dbconn_europe->query($sql);
}

// 2. BOM 테이블 MERGE (PL/SQL 블록)
$plsql_bom = <<<EOD
BEGIN
  MERGE INTO PBKADMIN.BOM B
  USING (
    SELECT * FROM PBKADMIN.BOM@ERP_DB_LINK
  ) A
  ON (B.BOM_CODE = A.BOM_CODE AND B.PR_CODE = A.PR_CODE)
  WHEN MATCHED THEN
    UPDATE SET
      B.BOM_QTY     = A.BOM_QTY,
      B.BOM_UNIT    = A.BOM_UNIT,
      B.BOM_UDATE   = A.BOM_UDATE,
      B.BOM_ORDER   = A.BOM_ORDER,
      B.UP1         = A.UP1,
      B.UP2         = A.UP2,
      B.REG_STCODE  = A.REG_STCODE,
      B.REG_DATE    = A.REG_DATE,
      B.REG_ISTCODE = A.REG_ISTCODE,
      B.REG_IDATE   = A.REG_IDATE,
      B.BOM_NEC     = A.BOM_NEC,
      B.BOM_MEMO    = A.BOM_MEMO,
      B.INPUT_QTY   = A.INPUT_QTY,
      B.GU          = A.GU
  WHEN NOT MATCHED THEN
    INSERT (
      BOM_CODE, PR_CODE, BOM_QTY, BOM_UNIT, BOM_UDATE, BOM_ORDER,
      UP1, UP2, REG_STCODE, REG_DATE, REG_ISTCODE, REG_IDATE,
      BOM_NEC, BOM_MEMO, INPUT_QTY, GU
    )
    VALUES (
      A.BOM_CODE, A.PR_CODE, A.BOM_QTY, A.BOM_UNIT, A.BOM_UDATE, A.BOM_ORDER,
      A.UP1, A.UP2, A.REG_STCODE, A.REG_DATE, A.REG_ISTCODE, A.REG_IDATE,
      A.BOM_NEC, A.BOM_MEMO, A.INPUT_QTY, A.GU
    );
  DBMS_OUTPUT.PUT_LINE('[SUCCESS] BOM MERGE 완료');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('[ERROR] ' || SQLERRM);
END;
EOD;

$conn = $dbconn_europe->dbconn;
oci_execute(oci_parse($conn, "BEGIN DBMS_OUTPUT.ENABLE(NULL); END;"));

$stmt = oci_parse($conn, $plsql_bom);
if (!oci_execute($stmt)) {
    $e = oci_error($stmt);
    echo "[OCI ERROR] " . $e['message'] . "\n";
} else {
    oci_commit($conn);
}

$stid = oci_parse($conn, "BEGIN DBMS_OUTPUT.GET_LINE(:line, :status); END;");
$has_output = false;
while (true) {
    oci_bind_by_name($stid, ":line", $buffer, 1000000);
    oci_bind_by_name($stid, ":status", $status, 5);
    oci_execute($stid);
    if ($status != 0) break;
    if (trim($buffer) !== "") {
        echo $buffer . "\n";
        $has_output = true;
    }
}
if (!$has_output) {
    echo "[NO DBMS_OUTPUT] BOM\n";
}




// 1. PRADT 테이블 컬럼 동기화 (필요시 컬럼 자동 추가)
$a_pradt_columns = $dbconn_sperp->query_rows(
    "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = 'PRADT' AND OWNER = 'PBKADMIN'"
);
$b_pradt_columns = $dbconn_europe->query_array(
    "SELECT COLUMN_NAME FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = 'PRADT' AND OWNER = 'PBKADMIN'",
    'COLUMN_NAME', 'COLUMN_NAME'
);

$alter_sqls = [];
foreach ($a_pradt_columns as $col) {
    $name = $col['COLUMN_NAME'];
    if (!isset($b_pradt_columns[$name])) {
        $type = $col['DATA_TYPE'];
        if ($type == 'VARCHAR2') {
            $type_str = "VARCHAR2(" . $col['DATA_LENGTH'] . ")";
        } else if ($type == 'NUMBER') {
            if ($col['DATA_PRECISION'] !== null && $col['DATA_SCALE'] !== null) {
                $type_str = "NUMBER(" . $col['DATA_PRECISION'] . "," . $col['DATA_SCALE'] . ")";
            } else if ($col['DATA_PRECISION'] !== null) {
                $type_str = "NUMBER(" . $col['DATA_PRECISION'] . ")";
            } else {
                $type_str = "NUMBER";
            }
        } else if ($type == 'CHAR') {
            $type_str = "CHAR(" . $col['DATA_LENGTH'] . ")";
        } else if ($type == 'DATE') {
            $type_str = "DATE";
        } else {
            $type_str = $type;
        }
        $alter_sqls[] = "ALTER TABLE PBKADMIN.PRADT ADD $name $type_str";
    }
}
foreach ($alter_sqls as $sql) {
    $dbconn_europe->iud_query($sql);
}

// 2. PRADT MERGE 쿼리 실행
$merge_sql = <<<EOT
BEGIN
  MERGE INTO PBKADMIN.PRADT B
  USING (
    SELECT * FROM PBKADMIN.PRADT@ERP_DB_LINK
  ) A
  ON (B.PR_CODE = A.PR_CODE)
  WHEN MATCHED THEN
    UPDATE SET
      B.PR_CODE1    = A.PR_CODE1,
      B.PR_CODE2    = A.PR_CODE2,
      B.PR_CODE3    = A.PR_CODE3,
      B.PR_CODE4    = A.PR_CODE4,
      B.PR_CODE5    = A.PR_CODE5,
      B.REG_STCODE  = A.REG_STCODE,
      B.REG_DATE    = A.REG_DATE,
      B.REG_ISTCODE = A.REG_ISTCODE,
      B.REG_IDATE   = A.REG_IDATE
  WHEN NOT MATCHED THEN
    INSERT (
      PR_CODE, PR_CODE1, PR_CODE2, PR_CODE3, PR_CODE4, PR_CODE5,
      REG_STCODE, REG_DATE, REG_ISTCODE, REG_IDATE
    )
    VALUES (
      A.PR_CODE, A.PR_CODE1, A.PR_CODE2, A.PR_CODE3, A.PR_CODE4, A.PR_CODE5,
      A.REG_STCODE, A.REG_DATE, A.REG_ISTCODE, A.REG_IDATE
    );
  DBMS_OUTPUT.PUT_LINE('[SUCCESS] PRADT MERGE 완료');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('[ERROR] ' || SQLERRM);
END;
EOT;


$conn = $dbconn_europe->dbconn;
oci_execute(oci_parse($conn, "BEGIN DBMS_OUTPUT.ENABLE(NULL); END;"));

$stmt = oci_parse($conn, $merge_sql);
if (!oci_execute($stmt)) {
    $e = oci_error($stmt);
    echo "[OCI ERROR] " . $e['message'] . "\n";
} else {
    oci_commit($conn);
}

$stid = oci_parse($conn, "BEGIN DBMS_OUTPUT.GET_LINE(:line, :status); END;");
$has_output = false;
while (true) {
    oci_bind_by_name($stid, ":line", $buffer, 1000000);
    oci_bind_by_name($stid, ":status", $status, 5);
    oci_execute($stid);
    if ($status != 0) break;
    if (trim($buffer) !== "") {
        echo $buffer . "\n";
        $has_output = true;
    }
}
if (!$has_output) {
    echo "[NO DBMS_OUTPUT] BOM\n";
}

// 커밋



// ... existing code ...

// 1. PR1 테이블 컬럼 동기화 (필요시 컬럼 자동 추가)
$a_pr1_columns = $dbconn_sperp->query_rows(
    "SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, DATA_PRECISION, DATA_SCALE FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = 'PR1' AND OWNER = 'PBKADMIN' AND DATA_TYPE NOT IN ('CLOB', 'BLOB')"
);
$b_pr1_columns = $dbconn_europe->query_array(
    "SELECT COLUMN_NAME FROM ALL_TAB_COLUMNS WHERE TABLE_NAME = 'PR1' AND OWNER = 'PBKADMIN'",
    'COLUMN_NAME', 'COLUMN_NAME'
);

$alter_sqls = [];
foreach ($a_pr1_columns as $col) {
    $name = $col['COLUMN_NAME'];
    if (!isset($b_pr1_columns[$name])) {
        $type = $col['DATA_TYPE'];
        if ($type == 'VARCHAR2') {
            $type_str = "VARCHAR2(" . $col['DATA_LENGTH'] . ")";
        } else if ($type == 'NUMBER') {
            if ($col['DATA_PRECISION'] !== null && $col['DATA_SCALE'] !== null) {
                $type_str = "NUMBER(" . $col['DATA_PRECISION'] . "," . $col['DATA_SCALE'] . ")";
            } else if ($col['DATA_PRECISION'] !== null) {
                $type_str = "NUMBER(" . $col['DATA_PRECISION'] . ")";
            } else {
                $type_str = "NUMBER";
            }
        } else if ($type == 'CHAR') {
            $type_str = "CHAR(" . $col['DATA_LENGTH'] . ")";
        } else if ($type == 'DATE') {
            $type_str = "DATE";
        } else {
            $type_str = $type;
        }
        $alter_sqls[] = "ALTER TABLE PBKADMIN.PR1 ADD $name $type_str";
    }
}
foreach ($alter_sqls as $sql) {
    $dbconn_europe->iud_query($sql);
}

// 2. PR1 MERGE 쿼리 실행 (CLOB 컬럼 제외)
$pr1_sql = <<<EOT
BEGIN
  MERGE INTO PBKADMIN.PR1 B
  USING (
    SELECT * FROM PBKADMIN.PR1@ERP_DB_LINK
  ) A
  ON (B.PR_CODE = A.PR_CODE)
  WHEN MATCHED THEN
    UPDATE SET
      B.ZEROTYPE        = A.ZEROTYPE,
      B.ORIGIN          = A.ORIGIN,
      B.CUSTODY         = A.CUSTODY,
      B.PERIOD          = A.PERIOD,
      B.SMEMO           = A.SMEMO,
      B.MEMOTYPE        = A.MEMOTYPE,
      B.ATTESTATION     = A.ATTESTATION,
      B.PRIZE           = A.PRIZE,
      B.DELIVERY        = A.DELIVERY,
      B.INGREDIENT      = A.INGREDIENT,
      B.PACKING         = A.PACKING,
      B.EFFICACY        = A.EFFICACY,
      B.RECOMMENDATION  = A.RECOMMENDATION,
      B.SMEMO2          = A.SMEMO2,
      B.MEMO2           = A.MEMO2,
      B.REG_STCODE      = A.REG_STCODE,
      B.REG_DATE        = A.REG_DATE,
      B.REG_ISTCODE     = A.REG_ISTCODE,
      B.REG_IDATE       = A.REG_IDATE,
      B.AS_OUP          = A.AS_OUP,
      B.AS_CHARGE       = A.AS_CHARGE,
      B.AS_FEE          = A.AS_FEE,
      B.AS_CENTEROUT    = A.AS_CENTEROUT
  WHEN NOT MATCHED THEN
    INSERT (
      PR_CODE, ZEROTYPE, ORIGIN, CUSTODY, PERIOD, SMEMO, MEMOTYPE,
      ATTESTATION, PRIZE, DELIVERY, INGREDIENT, PACKING, EFFICACY,
      RECOMMENDATION, SMEMO2, MEMO2,
      REG_STCODE, REG_DATE, REG_ISTCODE, REG_IDATE,
      AS_OUP, AS_CHARGE, AS_FEE, AS_CENTEROUT
    )
    VALUES (
      A.PR_CODE, A.ZEROTYPE, A.ORIGIN, A.CUSTODY, A.PERIOD, A.SMEMO, A.MEMOTYPE,
      A.ATTESTATION, A.PRIZE, A.DELIVERY, A.INGREDIENT, A.PACKING, A.EFFICACY,
      A.RECOMMENDATION, A.SMEMO2, A.MEMO2,
      A.REG_STCODE, A.REG_DATE, A.REG_ISTCODE, A.REG_IDATE,
      A.AS_OUP, A.AS_CHARGE, A.AS_FEE, A.AS_CENTEROUT
    );
  DBMS_OUTPUT.PUT_LINE('[SUCCESS] PR1 MERGE 완료 (CLOB 제외)');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('[ERROR] ' || SQLERRM);
END;
EOT;


$conn = $dbconn_europe->dbconn;
oci_execute(oci_parse($conn, "BEGIN DBMS_OUTPUT.ENABLE(NULL); END;"));

$stmt = oci_parse($conn, $pr1_sql);
if (!oci_execute($stmt)) {
    $e = oci_error($stmt);
    echo "[OCI ERROR] " . $e['message'] . "\n";
} else {
    oci_commit($conn);
}

$stid = oci_parse($conn, "BEGIN DBMS_OUTPUT.GET_LINE(:line, :status); END;");
$has_output = false;
while (true) {
    oci_bind_by_name($stid, ":line", $buffer, 1000000);
    oci_bind_by_name($stid, ":status", $status, 5);
    oci_execute($stid);
    if ($status != 0) break;
    if (trim($buffer) !== "") {
        echo $buffer . "\n";
        $has_output = true;
    }
}
if (!$has_output) {
    echo "[NO DBMS_OUTPUT] BOM\n";
}



## 스케즐 처리 상황 intra DB에 저장
crontab_execution(86400, "EUROPE TABLE SYNC 작업");












