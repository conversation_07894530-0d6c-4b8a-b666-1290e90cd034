<!DOCTYPE html><html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width; initial-scale=1.0; maximum-scale=1.0; user-scalable=0;">
<meta charset="utf-8">
<title>PHPExcel classes » \PHPExcel_Style_Color</title>
<meta name="author" content="<PERSON>">
<meta name="description" content="">
<link href="../css/template.css" rel="stylesheet" media="all">
<script src="../js/jquery-1.7.1.min.js" type="text/javascript"></script><script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script><script src="../js/jquery.mousewheel.min.js" type="text/javascript"></script><script src="../js/bootstrap.js" type="text/javascript"></script><script src="../js/template.js" type="text/javascript"></script><script src="../js/prettify/prettify.min.js" type="text/javascript"></script><link rel="shortcut icon" href="../img/favicon.ico">
<link rel="apple-touch-icon" href="../img/apple-touch-icon.png">
<link rel="apple-touch-icon" sizes="72x72" href="../img/apple-touch-icon-72x72.png">
<link rel="apple-touch-icon" sizes="114x114" href="../img/apple-touch-icon-114x114.png">
</head>
<body>
<div class="navbar navbar-fixed-top">
<div class="navbar-inner"><div class="container">
<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></a><a class="brand" href="../index.html">PHPExcel classes</a><div class="nav-collapse"><ul class="nav">
<li class="dropdown">
<a href="#api" class="dropdown-toggle" data-toggle="dropdown">
                                    API Documentation <b class="caret"></b></a><ul class="dropdown-menu">
<li><a>Packages</a></li>
<li><a href="../packages/Default.html"><i class="icon-folder-open"></i> Default</a></li>
<li><a href="../packages/JAMA.html"><i class="icon-folder-open"></i> JAMA</a></li>
<li><a href="../packages/JAMA%0D%0ACholesky%20decomposition%20class%0D%0AFor%20a%20symmetric,%20positive%20definite%20matrix%20A,%20the%20Cholesky%20decomposition%0D%0Ais%20an%20lower%20triangular%20matrix%20L%20so%20that%20A%20=%20L*L'.html"><i class="icon-folder-open"></i> JAMA
Cholesky decomposition class
For a symmetric, positive definite matrix A, the Cholesky decomposition
is an lower triangular matrix L so that A = L*L'</a></li>
<li><a href="../packages/JAMA%0D%0AClass%20to%20obtain%20eigenvalues%20and%20eigenvectors%20of%20a%20real%20matrix.html"><i class="icon-folder-open"></i> JAMA
Class to obtain eigenvalues and eigenvectors of a real matrix</a></li>
<li><a href="../packages/JAMA%0D%0AError%20handling.html"><i class="icon-folder-open"></i> JAMA
Error handling</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20LU%20decomposition%20is%20an%20m-by-n%0D%0Aunit%20lower%20triangular%20matrix%20L,%20an%20n-by-n%20upper%20triangular%20matrix%20U,%0D%0Aand%20a%20permutation%20vector%20piv%20of%20length%20m%20so%20that%20A(piv,:)%20=%20L*U.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the LU decomposition is an m-by-n
unit lower triangular matrix L, an n-by-n upper triangular matrix U,
and a permutation vector piv of length m so that A(piv,:) = L*U</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20QR%20decomposition%20is%20an%20m-by-n%0D%0Aorthogonal%20matrix%20Q%20and%20an%20n-by-n%20upper%20triangular%20matrix%20R%20so%20that%0D%0AA%20=%20Q*R.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the QR decomposition is an m-by-n
orthogonal matrix Q and an n-by-n upper triangular matrix R so that
A = Q*R</a></li>
<li><a href="../packages/JAMA%0D%0AFor%20an%20m-by-n%20matrix%20A%20with%20m%20&gt;=%20n,%20the%20singular%20value%20decomposition%20is%0D%0Aan%20m-by-n%20orthogonal%20matrix%20U,%20an%20n-by-n%20diagonal%20matrix%20S,%20and%0D%0Aan%20n-by-n%20orthogonal%20matrix%20V%20so%20that%20A%20=%20U*S*V'.html"><i class="icon-folder-open"></i> JAMA
For an m-by-n matrix A with m &gt;= n, the singular value decomposition is
an m-by-n orthogonal matrix U, an n-by-n diagonal matrix S, and
an n-by-n orthogonal matrix V so that A = U*S*V'</a></li>
<li><a href="../packages/JAMA%0D%0APythagorean%20Theorem:%0D%0Aa%20=%203%0D%0Ab%20=%204%0D%0Ar%20=%20sqrt(square(a)%20+%20square(b))%0D%0Ar%20=%205%0D%0Ar%20=%20sqrt(a%5E2%20+%20b%5E2)%20without%20under.overflow.html"><i class="icon-folder-open"></i> JAMA
Pythagorean Theorem:
a = 3
b = 4
r = sqrt(square(a) + square(b))
r = 5
r = sqrt(a^2 + b^2) without under/overflow</a></li>
<li><a href="../packages/PHPExcel.html"><i class="icon-folder-open"></i> PHPExcel</a></li>
</ul>
</li>
<li class="dropdown" id="charts-menu">
<a href="#charts" class="dropdown-toggle" data-toggle="dropdown">
                                    Charts <b class="caret"></b></a><ul class="dropdown-menu"><li><a href="../graph_class.html"><i class="icon-list-alt"></i> Class hierarchy diagram</a></li></ul>
</li>
<li class="dropdown" id="reports-menu">
<a href="#reports" class="dropdown-toggle" data-toggle="dropdown">
                                    Reports <b class="caret"></b></a><ul class="dropdown-menu">
<li><a href="../errors.html"><i class="icon-remove-sign"></i> Errors 
                <span class="label label-info">551</span></a></li>
<li><a href="../markers.html"><i class="icon-map-marker"></i> Markers 
                <ul>
<li>todo 
                <span class="label label-info">19</span>
</li>
<li>fixme 
                <span class="label label-info">10</span>
</li>
</ul></a></li>
<li><a href="../deprecated.html"><i class="icon-stop"></i> Deprecated elements 
                <span class="label label-info">12</span></a></li>
</ul>
</li>
</ul></div>
</div></div>
<div class="go_to_top"><a href="#___" style="color: inherit">Back to top  <i class="icon-upload icon-white"></i></a></div>
</div>
<div id="___" class="container">
<noscript><div class="alert alert-warning">
                            Javascript is disabled; several features are only available
                            if Javascript is enabled.
                        </div></noscript>
<div class="row">
<div class="span4">
<span class="btn-group visibility" data-toggle="buttons-checkbox"><button class="btn public active" title="Show public elements">Public</button><button class="btn protected" title="Show protected elements">Protected</button><button class="btn private" title="Show private elements">Private</button><button class="btn inherited active" title="Show inherited elements">Inherited</button></span><div class="btn-group view pull-right" data-toggle="buttons-radio">
<button class="btn details" title="Show descriptions and method names"><i class="icon-list"></i></button><button class="btn simple" title="Show only method names"><i class="icon-align-justify"></i></button>
</div>
<ul class="side-nav nav nav-list">
<li class="nav-header">
<i class="icon-custom icon-method"></i> Methods
                    <ul>
<li class="method public inherited"><a href="#method___clone" title="__clone :: Implement PHP __clone to create a deep clone, not just a shallow copy."><span class="description">Implement PHP __clone to create a deep clone, not just a shallow copy.</span><pre>__clone()</pre></a></li>
<li class="method public "><a href="#method___construct" title="__construct :: Create a new PHPExcel_Style_Color"><span class="description">Create a new PHPExcel_Style_Color</span><pre>__construct()</pre></a></li>
<li class="method public "><a href="#method_applyFromArray" title="applyFromArray :: Apply styles from array"><span class="description">Apply styles from array</span><pre>applyFromArray()</pre></a></li>
<li class="method public "><a href="#method_bindParent" title="bindParent :: Bind parent."><span class="description">Bind parent.</span><pre>bindParent()</pre></a></li>
<li class="method public "><a href="#method_changeBrightness" title="changeBrightness :: Adjust the brightness of a color"><span class="description">Adjust the brightness of a color</span><pre>changeBrightness()</pre></a></li>
<li class="method public "><a href="#method_getARGB" title="getARGB :: Get ARGB"><span class="description">Get ARGB</span><pre>getARGB()</pre></a></li>
<li class="method public inherited"><a href="#method_getActiveCell" title="getActiveCell :: Get the currently active cell coordinate in currently active sheet."><span class="description">Get the currently active cell coordinate in currently active sheet.</span><pre>getActiveCell()</pre></a></li>
<li class="method public inherited"><a href="#method_getActiveSheet" title="getActiveSheet :: Get the currently active sheet."><span class="description">Get the currently active sheet.</span><pre>getActiveSheet()</pre></a></li>
<li class="method public "><a href="#method_getBlue" title="getBlue :: Get the blue colour component of an RGB value"><span class="description">Get the blue colour component of an RGB value</span><pre>getBlue()</pre></a></li>
<li class="method public "><a href="#method_getGreen" title="getGreen :: Get the green colour component of an RGB value"><span class="description">Get the green colour component of an RGB value</span><pre>getGreen()</pre></a></li>
<li class="method public "><a href="#method_getHashCode" title="getHashCode :: Get hash code"><span class="description">Get hash code</span><pre>getHashCode()</pre></a></li>
<li class="method public inherited"><a href="#method_getIsSupervisor" title="getIsSupervisor :: Is this a supervisor or a cell style component?"><span class="description">Is this a supervisor or a cell style component?</span><pre>getIsSupervisor()</pre></a></li>
<li class="method public "><a href="#method_getRGB" title="getRGB :: Get RGB"><span class="description">Get RGB</span><pre>getRGB()</pre></a></li>
<li class="method public "><a href="#method_getRed" title="getRed :: Get the red colour component of an RGB value"><span class="description">Get the red colour component of an RGB value</span><pre>getRed()</pre></a></li>
<li class="method public inherited"><a href="#method_getSelectedCells" title="getSelectedCells :: Get the currently active cell coordinate in currently active sheet."><span class="description">Get the currently active cell coordinate in currently active sheet.</span><pre>getSelectedCells()</pre></a></li>
<li class="method public "><a href="#method_getSharedComponent" title="getSharedComponent :: Get the shared style component for the currently active cell in currently active sheet."><span class="description">Get the shared style component for the currently active cell in currently active sheet.</span><pre>getSharedComponent()</pre></a></li>
<li class="method public "><a href="#method_getStyleArray" title="getStyleArray :: Build style array from subcomponents"><span class="description">Build style array from subcomponents</span><pre>getStyleArray()</pre></a></li>
<li class="method public "><a href="#method_indexedColor" title="indexedColor :: Get indexed color"><span class="description">Get indexed color</span><pre>indexedColor()</pre></a></li>
<li class="method public "><a href="#method_setARGB" title="setARGB :: Set ARGB"><span class="description">Set ARGB</span><pre>setARGB()</pre></a></li>
<li class="method public "><a href="#method_setRGB" title="setRGB :: Set RGB"><span class="description">Set RGB</span><pre>setRGB()</pre></a></li>
</ul>
</li>
<li class="nav-header private">» Private
                    <ul><li class="method private "><a href="#method__getColourComponent" title="_getColourComponent :: Get a specified colour component of an RGB value"><span class="description">Get a specified colour component of an RGB value</span><pre>_getColourComponent()</pre></a></li></ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-property"></i> Properties
                    <ul></ul>
</li>
<li class="nav-header protected">» Protected
                    <ul>
<li class="property protected "><a href="#property__argb" title="$_argb :: ARGB - Alpha RGB"><span class="description"></span><pre>$_argb</pre></a></li>
<li class="property protected "><a href="#property__indexedColors" title="$_indexedColors :: Indexed colors array"><span class="description"></span><pre>$_indexedColors</pre></a></li>
<li class="property protected inherited"><a href="#property__isSupervisor" title="$_isSupervisor :: Supervisor?"><span class="description"></span><pre>$_isSupervisor</pre></a></li>
<li class="property protected inherited"><a href="#property__parent" title="$_parent :: Parent."><span class="description"></span><pre>$_parent</pre></a></li>
<li class="property protected "><a href="#property__parentPropertyName" title="$_parentPropertyName :: Parent property name"><span class="description"></span><pre>$_parentPropertyName</pre></a></li>
</ul>
</li>
<li class="nav-header">
<i class="icon-custom icon-constant"></i> Constants
                    <ul>
<li class="constant  "><a href="#constant_COLOR_BLACK" title="COLOR_BLACK :: "><span class="description">COLOR_BLACK</span><pre>COLOR_BLACK</pre></a></li>
<li class="constant  "><a href="#constant_COLOR_BLUE" title="COLOR_BLUE :: "><span class="description">COLOR_BLUE</span><pre>COLOR_BLUE</pre></a></li>
<li class="constant  "><a href="#constant_COLOR_DARKBLUE" title="COLOR_DARKBLUE :: "><span class="description">COLOR_DARKBLUE</span><pre>COLOR_DARKBLUE</pre></a></li>
<li class="constant  "><a href="#constant_COLOR_DARKGREEN" title="COLOR_DARKGREEN :: "><span class="description">COLOR_DARKGREEN</span><pre>COLOR_DARKGREEN</pre></a></li>
<li class="constant  "><a href="#constant_COLOR_DARKRED" title="COLOR_DARKRED :: "><span class="description">COLOR_DARKRED</span><pre>COLOR_DARKRED</pre></a></li>
<li class="constant  "><a href="#constant_COLOR_DARKYELLOW" title="COLOR_DARKYELLOW :: "><span class="description">COLOR_DARKYELLOW</span><pre>COLOR_DARKYELLOW</pre></a></li>
<li class="constant  "><a href="#constant_COLOR_GREEN" title="COLOR_GREEN :: "><span class="description">COLOR_GREEN</span><pre>COLOR_GREEN</pre></a></li>
<li class="constant  "><a href="#constant_COLOR_RED" title="COLOR_RED :: "><span class="description">COLOR_RED</span><pre>COLOR_RED</pre></a></li>
<li class="constant  "><a href="#constant_COLOR_WHITE" title="COLOR_WHITE :: "><span class="description">COLOR_WHITE</span><pre>COLOR_WHITE</pre></a></li>
<li class="constant  "><a href="#constant_COLOR_YELLOW" title="COLOR_YELLOW :: "><span class="description">COLOR_YELLOW</span><pre>COLOR_YELLOW</pre></a></li>
</ul>
</li>
</ul>
</div>
<div class="span8">
<a id="\PHPExcel_Style_Color"></a><ul class="breadcrumb">
<li>
<a href="../index.html"><i class="icon-custom icon-class"></i></a><span class="divider">\</span>
</li>
<li><a href="../namespaces/global.html">global</a></li>
<li class="active">
<span class="divider">\</span><a href="../classes/PHPExcel_Style_Color.html">PHPExcel_Style_Color</a>
</li>
</ul>
<div class="element class">
<p class="short_description">PHPExcel_Style_Color</p>
<div class="details">
<div class="long_description"></div>
<table class="table table-bordered">
<tr>
<th>category</th>
<td>PHPExcel</td>
</tr>
<tr>
<th>package</th>
<td><a href="../packages/PHPExcel.Style.html">PHPExcel_Style</a></td>
</tr>
<tr>
<th>copyright</th>
<td>Copyright (c) 2006 - 2014 PHPExcel (http://www.codeplex.com/PHPExcel)</td>
</tr>
</table>
<h3>
<i class="icon-custom icon-method"></i> Methods</h3>
<a id="method___clone"></a><div class="element clickable method public method___clone" data-toggle="collapse" data-target=".method___clone .collapse">
<h2>Implement PHP __clone to create a deep clone, not just a shallow copy.</h2>
<pre>__clone() </pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::__clone()</td>
</tr></table>
</div></div>
</div>
<a id="method___construct"></a><div class="element clickable method public method___construct" data-toggle="collapse" data-target=".method___construct .collapse">
<h2>Create a new PHPExcel_Style_Color</h2>
<pre>__construct(string $pARGB, boolean $isSupervisor, boolean $isConditional) </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pARGB</h4>
<code>string</code><p>ARGB value for the colour</p></div>
<div class="subelement argument">
<h4>$isSupervisor</h4>
<code>boolean</code><p>Flag indicating if this is a supervisor or not
								Leave this value at default unless you understand exactly what
									its ramifications are</p></div>
<div class="subelement argument">
<h4>$isConditional</h4>
<code>boolean</code><p>Flag indicating if this is a conditional style or not
								Leave this value at default unless you understand exactly what
									its ramifications are</p></div>
</div></div>
</div>
<a id="method_applyFromArray"></a><div class="element clickable method public method_applyFromArray" data-toggle="collapse" data-target=".method_applyFromArray .collapse">
<h2>Apply styles from array</h2>
<pre>applyFromArray(array $pStyles) : <a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><pre><code>$objPHPExcel->getActiveSheet()->getStyle('B2')->getFont()->getColor()->applyFromArray( array('rgb' => '808080') );
</code></pre></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pStyles</h4>
<code>array</code><p>Array containing style information</p></div>
<h3>Exceptions</h3>
<table class="table table-bordered"><tr>
<th><code><a href="../classes/PHPExcel_Exception.html">\PHPExcel_Exception</a></code></th>
<td></td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></code></div>
</div></div>
</div>
<a id="method_bindParent"></a><div class="element clickable method public method_bindParent" data-toggle="collapse" data-target=".method_bindParent .collapse">
<h2>Bind parent.</h2>
<pre>bindParent(mixed $parent, string $parentPropertyName) : <a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used for supervisor</p></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$parent</h4>
<code>mixed</code>
</div>
<div class="subelement argument">
<h4>$parentPropertyName</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></code></div>
</div></div>
</div>
<a id="method_changeBrightness"></a><div class="element clickable method public method_changeBrightness" data-toggle="collapse" data-target=".method_changeBrightness .collapse">
<h2>Adjust the brightness of a color</h2>
<pre>changeBrightness(string $hex, float $adjustPercentage) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$hex</h4>
<code>string</code><p>The colour as an RGBA or RGB value (e.g. FF00CCCC or CCDDEE)</p>
</div>
<div class="subelement argument">
<h4>$adjustPercentage</h4>
<code>float</code><p>The percentage by which to adjust the colour as a float from -1 to 1</p>
</div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>The adjusted colour as an RGBA or RGB value (e.g. FF00CCCC or CCDDEE)</div>
</div></div>
</div>
<a id="method_getARGB"></a><div class="element clickable method public method_getARGB" data-toggle="collapse" data-target=".method_getARGB .collapse">
<h2>Get ARGB</h2>
<pre>getARGB() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getActiveCell"></a><div class="element clickable method public method_getActiveCell" data-toggle="collapse" data-target=".method_getActiveCell .collapse">
<h2>Get the currently active cell coordinate in currently active sheet.</h2>
<pre>getActiveCell() : string</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used for supervisor</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::getActiveCell()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>E.g. 'A1'</div>
</div></div>
</div>
<a id="method_getActiveSheet"></a><div class="element clickable method public method_getActiveSheet" data-toggle="collapse" data-target=".method_getActiveSheet .collapse">
<h2>Get the currently active sheet.</h2>
<pre>getActiveSheet() : <a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used for supervisor</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::getActiveSheet()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Worksheet.html">\PHPExcel_Worksheet</a></code></div>
</div></div>
</div>
<a id="method_getBlue"></a><div class="element clickable method public method_getBlue" data-toggle="collapse" data-target=".method_getBlue .collapse">
<h2>Get the blue colour component of an RGB value</h2>
<pre>getBlue(string $RGB, boolean $hex) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$RGB</h4>
<code>string</code><p>The colour as an RGB value (e.g. FF00CCCC or CCDDEE</p>
</div>
<div class="subelement argument">
<h4>$hex</h4>
<code>boolean</code><p>Flag indicating whether the component should be returned as a hex or a
								decimal value</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>The blue colour component</div>
</div></div>
</div>
<a id="method_getGreen"></a><div class="element clickable method public method_getGreen" data-toggle="collapse" data-target=".method_getGreen .collapse">
<h2>Get the green colour component of an RGB value</h2>
<pre>getGreen(string $RGB, boolean $hex) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$RGB</h4>
<code>string</code><p>The colour as an RGB value (e.g. FF00CCCC or CCDDEE</p>
</div>
<div class="subelement argument">
<h4>$hex</h4>
<code>boolean</code><p>Flag indicating whether the component should be returned as a hex or a
								decimal value</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>The green colour component</div>
</div></div>
</div>
<a id="method_getHashCode"></a><div class="element clickable method public method_getHashCode" data-toggle="collapse" data-target=".method_getHashCode .collapse">
<h2>Get hash code</h2>
<pre>getHashCode() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>Hash code</div>
</div></div>
</div>
<a id="method_getIsSupervisor"></a><div class="element clickable method public method_getIsSupervisor" data-toggle="collapse" data-target=".method_getIsSupervisor .collapse">
<h2>Is this a supervisor or a cell style component?</h2>
<pre>getIsSupervisor() : boolean</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::getIsSupervisor()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response"><code>boolean</code></div>
</div></div>
</div>
<a id="method_getRGB"></a><div class="element clickable method public method_getRGB" data-toggle="collapse" data-target=".method_getRGB .collapse">
<h2>Get RGB</h2>
<pre>getRGB() : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Returns</h3>
<div class="subelement response"><code>string</code></div>
</div></div>
</div>
<a id="method_getRed"></a><div class="element clickable method public method_getRed" data-toggle="collapse" data-target=".method_getRed .collapse">
<h2>Get the red colour component of an RGB value</h2>
<pre>getRed(string $RGB, boolean $hex) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$RGB</h4>
<code>string</code><p>The colour as an RGB value (e.g. FF00CCCC or CCDDEE</p>
</div>
<div class="subelement argument">
<h4>$hex</h4>
<code>boolean</code><p>Flag indicating whether the component should be returned as a hex or a
								decimal value</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>The red colour component</div>
</div></div>
</div>
<a id="method_getSelectedCells"></a><div class="element clickable method public method_getSelectedCells" data-toggle="collapse" data-target=".method_getSelectedCells .collapse">
<h2>Get the currently active cell coordinate in currently active sheet.</h2>
<pre>getSelectedCells() : string</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used for supervisor</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::getSelectedCells()</td>
</tr></table>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>E.g. 'A1'</div>
</div></div>
</div>
<a id="method_getSharedComponent"></a><div class="element clickable method public method_getSharedComponent" data-toggle="collapse" data-target=".method_getSharedComponent .collapse">
<h2>Get the shared style component for the currently active cell in currently active sheet.</h2>
<pre>getSharedComponent() : <a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used for style supervisor</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></code></div>
</div></div>
</div>
<a id="method_getStyleArray"></a><div class="element clickable method public method_getStyleArray" data-toggle="collapse" data-target=".method_getStyleArray .collapse">
<h2>Build style array from subcomponents</h2>
<pre>getStyleArray(array $array) : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$array</h4>
<code>array</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code>array</code></div>
</div></div>
</div>
<a id="method_indexedColor"></a><div class="element clickable method public method_indexedColor" data-toggle="collapse" data-target=".method_indexedColor .collapse">
<h2>Get indexed color</h2>
<pre>indexedColor(int $pIndex, boolean $background) : <a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pIndex</h4>
<code>int</code><p>Index entry point into the colour array</p></div>
<div class="subelement argument">
<h4>$background</h4>
<code>boolean</code><p>Flag to indicate whether default background or foreground colour
                                        should be returned if the indexed colour doesn't exist</p>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></code></div>
</div></div>
</div>
<a id="method_setARGB"></a><div class="element clickable method public method_setARGB" data-toggle="collapse" data-target=".method_setARGB .collapse">
<h2>Set ARGB</h2>
<pre>setARGB(string $pValue) : <a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code>
</div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></code></div>
</div></div>
</div>
<a id="method_setRGB"></a><div class="element clickable method public method_setRGB" data-toggle="collapse" data-target=".method_setRGB .collapse">
<h2>Set RGB</h2>
<pre>setRGB(string $pValue) : <a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$pValue</h4>
<code>string</code><p>RGB value</p></div>
<h3>Returns</h3>
<div class="subelement response"><code><a href="../classes/PHPExcel_Style_Color.html">\PHPExcel_Style_Color</a></code></div>
</div></div>
</div>
<a id="method__getColourComponent"></a><div class="element clickable method private method__getColourComponent" data-toggle="collapse" data-target=".method__getColourComponent .collapse">
<h2>Get a specified colour component of an RGB value</h2>
<pre>_getColourComponent(string $RGB, int $offset, boolean $hex) : string</pre>
<div class="labels"><span class="label">Static</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>private</th>
<td></td>
</tr></table>
<h3>Parameters</h3>
<div class="subelement argument">
<h4>$RGB</h4>
<code>string</code><p>The colour as an RGB value (e.g. FF00CCCC or CCDDEE</p>
</div>
<div class="subelement argument">
<h4>$offset</h4>
<code>int</code><p>Position within the RGB value to extract</p></div>
<div class="subelement argument">
<h4>$hex</h4>
<code>boolean</code><p>Flag indicating whether the component should be returned as a hex or a
								decimal value</p></div>
<h3>Returns</h3>
<div class="subelement response">
<code>string</code>The extracted colour component</div>
</div></div>
</div>
<h3>
<i class="icon-custom icon-property"></i> Properties</h3>
<a id="property__argb"> </a><div class="element clickable property protected property__argb" data-toggle="collapse" data-target=".property__argb .collapse">
<h2></h2>
<pre>$_argb : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__indexedColors"> </a><div class="element clickable property protected property__indexedColors" data-toggle="collapse" data-target=".property__indexedColors .collapse">
<h2></h2>
<pre>$_indexedColors : array</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="property__isSupervisor"> </a><div class="element clickable property protected property__isSupervisor" data-toggle="collapse" data-target=".property__isSupervisor .collapse">
<h2></h2>
<pre>$_isSupervisor : boolean</pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::$$_isSupervisor</td>
</tr></table>
</div></div>
</div>
<a id="property__parent"> </a><div class="element clickable property protected property__parent" data-toggle="collapse" data-target=".property__parent .collapse">
<h2></h2>
<pre>$_parent : <a href="../classes/PHPExcel_Style.html">\PHPExcel_Style</a></pre>
<div class="labels"><span class="label">Inherited</span></div>
<div class="row collapse"><div class="detail-description">
<div class="long_description"><p>Only used for supervisor</p></div>
<table class="table table-bordered"><tr>
<th>inherited_from</th>
<td>\PHPExcel_Style_Supervisor::$$_parent</td>
</tr></table>
</div></div>
</div>
<a id="property__parentPropertyName"> </a><div class="element clickable property protected property__parentPropertyName" data-toggle="collapse" data-target=".property__parentPropertyName .collapse">
<h2></h2>
<pre>$_parentPropertyName : string</pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<h3>
<i class="icon-custom icon-constant"></i> Constants</h3>
<a id="constant_COLOR_BLACK"> </a><div class="element clickable constant  constant_COLOR_BLACK" data-toggle="collapse" data-target=".constant_COLOR_BLACK .collapse">
<h2>COLOR_BLACK</h2>
<pre>COLOR_BLACK </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COLOR_BLUE"> </a><div class="element clickable constant  constant_COLOR_BLUE" data-toggle="collapse" data-target=".constant_COLOR_BLUE .collapse">
<h2>COLOR_BLUE</h2>
<pre>COLOR_BLUE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COLOR_DARKBLUE"> </a><div class="element clickable constant  constant_COLOR_DARKBLUE" data-toggle="collapse" data-target=".constant_COLOR_DARKBLUE .collapse">
<h2>COLOR_DARKBLUE</h2>
<pre>COLOR_DARKBLUE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COLOR_DARKGREEN"> </a><div class="element clickable constant  constant_COLOR_DARKGREEN" data-toggle="collapse" data-target=".constant_COLOR_DARKGREEN .collapse">
<h2>COLOR_DARKGREEN</h2>
<pre>COLOR_DARKGREEN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COLOR_DARKRED"> </a><div class="element clickable constant  constant_COLOR_DARKRED" data-toggle="collapse" data-target=".constant_COLOR_DARKRED .collapse">
<h2>COLOR_DARKRED</h2>
<pre>COLOR_DARKRED </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COLOR_DARKYELLOW"> </a><div class="element clickable constant  constant_COLOR_DARKYELLOW" data-toggle="collapse" data-target=".constant_COLOR_DARKYELLOW .collapse">
<h2>COLOR_DARKYELLOW</h2>
<pre>COLOR_DARKYELLOW </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COLOR_GREEN"> </a><div class="element clickable constant  constant_COLOR_GREEN" data-toggle="collapse" data-target=".constant_COLOR_GREEN .collapse">
<h2>COLOR_GREEN</h2>
<pre>COLOR_GREEN </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COLOR_RED"> </a><div class="element clickable constant  constant_COLOR_RED" data-toggle="collapse" data-target=".constant_COLOR_RED .collapse">
<h2>COLOR_RED</h2>
<pre>COLOR_RED </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COLOR_WHITE"> </a><div class="element clickable constant  constant_COLOR_WHITE" data-toggle="collapse" data-target=".constant_COLOR_WHITE .collapse">
<h2>COLOR_WHITE</h2>
<pre>COLOR_WHITE </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
<a id="constant_COLOR_YELLOW"> </a><div class="element clickable constant  constant_COLOR_YELLOW" data-toggle="collapse" data-target=".constant_COLOR_YELLOW .collapse">
<h2>COLOR_YELLOW</h2>
<pre>COLOR_YELLOW </pre>
<div class="labels"></div>
<div class="row collapse"><div class="detail-description"><div class="long_description"></div></div></div>
</div>
</div>
</div>
</div>
</div>
<div class="row"><footer class="span12">
            Template is built using <a href="http://twitter.github.com/bootstrap/">Twitter Bootstrap 2</a> and icons provided by <a href="http://glyphicons.com/">Glyphicons</a>.<br>
            Documentation is powered by <a href="http://www.phpdoc.org/">phpDocumentor 2.0.0a12</a> and<br>
            generated on 2014-03-02T15:27:37Z.<br></footer></div>
</div>
</body>
</html>
