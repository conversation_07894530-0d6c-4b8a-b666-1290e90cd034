#!/usr/local/bin/php -q
<?php
// 0 9 * * * php -q /home/<USER>/sperp/faulty_issue.psh
# PI전표 승인원 미등록 건
$ROOT_PATH = "/home/<USER>";
include($ROOT_PATH . "/inc/func.php");
include($ROOT_PATH . "/inc/db_controller.php");
include($ROOT_PATH . "/inc/Encode.php");

$dbconn_sperp_posbank = new DBController($db['sperp_posbank']);
if(empty($dbconn_sperp_posbank->success)) {
	echo "dbconn error [" . $db['sperp_posbank']['host'] . "] 데이터베이스 연결 실패";
}

$dbconn_posbank_intra = new DBController($db['posbank_intra']);
if(empty($dbconn_posbank_intra->success)) {
	echo "dbconn error [" . $db['posbank_intra']['host'] . "] 데이터베이스 연결 실패";
}
/**********************************************************/

if(in_array(date('w'),array("0","6"))){
	echo date("Y-m-d") . " - 휴무일\n";
	## 스케즐 처리 상황 intra DB에 저장
	crontab_execution(86400, "PI전표 승인원 미등록 알람");
	exit;
}

$SQL = "SELECT NAME FROM HOLIDAY_DATA WHERE to_char(HDATE,'YYYYMMDD')='".date('Ymd')."'";
$HOLIDAY_NM = $dbconn_sperp_posbank->query_one($SQL);
if($HOLIDAY_NM){
	echo date("Y-m-d") . " - 휴무일(".$HOLIDAY_NM.")\n";
	## 스케즐 처리 상황 intra DB에 저장
	crontab_execution(86400, "PI전표 승인원 미등록 알람");
	exit;
}

	$SQL = "SELECT
			    HID,PONO,(SELECT BAS_OP4 FROM BAS WHERE BAS_CODE = 'E066') ASSING_ST --,PR_CODE,PR_NAME,STATE
			FROM(
			SELECT
			    A.RCT_CODE||A.HDATE||A.HNO HID,C.PONO,
			    B.PR_CODE,PR.PR_NAME,
			    (SELECT NVL(D.PR_CODE,'0') FROM APPROVER_H D WHERE B.PR_CODE=D.PR_CODE AND D.STATE='1' GROUP BY D.PR_CODE) STATE
			    FROM EPIH A
			    LEFT JOIN EPID B ON A.RCT_CODE=B.RCT_CODE AND A.HDATE=B.HDATE AND A.HNO=B.HNO
			    LEFT JOIN PR ON B.PR_CODE=PR.PR_CODE
			    LEFT JOIN PRKIND PK ON (PR.PR_CODE=PK.PR_CODE)
			    LEFT JOIN EPOH C ON A.EPONO=C.RCT_CODE||C.HDATE||C.HNO
			    --LEFT JOIN APPROVER_H D ON B.PR_CODE=D.PR_CODE AND D.STATE='1'
			    WHERE B.FSTATE <> '4'
			    AND PK.PR_WJS = 'C4J'
			    AND A.HDATE >='********'
			) WHERE STATE IS NULL
		GROUP BY HID,PONO
	";

	//품목 으로 변경
	$SQL = "
	    SELECT
	       HDATE HID,PONO,PR_CODE,PR_NAME,(SELECT BAS_OP4 FROM BAS WHERE BAS_CODE = 'E066') ASSING_ST
	    FROM(
	    SELECT
		A.HDATE,C.PONO,B.PR_CODE,PR.PR_NAME,
		 (SELECT NVL(D.PR_CODE,'0') FROM APPROVER_H D WHERE B.PR_CODE=D.PR_CODE AND D.STATE='1' GROUP BY D.PR_CODE) STATE
		FROM EPIH A
		LEFT JOIN EPID B ON A.RCT_CODE=B.RCT_CODE AND A.HDATE=B.HDATE AND A.HNO=B.HNO
		LEFT JOIN PR ON B.PR_CODE=PR.PR_CODE
		LEFT JOIN PRKIND PK ON (PR.PR_CODE=PK.PR_CODE)
		LEFT JOIN EPOH C ON A.EPONO=C.RCT_CODE||C.HDATE||C.HNO
		WHERE B.FSTATE <> '4'
		AND PK.PR_WJS = 'C4J'
		AND A.HDATE >='********'
		ORDER BY HDATE,PONO
		) WHERE STATE IS NULL
		"
		;

$arrRow = $dbconn_sperp_posbank->query_rows($SQL);

$SQL2 = "SELECT BAS_OP4 as STCODE FROM BAS WHERE BAS_CODE='E066'";
$row_bas = $dbconn_sperp_posbank->query_one($SQL2);
// print_r($row_bas);
//$arr_ST = explode(",", $row_bas['STCODE']);

$arr_HID = [];
$arr_ST_HID = [];
if($arrRow){
	foreach($arrRow as $key => $row) {
		$arr_HID[$row['HID']] = $row;
		if($row['ASSING_ST']){
			$arr_ST = explode(",", $row['ASSING_ST']);
			if($arr_ST){
				foreach($arr_ST as $stcode) {
					$arr_ST_HID[$stcode][] = $row['HID'];
				}
			}
		}
	}
}


if($arr_ST_HID){
	foreach($arr_ST_HID as $stcode => $arr_data) {

		echo "[".$stcode."]\n";

		$title = "[PI 등록건 중 승인원 미등록된 품목] " . sizeof($arr_data) . "건";
		$content = "<div style=\"font-size:12px;\">";
		$content .= "<div><b>승인원 미등록시 생산이 불가하니 승인원 등록해 주세요</b></div>";
		if($arr_data){
			foreach($arr_data as $HID) {
				$row2 = $arr_HID[$HID];
				$content .= "<div style=\"padding:5px;margin:10px 0;border:1px solid #dddddd;\">";

				$content .= "<div><b>[품목코드]</b> ".$row2['PR_CODE']."</div>";
				$content .= "<div><b>[품목명]</b> ".$row2['PR_NAME']."</div>";
				$content .= "<div><b>[등록일]</b> ".$HID."</div>";
				$content .= "<div><b>[PONO]</b> ".$row2['PONO']."</div>";

				$content .= "</div>";
				$content .= "<div>";
			}
		}

//if($stcode=="100999"){
//$stcode="100695";

		//인트라넷 업무연락 보내는 함수(ERP 사원코드)
		$rs = intra_send_erp('',[$stcode],$title,$content);
		echo date("Y-m-d H:i:s")." 업무연락 발송 - ".$title . "(".$stcode.") - ";
		echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";

		// 구글워크스테이션 웹훅 보내기
		$goolgework_Params = [];
		$goolgework_Params['PROGRAM'] = "sperp";
		$goolgework_Params['GU'] = "qms";
		$goolgework_Params['ID'] = [$stcode];
		$goolgework_Params['PREVIEW'] = $title;
		$goolgework_Params['TITLE'] = "[PI 등록건 중 승인원 미등록된 품목]";
		$goolgework_Params['MESSAGE'][] = ["PI전표 승인원 미등록 " . sizeof($arr_data) . "건 있습니다."];
		$rs = goolgework_send($goolgework_Params);

		echo date("Y-m-d H:i:s")." 구글챗 발송 - ".$title . "(".$stcode.") - ";
		echo json_encode($rs, JSON_UNESCAPED_UNICODE)."\n";
//}
	}
}


##### End. 2024.04.26. 신규 스케줄링
###########################################


## 스케즐 처리 상황 monitor DB에 저장
crontab_execution(86400, "PI전표 승인원 미등록 알람");

echo date("Y-m-d H:i:s")." - 끝\n";
?>
